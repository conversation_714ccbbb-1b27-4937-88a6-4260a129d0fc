import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as cwl from 'aws-cdk-lib/aws-logs';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as kms from 'aws-cdk-lib/aws-kms';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import * as build from 'aws-cdk-lib/aws-codebuild';
import * as codePipeline from 'aws-cdk-lib/aws-codepipeline';
import * as codePipelineActions from 'aws-cdk-lib/aws-codepipeline-actions';
import * as events from 'aws-cdk-lib/aws-events';
import * as eventsTarget from 'aws-cdk-lib/aws-events-targets';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import * as codepipeline from 'aws-cdk-lib/aws-codepipeline';
import * as notifications from 'aws-cdk-lib/aws-codestarnotifications';
import * as sns from 'aws-cdk-lib/aws-sns';
import { IBatchParam } from 'params/interface';
import { CWLogToDataFirehose, CWLogToDataFirehoseProps } from '../cw-log-to-kinesis-data-firehose-construct';

interface BatchPipelineProps {
  /**
   * Project and environment prefix
   */
  pjPrefix: string;

  /**
   * The common prefix of the project
   */
  pjCommonPrefix: string;

  /**
   * Gevanni Service ID
   */
  serviceId: string;

  /**
   * Batch name
   */
  batchName: string;

  /**
   * ECS cluster it made by ecs-app-stack
   */
  cluster: ecs.ICluster;

  /**
   * Batch parameters for each task
   */
  batchParams: IBatchParam['taskParam'];

  /**
   * Firelens parameters
   */
  applicationLogTransferParam: {
    FluentBitImage: string;
    firelensLogGroupRemovalPolicy?: cdk.RemovalPolicy;
    firehoseArn: string;
    firehoseName: string;
  };

  /**
   * Parameters of log transfer to New Relic from code build
   */
  codeBuildLogTransferParam?: Pick<
    CWLogToDataFirehoseProps,
    'firehoseBucketLifecycleRules' | 'logRemovalPolicyParam' | 'secretArn'
  >;

  /**
   * Security group for the each of tasks
   */
  taskSecurityGroup: ec2.ISecurityGroup;

  /**
   * VPC
   */
  vpc: ec2.IVpc;

  /**
   * App key
   */
  appKey: kms.IKey;

  /**
   * ECS tasl execution role
   */
  taskExecutionRole: iam.IRole;

  /**
   * ECR lifecycle rules
   */
  ecrLifecycleRules?: ecr.LifecycleRule[];

  /**
   * ECR removal policy parameters
   */
  ecrRemovalPolicyParam?: {
    removalPolicy?: cdk.RemovalPolicy;
    emptyOnDelete?: boolean;
  };

  /**
   * Batch pipeline bucket removal policy parameters
   * If not specified, the bucket will be retained
   */
  pipelineBucketRemovalPolicyParam?: {
    removalPolicy: cdk.RemovalPolicy;
    autoDeleteObjects: boolean;
  };

  /**
   * Batch pipeline bucket removal policy parameters
   * If not specified, the bucket will be retained
   */
  apiTriggerEnvs?: {
    restApiId: string;
    rootResourceId: string;
    statemachineExecutionFunctionArn: string;
    apiRoleArn: string;
    batchTriggerModelName: string;
    stageName: string;
  };

  /**
   * SNS topic ARN for sending pipeline status
   */
  alarmTopic: sns.ITopic;
}

export class BatchPipeline extends Construct {
  public readonly taskSecrets: secretsmanager.ISecret[] = [];
  public readonly containerRepositories: ecr.IRepository[] = [];
  public readonly pipelineSourceBucket: s3.IBucket;

  constructor(scope: Construct, id: string, props: BatchPipelineProps) {
    super(scope, id);

    const sourceBucket = new s3.Bucket(this, 'SourceBucket', {
      versioned: true,
      eventBridgeEnabled: true,
      removalPolicy: props.pipelineBucketRemovalPolicyParam?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
      autoDeleteObjects: props.pipelineBucketRemovalPolicyParam?.autoDeleteObjects ?? false,
    });
    new ssm.StringParameter(this, 'SourceBucketParameter', {
      stringValue: sourceBucket.bucketName,
      parameterName: `/${props.pjPrefix}/batch/${props.batchName}/bucket/source`,
    });
    this.pipelineSourceBucket = sourceBucket;

    const stateMachineRole = new iam.Role(this, 'StateMachineRole', {
      assumedBy: new iam.ServicePrincipal('states.amazonaws.com'),
      inlinePolicies: {
        allowRunTask: new iam.PolicyDocument({
          statements: [
            // https://docs.aws.amazon.com/ja_jp/step-functions/latest/dg/connect-ecs.html
            new iam.PolicyStatement({
              actions: ['ecs:RunTask', 'ecs:DescribeTasks', 'ecs:StopTask', 'iam:PassRole'],
              resources: ['*'],
            }),
            new iam.PolicyStatement({
              actions: ['events:PutTargets', 'events:PutRule', 'events:DescribeRule'],
              resources: [
                `arn:aws:events:${cdk.Stack.of(this).region}:${
                  cdk.Stack.of(this).account
                }:rule/StepFunctionsGetEventsForECSTaskRule`,
              ],
            }),
          ],
        }),
      },
    });

    const defaultLifecycleRules: ecr.LifecycleRule[] = [
      {
        description: 'Keep last 10 images',
        maxImageCount: 10,
      },
    ];
    const lifecycleRules = props.ecrLifecycleRules ?? defaultLifecycleRules;

    const repositories = [];
    for (const batchParam of props.batchParams) {
      const { taskName, containerNames, crossAccountRoleArns } = batchParam;
      this._createTaskRole(
        props.pjPrefix,
        props.batchName,
        taskName,
        props.applicationLogTransferParam.firehoseArn,
        crossAccountRoleArns,
      );
      const secret = this._createSecret(props.pjPrefix, props.batchName, taskName);
      this.taskSecrets.push(secret);

      for (const containerName of containerNames) {
        const repositoryLogicalIdSuffix = `task/${taskName}/container/${containerName}`;
        const repository = this._createRepository(
          props.pjPrefix,
          props.batchName,
          repositoryLogicalIdSuffix,
          lifecycleRules,
          props.ecrRemovalPolicyParam?.removalPolicy,
          props.ecrRemovalPolicyParam?.emptyOnDelete,
        );
        repositories.push(repository);
      }
    }
    this.containerRepositories = repositories;

    const fireLensLogGroup = new cwl.LogGroup(this, 'FireLensLog', {
      // 保存期間3ヵ月とする
      retention: cwl.RetentionDays.THREE_MONTHS,
      encryptionKey: props.appKey,
      logGroupName: cdk.PhysicalName.GENERATE_IF_NEEDED,
      removalPolicy: props.applicationLogTransferParam?.firelensLogGroupRemovalPolicy ?? cdk.RemovalPolicy.RETAIN,
    });

    const codeBuildLogGroup = new cwl.LogGroup(this, 'Log', {
      retention: cwl.RetentionDays.ONE_MONTH,
      encryptionKey: props.appKey,
      logGroupName: cdk.PhysicalName.GENERATE_IF_NEEDED,
    });

    if (props.codeBuildLogTransferParam != undefined) {
      new CWLogToDataFirehose(this, 'MetricFirehose', {
        firehoseStreamName: `${props.serviceId}-${props.batchName}-Build`,
        firehoseLogGroupName: `/aws/firehose/${props.pjPrefix}/${props.batchName}/buildlogs`,
        sourceCWLogGroup: codeBuildLogGroup,
        httpEndpointUrl: 'https://aws-api.newrelic.com/firehose/v1',
        httpEndpointName: 'New Relic',
        s3BackupMode: 'FailedDataOnly',
        ...props.codeBuildLogTransferParam,
      });
    }

    const privateSubnets = cdk.Lazy.list({
      produce: () =>
        props.vpc.selectSubnets({
          subnetGroupName: 'Private',
        }).subnetIds,
    });

    const deployProject = new build.PipelineProject(this, 'DeployProject', {
      environment: {
        buildImage: build.LinuxLambdaBuildImage.AMAZON_LINUX_2023_PYTHON_3_12,
        computeType: build.ComputeType.LAMBDA_1GB,
      },
      environmentVariables: {
        CLUSTER_NAME: {
          value: props.cluster.clusterName,
        },
        FIRELENS_IMAGE_URI: {
          value: props.applicationLogTransferParam.FluentBitImage,
        },
        STREAM_NAME: {
          value: props.applicationLogTransferParam.firehoseName,
        },
        LOG_GROUP_FIRELENS: {
          value: fireLensLogGroup.logGroupName,
        },
        EXECUTION_ROLE_ARN: {
          value: props.taskExecutionRole.roleArn,
        },
        SUBNET_ID_0: {
          value: cdk.Fn.select(0, privateSubnets),
        },
        SUBNET_ID_1: {
          value: cdk.Fn.select(1, privateSubnets),
        },
        SUBNET_ID_2: {
          value: cdk.Fn.select(2, privateSubnets),
        },
        SECURITY_GROUP_ID: {
          value: props.taskSecurityGroup.securityGroupId,
        },
        BATCH_NAME: {
          value: props.batchName,
        },
        STATE_MACHINE_ROLE_ARN: {
          value: stateMachineRole.roleArn,
        },
        STATE_MACHINE_EXECUTOR_ROLE_ARN: {
          value: ssm.StringParameter.valueFromLookup(this, `/${props.pjCommonPrefix}/role/eventBridgeServiceRoleArn`),
        },
        STATE_MACHINE_EXECUTOR_ARN: {
          value: ssm.StringParameter.valueFromLookup(this, `/${props.pjCommonPrefix}/lambda/stateMachineExecutorArn`),
        },
        GEVANNI_SERVICE_NAME: {
          value: props.serviceId,
        },
        SNS_TOPIC_ARN: {
          value: props.alarmTopic.topicArn,
        },
        ...(props.apiTriggerEnvs != null
          ? {
              REST_API_ID: { value: props.apiTriggerEnvs.restApiId },
              ROOT_RESOURCE_ID: { value: props.apiTriggerEnvs.rootResourceId },
              STATE_MACHINE_EXECUTOR_ARN_FOR_API: { value: props.apiTriggerEnvs.statemachineExecutionFunctionArn },
              API_ROLE: { value: props.apiTriggerEnvs.apiRoleArn },
              BATCH_TRIGGER_BODY_MODEL: { value: props.apiTriggerEnvs.batchTriggerModelName },
              API_STAGE_NAME: { value: props.apiTriggerEnvs.stageName },
            }
          : {}),
      },
      buildSpec: build.BuildSpec.fromObject({
        version: '0.2',
        env: {},
        phases: {
          build: {
            commands: [
              'ls -lR',
              'python -m venv .venv && . ./.venv/bin/activate',
              'pip install -r requirements.txt > /dev/null',
              'python main.py',
            ],
          },
        },
      }),
      logging: {
        cloudWatch: {
          logGroup: codeBuildLogGroup,
        },
      },
    });

    deployProject.addToRolePolicy(
      new iam.PolicyStatement({
        actions: [
          'ecs:RegisterTaskDefinition',
          'ecs:ListTagsForResource',
          'ecs:TagResource',
          'states:DescribeStateMachine',
          'states:CreateStateMachine',
          'states:UpdateStateMachine',
          'states:ListTagsForResource',
          'states:TagResource',
          'scheduler:GetSchedule',
          'scheduler:UpdateSchedule',
          'scheduler:CreateScheduleGroup',
          'scheduler:GetScheduleGroup',
          'scheduler:CreateSchedule',
          'scheduler:ListTagsForResource',
          'scheduler:TagResource',
          'apigateway:POST',
          'apigateway:GET',
          'apigateway:PUT',
          'apigateway:PATCH',
          'ssm:GetParameter',
          'sts:AssumeRole',
          'iam:PassRole',
        ],
        resources: ['*'],
      }),
    );

    // Add permission for SNS for StateMachine
    stateMachineRole.addToPolicy(
      new iam.PolicyStatement({
        actions: ['sns:Publish'],
        resources: [props.alarmTopic.topicArn],
      }),
    );

    const sourceOutput = new codePipeline.Artifact();

    const pipeline = new codePipeline.Pipeline(this, 'Pipeline', {
      pipelineType: codePipeline.PipelineType.V1,
    });

    pipeline.addStage({
      stageName: 'Source',
      actions: [
        new codePipelineActions.S3SourceAction({
          actionName: 'SourceBucket',
          bucket: sourceBucket,
          bucketKey: 'image.zip',
          output: sourceOutput,
          trigger: codePipelineActions.S3Trigger.NONE,
        }),
      ],
    });

    pipeline.addStage({
      stageName: 'Deploy',
      actions: [
        new codePipelineActions.CodeBuildAction({
          actionName: 'DeployProject',
          input: sourceOutput,
          project: deployProject,
        }),
      ],
    });

    new events.Rule(this, 'PipelineTriggerEventRule', {
      eventPattern: {
        account: [cdk.Stack.of(this).account],
        source: ['aws.s3'],
        detailType: ['Object Created'],
        detail: {
          bucket: {
            name: [sourceBucket.bucketName],
          },
          object: {
            key: ['image.zip'],
          },
        },
      },
      targets: [new eventsTarget.CodePipeline(pipeline)],
    });

    // Notification rule for pipeline status
    new notifications.NotificationRule(this, 'PipelineNotifications', {
      source: pipeline,
      events: [
        codepipeline.PipelineNotificationEvents.PIPELINE_EXECUTION_STARTED,
        codepipeline.PipelineNotificationEvents.PIPELINE_EXECUTION_SUCCEEDED,
        codepipeline.PipelineNotificationEvents.PIPELINE_EXECUTION_FAILED,
      ],
      targets: [props.alarmTopic],
      detailType: notifications.DetailType.FULL,
    });
  }

  private _createTaskRole(
    pjPrefix: string,
    batchName: string,
    taskName: string,
    firehoseArn: string,
    crossAccountRoleArns?: string[],
  ) {
    const taskRole = new iam.Role(this, `TaskRole${taskName}`, {
      assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
      managedPolicies: [{ managedPolicyArn: 'arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore' }],
      inlinePolicies: {
        putRecordToFirehose: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              actions: ['firehose:PutRecord', 'firehose:PutRecordBatch'],
              resources: [firehoseArn],
            }),
          ],
        }),
      },
    });
    if (crossAccountRoleArns != undefined) {
      taskRole.addToPolicy(
        new iam.PolicyStatement({
          actions: ['sts:AssumeRole'],
          resources: crossAccountRoleArns,
        }),
      );
    }
    new ssm.StringParameter(this, `TaskRoleArn${taskName}`, {
      stringValue: taskRole.roleArn,
      parameterName: `/${pjPrefix}/batch/${batchName}/role/task/${taskName}`,
    });

    return taskRole;
  }

  private _createSecret(pjPrefix: string, batchName: string, taskName: string) {
    const secret = new secretsmanager.Secret(this, `Secret${taskName}`, {
      secretName: `${pjPrefix}/${taskName}`,
      secretObjectValue: {},
    });
    new ssm.StringParameter(this, `SecretsParameter${taskName}`, {
      stringValue: secret.secretArn,
      parameterName: `/${pjPrefix}/batch/${batchName}/secret/task/${taskName}`,
    });

    return secret;
  }

  private _createRepository(
    pjPrefix: string,
    batchName: string,
    repositoryLogicalIdSuffix: string,
    lifecycleRules: ecr.LifecycleRule[],
    removalPolicy?: cdk.RemovalPolicy,
    emptyOnDelete?: boolean,
  ) {
    const repository = new ecr.Repository(this, `Repo${repositoryLogicalIdSuffix}`, {
      imageScanOnPush: true,
      lifecycleRules,
      removalPolicy: removalPolicy ?? cdk.RemovalPolicy.RETAIN,
      emptyOnDelete: emptyOnDelete ?? false,
    });
    new ssm.StringParameter(this, `Repo${repositoryLogicalIdSuffix}Parameter`, {
      stringValue: repository.repositoryName,
      parameterName: `/${pjPrefix}/batch/${batchName}/repository/${repositoryLogicalIdSuffix}`,
    });
    return repository;
  }
}
