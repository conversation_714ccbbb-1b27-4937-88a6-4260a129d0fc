import { Construct } from 'constructs';
import * as cdk from 'aws-cdk-lib';
import * as cwl from 'aws-cdk-lib/aws-logs';
import * as apigw from 'aws-cdk-lib/aws-apigateway';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as lambda from 'aws-cdk-lib/aws-lambda';

import * as path from 'path';

export interface ApiBatchTriggerProps {
  serviceId: string;
  availableBatchNames: string[];
}

const CONTENT_TYPE = 'application/json';

export class ApiBatchTrigger extends Construct {
  public readonly api: apigw.IRestApi;
  public readonly statemachineExecutionFunction: lambda.IFunction;
  public readonly apiRole: iam.IRole;
  public readonly batchApiTriggerBodyModel: apigw.IModel;

  constructor(scope: Construct, id: string, props: ApiBatchTriggerProps) {
    super(scope, id);

    const apiName = `${props.serviceId}-batch-trigger-api`;
    const api = new apigw.RestApi(this, 'RestApi', {
      restApiName: apiName,
      deployOptions: {
        accessLogDestination: new apigw.LogGroupLogDestination(
          new cwl.LogGroup(this, 'LogGroup', {
            logGroupName: `/aws/restapi/${apiName}`,
            removalPolicy: cdk.RemovalPolicy.DESTROY,
          }),
        ),
        loggingLevel: apigw.MethodLoggingLevel.INFO,
        metricsEnabled: true,
        stageName: props.serviceId,
      },
    });
    this.api = api;

    const availableBatchNames = props.availableBatchNames.map((name) => `${props.serviceId}-${name}`);
    // A RestApi resource must have at least one method,
    // so use mock integration to create a method that returns the necessary information when executing the API
    const method = api.root.addMethod(
      'GET',
      new apigw.MockIntegration({
        requestTemplates: {
          [CONTENT_TYPE]: JSON.stringify({ statusCode: 200 }),
        },
        integrationResponses: [
          {
            statusCode: '200',
            responseTemplates: {
              [CONTENT_TYPE]: JSON.stringify({
                statusCode: 200,
                message: JSON.stringify({ availableBatchNames }),
              }),
            },
          },
        ],
      }),
      {
        authorizationType: apigw.AuthorizationType.IAM,
      },
    );
    method.addMethodResponse({ statusCode: '200' });

    const batchApiTriggerBodySchema = {
      schema: apigw.JsonSchemaVersion.DRAFT4,
      title: 'batchApiTriggerBody',
      type: apigw.JsonSchemaType.OBJECT,
      required: ['batchName'],
      properties: {
        batchName: {
          type: apigw.JsonSchemaType.STRING,
        },
        command: {
          type: apigw.JsonSchemaType.ARRAY,
          items: {
            type: apigw.JsonSchemaType.OBJECT,
            properties: {
              containerName: {
                type: apigw.JsonSchemaType.STRING,
              },
              passedCommand: {
                type: apigw.JsonSchemaType.ARRAY,
                items: {
                  type: apigw.JsonSchemaType.STRING,
                },
              },
            },
          },
        },
      },
    };

    const batchApiTriggerBodyModel = api.addModel('BatchApiTriggerBodyModel', {
      contentType: CONTENT_TYPE,
      description: 'Body of the batch api trigger',
      schema: batchApiTriggerBodySchema,
    });
    this.batchApiTriggerBodyModel = batchApiTriggerBodyModel;

    const apiRole = new iam.Role(this, 'ApiRole', {
      assumedBy: new iam.ServicePrincipal('apigateway.amazonaws.com'),
    });
    this.apiRole = apiRole;

    // The stateMachineExecutionFunction should be invoked by any method of the API Gateway
    const statemachineExecutionFunction = new lambda.Function(this, 'StateMachineExecutor', {
      runtime: lambda.Runtime.PYTHON_3_12,
      handler: 'app.lambda_handler',
      timeout: cdk.Duration.seconds(120),
      code: lambda.Code.fromAsset(path.join(__dirname, '../../../lambda/stepfunctions-executor-for-api'), {
        bundling: {
          image: lambda.Runtime.PYTHON_3_12.bundlingImage,
          command: ['bash', '-c', 'pip install -r requirements.txt -t /asset-output && cp -au . /asset-output'],
        },
      }),
      role: new iam.Role(this, 'StateMachineExecutorRole', {
        assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
        managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole')],
        inlinePolicies: {
          stateMachineExecutionPolicy: new iam.PolicyDocument({
            statements: [
              new iam.PolicyStatement({
                actions: ['states:StartExecution'],
                resources: availableBatchNames.map(
                  (name) =>
                    `arn:aws:states:${cdk.Stack.of(this).region}:${cdk.Stack.of(this).account}:stateMachine:${name}`,
                ),
              }),
            ],
          }),
        },
      }),
    });
    this.statemachineExecutionFunction = statemachineExecutionFunction;
    statemachineExecutionFunction.addPermission('InvokePermission', {
      principal: new iam.ServicePrincipal('apigateway.amazonaws.com'),
      action: 'lambda:InvokeFunction',
      sourceArn: `arn:aws:apigateway:${cdk.Stack.of(this).region}::/apis/${api.restApiId}/*/*/*`,
    });
    statemachineExecutionFunction.grantInvoke(apiRole);
  }
}
