import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { IVpc } from 'aws-cdk-lib/aws-ec2';
import { aws_ecs as ecs } from 'aws-cdk-lib';
import { ITopic } from 'aws-cdk-lib/aws-sns';
import { aws_events as cwe } from 'aws-cdk-lib';
import { aws_events_targets as cwet } from 'aws-cdk-lib';
import { aws_servicediscovery as sd } from 'aws-cdk-lib';
import * as ssm from 'aws-cdk-lib/aws-ssm';

export interface EcsCommonConstructProps extends cdk.StackProps {
  /**
   * ECS application VPC
   */
  vpc: IVpc;
  /**
   * SNS topic ARN for sending alarm
   */
  alarmTopic: ITopic;

  /**
   * SNS topic ARN for sending alarm to application team
   */
  alarmTopicForAppTeam: ITopic;
  /**
   * Project and environment prefix
   */
  prefix: string;
  /**
   * ECS application name
   */
  backAppName?: string;
  /**
   * ECS application port Number
   */
  backPortNumber?: number;
  /**
   * Service Discovery namespace
   */
  namespace: sd.IPrivateDnsNamespace;
  /**
   * ServiceId
   */
  serviceId: string;
  /**
   * Allow the creation of ECS service-related resources on only one side of the front/back end.
   */
  deployResource: string;
}

export class EcsCommonConstruct extends Construct {
  public readonly ecsCluster: ecs.Cluster;
  public readonly cloudMapService: sd.IService;

  constructor(scope: Construct, id: string, props: EcsCommonConstructProps) {
    super(scope, id);

    // --------------------- Fargate Cluster ----------------------------

    // ---- Cluster definition

    // Fargate Cluster
    // -  Enabling CloudWatch ContainerInsights
    const ecsCluster = new ecs.Cluster(this, 'Cluster', {
      vpc: props.vpc,
      containerInsightsV2: ecs.ContainerInsights.ENHANCED,
      enableFargateCapacityProviders: true,
      clusterName: cdk.PhysicalName.GENERATE_IF_NEEDED, // for crossRegionReferences
    });
    this.ecsCluster = ecsCluster;

    ecsCluster.addDefaultCapacityProviderStrategy([
      {
        capacityProvider: 'FARGATE',
        base: 0,
        weight: 1,
      },
    ]);

    if (props.deployResource === 'ALL' || props.deployResource === 'BACKEND') {
      // The Cloud Map service for service discovery
      const cloudMapService = new sd.Service(this, 'cloudMapService', {
        namespace: props.namespace,
        name: `backend-sd-${props.serviceId}`,
      });
      this.cloudMapService = cloudMapService;

      // for bastion
      new ssm.StringParameter(this, 'SSMDnsRecordName', {
        parameterName: `/${props.prefix}/${props.backAppName}/DnsRecordName`,
        stringValue: `${cloudMapService.serviceName}.${props.namespace.namespaceName}`,
      });

      new ssm.StringParameter(this, 'SSMCloudMapServiceArn', {
        parameterName: `/${props.prefix}/CloudMapServiceArn`,
        stringValue: cloudMapService.serviceArn,
      });
    }

    // ----------------------- Event notification for ECS -----------------------------
    // https://docs.aws.amazon.com/AmazonECS/latest/developerguide/ecs_cwe_events.html#ecs_service_events
    new cwe.Rule(this, 'ECSServiceActionEventRule', {
      description: 'CloudWatch Event Rule to send notification on ECS Service action events.',
      enabled: true,
      eventPattern: {
        source: ['aws.ecs'],
        detailType: ['ECS Service Action'],
        detail: {
          eventType: ['WARN', 'ERROR'],
          clusterArn: [ecsCluster.clusterArn],
        },
      },
      targets: [new cwet.SnsTopic(props.alarmTopic)],
    });

    // https://docs.aws.amazon.com/AmazonECS/latest/developerguide/ecs_cwe_events.html#ecs_service_deployment_events
    new cwe.Rule(this, 'ECSServiceDeploymentEventRule', {
      description: 'CloudWatch Event Rule to send notification on ECS Service deployment events.',
      enabled: true,
      eventPattern: {
        source: ['aws.ecs'],
        detailType: ['ECS Deployment State Change'],
        detail: {
          eventType: ['WARN', 'ERROR'],
          clusterArn: [ecsCluster.clusterArn],
        },
      },
      targets: [new cwet.SnsTopic(props.alarmTopic)],
    });
    // send alarm to application team
    new cwe.Rule(this, 'ECSServiceDeploymentEventRuleForAppTeam', {
      enabled: true,
      eventPattern: {
        source: ['aws.ecs'],
        detailType: ['ECS Deployment State Change'],
        detail: {
          eventType: ['ERROR'],
          clusterArn: [ecsCluster.clusterArn],
        },
      },
      targets: [new cwet.SnsTopic(props.alarmTopicForAppTeam)],
    });

    // for newrelic
    new ssm.StringParameter(this, 'SSMEcsClusterName', {
      parameterName: `/${props.prefix}/ECSClusterName`,
      stringValue: ecsCluster.clusterName,
    });
  }
}
