import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as codepipeline from 'aws-cdk-lib/aws-codepipeline';
import * as actions from 'aws-cdk-lib/aws-codepipeline-actions';
import * as codebuild from 'aws-cdk-lib/aws-codebuild';
import { ISecret } from 'aws-cdk-lib/aws-secretsmanager';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import { aws_elasticloadbalancingv2 as elbv2 } from 'aws-cdk-lib';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { IVpc } from 'aws-cdk-lib/aws-ec2';
import { aws_logs as cwl } from 'aws-cdk-lib';
import { aws_servicediscovery as sd } from 'aws-cdk-lib';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { aws_ecr as ecr } from 'aws-cdk-lib';
import { aws_kms as kms } from 'aws-cdk-lib';
import { aws_kinesisfirehose as firehose } from 'aws-cdk-lib';
import * as events from 'aws-cdk-lib/aws-events';
import * as eventsTarget from 'aws-cdk-lib/aws-events-targets';
import * as notifications from 'aws-cdk-lib/aws-codestarnotifications';
import * as sns from 'aws-cdk-lib/aws-sns';
import { IRemovalPolicyParam, ICrossAccountDeployPipelineParam } from '../../../../params/interface';
import { CWLogToDataFirehose } from '../../cw-log-to-kinesis-data-firehose-construct';

export interface PipelineEcspressoConstructProps {
  /**
   * Project and environment prefix
   */
  prefix: string;
  /**
   * The common prefix of the project
   */
  pjCommonPrefix: string;
  /**
   * ECS application name
   */
  appName: string;
  /**
   * ECS cluster properties
   */
  ecsCluster: ecs.Cluster;
  /**
   * ECS application service name
   */
  ecsServiceName: string;
  /**
   * Application Load Balancer target group properties
   *
   * @default - ''
   */
  targetGroup?: elbv2.ApplicationTargetGroup;
  /**
   * ECS application security group
   */
  securityGroup: ec2.SecurityGroup;
  /**
   * ECS application VPC
   */
  vpc: IVpc;
  /**
   * FireLens Container Image Name
   */
  fireLensImage: string;
  /**
   * Firehose Stream of FireLens Destination
   */
  firehoseStream: firehose.CfnDeliveryStream;
  /**
   * FireLens log group
   */
  logGroupForFireLens: cwl.LogGroup;
  /**
   * ECS application log group for service connect
   */
  logGroupForServiceConnect: cwl.LogGroup;
  /**
   * CloudMap Service
   */
  cloudMapService?: sd.IService;
  /**
   * ECS execution role
   */
  executionRole: iam.Role;
  /**
   * ECS task role
   *
   * @default - none
   */
  taskRole: iam.Role;
  /**
   * ALB target group port and ECS app port
   */
  portNumber?: number;
  /**
   * ECR repository
   */
  ecrRepository: ecr.IRepository;
  /**
   * Flag to determine the filename of image.zip
   */
  imageZipFlag: string;
  /**
   * KMS key for encryption
   */
  appKey: kms.IKey;
  /**
   * Lifecycle rules of Firehose (code build) log bucket
   */
  codeBuildBucketLogLifecycleRules: s3.LifecycleRule[];
  /**
   * Log Removal Policy Param
   */
  logRemovalPolicyParam?: IRemovalPolicyParam;
  /**
   * Pipeline bucket Removal policy params
   */
  pipelineBucketRemovalPolicyParam?: IRemovalPolicyParam;
  /**
   * SecretsManager
   */
  secret: ISecret;
  /**
   * Secret Manager Arn of New Relic LicenseKey
   */
  newrelicSecretArn: string;
  /**
   * Project EnvName
   */
  EnvName: string;
  /**
   * Pipeline source bucket lifecycle rules
   */
  pipelineSourceBucketLifeCycleRules: s3.LifecycleRule[];
  /**
   * SNS topic ARN for sending pipeline status
   */
  alarmTopic: sns.ITopic;
  /**
   * serviceId
   */
  serviceId: string;
  /**
   * Service Discovery namespace arn
   */
  namespaceArn: string;
  /**
   * Whether the application is backend
   */
  isBackend?: boolean;
  /**
   * Required parameter if deploying a cross-account pipeline. Specify this parameter to deploy Rolling pipeline-related resources to perform a cross-account deployment.
   */
  crossAccountDeployPipelineParam?: ICrossAccountDeployPipelineParam;
}

export class PipelineEcspressoConstruct extends Construct {
  public readonly sourceBucket: s3.Bucket;

  constructor(scope: Construct, id: string, props: PipelineEcspressoConstructProps) {
    super(scope, id);

    // If the delete flag is True, the old Rolling pipeline deployed to the Gevanni account is deleted. (The pipeline will not be deployed to the Gevanni account)
    const deployGevanniPipeline: boolean = props.crossAccountDeployPipelineParam?.deleteOldPipeline ? false : true;

    // TargetGroupが指定されていない場合は、空文字をCodeBuildの環境変数として設定
    const targetGroupArn = props.targetGroup?.targetGroupArn || '';

    // Set the name of the file that will trigger the pipeline
    let imageZipName;
    if (props.imageZipFlag == 'front') {
      imageZipName = 'image_front.zip';
    } else {
      imageZipName = 'image_backend.zip';
    }

    //Dev環境のみ空のタグを設定
    const BillingTagKye = props.EnvName.startsWith('Dev') ? ' ' : 'CmBillingGroup';
    const BillingTagValue = props.EnvName.startsWith('Dev') ? ' ' : props.prefix;
    const PropaGateTags = props.EnvName.startsWith('Dev') ? 'NONE' : 'SERVICE';

    // -- old pipeline ----
    if (deployGevanniPipeline) {
      // cloudMapServiceが指定されていない場合は、空文字をCodeBuildの環境変数として設定
      const cloudMapServiceArn = props.cloudMapService?.serviceArn || '';

      const discoveryName = props.isBackend ? `backend-${props.serviceId}` : '';

      const sourceBucket = new s3.Bucket(this, 'PipelineSourceBucket', {
        versioned: true,
        eventBridgeEnabled: true,
        removalPolicy: props.pipelineBucketRemovalPolicyParam?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
        autoDeleteObjects: props.pipelineBucketRemovalPolicyParam?.autoDeleteObjects ?? false,
        lifecycleRules: props.pipelineSourceBucketLifeCycleRules,
      });
      if (!props.crossAccountDeployPipelineParam) {
        this.sourceBucket = sourceBucket;
      }

      // SSM Parameter for ECR Tag
      const ecrTag = new ssm.StringParameter(this, 'ecrTag', {
        parameterName: `/${props.prefix}/${props.appName}/ecrTag`,
        stringValue: 'sample',
      });

      const codeBuildLogGroup = new cwl.LogGroup(this, `Log`, {
        retention: cwl.RetentionDays.ONE_MONTH,
        encryptionKey: props.appKey,
        logGroupName: cdk.PhysicalName.GENERATE_IF_NEEDED,
      });

      const codeBuildLogToNewRelic = new CWLogToDataFirehose(this, 'MetricFirehose', {
        firehoseStreamName: `${props.prefix}-${props.appName}-BuildLog-Stream`,
        firehoseLogGroupName: `/aws/firehose/${props.prefix}/${props.appName}/buildlogs`,
        sourceCWLogGroup: codeBuildLogGroup,
        httpEndpointUrl: 'https://aws-api.newrelic.com/firehose/v1',
        httpEndpointName: 'New Relic',
        s3BackupMode: 'FailedDataOnly',
        secretArn: props.newrelicSecretArn,
        logRemovalPolicyParam: props.logRemovalPolicyParam,
        firehoseBucketLifecycleRules: props.codeBuildBucketLogLifecycleRules,
      });

      const deployProject = new codebuild.PipelineProject(this, 'DeployProject', {
        environment: {
          buildImage: codebuild.LinuxBuildImage.AMAZON_LINUX_2_2,
        },
        environmentVariables: {
          ECS_CLUSTER: {
            value: props.ecsCluster.clusterName,
          },
          ECS_SERVICE: {
            value: props.ecsServiceName,
          },
          TARGET_GROUP_ARN: {
            value: targetGroupArn,
          },
          SECURITY_GROUP: {
            value: props.securityGroup.securityGroupId,
          },
          // Subnet数が3の前提
          SUBNET_1: {
            value: props.vpc.selectSubnets({
              subnetGroupName: 'Private',
            }).subnetIds[0],
          },
          SUBNET_2: {
            value: props.vpc.selectSubnets({
              subnetGroupName: 'Private',
            }).subnetIds[1],
          },
          SUBNET_3: {
            value:
              props.vpc.selectSubnets({
                subnetGroupName: 'Private',
              }).subnetIds[2] ?? '',
          },
          FIRELENS_IMAGE_NAME: {
            value: props.fireLensImage,
          },
          STREAM_NAME: {
            value: props.firehoseStream.deliveryStreamName,
          },
          LOG_GROUP_FIRELENS: {
            value: props.logGroupForFireLens.logGroupName,
          },
          LOG_GROUP_SERVICE_CONNECT: {
            value: props.logGroupForServiceConnect.logGroupName,
          },
          EXECUTION_ROLE_ARN: {
            value: props.executionRole.roleArn,
          },
          TASK_ROLE: {
            value: props.taskRole.roleArn,
          },
          FAMILY: {
            value: `${props.prefix}-${props.appName}-Taskdef`,
          },
          NAMESPACE: {
            value: props.namespaceArn,
          },
          CLOUDMAP_SERVICE_ARN: {
            value: cloudMapServiceArn,
          },
          DISCOVERY_NAME: {
            value: discoveryName,
          },
          PORT_NUMBER: {
            value: props.portNumber ?? '',
          },
          PARAMETER_NAME: {
            value: ecrTag.parameterName,
          },
          IMAGE_URI: {
            value: props.ecrRepository.repositoryUri,
          },
          ECR_TAG: {
            value: ecrTag.parameterName,
          },
          CM_BILLING_GROUP_TAG_KEY: {
            value: BillingTagKye,
          },
          CM_BILLING_GROUP_TAG: {
            value: BillingTagValue,
          },
          ServiceID_TAG: {
            value: props.serviceId,
          },
          PROPAGATE_TAG: {
            value: PropaGateTags,
          },
        },
        buildSpec: codebuild.BuildSpec.fromObject({
          version: '0.2',
          phases: {
            pre_build: {
              commands: [
                // 最新バージョンは表示しつつ、installは固定バージョンを使用
                'echo "The latest version of ecspresso is (It only shows up the log) :"',
                'curl -s https://api.github.com/repos/kayac/ecspresso/releases/latest | jq .tag_name',
                'curl -sL -o ecspresso_2.4.5_linux_amd64.tar.gz https://github.com/kayac/ecspresso/releases/download/v2.4.5/ecspresso_2.4.5_linux_amd64.tar.gz',
                'tar -zxf ecspresso_2.4.5_linux_amd64.tar.gz',
                'sudo install ecspresso /usr/local/bin/ecspresso',
                'ecspresso version',
              ],
            },
            build: {
              commands: [
                //https://github.com/kayac/ecspresso
                'export IMAGE_TAG=$(aws ssm get-parameter --name ${PARAMETER_NAME} --query "Parameter.Value" --output text)',
                'export IMAGE1_NAME=${IMAGE_URI}:${IMAGE_TAG}',
                'ls -lR',
                'ecs_service_status=$(aws ecs describe-services --cluster "$ECS_CLUSTER" --services "$ECS_SERVICE" --query \'services[0].status\' --output text 2>/dev/null)',
                'ecspresso deploy --config image-backend/ecspresso.yml',
                'python ./image-backend/config.py',
                'if [ "$ecs_service_status" != "ACTIVE" ]; then python ./image-backend/config.py --wait; else echo "ECS service \'$ECS_SERVICE\' exist, skipping wait."; fi',
              ],
            },
          },
        }),
        logging: {
          cloudWatch: {
            logGroup: codeBuildLogGroup,
          },
        },
      });

      // ワイルドカード指定
      deployProject.addToRolePolicy(
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: [
            'ecs:RegisterTaskDefinition',
            'ecs:ListTaskDefinitions',
            'ecs:DescribeTaskDefinition',
            'application-autoscaling:DescribeScalableTargets',
          ],
          resources: ['*'],
        }),
      );

      deployProject.addToRolePolicy(
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: [
            'ecs:TagResource',
            'application-autoscaling:RegisterScalableTarget',
            'application-autoscaling:DeregisterScalableTarget',
            'application-autoscaling:PutScalingPolicy',
            'application-autoscaling:DeleteScalingPolicy',
            'application-autoscaling:DescribeScalingPolicies',
            'elasticloadbalancing:ModifyTargetGroup',
            'servicediscovery:GetNamespace',
            'iam:CreateServiceLinkedRole',
          ],
          resources: ['*'],
        }),
      );

      const prefixSsmArn = `arn:aws:ssm:${cdk.Stack.of(this).region}:${cdk.Stack.of(this).account}:parameter`;
      const prefixEcsArn = `arn:aws:ecs:${cdk.Stack.of(this).region}:${cdk.Stack.of(this).account}`;

      deployProject.addToRolePolicy(
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: [
            'ecs:CreateService',
            'ecs:UpdateService',
            'ecs:DescribeServices',
            'ssm:GetParameter',
            'secretsmanager:GetSecretValue',
          ],
          resources: [
            `${prefixEcsArn}:service/${props.ecsCluster.clusterName}/${props.ecsServiceName}`,
            `${prefixSsmArn}:parameter/${props.pjCommonPrefix}/*`,
            `${prefixSsmArn}/${props.prefix}/*`,
            props.secret.secretArn,
          ],
        }),
      );

      if (props.targetGroup) {
        deployProject.addToRolePolicy(
          new iam.PolicyStatement({
            effect: iam.Effect.ALLOW,
            actions: ['elasticloadbalancing:ModifyTargetGroup'],
            resources: [targetGroupArn],
          }),
        );
      }

      if (props.taskRole) {
        deployProject.addToRolePolicy(
          new iam.PolicyStatement({
            effect: iam.Effect.ALLOW,
            actions: ['iam:PassRole'],
            resources: [props.executionRole.roleArn, props.taskRole.roleArn],
          }),
        );
      } else {
        deployProject.addToRolePolicy(
          new iam.PolicyStatement({
            effect: iam.Effect.ALLOW,
            actions: ['iam:PassRole'],
            resources: [props.executionRole.roleArn],
          }),
        );
      }

      const sourceOutput = new codepipeline.Artifact();

      const sourceAction = new actions.S3SourceAction({
        actionName: 'SourceBucket',
        bucket: sourceBucket,
        bucketKey: imageZipName,
        output: sourceOutput,
        trigger: actions.S3Trigger.NONE, // default: S3Trigger.POLL,option: S3Trigger.EVENT
      });

      const deployAction = new actions.CodeBuildAction({
        actionName: 'DeployProject',
        input: sourceOutput,
        project: deployProject,
      });

      const pipeline = new codepipeline.Pipeline(this, 'Pipeline', {});

      pipeline.addStage({
        stageName: 'Source',
        actions: [sourceAction],
      });

      pipeline.addStage({
        stageName: 'Deploy',
        actions: [deployAction],
      });

      // Automate change detection with EventBridge
      // https://docs.aws.amazon.com/ja_jp/codepipeline/latest/userguide/update-change-detection.html#update-change-detection-S3-event
      new events.Rule(this, 'PipelineTrigerEventRule', {
        eventPattern: {
          source: ['aws.s3'],
          detailType: ['Object Created'],
          detail: {
            bucket: {
              name: [sourceBucket.bucketName],
            },
            object: {
              key: [imageZipName],
            },
          },
        },
        targets: [new eventsTarget.CodePipeline(pipeline)],
      });

      pipeline.artifactBucket.applyRemovalPolicy(
        props.pipelineBucketRemovalPolicyParam?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
      );

      // Notification rule for pipeline status
      new notifications.NotificationRule(this, 'PipelineNotifications', {
        source: pipeline,
        events: [
          codepipeline.PipelineNotificationEvents.PIPELINE_EXECUTION_STARTED,
          codepipeline.PipelineNotificationEvents.PIPELINE_EXECUTION_SUCCEEDED,
          codepipeline.PipelineNotificationEvents.PIPELINE_EXECUTION_FAILED,
        ],
        targets: [props.alarmTopic],
        detailType: notifications.DetailType.FULL,
      });
    }

    // -- cross account ----
    if (props.crossAccountDeployPipelineParam) {
      const eventBusArn =
        props.crossAccountDeployPipelineParam?.eventBusArn ??
        `arn:aws:events:${cdk.Stack.of(this).region}:${
          props.crossAccountDeployPipelineParam.crossAccessAccountId
        }:event-bus/${props.crossAccountDeployPipelineParam.crossAccessEnvName}${
          props.crossAccountDeployPipelineParam.crossAccessPjPrefix
        }-CrossAccountEventBus`;

      // クロスアカウントアクセス用 IAM Role
      const crossAccessRole = new iam.Role(this, 'CrossAccessRole', {
        roleName: `${props.prefix}${props.appName}-CrossAccessRole`,
        assumedBy: new iam.AnyPrincipal().withConditions({
          ArnLike: {
            'aws:PrincipalArn': [
              `arn:aws:iam::${props.crossAccountDeployPipelineParam.crossAccessAccountId}:role/${props.serviceId}${props.crossAccountDeployPipelineParam.crossAccessPjPrefix}${props.appName}-PipelineRole`,
            ],
          },
        }),
        inlinePolicies: {
          allowSsmPolicy: new iam.PolicyDocument({
            statements: [
              new iam.PolicyStatement({
                actions: ['ssm:GetParameter'],
                effect: iam.Effect.ALLOW,
                resources: [
                  `arn:aws:ssm:ap-northeast-1:${cdk.Stack.of(this).account}:parameter/${props.serviceId}/*`,
                  `arn:aws:ssm:ap-northeast-1:${cdk.Stack.of(this).account}:parameter/${props.prefix}/*`,
                  `arn:aws:ssm:ap-northeast-1:${cdk.Stack.of(this).account}:parameter/${props.prefix}/${
                    props.appName
                  }/*`,
                ],
              }),
            ],
          }),
          allowSecretsPolicy: new iam.PolicyDocument({
            statements: [
              new iam.PolicyStatement({
                actions: ['secretsmanager:GetSecretValue'],
                effect: iam.Effect.ALLOW,
                resources: [props.secret.secretArn],
              }),
            ],
          }),
          // Ecspresso コマンド実行用権限
          // https://zenn.dev/fujiwara/books/ecspresso-handbook-v2/viewer/reference
          allowedEcspressoPolicy: new iam.PolicyDocument({
            statements: [
              new iam.PolicyStatement({
                actions: [
                  'ecs:*',
                  'iam:PassRole',
                  'application-autoscaling:Describe*',
                  'codedeploy:List*',
                  'codedeploy:CreateDeployment',
                  'codedeploy:BatchGet*',
                  'codedeploy:GetDeploymentConfig',
                  'codedeploy:RegisterApplicationRevision',
                  'codedeploy:ListDeployments',
                  'codedeploy:GetDeployment',
                  'codedeploy:ContinueDeployment',
                  'codedeploy:StopDeployment',
                  'servicediscovery:GetNamespace',
                  'iam:CreateServiceLinkedRole',
                ],
                effect: iam.Effect.ALLOW,
                resources: ['*'],
              }),
            ],
          }),
          // config.py実行用権限
          allowedConfigPolicy: new iam.PolicyDocument({
            statements: [
              new iam.PolicyStatement({
                actions: [
                  'application-autoscaling:DescribeScalableTargets',
                  'application-autoscaling:RegisterScalableTarget',
                  'application-autoscaling:DeregisterScalableTarget',
                  'application-autoscaling:PutScalingPolicy',
                  'application-autoscaling:DeleteScalingPolicy',
                  'application-autoscaling:DescribeScalingPolicies',
                  'elasticloadbalancing:ModifyTargetGroup',
                ],
                effect: iam.Effect.ALLOW,
                resources: ['*'],
              }),
            ],
          }),
        },
      });

      if (props.targetGroup) {
        crossAccessRole.addToPolicy(
          new iam.PolicyStatement({
            effect: iam.Effect.ALLOW,
            actions: ['elasticloadbalancing:ModifyTargetGroup'],
            resources: [targetGroupArn],
          }),
        );
      }

      if (props.taskRole) {
        crossAccessRole.addToPolicy(
          new iam.PolicyStatement({
            effect: iam.Effect.ALLOW,
            actions: ['iam:PassRole'],
            resources: [props.executionRole.roleArn, props.taskRole.roleArn],
          }),
        );
      } else {
        crossAccessRole.addToPolicy(
          new iam.PolicyStatement({
            effect: iam.Effect.ALLOW,
            actions: ['iam:PassRole'],
            resources: [props.executionRole.roleArn],
          }),
        );
      }

      // Pipeline SourceAction 用バケット
      this.sourceBucket = new s3.Bucket(this, 'AppPipelineSourceBucket', {
        bucketName: `${props.prefix.toLocaleLowerCase()}${props.appName.toLocaleLowerCase()}-sourcebucket`,
        versioned: true,
        eventBridgeEnabled: true,
        removalPolicy: props.pipelineBucketRemovalPolicyParam?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
        autoDeleteObjects: props.pipelineBucketRemovalPolicyParam?.autoDeleteObjects ?? false,
        lifecycleRules: props.pipelineSourceBucketLifeCycleRules,
      });
      this.sourceBucket.addToResourcePolicy(
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          principals: [
            new iam.AnyPrincipal().withConditions({
              ArnLike: {
                'aws:PrincipalArn': [
                  `arn:aws:iam::${props.crossAccountDeployPipelineParam.crossAccessAccountId}:role/${props.crossAccountDeployPipelineParam.crossAccessEnvName}${props.crossAccountDeployPipelineParam.crossAccessPjPrefix}${props.appName}-PipelineRole`,
                  `arn:aws:iam::${props.crossAccountDeployPipelineParam.crossAccessAccountId}:role/${props.crossAccountDeployPipelineParam.crossAccessEnvName}${props.crossAccountDeployPipelineParam.crossAccessPjPrefix}${props.appName}-LambdaRole`,
                ],
              },
            }),
          ],
          actions: ['s3:*'],
          resources: [`${this.sourceBucket.bucketArn}`, `${this.sourceBucket.bucketArn}/*`],
        }),
      );

      const crossaccountEventBus = events.EventBus.fromEventBusArn(this, 'CrossAccountEventBus', eventBusArn);

      new events.Rule(this, 'PipelineTriggerEventRule', {
        eventPattern: {
          source: ['aws.s3'],
          detailType: ['Object Created'],
          detail: {
            bucket: {
              name: [this.sourceBucket.bucketName],
            },
            object: {
              key: [imageZipName],
            },
          },
        },
        targets: [new eventsTarget.EventBus(crossaccountEventBus)],
      });

      new ssm.StringParameter(this, 'SSMTaskFamily', {
        parameterName: `/${props.prefix}/${props.appName}/TaskFamily`,
        stringValue: `${props.prefix}-${props.appName}-Taskdef`,
      });

      new ssm.StringParameter(this, 'SSMFirelensImageName', {
        parameterName: `/${props.prefix}/${props.appName}/FirelensImageName`,
        stringValue: props.fireLensImage,
      });

      new ssm.StringParameter(this, 'SSMStreamName', {
        parameterName: `/${props.prefix}/${props.appName}/StreamName`,
        stringValue: props.firehoseStream.deliveryStreamName ?? '',
      });

      new ssm.StringParameter(this, 'SSMCmBillingGroupTagKey', {
        parameterName: `/${props.prefix}/${props.appName}/CmBillingGroupTagKey`,
        stringValue: BillingTagKye,
      });

      new ssm.StringParameter(this, 'SSMCmBillingGroupTag', {
        parameterName: `/${props.prefix}/${props.appName}/CmBillingGroupTag`,
        stringValue: BillingTagValue,
      });

      new ssm.StringParameter(this, 'SSMServiceIdTag', {
        parameterName: `/${props.prefix}/${props.appName}/ServiceIdTag`,
        stringValue: props.serviceId,
      });

      new ssm.StringParameter(this, 'SSMPropagateTag', {
        parameterName: `/${props.prefix}/${props.appName}/PropagateTag`,
        stringValue: PropaGateTags,
      });
    }

    new ssm.StringParameter(this, 'SSMSourceBucketName', {
      parameterName: `/${props.prefix}/${props.appName}/SourceBucketName`,
      stringValue: this.sourceBucket.bucketName,
    });
  }
}
