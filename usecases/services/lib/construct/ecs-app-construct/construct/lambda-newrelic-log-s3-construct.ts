import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as s3 from 'aws-cdk-lib/aws-s3';

export interface NewRelicLogS3Props {
  prefix: string;
  removalPolicy?: cdk.RemovalPolicy;
  newrelicSecretArn: string;
  albLogBucket: s3.Bucket;
  layer: lambda.ILayerVersion;
  appName: string;
}

export class NewRelicLogIngestionS3Construct extends Construct {
  public readonly function: lambda.Function;
  constructor(scope: Construct, id: string, props: NewRelicLogS3Props) {
    super(scope, id);

    const lambdaLogGroup = new logs.LogGroup(this, 'FunctionLogGroup', {
      logGroupName: `/aws/lambda/${props.prefix}/${props.appName}/NewRelicFunctionLog`,
      retention: logs.RetentionDays.ONE_YEAR,
      removalPolicy: props.removalPolicy ?? cdk.RemovalPolicy.DESTROY,
    });

    const lambdaFunction = new lambda.Function(this, 'function', {
      functionName: `${props.prefix}-${props.appName}-nr-log`,
      runtime: lambda.Runtime.PYTHON_3_12,
      layers: [props.layer],
      handler: 'NewRelicLogIngestionS3.lambda_handler',
      code: lambda.Code.fromAsset('lambda/NewRelic'),
      timeout: cdk.Duration.seconds(900),
      logGroup: lambdaLogGroup,
      memorySize: 256,
      environment: {
        LICENSE_KEY_ARN: props.newrelicSecretArn,
        LOG_TYPE: 'alb',
        DEBUG_ENABLED: 'false',
      },
    });
    this.function = lambdaFunction;

    const lambdaPolicy = new iam.PolicyStatement({
      actions: ['s3:GetObject', 's3:ListBucket', 'secretsmanager:GetSecretValue'],
      resources: [props.newrelicSecretArn, props.albLogBucket.bucketArn, `${props.albLogBucket.bucketArn}/*`],
    });
    this.function.addToRolePolicy(lambdaPolicy);
  }
}
