import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { ISecret } from 'aws-cdk-lib/aws-secretsmanager';

interface EcsTaskRoleProps extends cdk.StackProps {
  /**
   * ECS application name
   */
  appName: string;
  /**
   * Project and environment prefix
   */
  prefix: string;
  /**
   * Custom policy statements to add to the role
   * @default - No custom policy statements
   */
  policyStatements?: iam.PolicyStatement[];
  /**
   * List of AWS managed policy name to add to the role
   * @default - No AWS managed policy added to the role
   */
  managedPolicy?: string[];
  /**
   * List of allowed cross-account role arn to add to the role
   * @default - No allowed cross-account role added to the role
   */
  crossAccountRoleArns?: string[];
  /**
   * SecretArn for app teams
   */
  secret: ISecret;
}
export class EcsTaskRole extends Construct {
  readonly taskrole: iam.Role;
  readonly ecsTaskExecutionRole: iam.Role;

  constructor(scope: Construct, id: string, props: EcsTaskRoleProps) {
    super(scope, id);

    // Create execution Role
    const inlinePolicies: { [key: string]: iam.PolicyDocument } = {
      createLogs: new iam.PolicyDocument({
        statements: [
          new iam.PolicyStatement({
            actions: ['logs:CreateLogGroup'],
            resources: ['*'],
          }),
        ],
      }),
      getSecret: new iam.PolicyDocument({
        statements: [
          new iam.PolicyStatement({
            actions: ['secretsmanager:GetSecretValue'],
            resources: [props.secret.secretArn],
          }),
        ],
      }),
    };
    // Role for ECS Agent
    // The task execution role grants the Amazon ECS container and Fargate agents permission to make AWS API calls on your behalf.
    // https://docs.aws.amazon.com/AmazonECS/latest/developerguide/task_execution_IAM_role.html
    const executionRole = new iam.Role(this, 'EcsTaskExecutionRole', {
      assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
      managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AmazonECSTaskExecutionRolePolicy')],
      inlinePolicies: inlinePolicies,
    });
    this.ecsTaskExecutionRole = executionRole;

    // Create Task Role
    this.taskrole = new iam.Role(this, `EcsTaskRole`, {
      roleName: `${props.prefix}-${props.appName}-taskrole`,
      assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
      description: `${props.prefix} ${props.appName} Task Role`,
    });

    // ECS base policy
    const commonPolicy = new iam.PolicyStatement({
      actions: [
        'ssmmessages:CreateControlChannel',
        'ssmmessages:CreateDataChannel',
        'ssmmessages:OpenControlChannel',
        'ssmmessages:OpenDataChannel',
        'logs:CreateLogGroup',
        'logs:DescribeLogStreams',
        'logs:DescribeLogGroups',
        'logs:PutLogEvents',
        'firehose:PutRecord',
        'firehose:PutRecordBatch',
      ],
      resources: ['*'],
    });
    this.taskrole.addToPolicy(commonPolicy);

    // Provided policy statements
    if (props.policyStatements) {
      props.policyStatements.forEach((statement) => {
        this.taskrole.addToPolicy(statement);
      });
    }

    // Provided AWS Managed policy
    if (props.managedPolicy) {
      props.managedPolicy.forEach((policyName) => {
        this.taskrole.addManagedPolicy(iam.ManagedPolicy.fromAwsManagedPolicyName(policyName));
      });
    }

    // Add Cross Account Access
    if (props.crossAccountRoleArns) {
      props.crossAccountRoleArns.forEach((roleArn) => {
        this.taskrole.addToPolicy(
          new iam.PolicyStatement({
            effect: iam.Effect.ALLOW,
            actions: ['sts:AssumeRole'],
            resources: [roleArn],
          }),
        );
      });
    }

    // Output
    new cdk.CfnOutput(this, `${props.appName}-EcsTaskRole-Arn`, {
      value: this.taskrole.roleArn,
      exportName: `${props.prefix}-${props.appName}-ecs-task-role-arn`,
    });

    new ssm.StringParameter(this, 'SSMExecutionRoleArn', {
      parameterName: `/${props.prefix}/${props.appName}/ExecutionRoleArn`,
      stringValue: executionRole.roleArn,
    });

    new ssm.StringParameter(this, 'SSMTaskRoleArn', {
      parameterName: `/${props.prefix}/${props.appName}/TaskRoleArn`,
      stringValue: this.taskrole.roleArn,
    });
  }
}
