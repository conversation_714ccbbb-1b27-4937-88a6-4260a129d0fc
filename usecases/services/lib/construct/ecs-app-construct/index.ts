import * as iam from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';
import { ISecurityGroup, IVpc, Port } from 'aws-cdk-lib/aws-ec2';
import * as kms from 'aws-cdk-lib/aws-kms';
import { ITopic } from 'aws-cdk-lib/aws-sns';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import { EcsappConstruct } from './construct/ecs-app-construct';
import { EcsCommonConstruct } from './construct/ecs-common-construct';
import { PipelineEcspressoConstruct } from './construct/pipeline-ecspresso-construct';
import { PipelineBlueGreenConstruct } from './construct/pipeline-blue-green-construct';
import { AlbConstruct } from './construct/alb-construct';
import { EcsTaskRole } from './construct/ecs-task-role-construct';
import {
  IEcsAlbParam,
  IEcsParam,
  IEcsTaskRoleParam,
  IRemovalPolicyParam,
  IBlueGreenPipelineParam,
  ICrossAccountDeployPipelineParam,
} from '../../../params/interface';
import { BastionECSAppConstruct } from './construct/bastion-ecs-construct';
import { aws_s3 as s3 } from 'aws-cdk-lib';
import { DataFirehose } from '../data-firehose-construct';
import { ILayerVersion } from 'aws-cdk-lib/aws-lambda';
import { aws_servicediscovery as sd } from 'aws-cdk-lib';
import { AlbBgConstruct } from './construct/alb-blue-green-construct';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as ssm from 'aws-cdk-lib/aws-ssm';

interface EcsAppProps {
  serviceKey: string;
  /**
   * ECS application VPC
   */
  vpc: IVpc;
  /**
   * KMS key for encryption
   */
  appKey: kms.IKey;
  /**
   * SNS topic ARN for sending alarm
   */
  alarmTopic: ITopic;
  /**
   * SNS topic ARN for sending alarm For App Team
   */
  alarmTopicForAppTeam: ITopic;
  /**
   * Project and environment prefix
   */
  prefix: string;
  /**
   * The common prefix of the project
   */
  pjCommonPrefix: string;
  /**
   * ECS frontend tasks parameters
   *
   * @example - [{ appName: 'EcsApp', portNumber: 80, path: '/path', lifecycleRules: [{ description: 'Keep last 30 images', maxImageCount: 30 }]}]
   */
  EcsFrontTask?: IEcsAlbParam;
  /**
   * ECS backend tasks parameters
   *
   * @example - [{ appName: 'EcsBackend', portNumber: 8080, path: '/path}]
   */
  EcsBackTask?: IEcsParam;
  /**
   * policy parameters to add to the role for ECS frontend tasks
   */
  ecsFrontTaskRole?: IEcsTaskRoleParam;
  /**
   * policy parameters to add to the role for ECS backend tasks
   */
  ecsBackTaskRole?: IEcsTaskRoleParam;
  /**
   * ECS frontend tasks parameters
   *
   * @example - [{ appName: 'EcsApp', portNumber: 80, path: '/path', lifecycleRules: [{ description: 'Keep last 30 images', maxImageCount: 30 }]}]
   */
  EcsFrontBgTask?: IEcsAlbParam;
  /**
   * ECS backend tasks parameters
   *
   * @example - [{ appName: 'EcsBackend', portNumber: 8080, path: '/path}]
   */
  EcsBackBgTask?: IEcsParam;
  /**
   * policy parameters to add to the role for ECS frontend tasks
   */
  ecsFrontBgTaskRole?: IEcsTaskRoleParam;
  /**
   * policy parameters to add to the role for ECS backend tasks
   */
  ecsBackBgTaskRole?: IEcsTaskRoleParam;
  /**
   * Whether to create ECS bastion task
   *
   * @default - false
   */
  ecsBastionTasks?: boolean;
  /**
   * Project EnvName
   *
   */
  EnvName: string;
  /**
   * ECS bastion parameters
   */
  ecsBastionParams?: {
    ecrRepository: ecr.IRepository;
    taskExecutionRole: iam.IRole;
    policyStatements?: iam.PolicyStatement[];
    managedPolicy?: string[];
  };
  /**
   * ACM ARN
   */
  AcmArn?: string;
  /**
   * Lifecycle rules of ALB access log bucket
   */
  accessLogBucketLifecycleRules: s3.LifecycleRule[];
  /**
   * Lifecycle rules of Firehose (Application) log bucket
   */
  appLogBucketLifecycleRules: s3.LifecycleRule[];
  /**
   * Lifecycle rules of Firehose (pipeline) log bucket
   */
  buildLogBucketLifecycleRules: s3.LifecycleRule[];
  /**
   * Log Removal Policy Param
   */
  logRemovalPolicyParam?: IRemovalPolicyParam;
  /**
   * Pipeline bucket Removal policy params
   */
  pipelineBucketRemovalPolicyParam?: IRemovalPolicyParam;
  /**
   * ECR Removal Policy Param
   */
  ecrRemovalPolicyParam?: IRemovalPolicyParam;
  /**
   * Pipeline source bucket lifecycle rules
   */
  pipelineSourceBucketLifeCycleRules: s3.LifecycleRule[];
  /**
   * FireLens Container Image
   */
  fireLensImageBase: string;
  /**
   * Secret Manager Arn of New Relic LicenseKey
   */
  newrelicSecretArn: string;
  /*
   * New Relic Lambda Layer
   */
  newrelicLayer: ILayerVersion;
  /**
   * Batch Security Group
   */
  batchSecurityGroup: ISecurityGroup;
  /**
   * Service Discovery namespace
   */
  namespace: sd.IPrivateDnsNamespace;
  /**
   * Service Discovery namespace arn
   */
  namespaceArn: string;
  /**
   * ServiceId
   */
  serviceId: string;
  /**
   * Allow the creation of ECS service-related resources on only one side of the front/back end.
   */
  deployResource: string;
  deployControl: string;
  ecspressoFrontDeployPipelineParam?: ICrossAccountDeployPipelineParam;
  ecspressoBackDeployPipelineParam?: ICrossAccountDeployPipelineParam;
  blueGreenFrontDeployPipelineParam?: IBlueGreenPipelineParam;
  blueGreenBackDeployPipelineParam?: IBlueGreenPipelineParam;
}

export class EcsApp extends Construct {
  public readonly ecsCommon: EcsCommonConstruct;
  public readonly firehose: DataFirehose;
  public readonly frontAlb: AlbConstruct;
  public readonly frontAlbBg: AlbBgConstruct;
  public readonly frontEcsApps: EcsappConstruct;
  public readonly frontEcsAppsBg: EcsappConstruct;
  public readonly frontEcsAppPipeline: PipelineEcspressoConstruct;
  public readonly frontEcsAppBgPipeline: PipelineBlueGreenConstruct;
  public readonly backAlbBg: AlbBgConstruct;
  public readonly backEcsApps: EcsappConstruct;
  public readonly backEcsAppsBg: EcsappConstruct;
  public readonly bastionApp: BastionECSAppConstruct;
  public readonly backEcsAppPipeline: PipelineEcspressoConstruct;
  public readonly backEcsAppBgPipeline: PipelineBlueGreenConstruct;
  public readonly backEcsTaskRole: iam.IRole;
  public readonly backEcsBgTaskRole: iam.IRole;

  constructor(scope: Construct, id: string, props: EcsAppProps) {
    super(scope, id);

    //ECS Common
    const ecsCommon = new EcsCommonConstruct(this, `ECSCommon`, {
      vpc: props.vpc,
      alarmTopic: props.alarmTopic,
      alarmTopicForAppTeam: props.alarmTopicForAppTeam,
      prefix: props.prefix,
      backAppName: props.EcsBackTask?.appName,
      backPortNumber: props.EcsBackTask?.portNumber,
      // -- SAMPLE: Pass your own ECR repository and your own image
      //  repository: ecr.repository,
      //  imageTag: build_container.imageTag,
      namespace: props.namespace,
      serviceId: props.serviceId,
      deployResource: props.deployResource,
    });
    this.ecsCommon = ecsCommon;

    //Bastion Container
    if (props.ecsBastionTasks && props.ecsBastionParams) {
      const bastionApp = new BastionECSAppConstruct(this, `Bastion-ECSAPP`, {
        pjPrefix: props.prefix,
        vpc: props.vpc,
        appKey: props.appKey,
        ecrRepository: props.ecsBastionParams.ecrRepository,
        containerImageTag: 'bastionimage',
        containerConfig: {
          cpu: 256,
          memoryLimitMiB: 512,
        },
        ecsTaskExecutionRole: props.ecsBastionParams.taskExecutionRole,
        // Change policyStatements if you want to add a policy to the task role
        policyStatements: props.ecsBastionParams.policyStatements,
        // Provide managed policy to add role if needed
        managedPolicy: props.ecsBastionParams.managedPolicy,
        fargateLogGroupRemovalPolicy: props.logRemovalPolicyParam?.removalPolicy,
      });
      this.bastionApp = bastionApp;
    }

    //  DataFirehose for Container Log Ingestion to New Relic
    const appLogFirehose = new DataFirehose(this, `AppLogDatFirehose`, {
      firehoseStreamName: `${props.prefix}-AppLog-Stream`,
      firehoseLogGroupName: `/aws/firehose/${props.prefix}/applogs`,
      httpEndpointUrl: 'https://aws-api.newrelic.com/firehose/v1',
      httpEndpointName: 'New Relic',
      s3BackupMode: 'AllData',
      secretArn: props.newrelicSecretArn,
      logRemovalPolicyParam: props.logRemovalPolicyParam,
      firehoseBucketLifecycleRules: props.appLogBucketLifecycleRules,
    });
    this.firehose = appLogFirehose;

    // ECSサービスパターン1. Frontend Rolling
    // Public ALB + ECS resources(Repo, Log Group, SG, CloudWatch) + ecspresso(Rolling update) Pipeline
    // ※ECSサービスはパイプラインのecspressoコマンドにて作成
    if (
      props.EcsFrontTask &&
      props.ecsFrontTaskRole &&
      (props.deployControl === 'ALL' || props.deployControl === 'ROLLING') &&
      (props.deployResource === 'ALL' || props.deployResource === 'FRONTEND')
    ) {
      // Create Origin Resources
      const frontAlb = new AlbConstruct(this, `FrontAlb`, {
        vpc: props.vpc,
        alarmTopic: props.alarmTopic,
        ecsApps: props.EcsFrontTask,
        AcmArn: props.AcmArn,
        accessLogBucketLifecycleRules: props.accessLogBucketLifecycleRules,
        logRemovalPolicyParam: props.logRemovalPolicyParam,
        prefix: props.prefix,
        newrelicSecretArn: props.newrelicSecretArn,
        newrelicLayer: props.newrelicLayer,
        enableAlarm: props.EcsFrontTask.enableAlarm,
        appName: props.EcsFrontTask.appName,
      });
      this.frontAlb = frontAlb;

      const frontEcsApps = new EcsappConstruct(this, `${props.EcsFrontTask.appName}-FrontApp-Ecs-Resources`, {
        serviceKey: props.serviceKey,
        vpc: props.vpc,
        ecsCluster: ecsCommon.ecsCluster,
        appName: props.EcsFrontTask.appName,
        prefix: props.prefix,
        appKey: props.appKey,
        alarmTopic: props.alarmTopic,
        allowFromSG: [frontAlb.appAlbSecurityGroup],
        portNumber: props.EcsFrontTask.portNumber,
        useServiceConnect: true,
        ecrLifecycleRules: props.EcsFrontTask.lifecycleRules,
        ecrRemovalPolicyParam: props.ecrRemovalPolicyParam,
        fireLensLogGroupRemovalPolicy: props.logRemovalPolicyParam?.removalPolicy,
        serviceConnectLogGroupRemovalPolicy: props.logRemovalPolicyParam?.removalPolicy,
        enableAlarm: props.EcsFrontTask.enableAlarm,
      });
      this.frontEcsApps = frontEcsApps;

      //Pipeline for Frontend Rolling
      const frontEcsTaskRole = new EcsTaskRole(this, `${frontEcsApps.appName}-FrontApp-EcsTaskRole`, {
        appName: frontEcsApps.ecsServiceName,
        prefix: props.prefix,
        // Change policyStatements if you want to add a policy to the task role
        policyStatements: props.ecsFrontTaskRole.policyStatements,
        // Provide managed policy to add role if needed
        managedPolicy: props.ecsFrontTaskRole.managedPolicy,
        secret: frontEcsApps.secret,
        crossAccountRoleArns: props.ecsFrontTaskRole.crossAccountRoleArns,
      });

      const frontEcsAppPipeline = new PipelineEcspressoConstruct(this, `${frontEcsApps.appName}-FrontApp-Pipeline`, {
        prefix: `${props.prefix}`,
        pjCommonPrefix: props.pjCommonPrefix,
        appName: frontEcsApps.appName,
        ecsCluster: ecsCommon.ecsCluster,
        ecsServiceName: frontEcsApps.ecsServiceName,
        targetGroup: frontAlb.AlbTg.lbForAppTargetGroup,
        securityGroup: frontEcsApps.securityGroupForFargate,
        vpc: props.vpc,
        fireLensImage: `${props.fireLensImageBase}:fluentbitimage${props.EcsFrontTask?.appLanguage ?? ''}`,
        firehoseStream: appLogFirehose.stream,
        logGroupForFireLens: frontEcsApps.fireLensLogGroup,
        logGroupForServiceConnect: frontEcsApps.serviceConnectLogGroup,
        namespaceArn: props.namespaceArn,
        executionRole: frontEcsTaskRole.ecsTaskExecutionRole,
        taskRole: frontEcsTaskRole.taskrole,
        portNumber: frontEcsApps.portNumber,
        ecrRepository: frontEcsApps.ecrRepository,
        imageZipFlag: 'front',
        appKey: props.appKey,
        codeBuildBucketLogLifecycleRules: props.buildLogBucketLifecycleRules,
        logRemovalPolicyParam: props.logRemovalPolicyParam,
        pipelineBucketRemovalPolicyParam: props.pipelineBucketRemovalPolicyParam,
        secret: frontEcsApps.secret,
        newrelicSecretArn: props.newrelicSecretArn,
        EnvName: props.EnvName,
        pipelineSourceBucketLifeCycleRules: props.pipelineSourceBucketLifeCycleRules,
        alarmTopic: props.alarmTopicForAppTeam,
        serviceId: props.serviceId,
        crossAccountDeployPipelineParam: props.ecspressoFrontDeployPipelineParam,
      });
      this.frontEcsAppPipeline = frontEcsAppPipeline;
    }

    // ECSサービスパターン2. Backend Rolling
    // ECS resources(Repo, Log Group, SG, CloudWatch) + ecspresso(Rolling update) Pipeline
    // ※ECSサービスはパイプラインのecspressoコマンドにて作成
    if (
      props.EcsBackTask &&
      props.ecsBackTaskRole &&
      (props.deployControl === 'ALL' || props.deployControl === 'ROLLING') &&
      (props.deployResource === 'ALL' || props.deployResource === 'BACKEND')
    ) {
      let securityGroupsOtherECSApp: ec2.ISecurityGroup[] = [];
      if (props.EcsBackTask.AllowSGOtherECSApp) {
        securityGroupsOtherECSApp = props.EcsBackTask.AllowSGOtherECSApp.map((service) => {
          const parameterPath = `/${service.serviceId}/${service.appName}/EcsSGId`;
          const sgId = ssm.StringParameter.valueForStringParameter(this, parameterPath);
          return ec2.SecurityGroup.fromSecurityGroupId(this, `SG-${service.serviceId}`, sgId);
        });
      }

      const allowFromSG: ec2.SecurityGroup[] = [];
      if (this.frontEcsApps && this.frontEcsApps.securityGroupForFargate) {
        allowFromSG.push(this.frontEcsApps.securityGroupForFargate);
      }
      allowFromSG.push(this.bastionApp.securityGroup);

      const backEcsApps = new EcsappConstruct(this, `${props.EcsBackTask.appName}-BackApp-Ecs-Resources`, {
        serviceKey: props.serviceKey,
        vpc: props.vpc,
        ecsCluster: ecsCommon.ecsCluster,
        appName: props.EcsBackTask.appName,
        prefix: props.prefix,
        appKey: props.appKey,
        alarmTopic: props.alarmTopic,
        allowFromSG: allowFromSG,
        AllowSGOtherECSApp: securityGroupsOtherECSApp,
        portNumber: props.EcsBackTask.portNumber,
        useServiceConnect: true,
        ecrLifecycleRules: props.EcsBackTask.lifecycleRules,
        ecrRemovalPolicyParam: props.ecrRemovalPolicyParam,
        fireLensLogGroupRemovalPolicy: props.logRemovalPolicyParam?.removalPolicy,
        serviceConnectLogGroupRemovalPolicy: props.logRemovalPolicyParam?.removalPolicy,
        enableAlarm: props.EcsBackTask.enableAlarm,
      });
      this.backEcsApps = backEcsApps;
      backEcsApps.securityGroupForFargate.addIngressRule(
        props.batchSecurityGroup,
        Port.tcp(props.EcsBackTask.portNumber),
      );

      //Pipeline for Backend Rolling
      const backEcsTaskRole = new EcsTaskRole(this, `${backEcsApps.appName}-BackApp-EcsTaskRole`, {
        appName: backEcsApps.ecsServiceName,
        prefix: props.prefix,
        // Change policyStatements if you want to add a policy to the task role
        policyStatements: props.ecsBackTaskRole.policyStatements,
        // Provide managed policy to add role if needed
        managedPolicy: props.ecsBackTaskRole.managedPolicy,
        secret: backEcsApps.secret,
        crossAccountRoleArns: props.ecsBackTaskRole.crossAccountRoleArns,
      });
      this.backEcsTaskRole = backEcsTaskRole.taskrole;

      const backEcsAppPipeline = new PipelineEcspressoConstruct(this, `${backEcsApps.appName}-BackApp-Pipeline`, {
        prefix: `${props.prefix}`,
        pjCommonPrefix: props.pjCommonPrefix,
        appName: backEcsApps.appName,
        ecsCluster: ecsCommon.ecsCluster,
        ecsServiceName: backEcsApps.ecsServiceName,
        securityGroup: backEcsApps.securityGroupForFargate,
        vpc: props.vpc,
        fireLensImage: `${props.fireLensImageBase}:fluentbitimage${props.EcsBackTask?.appLanguage ?? ''}`,
        firehoseStream: appLogFirehose.stream,
        logGroupForFireLens: backEcsApps.fireLensLogGroup,
        logGroupForServiceConnect: backEcsApps.serviceConnectLogGroup,
        namespaceArn: props.namespaceArn,
        cloudMapService: ecsCommon.cloudMapService,
        executionRole: backEcsTaskRole.ecsTaskExecutionRole,
        taskRole: backEcsTaskRole.taskrole,
        portNumber: backEcsApps.portNumber,
        ecrRepository: backEcsApps.ecrRepository,
        imageZipFlag: 'backend',
        appKey: props.appKey,
        codeBuildBucketLogLifecycleRules: props.buildLogBucketLifecycleRules,
        logRemovalPolicyParam: props.logRemovalPolicyParam,
        pipelineBucketRemovalPolicyParam: props.pipelineBucketRemovalPolicyParam,
        secret: backEcsApps.secret,
        newrelicSecretArn: props.newrelicSecretArn,
        EnvName: props.EnvName,
        pipelineSourceBucketLifeCycleRules: props.pipelineSourceBucketLifeCycleRules,
        alarmTopic: props.alarmTopicForAppTeam,
        serviceId: props.serviceId,
        isBackend: true,
        crossAccountDeployPipelineParam: props.ecspressoBackDeployPipelineParam,
      });
      this.backEcsAppPipeline = backEcsAppPipeline;
    }

    // ECS サービスパターン 3. Frontend Blue/Green
    if (
      props.EcsFrontBgTask &&
      props.ecsFrontBgTaskRole &&
      props.blueGreenFrontDeployPipelineParam &&
      (props.deployControl === 'ALL' || props.deployControl === 'BLUE_GREEN') &&
      (props.deployResource === 'ALL' || props.deployResource === 'FRONTEND')
    ) {
      //Create Origin Resources
      const frontAlbBg = new AlbBgConstruct(this, `${props.EcsFrontBgTask.appName}-FrontAlbBg`, {
        vpc: props.vpc,
        alarmTopic: props.alarmTopic,
        ecsApps: props.EcsFrontBgTask,
        AcmArn: props.AcmArn,
        accessLogBucketLifecycleRules: props.accessLogBucketLifecycleRules,
        logRemovalPolicyParam: props.logRemovalPolicyParam,
        prefix: props.prefix,
        newrelicSecretArn: props.newrelicSecretArn,
        newrelicLayer: props.newrelicLayer,
        enableAlarm: props.EcsFrontBgTask.enableAlarm,
        appName: props.EcsFrontBgTask.appName,
        subnetGroupName: 'Public',
      });
      this.frontAlbBg = frontAlbBg;

      const frontEcsAppsBg = new EcsappConstruct(this, `${props.EcsFrontBgTask.appName}-FrontApp-Ecs-Resources-Bg`, {
        serviceKey: props.serviceKey,
        vpc: props.vpc,
        ecsCluster: ecsCommon.ecsCluster,
        appName: props.EcsFrontBgTask.appName,
        prefix: props.prefix,
        appKey: props.appKey,
        alarmTopic: props.alarmTopic,
        allowFromSG: [frontAlbBg.appAlbSecurityGroup],
        portNumber: props.EcsFrontBgTask.portNumber,
        useServiceConnect: true,
        ecrLifecycleRules: props.EcsFrontBgTask.lifecycleRules,
        ecrRemovalPolicyParam: props.ecrRemovalPolicyParam,
        fireLensLogGroupRemovalPolicy: props.logRemovalPolicyParam?.removalPolicy,
        serviceConnectLogGroupRemovalPolicy: props.logRemovalPolicyParam?.removalPolicy,
        enableAlarm: props.EcsFrontBgTask.enableAlarm,
      });
      this.frontEcsAppsBg = frontEcsAppsBg;

      //Pipeline for Frontend Blue/Green
      const frontEcsBgTaskRole = new EcsTaskRole(this, `${frontEcsAppsBg.appName}-FrontAppBg-EcsTaskRole`, {
        appName: frontEcsAppsBg.ecsServiceName,
        prefix: props.prefix,
        // Change policyStatements if you want to add a policy to the task role
        policyStatements: props.ecsFrontBgTaskRole.policyStatements,
        // Provide managed policy to add role if needed
        managedPolicy: props.ecsFrontBgTaskRole.managedPolicy,
        secret: frontEcsAppsBg.secret,
        crossAccountRoleArns: props.ecsFrontBgTaskRole.crossAccountRoleArns,
      });

      this.frontEcsAppBgPipeline = new PipelineBlueGreenConstruct(this, `${frontEcsAppsBg.appName}-PipelineBlueGreen`, {
        imageZipFlag: 'front',
        prefix: props.prefix,
        pipelineSourceBucketLifeCycleRules: props.pipelineSourceBucketLifeCycleRules,
        appName: frontEcsAppsBg.appName,
        ecsCluster: ecsCommon.ecsCluster,
        ecsServiceName: frontEcsAppsBg.ecsServiceName,
        securityGroup: frontEcsAppsBg.securityGroupForFargate,
        vpc: props.vpc,
        fireLensImage: `${props.fireLensImageBase}:fluentbitimage${props.EcsFrontBgTask?.appLanguage ?? ''}`,
        firehoseStream: appLogFirehose.stream,
        pipelineBucketRemovalPolicyParam: props.pipelineBucketRemovalPolicyParam,
        secret: frontEcsAppsBg.secret,
        EnvName: props.EnvName,
        serviceId: props.serviceId,
        blueTargetGroup: frontAlbBg.AlbBlueTg.lbForAppTargetGroup,
        greenTargetGroup: frontAlbBg.AlbGreenTg.lbForAppTargetGroup,
        listener: frontAlbBg.ALbListenerBlue,
        testListener: frontAlbBg.ALbListenerGreen,
        ...props.blueGreenFrontDeployPipelineParam,
      });
    }

    // ECS サービスパターン 4. Backend Blue/Green
    if (
      props.EcsBackBgTask &&
      props.ecsBackBgTaskRole &&
      props.blueGreenBackDeployPipelineParam &&
      (props.deployControl === 'ALL' || props.deployControl === 'BLUE_GREEN') &&
      (props.deployResource === 'ALL' || props.deployResource === 'BACKEND')
    ) {
      let securityGroupsOtherECSAppBg: ec2.ISecurityGroup[] = [];
      if (props.EcsBackBgTask.AllowSGOtherECSApp) {
        securityGroupsOtherECSAppBg = props.EcsBackBgTask.AllowSGOtherECSApp.map((service) => {
          const parameterPath = `/${service.serviceId}/${service.appName}/EcsSGId`;
          const sgId = ssm.StringParameter.valueForStringParameter(this, parameterPath);
          return ec2.SecurityGroup.fromSecurityGroupId(this, `SG-${service.serviceId}`, sgId);
        });
      }

      const allowFromSGBg: ec2.SecurityGroup[] = [];
      if (this.frontEcsAppsBg && this.frontEcsAppsBg.securityGroupForFargate) {
        allowFromSGBg.push(this.frontEcsAppsBg.securityGroupForFargate);
      }
      allowFromSGBg.push(this.bastionApp.securityGroup);

      const backAlbBg = new AlbBgConstruct(this, `${props.EcsBackBgTask.appName}-BackAlbBg`, {
        vpc: props.vpc,
        alarmTopic: props.alarmTopic,
        ecsApps: props.EcsBackBgTask,
        AcmArn: props.AcmArn,
        accessLogBucketLifecycleRules: props.accessLogBucketLifecycleRules,
        logRemovalPolicyParam: props.logRemovalPolicyParam,
        prefix: props.prefix,
        newrelicSecretArn: props.newrelicSecretArn,
        newrelicLayer: props.newrelicLayer,
        enableAlarm: props.EcsBackBgTask.enableAlarm,
        appName: props.EcsBackBgTask.appName,
        subnetGroupName: 'Private',
      });
      this.backAlbBg = backAlbBg;

      allowFromSGBg.push(backAlbBg.appAlbSecurityGroup);

      const backEcsAppsBg = new EcsappConstruct(this, `${props.EcsBackBgTask.appName}-BackAppBg-Ecs-Resources`, {
        serviceKey: props.serviceKey,
        vpc: props.vpc,
        ecsCluster: ecsCommon.ecsCluster,
        appName: props.EcsBackBgTask.appName,
        prefix: props.prefix,
        appKey: props.appKey,
        alarmTopic: props.alarmTopic,
        allowFromSG: allowFromSGBg,
        AllowSGOtherECSApp: securityGroupsOtherECSAppBg,
        portNumber: props.EcsBackBgTask.portNumber,
        useServiceConnect: true,
        ecrLifecycleRules: props.EcsBackBgTask.lifecycleRules,
        ecrRemovalPolicyParam: props.ecrRemovalPolicyParam,
        fireLensLogGroupRemovalPolicy: props.logRemovalPolicyParam?.removalPolicy,
        serviceConnectLogGroupRemovalPolicy: props.logRemovalPolicyParam?.removalPolicy,
        enableAlarm: props.EcsBackBgTask.enableAlarm,
      });
      this.backEcsAppsBg = backEcsAppsBg;
      backEcsAppsBg.securityGroupForFargate.addIngressRule(
        props.batchSecurityGroup,
        Port.tcp(props.EcsBackBgTask.portNumber),
      );

      //Pipeline for Backend Blue/Green
      const backEcsBgTaskRole = new EcsTaskRole(this, `${backEcsAppsBg.appName}-BackAppBg-EcsTaskRole`, {
        appName: backEcsAppsBg.ecsServiceName,
        prefix: props.prefix,
        // Change policyStatements if you want to add a policy to the task role
        policyStatements: props.ecsBackBgTaskRole.policyStatements,
        // Provide managed policy to add role if needed
        managedPolicy: props.ecsBackBgTaskRole.managedPolicy,
        secret: backEcsAppsBg.secret,
        crossAccountRoleArns: props.ecsBackBgTaskRole.crossAccountRoleArns,
      });
      this.backEcsBgTaskRole = backEcsBgTaskRole.taskrole;

      this.backEcsAppBgPipeline = new PipelineBlueGreenConstruct(this, `${backEcsAppsBg.appName}-BackApp-Pipeline`, {
        prefix: `${props.prefix}`,
        appName: backEcsAppsBg.appName,
        ecsCluster: ecsCommon.ecsCluster,
        ecsServiceName: backEcsAppsBg.ecsServiceName,
        securityGroup: backEcsAppsBg.securityGroupForFargate,
        vpc: props.vpc,
        fireLensImage: `${props.fireLensImageBase}:fluentbitimage${props.EcsBackBgTask?.appLanguage ?? ''}`,
        firehoseStream: appLogFirehose.stream,
        imageZipFlag: 'back',
        pipelineBucketRemovalPolicyParam: props.pipelineBucketRemovalPolicyParam,
        secret: backEcsAppsBg.secret,
        EnvName: props.EnvName,
        pipelineSourceBucketLifeCycleRules: props.pipelineSourceBucketLifeCycleRules,
        serviceId: props.serviceId,
        blueTargetGroup: backAlbBg.AlbBlueTg.lbForAppTargetGroup,
        greenTargetGroup: backAlbBg.AlbGreenTg.lbForAppTargetGroup,
        listener: backAlbBg.ALbListenerBlue,
        testListener: backAlbBg.ALbListenerGreen,
        ...props.blueGreenBackDeployPipelineParam,
      });
    }
  }
}
