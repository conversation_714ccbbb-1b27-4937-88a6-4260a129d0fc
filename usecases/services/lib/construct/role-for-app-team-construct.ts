import * as cdk from 'aws-cdk-lib';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as apigw from 'aws-cdk-lib/aws-apigateway';
import { Construct } from 'constructs';

export interface RoleForAppTeamProps {
  /**
   * The prefix of the project
   */
  readonly pjPrefix: string;
  /**
   * The common prefix of the project
   */
  readonly pjCommonPrefix: string;
  /**
   * The name of the ECS cluster
   */
  ecsClusterName: string;
  /**
   * The list of users who are allowed to assume the role.
   */
  readonly allowUsers: string[];
  /**
   * The name of the ECS task definition
   */
  readonly taskDefName: string;
  /**
   * Whether the role has a role for the vendor
   */
  readonly hasRoleForVendor?: boolean;
  /**
   * The vendor bastion account ID
   */
  readonly vendorBastionAccountId?: string;
  /**
   * The SecretsManager Arn for ECS frontend application
   */
  readonly frontsecretArn: string;
  /**
   * The SecretsManager Arn for ECS frontend application
   */
  readonly frontbgsecretArn: string;
  /**
   * The SecretsManager Arn for ECS backend application
   */
  readonly backsecretArn: string;
  /**
   * The SecretsManager Arn for ECS backend application
   */
  readonly backbgsecretArn: string;
  /**
   * The SecretsManager Arn for ECS task of batch applications
   */
  readonly batchSecretsArns?: string[];
  /**
   * The ECS frontend application name
   */
  readonly frontAppName: string;
  /**
   * The ECS frontend application name
   */
  readonly frontBgAppName: string;
  /**
   * The ECS backend application name
   */
  readonly backAppName: string;
  /**
   * The ECS backend application name
   */
  readonly backBgAppName: string;
  /**
   * The S3 bucket ARN of frontend ecspresso pipeline
   */
  readonly frontSourceBucketArn: string;
  /**
   * The S3 bucket ARN of frontend ecspresso pipeline
   */
  readonly frontBgSourceBucketArn: string;
  /**
   * The S3 bucket ARN of backend ecspresso pipeline
   */
  readonly backSourceBucketArn: string;
  /**
   * The S3 bucket ARN of backend ecspresso pipeline
   */
  readonly backBgSourceBucketArn: string;
  /**
   * The S3 bucket ARN of batch pipelines
   */
  readonly batchSourceBucketArns?: string[];
  /**
   * Task execution role of bastion container
   */
  readonly bastionExecutionRoleArn: string;
  /**
   * Service task role of bastion container
   */
  readonly bastionServiceTaskRoleArn: string;
  /**
   * batch names
   */
  readonly batchNames?: string[];
  /**
   * batch api trigger
   */
  readonly batchApi?: apigw.IRestApi;
  /**
   * Allow the creation of ECS service-related resources on only one side of the front/back end.
   */
  readonly deployResource: string;
  /**
   * Specify the ECS Deploy Controller to be created.
   */
  readonly deployController: 'ALL' | 'ROLLING' | 'BLUE_GREEN';
}

export class RoleForAppTeam extends Construct {
  constructor(scope: Construct, id: string, props: RoleForAppTeamProps) {
    super(scope, id);

    const prefixSsmArn = `arn:aws:ssm:${cdk.Stack.of(this).region}:${cdk.Stack.of(this).account}`;
    const prefixEcsArn = `arn:aws:ecs:${cdk.Stack.of(this).region}:${cdk.Stack.of(this).account}`;

    /*
      【TODO】
      フロントエンドとバックエンドでアプリチームが異なる場合がある。
      ポリシーの分離対応は追って対応する。
    */
    const createPolicies = (
      resourceType: 'ALL' | 'FRONTEND' | 'BACKEND',
      controlType: 'ALL' | 'ROLLING' | 'BLUE_GREEN',
    ) => {
      const isFrontend = resourceType === 'FRONTEND' || resourceType === 'ALL';
      const isBackend = resourceType === 'BACKEND' || resourceType === 'ALL';
      const isRolling = controlType === 'ROLLING' || controlType === 'ALL';
      const isBlueGreen = controlType === 'BLUE_GREEN' || controlType === 'ALL';

      return {
        'allow-ssm-parameter': new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              actions: ['ssm:GetParameter'],
              resources: [
                `${prefixSsmArn}:parameter/${props.pjCommonPrefix}/*`,
                `${prefixSsmArn}:parameter/${props.pjPrefix}/*`,
              ],
            }),
            ...(props.batchSecretsArns
              ? [
                  new iam.PolicyStatement({
                    actions: ['ssm:GetParametersByPath', 'ssm:GetParameter'],
                    resources: [`${prefixSsmArn}:parameter/${props.pjPrefix}/batch/*`],
                  }),
                ]
              : []),
          ],
        }),
        'allow-secret-manager': new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              actions: ['secretsmanager:GetSecretValue', 'secretsmanager:PutSecretValue'],
              resources: [
                ...(isFrontend && isRolling ? [props.frontsecretArn] : []),
                ...(isBackend && isRolling ? [props.backsecretArn] : []),
                ...(isFrontend && isBlueGreen ? [props.frontbgsecretArn] : []),
                ...(isBackend && isBlueGreen ? [props.backbgsecretArn] : []),
                ...(props.batchSecretsArns ?? []),
              ],
            }),
          ],
        }),
        'allow-ecs-task-definition': new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              // ワイルドカード指定
              actions: ['ecs:DescribeTaskDefinition'],
              resources: ['*'],
            }),
          ],
        }),
        'allow-ecs-task': new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              actions: ['ecs:DescribeTasks', 'ecs:RunTask'],
              resources: [`${prefixEcsArn}:task/*`, `${prefixEcsArn}:task-definition/${props.taskDefName}:*`],
            }),
          ],
        }),
        'allow-session-manager': new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              actions: ['ssm:StartSession', 'ssm:TerminateSession'],
              resources: [`${prefixEcsArn}:task/*`, `${prefixSsmArn}:session/*`, `${prefixSsmArn}:document/*`],
            }),
          ],
        }),
        'allow-s3-bucket': new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              actions: ['s3:GetObject', 's3:PutObject'],
              resources: [
                ...(isFrontend && isRolling && props.frontSourceBucketArn ? [`${props.frontSourceBucketArn}/*`] : []),
                ...(isBackend && isRolling && props.backSourceBucketArn ? [`${props.backSourceBucketArn}/*`] : []),
                ...(isFrontend && isBlueGreen ? [`${props.frontBgSourceBucketArn}/*`] : []),
                ...(isBackend && isBlueGreen ? [`${props.backBgSourceBucketArn}/*`] : []),
                ...(props.batchSourceBucketArns ?? []),
              ],
            }),
          ],
        }),
        'allow-iam-role': new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              actions: ['iam:PassRole'],
              resources: [props.bastionExecutionRoleArn, props.bastionServiceTaskRoleArn],
            }),
          ],
        }),
        'allow-ecs-list-services-tasks': new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              actions: ['ecs:ListServices', 'ecs:ListTasks'],
              resources: ['*'],
              conditions: {
                StringEquals: {
                  'ecs:cluster': `${prefixEcsArn}:cluster/${props.ecsClusterName}`,
                },
              },
            }),
          ],
        }),
        'allow-ecs-execute-command': new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              actions: ['ecs:ExecuteCommand'],
              resources: [
                `${prefixEcsArn}:cluster/${props.ecsClusterName}`,
                `${prefixEcsArn}:task/${props.ecsClusterName}/*`,
              ],
            }),
          ],
        }),
        ...(props.batchNames != undefined
          ? {
              'allow-to-execute-batch': new iam.PolicyDocument({
                statements: [
                  new iam.PolicyStatement({
                    actions: ['states:StartExecution', 'states:DescribeStateMachine'],
                    resources: props.batchNames.map(
                      (batchName) =>
                        `arn:aws:states:${cdk.Stack.of(this).region}:${
                          cdk.Stack.of(this).account
                        }:stateMachine:${batchName}`,
                    ),
                  }),
                ],
              }),
            }
          : {}),
        ...(props.batchApi != undefined
          ? {
              'allow-to-invoke-api': new iam.PolicyDocument({
                statements: [
                  new iam.PolicyStatement({
                    actions: ['execute-api:Invoke'],
                    resources: [props.batchApi.arnForExecuteApi()],
                  }),
                ],
              }),
            }
          : {}),
      };
    };

    if (['ALL', 'FRONTEND', 'BACKEND'].includes(props.deployResource)) {
      const inlinePolicies = createPolicies(
        props.deployResource as 'ALL' | 'FRONTEND' | 'BACKEND',
        props.deployController as 'ALL' | 'ROLLING' | 'BLUE_GREEN',
      );

      const role = new iam.Role(this, `${props.pjPrefix}-RoleForAppTeam`, {
        roleName: `${props.pjPrefix}-RoleForAppTeam`,
        assumedBy: new iam.AccountPrincipal(cdk.Stack.of(this).account).withConditions({
          StringEquals: {
            'aws:PrincipalTag/SsoUserName': props.allowUsers,
          },
        }),
        inlinePolicies,
      });

      new cdk.CfnOutput(this, `${props.pjPrefix}-RoleForAppTeamArn`, {
        value: role.roleArn,
        exportName: `${props.pjPrefix}-RoleForAppTeamArn`,
      });

      if (props.hasRoleForVendor && props.vendorBastionAccountId) {
        const roleForVendor = new iam.Role(this, `${props.pjPrefix}-RoleForVendor`, {
          roleName: `${props.pjPrefix}-RoleForVendor`,
          assumedBy: new iam.AccountPrincipal(props.vendorBastionAccountId).withConditions({
            Bool: {
              'aws:MultiFactorAuthPresent': true,
            },
          }),
          inlinePolicies,
        });

        new cdk.CfnOutput(this, `${props.pjPrefix}-RoleForVendorArn`, {
          value: roleForVendor.roleArn,
          exportName: `${props.pjPrefix}-RoleForVendorArn`,
        });
      }
    }
  }
}
