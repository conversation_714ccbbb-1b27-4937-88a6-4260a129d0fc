import * as cdk from 'aws-cdk-lib';
import * as efs from 'aws-cdk-lib/aws-efs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as kms from 'aws-cdk-lib/aws-kms';
import * as backup from 'aws-cdk-lib/aws-backup';
import * as events from 'aws-cdk-lib/aws-events';
import { Construct } from 'constructs';

export interface EfsProps {
  readonly pjPrefix: string;
  readonly vpc: ec2.IVpc;
  readonly appKey: kms.IKey;
  readonly backendServerSecurityGroup?: ec2.SecurityGroup;
  readonly frontServerSecurityGroup?: ec2.SecurityGroup;
  readonly bastionSecurityGroup?: ec2.SecurityGroup;
  readonly batchSecurityGroups?: ec2.ISecurityGroup[];
  readonly lifecyclePolicy?: efs.LifecyclePolicy;
  readonly outOfInfrequentAccessPolicy?: efs.OutOfInfrequentAccessPolicy;
  readonly throughputMode: efs.ThroughputMode;
  readonly performanceMode: efs.PerformanceMode;
  readonly removalPolicy?: cdk.RemovalPolicy;
  readonly hasBackup?: boolean;
  readonly backupParams?: {
    schedule: events.Schedule;
    retentionPeriod: cdk.Duration;
    removalPolicy?: cdk.RemovalPolicy;
  };
}

export class Efs extends Construct {
  public readonly fileSystem: efs.FileSystem;
  public readonly securityGroup: ec2.SecurityGroup;

  constructor(scope: Construct, id: string, props: EfsProps) {
    super(scope, id);

    // Create security group for EFS
    const efsSecurityGroup = new ec2.SecurityGroup(this, 'EfsSecurityGroup', {
      vpc: props.vpc,
      description: 'EFS Security Group',
    });

    // For Backend connect
    if (props.backendServerSecurityGroup) {
      efsSecurityGroup.connections.allowFrom(props.backendServerSecurityGroup, ec2.Port.tcp(2049));
    }

    // For Frontend connect
    if (props.frontServerSecurityGroup) {
      efsSecurityGroup.connections.allowFrom(props.frontServerSecurityGroup, ec2.Port.tcp(2049));
    }

    // For Bastion connect
    if (props.bastionSecurityGroup) {
      efsSecurityGroup.connections.allowFrom(props.bastionSecurityGroup, ec2.Port.tcp(2049));
    }

    // For Batch connect
    if (props.batchSecurityGroups) {
      for (const batchSecurityGroup of props.batchSecurityGroups) {
        efsSecurityGroup.connections.allowFrom(batchSecurityGroup, ec2.Port.tcp(2049));
      }
    }

    // Create EFS
    this.fileSystem = new efs.FileSystem(this, 'EfsFileSystem', {
      fileSystemName: `${props.pjPrefix}-efs`,
      vpc: props.vpc,
      vpcSubnets: { subnetType: ec2.SubnetType.PRIVATE_ISOLATED },
      securityGroup: efsSecurityGroup,
      encrypted: true,
      kmsKey: props.appKey,
      lifecyclePolicy: props.lifecyclePolicy,
      outOfInfrequentAccessPolicy: props.outOfInfrequentAccessPolicy,
      throughputMode: props.throughputMode,
      performanceMode: props.performanceMode,
      removalPolicy: props.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
      enableAutomaticBackups: false,
    });

    // Create AWS Backup Plan for EFS
    if (props.hasBackup) {
      const backupPlan = new backup.BackupPlan(this, 'EfsBackupPlan', {
        backupPlanName: `${props.pjPrefix}-efs`,
        backupPlanRules: [
          new backup.BackupPlanRule({
            backupVault: new backup.BackupVault(this, 'EfsBackupVault', {
              backupVaultName: `${props.pjPrefix}-efs-backup-vault`,
              removalPolicy: props.backupParams?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
            }),
            scheduleExpression: props.backupParams?.schedule,
            deleteAfter: props.backupParams?.retentionPeriod,
          }),
        ],
      });
      backupPlan.addSelection('EfsBackupSelection', {
        resources: [backup.BackupResource.fromEfsFileSystem(this.fileSystem)],
      });
    }

    // Output
    new cdk.CfnOutput(this, 'EfsFileSystemId', {
      value: this.fileSystem.fileSystemId,
      exportName: `${props.pjPrefix}-FileSystemId`,
    });
  }
}
