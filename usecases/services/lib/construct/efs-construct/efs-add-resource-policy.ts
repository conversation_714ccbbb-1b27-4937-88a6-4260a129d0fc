import * as cdk from 'aws-cdk-lib';
import * as efs from 'aws-cdk-lib/aws-efs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';

export interface EfsAddResourcePolicyProps {
  /**
   * EFS file system
   */
  readonly fileSystem: efs.FileSystem;
  /**
   * EFS security group
   */
  readonly efsSecurityGroup: ec2.SecurityGroup;
  /**
   * Is use shared transfer family
   */
  readonly sharedTransferFamilyAccountID?: string;
}

export class EfsAddResourcePolicy extends Construct {
  constructor(scope: Construct, id: string, props: EfsAddResourcePolicyProps) {
    super(scope, id);

    const actions = [
      'elasticfilesystem:ClientRootAccess',
      'elasticfilesystem:ClientWrite',
      'elasticfilesystem:ClientMount',
    ];

    const fileSystemPolicies: iam.PolicyStatement[] = [];

    fileSystemPolicies.push(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        principals: [new iam.AnyPrincipal()],
        actions,
        conditions: {
          Bool: {
            'elasticfilesystem:AccessedViaMountTarget': 'true',
          },
        },
      }),
    );

    if (props.sharedTransferFamilyAccountID) {
      // Get file system ID from Output
      const fileSystemId = cdk.Fn.importValue('FileSystemId');

      const sharedTransferFamilyPrincipals = [
        `arn:aws:iam::${props.sharedTransferFamilyAccountID}:root`,
        `arn:aws:iam::${props.sharedTransferFamilyAccountID}:role/Transfer-Access-Role-${fileSystemId}`,
      ];

      for (const principal of sharedTransferFamilyPrincipals) {
        fileSystemPolicies.push(
          new iam.PolicyStatement({
            effect: iam.Effect.ALLOW,
            principals: [new iam.ArnPrincipal(principal)],
            actions,
          }),
        );
      }
    }

    fileSystemPolicies.forEach((policy) => {
      props.fileSystem.addToResourcePolicy(policy);
    });
  }
}
