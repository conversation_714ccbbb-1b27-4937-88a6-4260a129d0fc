import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { aws_rds as rds } from 'aws-cdk-lib';
import { aws_sns as sns } from 'aws-cdk-lib';
import { AuroraBaseClusterProps } from '../construct/aurora-constructs/aurora-cluster-construct';
import { AuroraPostgresql } from '../construct/aurora-constructs/aurora-postgresql-construct';
import { AuroraMysql } from '../construct/aurora-constructs/aurora-mysql-construct';
import { AuroraAlarm } from '../construct/aurora-constructs/aurora-alarm-construct';
import { CWLogToDataFirehose } from '../construct/cw-log-to-kinesis-data-firehose-construct';
import { LifecycleRule } from 'aws-cdk-lib/aws-s3';

export interface DbAuroraStackProps extends cdk.StackProps, AuroraBaseClusterProps {
  dbVersion: rds.AuroraMysqlEngineVersion | rds.AuroraPostgresEngineVersion;
  alarmTopic: sns.Topic | sns.ITopic;
  pjPrefix: string;
  newrelicSecretArn: string;
  logRemovalPolicyParam?: {
    removalPolicy: cdk.RemovalPolicy;
    autoDeleteObjects: boolean;
  };
  codeBuildBucketLogLifecycleRules: LifecycleRule[];
}

export class DbAuroraStack extends cdk.Stack {
  public readonly dbClusterName: string;
  public readonly proxy?: rds.DatabaseProxy;

  constructor(scope: Construct, id: string, props: DbAuroraStackProps) {
    super(scope, id, props);

    // ----------------------- Create Cluster -----------------------------

    const auroraClusterSpec: AuroraBaseClusterProps = {
      pjPrefix: props.pjPrefix,
      dbName: props.dbName,
      dbUser: props.dbUser,
      auroraMinAcu: props.auroraMinAcu,
      auroraMaxAcu: props.auroraMaxAcu,
      vpc: props.vpc,
      vpcSubnets: props.vpcSubnets,
      writerInstanceType: props.writerInstanceType,
      readers: props.readers,
      appKey: props.appKey,
      enablePerformanceInsights: props.enablePerformanceInsights,
      ingressSecurityGroups: props.ingressSecurityGroups,
      clusterParameters: props.clusterParameters,
      instanceParameters: props.instanceParameters,
      backupRetentionDays: props.backupRetentionDays,
      backupPreferredWindow: props.backupPreferredWindow,
      preferredMaintenanceWindow: props.preferredMaintenanceWindow,
      autoMinorVersionUpgrade: props.autoMinorVersionUpgrade,
      removalPolicy: props.removalPolicy,
      deletionProtection: props.deletionProtection ?? false,
      enableRdsProxy: props.enableRdsProxy,
      rdsProxyConfig: props.rdsProxyConfig,
    };

    let auroraCluster: AuroraPostgresql | AuroraMysql;

    if (props.dbVersion instanceof rds.AuroraPostgresEngineVersion) {
      // PostgreSQL
      auroraCluster = new AuroraPostgresql(this, 'AuroraPostgresqlCluster', {
        dbVersion: props.dbVersion,
        ...auroraClusterSpec,
      });
    } else {
      // MySQL
      auroraCluster = new AuroraMysql(this, 'AuroraMysqlCluster', {
        dbVersion: props.dbVersion,
        ...auroraClusterSpec,
      });
    }

    const cluster = auroraCluster.cluster;
    this.dbClusterName = cluster.clusterIdentifier;
    this.proxy = auroraCluster.proxy;

    const auroraLogGroup = auroraCluster.cluster.cloudwatchLogGroups;
    // flat map to get all log groups
    const logGroups = Object.values(auroraLogGroup).flatMap((logGroup) => logGroup);

    const auroraLogToNewRelic = new CWLogToDataFirehose(this, 'AuroraLog', {
      firehoseStreamName: `${props.pjPrefix}-aurora-stream`,
      firehoseLogGroupName: `/aws/firehose/${props.pjPrefix}/logs`,
      sourceCWLogGroup: logGroups,
      httpEndpointUrl: 'https://aws-api.newrelic.com/firehose/v1',
      httpEndpointName: 'New Relic',
      s3BackupMode: 'FailedDataOnly',
      secretArn: props.newrelicSecretArn,
      logRemovalPolicyParam: props.logRemovalPolicyParam,
      firehoseBucketLifecycleRules: props.codeBuildBucketLogLifecycleRules,
    });
    auroraLogToNewRelic.node.addDependency(cluster);
    // ----------------------- Alarms for RDS -----------------------------

    new AuroraAlarm(this, 'AuroraAlarm', {
      cluster,
      alarmTopic: props.alarmTopic,
    });
  }
}
