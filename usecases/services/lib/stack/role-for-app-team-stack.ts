import * as cdk from 'aws-cdk-lib';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import * as apigw from 'aws-cdk-lib/aws-apigateway';
import { Construct } from 'constructs';
import { RoleForAppTeam } from '../construct/role-for-app-team-construct';

export interface RoleForAppTeamStackProps extends cdk.StackProps {
  pjPrefix: string;
  pjCommonPrefix: string;
  ecsClusterName: string;
  allowUsers: string[];
  taskDefName: string;
  hasRoleForVendor?: boolean;
  vendorBastionAccountId?: string;
  // rolling
  frontsecretArn: string;
  backsecretArn: string;
  frontAppName: string;
  backAppName: string;
  frontSourceBucketArn: string;
  backSourceBucketArn: string;
  // bluegreen
  frontbgSourceBucketArn: string;
  backbgSourceBucketArn: string;
  frontbgAppName: string;
  backbgAppName: string;
  frontbgsecretArn: string;
  backbgsecretArn: string;
  batchSecretArns?: string[];
  batchSourceBucketArns?: string[];
  bastionServiceTaskRoleArn: string;
  batchNames?: string[];
  batchApi?: apigw.IRestApi;
  /**
   * Allow the creation of ECS service-related resources on only one side of the front/back end.
   */
  deployResource: string;
  /**
   * Specify the ECS Deploy Controller to be created.
   */
  deployController: 'ALL' | 'ROLLING' | 'BLUE_GREEN';
}

export class RoleForAppTeamStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: RoleForAppTeamStackProps) {
    super(scope, id, props);

    const bastionExecutionRoleArn = ssm.StringParameter.fromStringParameterName(
      this,
      'importBastionExecutionRoleArn',
      `/${props.pjCommonPrefix}/bastion/ecs/task-execution-role-arn`,
    ).stringValue;

    new RoleForAppTeam(this, 'RoleForAppTeam', {
      pjPrefix: props.pjPrefix,
      pjCommonPrefix: props.pjCommonPrefix,
      ecsClusterName: props.ecsClusterName,
      allowUsers: props.allowUsers,
      taskDefName: props.taskDefName,
      hasRoleForVendor: props.hasRoleForVendor,
      vendorBastionAccountId: props.vendorBastionAccountId,
      frontsecretArn: props.frontsecretArn,
      frontbgsecretArn: props.frontbgsecretArn,
      backsecretArn: props.backsecretArn,
      backbgsecretArn: props.backbgsecretArn,
      frontAppName: props.frontAppName,
      frontBgAppName: props.frontbgAppName,
      backAppName: props.backAppName,
      backBgAppName: props.backbgAppName,
      frontSourceBucketArn: props.frontSourceBucketArn,
      frontBgSourceBucketArn: props.frontbgSourceBucketArn,
      backSourceBucketArn: props.backSourceBucketArn,
      backBgSourceBucketArn: props.backbgSourceBucketArn,
      batchSecretsArns: props.batchSecretArns,
      batchSourceBucketArns: props.batchSourceBucketArns,
      bastionExecutionRoleArn: bastionExecutionRoleArn,
      bastionServiceTaskRoleArn: props.bastionServiceTaskRoleArn,
      batchNames: props.batchNames,
      batchApi: props.batchApi,
      deployResource: props.deployResource,
      deployController: props.deployController,
    });
  }
}
