import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { Topic } from 'aws-cdk-lib/aws-sns';
import { SNS } from '../construct/sns-construct';
import { Chatbot } from '../construct/chatbot-construct';

export interface NotifierStackProps extends cdk.StackProps {
  pjPrefix: string;
  isCreateChatbot: boolean;
  notifyEmail?: string;
  slackChannelConfiguration?: {
    channelId: string;
    workspaceId: string;
  };
  teamsChannelConfiguration?: {
    channelName: string;
    teamId: string;
    tenantId: string;
    teamsChannelId: string;
  };
}

export class NotifierStack extends cdk.Stack {
  public alarmTopic: Topic;
  constructor(scope: Construct, id: string, props: NotifierStackProps) {
    super(scope, id, props);

    const alarmTopic = new SNS(this, `${props.pjPrefix}-Alarm`, {
      notifyEmail: props.notifyEmail,
    });
    this.alarmTopic = alarmTopic.topic;

    if (props.isCreateChatbot)
      new Chatbot(this, `${props.pjPrefix}-Chatbot`, {
        topicArn: alarmTopic.topic.topicArn,
        slackChannelConfiguration: props.slackChannelConfiguration,
        teamsChannelConfiguration: props.teamsChannelConfiguration,
      });
  }
}
