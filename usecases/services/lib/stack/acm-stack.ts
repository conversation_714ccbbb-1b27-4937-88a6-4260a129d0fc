import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as ssm from 'aws-cdk-lib/aws-ssm';

export interface AcmProps extends cdk.StackProps {
  AcmDomainName: string;
  HostZoneId: string;
  AssumeRoleArn: string;
  prefix: string;
  providerServiceToken: string;
}

export class AcmStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: AcmProps) {
    super(scope, id, props);

    //カスタムリソース作成
    const customResource = new cdk.CustomResource(this, 'CustomResource', {
      serviceToken: props.providerServiceToken,
      properties: {
        DOMAIN_NAME: props.AcmDomainName,
        HOSTED_ZONE_ID: props.HostZoneId,
        ROLE_ARN: props.AssumeRoleArn,
      },
    });

    //SSMパラメータストアにACMARNを格納
    new ssm.StringParameter(this, 'AcmArn', {
      parameterName: `/${props.prefix}/AcmArn`,
      stringValue: customResource.getAtt('ACMARN').toString(),
    });
  }
}
