import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as elasticache from 'aws-cdk-lib/aws-elasticache';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as kms from 'aws-cdk-lib/aws-kms';
import * as sns from 'aws-cdk-lib/aws-sns';
import { aws_s3 as s3 } from 'aws-cdk-lib';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import {
  IElastiCacheTypeParam,
  IElastiCacheParam,
  IRemovalPolicyParam,
  IElastiCacheCrossServiceParam,
} from 'params/interface';
import { ElastiCache } from '../construct/elasticache-contructs/elasticache-self-designed-contruct';
import { ElastiCacheServerless } from '../construct/elasticache-contructs/elasticache-serverless-contruct';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import * as crypto from 'crypto';

export interface ModuleStackProps extends cdk.StackProps {
  pjPrefix: string;
  myVpc: ec2.IVpc;
  appKey: kms.IKey;
  alarmTopic: sns.ITopic;
  backendServerSecurityGroup?: ec2.SecurityGroup;
  frontServerSecurityGroup?: ec2.SecurityGroup;
  bastionSecurityGroup?: ec2.SecurityGroup;
  batchSecurityGroups?: ec2.ISecurityGroup[];
  logRemovalPolicy?: cdk.RemovalPolicy;
  ElastiCacheParam: IElastiCacheParam;
  elastiCacheType: IElastiCacheTypeParam;
  newrelicSecretArn: string;
  logRemovalPolicyParam?: IRemovalPolicyParam;
  LogBucketLifecycleRules: s3.LifecycleRule[];
  allowSgCrossService?: IElastiCacheCrossServiceParam['allowSgCrossService'];
}

export class ElastiCacheStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: ModuleStackProps) {
    super(scope, id, props);

    const secret = new secretsmanager.CfnSecret(this, 'ElastiCacheSecret', {});

    const securityGroupFor = new ec2.SecurityGroup(this, 'ElastiCacheSecuritygGroup', {
      vpc: props.myVpc,
    });
    // Add security group for ElastiCache
    // For Backend Ecs App
    if (props.backendServerSecurityGroup) {
      securityGroupFor.connections.allowFrom(props.backendServerSecurityGroup, ec2.Port.tcp(6379));
    }
    // For Frontend Ecs App
    if (props.frontServerSecurityGroup) {
      securityGroupFor.connections.allowFrom(props.frontServerSecurityGroup, ec2.Port.tcp(6379));
    }
    // For Bastion Container
    if (props.bastionSecurityGroup) {
      securityGroupFor.connections.allowFrom(props.bastionSecurityGroup, ec2.Port.tcp(6379));
    }
    // For Batch App
    if (props.batchSecurityGroups) {
      for (const batchSecurityGroup of props.batchSecurityGroups) {
        securityGroupFor.connections.allowFrom(batchSecurityGroup, ec2.Port.tcp(6379));
      }
    }
    // For Cross Service
    if (props.allowSgCrossService) {
      props.allowSgCrossService.forEach((path) => {
        const hashedPath = crypto.createHash('sha256').update(path.ssmParameterPath).digest('hex').slice(0, 16);
        const securityGroup = ec2.SecurityGroup.fromSecurityGroupId(
          this,
          `SG-${hashedPath}`,
          ssm.StringParameter.valueForStringParameter(this, path.ssmParameterPath),
          {
            mutable: false,
          },
        );
        securityGroupFor.connections.allowFrom(securityGroup, ec2.Port.tcp(6379));
      });
    }

    // Add subnet for ElastiCache
    const subnetgroup = new elasticache.CfnSubnetGroup(this, 'ElastiCacheSubnetGroup', {
      cacheSubnetGroupName: cdk.Stack.of(this).stackName + '-Subnetgroup',
      description: 'for ElastiCache',
      subnetIds: props.myVpc.isolatedSubnets.map(({ subnetId }) => subnetId),
    });

    if (props.elastiCacheType.elastiCacheType === 'SERVERLESS') {
      // Create elasticace serverless mode
      new ElastiCacheServerless(this, 'ElastiCacheServerless', {
        pjPrefix: props.pjPrefix,
        myVpc: props.myVpc,
        appKey: props.appKey,
        ElastiCacheParam: props.ElastiCacheParam.ElastiCacheServerlessParam,
        securityGroup: securityGroupFor,
        newrelicSecretArn: props.newrelicSecretArn,
        logRemovalPolicyParam: props.logRemovalPolicyParam,
        logBucketLifecycleRules: props.LogBucketLifecycleRules,
      });
    } else {
      // Create elasticace provision mode
      new ElastiCache(this, 'ElastiCache', {
        pjPrefix: props.pjPrefix,
        myVpc: props.myVpc,
        appKey: props.appKey,
        alarmTopic: props.alarmTopic,
        ElastiCacheParam: props.ElastiCacheParam.ElastiCacheSelfDesignedParam,
        secret: secret,
        subnetgroup: subnetgroup,
        securityGroup: securityGroupFor,
        newrelicSecretArn: props.newrelicSecretArn,
        logRemovalPolicyParam: props.logRemovalPolicyParam,
        LogBucketLifecycleRules: props.LogBucketLifecycleRules,
      });
    }
  }
}
