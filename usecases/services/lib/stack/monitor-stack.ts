import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { aws_cloudwatch as cw } from 'aws-cdk-lib';
import { Dashboard } from '../construct/dashboard-construct';
import * as ssm from 'aws-cdk-lib/aws-ssm';

interface MonitorStackProps extends cdk.StackProps {
  pjPrefix: string;
  dashboardName: string;
  albFullName: string;
  appTargetGroupName: string;
  ecsClusterName: string;
  ecsAlbServiceName: string;
  ecsInternalServiceName: string;
  ecsTargetUtilizationPercent: number;
  ecsScaleOnRequestCount: number;
  isExistAlbTgUnHealthyHostCountAlarm?: boolean;
  /**
   * Allow the creation of ECS service-related resources on only one side of the front/back end.
   */
  deployResource: string;
}

export class MonitorStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: MonitorStackProps) {
    super(scope, id, props);

    let albTgUnHealthyHostCountAlarm = undefined;

    if (props.isExistAlbTgUnHealthyHostCountAlarm) {
      // get albTgUnHealthyHostCountAlarm ARN from SSM
      const albTgUnHealthyHostCountAlarmArn = ssm.StringParameter.fromStringParameterName(
        this,
        'importAlbTgUnHealthyHostCountAlarmArn',
        `/${props.pjPrefix}/alb/TgUnHealthyHostCountAlarmArn`,
      ).stringValue;

      // get Alarm from ARN
      albTgUnHealthyHostCountAlarm = cw.Alarm.fromAlarmArn(
        this,
        'importAlbTgUnHealthyHostCountAlarm',
        albTgUnHealthyHostCountAlarmArn,
      );
    }

    // Dashboard
    new Dashboard(this, `${props.pjPrefix}-Dashboard`, {
      dashboardName: props.dashboardName,
      albFullName: props.albFullName,
      appTargetGroupName: props.appTargetGroupName,
      albTgUnHealthyHostCountAlarm: albTgUnHealthyHostCountAlarm,
      ecsClusterName: props.ecsClusterName,
      ecsAlbServiceName: props.ecsAlbServiceName,
      ecsInternalServiceName: props.ecsInternalServiceName,
      // AutoScaleはCDK外で管理のため、固定値を修正要で設定
      ecsScaleOnRequestCount: props.ecsScaleOnRequestCount,
      ecsTargetUtilizationPercent: props.ecsTargetUtilizationPercent,
      deployResource: props.deployResource,
    });
  }
}
