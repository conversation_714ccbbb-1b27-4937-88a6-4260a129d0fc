import * as cdk from 'aws-cdk-lib';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { Construct } from 'constructs';
import { IVpc } from 'aws-cdk-lib/aws-ec2';
import { IKey } from 'aws-cdk-lib/aws-kms';
import { ITopic } from 'aws-cdk-lib/aws-sns';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import {
  IEcsAlbParam,
  IEcsParam,
  IEcsTaskRoleParam,
  IBastionTaskRoleParam,
  IACMParam,
  IRemovalPolicyParam,
  IBlueGreenPipelineParam,
  ICrossAccountDeployPipelineParam,
} from '../../params/interface';
import { EcsApp } from '../construct/ecs-app-construct';
import { aws_s3 as s3 } from 'aws-cdk-lib';
import { ILayerVersion } from 'aws-cdk-lib/aws-lambda';
import { aws_servicediscovery as sd } from 'aws-cdk-lib';

interface EcsAppStackProps extends cdk.StackProps {
  serviceKey: string;
  vpc: IVpc;
  appKey: IKey;
  alarmTopic: ITopic;
  alarmTopicForAppTeam: ITopic;
  prefix: string;
  serviceId: string;
  pjCommonPrefix: string;
  EcsFrontTask: IEcsAlbParam;
  EcsBackTask: IEcsParam;
  ecsFrontTaskRole: IEcsTaskRoleParam;
  ecsBackTaskRole: IEcsTaskRoleParam;
  ecsBastionTasks?: boolean;
  bastionTaskRole?: IBastionTaskRoleParam;
  AlbAcm: IACMParam;
  accessLogBucketLifecycleRules: s3.LifecycleRule[];
  appLogBucketLifecycleRules: s3.LifecycleRule[];
  buildLogBucketLifecycleRules: s3.LifecycleRule[];
  logRemovalPolicyParam?: IRemovalPolicyParam;
  otherRemovalPolicyParam?: IRemovalPolicyParam;
  fireLensImageBase: string;
  newrelicSecretArn: string;
  newrelicLayer: ILayerVersion;
  EnvName: string;
  /**
   * Pipeline source bucket lifecycle rules
   */
  pipelineSourceBucketLifeCycleRules: s3.LifecycleRule[];
  /**
   * Service Discovery namespace
   */
  namespace: sd.IPrivateDnsNamespace;
  /**
   * Service Discovery namespace arn
   */
  namespaceArn: string;
  /**
   * Allow the creation of ECS service-related resources on only one side of the front/back end.
   */
  deployResource: string;
  deployControl: string;
  EcsFrontBgTask?: IEcsAlbParam;
  EcsBackBgTask?: IEcsParam;
  ecsFrontBgTaskRole?: IEcsTaskRoleParam;
  ecsBackBgTaskRole?: IEcsTaskRoleParam;
  ecspressoFrontDeployPipelineParam?: ICrossAccountDeployPipelineParam;
  ecspressoBackDeployPipelineParam?: ICrossAccountDeployPipelineParam;
  blueGreenFrontDeployPipelineParam?: IBlueGreenPipelineParam;
  blueGreenBackDeployPipelineParam?: IBlueGreenPipelineParam;
}

export class EcsAppStack extends cdk.Stack {
  public readonly app: EcsApp;
  public readonly batchSecurityGroupForConnectingBackend: ec2.ISecurityGroup;
  constructor(scope: Construct, id: string, props: EcsAppStackProps) {
    super(scope, id, props);

    const bastionEcrRepositoryName = ssm.StringParameter.fromStringParameterName(
      this,
      'importBastionEcrRepositoryName',
      `/${props.pjCommonPrefix}/bastion/ecr/repository-name`,
    ).stringValue;
    const bastionEcrRepository = ecr.Repository.fromRepositoryName(
      this,
      'importBastionEcrRepository',
      bastionEcrRepositoryName,
    );

    const bastionEcsTaskExecutionRoleArn = ssm.StringParameter.fromStringParameterName(
      this,
      'importBastionEcsTaskExecutionRoleArn',
      `/${props.pjCommonPrefix}/bastion/ecs/task-execution-role-arn`,
    ).stringValue;
    const bastionEcsTaskExecutionRole = iam.Role.fromRoleArn(
      this,
      'importBastionEcsTaskExecutionRole',
      bastionEcsTaskExecutionRoleArn,
    );

    // If a connection is created in the batch stack, it will be a circular reference,
    // so the security group must be created in advance to connect to the backend container.
    const batchSecurityGroupForConnectingBackend = new ec2.SecurityGroup(this, 'BatchSecurityGroup', {
      vpc: props.vpc,
    });
    this.batchSecurityGroupForConnectingBackend = batchSecurityGroupForConnectingBackend;

    let AcmArn = undefined;
    if (props.AlbAcm.AcmDomainName && (props.deployResource === 'ALL' || props.deployResource === 'FRONTEND')) {
      AcmArn = ssm.StringParameter.fromStringParameterName(this, 'importAcmArn', `/${props.prefix}/AcmArn`).stringValue;
    }

    const ecs = new EcsApp(this, 'ECSApp', {
      serviceKey: props.serviceKey,
      AcmArn: AcmArn,
      vpc: props.vpc,
      appKey: props.appKey,
      alarmTopic: props.alarmTopic,
      alarmTopicForAppTeam: props.alarmTopicForAppTeam,
      prefix: props.prefix,
      pjCommonPrefix: props.pjCommonPrefix,
      EcsFrontTask: props.EcsFrontTask,
      EcsBackTask: props.EcsBackTask,
      ecsFrontTaskRole: props.ecsFrontTaskRole,
      ecsBackTaskRole: props.ecsBackTaskRole,
      ecsBastionTasks: props.ecsBastionTasks ?? true,
      EnvName: props.EnvName,
      fireLensImageBase: props.fireLensImageBase,
      newrelicSecretArn: props.newrelicSecretArn,
      newrelicLayer: props.newrelicLayer,
      ecsBastionParams: {
        ecrRepository: bastionEcrRepository,
        taskExecutionRole: bastionEcsTaskExecutionRole,
        policyStatements: props.bastionTaskRole?.policyStatements,
        managedPolicy: props.bastionTaskRole?.managedPolicy,
      },
      accessLogBucketLifecycleRules: props.accessLogBucketLifecycleRules,
      appLogBucketLifecycleRules: props.appLogBucketLifecycleRules,
      buildLogBucketLifecycleRules: props.buildLogBucketLifecycleRules,
      logRemovalPolicyParam: props.logRemovalPolicyParam,
      pipelineBucketRemovalPolicyParam: props.otherRemovalPolicyParam,
      ecrRemovalPolicyParam: props.otherRemovalPolicyParam,
      pipelineSourceBucketLifeCycleRules: props.pipelineSourceBucketLifeCycleRules,
      batchSecurityGroup: batchSecurityGroupForConnectingBackend,
      namespace: props.namespace,
      namespaceArn: props.namespaceArn,
      serviceId: props.serviceId,
      deployResource: props.deployResource,
      deployControl: props.deployControl,
      EcsFrontBgTask: props.EcsFrontBgTask,
      EcsBackBgTask: props.EcsBackBgTask,
      ecsFrontBgTaskRole: props.ecsFrontBgTaskRole,
      ecsBackBgTaskRole: props.ecsBackBgTaskRole,
      ecspressoFrontDeployPipelineParam: props.ecspressoFrontDeployPipelineParam,
      ecspressoBackDeployPipelineParam: props.ecspressoBackDeployPipelineParam,
      blueGreenFrontDeployPipelineParam: props.blueGreenFrontDeployPipelineParam,
      blueGreenBackDeployPipelineParam: props.blueGreenBackDeployPipelineParam,
    });
    this.app = ecs;
  }
}
