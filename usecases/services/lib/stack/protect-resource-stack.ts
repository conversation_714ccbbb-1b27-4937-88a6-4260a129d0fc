import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { CustomResourceConstruct } from '../construct/custom-resource-construct';
interface ProtectResourceStackProps extends cdk.StackProps {
  pjPrefix: string;
  stackResourceProtection: {
    [key: string]: boolean;
  };
}
export class ProtectResourceStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: ProtectResourceStackProps) {
    super(scope, id, props);
    /* eslint-disable @typescript-eslint/no-unused-vars */
    const stackEnableProtection = Object.entries(props.stackResourceProtection)
      .filter(([key, value]) => value === true)
      .map(([key, value]) => key)
      .join(',');
    // filter key have value is false
    /* eslint-disable @typescript-eslint/no-unused-vars */
    const stackDisableProtection = Object.entries(props.stackResourceProtection)
      .filter(([key, value]) => value === false)
      .map(([key, value]) => key)
      .join(',');
    const customResourceConstruct = new CustomResourceConstruct(this, 'CustomResource', {
      properties: {
        StackEnableProtection: stackEnableProtection,
        StackDisableProtection: stackDisableProtection,
      },
    });
    customResourceConstruct.onEventHandlerFunction.role?.attachInlinePolicy(
      new cdk.aws_iam.Policy(this, 'SetStackPolicy', {
        statements: [
          new cdk.aws_iam.PolicyStatement({
            actions: ['cloudformation:SetStackPolicy', 'cloudformation:UpdateTerminationProtection'],
            resources: ['*'],
            effect: cdk.aws_iam.Effect.ALLOW,
          }),
        ],
      }),
    );
  }
}
