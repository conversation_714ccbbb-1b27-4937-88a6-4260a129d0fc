// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DbAuroraStack Should match snapshot 1`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "AuroraAlarmAuroraCPUUtil120227F8": Object {
      "Properties": Object {
        "ActionsEnabled": true,
        "AlarmActions": Array [
          Object {
            "Fn::ImportValue": "GEVANNI-service-ImportResources:ExportsOutputRefimportAlarmTopicArnParameterE7221DC7",
          },
        ],
        "ComparisonOperator": "GreaterThanOrEqualToThreshold",
        "DatapointsToAlarm": 3,
        "Dimensions": Array [
          Object {
            "Name": "DBClusterIdentifier",
            "Value": Object {
              "Ref": "AuroraMysqlClusterAurora2CC0C6CE",
            },
          },
        ],
        "EvaluationPeriods": 3,
        "MetricName": "CPUUtilization",
        "Namespace": "AWS/RDS",
        "Period": 60,
        "Statistic": "Average",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
        "Threshold": 90,
      },
      "Type": "AWS::CloudWatch::Alarm",
    },
    "AuroraAlarmRdsEventsCluster34701E5D": Object {
      "Properties": Object {
        "Enabled": true,
        "EventCategories": Array [
          "failure",
          "failover",
          "maintenance",
        ],
        "SnsTopicArn": Object {
          "Fn::ImportValue": "GEVANNI-service-ImportResources:ExportsOutputRefimportAlarmTopicArnParameterE7221DC7",
        },
        "SourceType": "db-cluster",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::RDS::EventSubscription",
    },
    "AuroraAlarmRdsEventsInstances85C90044": Object {
      "Properties": Object {
        "Enabled": true,
        "EventCategories": Array [
          "availability",
          "configuration change",
          "deletion",
          "failover",
          "failure",
          "maintenance",
          "notification",
          "recovery",
        ],
        "SnsTopicArn": Object {
          "Fn::ImportValue": "GEVANNI-service-ImportResources:ExportsOutputRefimportAlarmTopicArnParameterE7221DC7",
        },
        "SourceType": "db-instance",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::RDS::EventSubscription",
    },
    "AuroraLogCfnSubscriptionFilter1F4DCC91B": Object {
      "Properties": Object {
        "DestinationArn": Object {
          "Fn::GetAtt": Array [
            "AuroraLogFirehoseFirehoseStream58CBDDFD",
            "Arn",
          ],
        },
        "FilterPattern": "",
        "LogGroupName": Object {
          "Fn::Join": Array [
            "",
            Array [
              "/aws/rds/cluster/",
              Object {
                "Ref": "AuroraMysqlClusterAurora2CC0C6CE",
              },
              "/error",
            ],
          ],
        },
        "RoleArn": Object {
          "Fn::GetAtt": Array [
            "AuroraLogToKinesisFirehoseRole0F9BD42D",
            "Arn",
          ],
        },
      },
      "Type": "AWS::Logs::SubscriptionFilter",
    },
    "AuroraLogCfnSubscriptionFilter2DB3EB2E3": Object {
      "Properties": Object {
        "DestinationArn": Object {
          "Fn::GetAtt": Array [
            "AuroraLogFirehoseFirehoseStream58CBDDFD",
            "Arn",
          ],
        },
        "FilterPattern": "",
        "LogGroupName": Object {
          "Fn::Join": Array [
            "",
            Array [
              "/aws/rds/cluster/",
              Object {
                "Ref": "AuroraMysqlClusterAurora2CC0C6CE",
              },
              "/general",
            ],
          ],
        },
        "RoleArn": Object {
          "Fn::GetAtt": Array [
            "AuroraLogToKinesisFirehoseRole0F9BD42D",
            "Arn",
          ],
        },
      },
      "Type": "AWS::Logs::SubscriptionFilter",
    },
    "AuroraLogCfnSubscriptionFilter38E68C835": Object {
      "Properties": Object {
        "DestinationArn": Object {
          "Fn::GetAtt": Array [
            "AuroraLogFirehoseFirehoseStream58CBDDFD",
            "Arn",
          ],
        },
        "FilterPattern": "",
        "LogGroupName": Object {
          "Fn::Join": Array [
            "",
            Array [
              "/aws/rds/cluster/",
              Object {
                "Ref": "AuroraMysqlClusterAurora2CC0C6CE",
              },
              "/slowquery",
            ],
          ],
        },
        "RoleArn": Object {
          "Fn::GetAtt": Array [
            "AuroraLogToKinesisFirehoseRole0F9BD42D",
            "Arn",
          ],
        },
      },
      "Type": "AWS::Logs::SubscriptionFilter",
    },
    "AuroraLogCfnSubscriptionFilter497CB804B": Object {
      "Properties": Object {
        "DestinationArn": Object {
          "Fn::GetAtt": Array [
            "AuroraLogFirehoseFirehoseStream58CBDDFD",
            "Arn",
          ],
        },
        "FilterPattern": "",
        "LogGroupName": Object {
          "Fn::Join": Array [
            "",
            Array [
              "/aws/rds/cluster/",
              Object {
                "Ref": "AuroraMysqlClusterAurora2CC0C6CE",
              },
              "/audit",
            ],
          ],
        },
        "RoleArn": Object {
          "Fn::GetAtt": Array [
            "AuroraLogToKinesisFirehoseRole0F9BD42D",
            "Arn",
          ],
        },
      },
      "Type": "AWS::Logs::SubscriptionFilter",
    },
    "AuroraLogFirehoseCwLogGroup334A42D1": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "LogGroupName": "/aws/firehose/GEVANNI-service/logs",
        "RetentionInDays": 90,
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::Logs::LogGroup",
      "UpdateReplacePolicy": "Delete",
    },
    "AuroraLogFirehoseCwLogStream50F03263": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "LogGroupName": Object {
          "Ref": "AuroraLogFirehoseCwLogGroup334A42D1",
        },
      },
      "Type": "AWS::Logs::LogStream",
      "UpdateReplacePolicy": "Delete",
    },
    "AuroraLogFirehoseFirehoseLogBucket8FB753CE": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "NoncurrentVersionExpiration": Object {
                "NewerNoncurrentVersions": 20,
                "NoncurrentDays": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "AuroraLogFirehoseFirehoseLogBucketAutoDeleteObjectsCustomResourceD4A20E34": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "AuroraLogFirehoseFirehoseLogBucketPolicyB22472B3",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "AuroraLogFirehoseFirehoseLogBucket8FB753CE",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "AuroraLogFirehoseFirehoseLogBucketPolicyB22472B3": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "AuroraLogFirehoseFirehoseLogBucket8FB753CE",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "AuroraLogFirehoseFirehoseLogBucket8FB753CE",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "AuroraLogFirehoseFirehoseLogBucket8FB753CE",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "AuroraLogFirehoseFirehoseLogBucket8FB753CE",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "AuroraLogFirehoseFirehoseLogBucket8FB753CE",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "AuroraLogFirehoseFirehoseRole56F7E896": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "firehose.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "AuroraLogFirehoseFirehoseRoleDefaultPolicy4401865C": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:PutObject",
                "s3:GetBucketLocation",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "AuroraLogFirehoseFirehoseLogBucket8FB753CE",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "AuroraLogFirehoseFirehoseLogBucket8FB753CE",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": "secretsmanager:GetSecretValue",
              "Effect": "Allow",
              "Resource": Object {
                "Fn::ImportValue": "GEVANNI-service-Secrets:ExportsOutputFnGetAttGEVANNIserviceNewRelicSecretIdF8C92B1E",
              },
            },
            Object {
              "Action": "logs:PutLogEvents",
              "Effect": "Allow",
              "Resource": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    Object {
                      "Fn::GetAtt": Array [
                        "AuroraLogFirehoseCwLogGroup334A42D1",
                        "Arn",
                      ],
                    },
                    ":*",
                  ],
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "AuroraLogFirehoseFirehoseRoleDefaultPolicy4401865C",
        "Roles": Array [
          Object {
            "Ref": "AuroraLogFirehoseFirehoseRole56F7E896",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "AuroraLogFirehoseFirehoseStream58CBDDFD": Object {
      "Properties": Object {
        "DeliveryStreamName": "GEVANNI-service-aurora-stream",
        "DeliveryStreamType": "DirectPut",
        "HttpEndpointDestinationConfiguration": Object {
          "BufferingHints": Object {
            "IntervalInSeconds": 60,
            "SizeInMBs": 1,
          },
          "CloudWatchLoggingOptions": Object {
            "Enabled": true,
            "LogGroupName": Object {
              "Ref": "AuroraLogFirehoseCwLogGroup334A42D1",
            },
            "LogStreamName": Object {
              "Ref": "AuroraLogFirehoseCwLogStream50F03263",
            },
          },
          "EndpointConfiguration": Object {
            "Name": "New Relic",
            "Url": "https://aws-api.newrelic.com/firehose/v1",
          },
          "RequestConfiguration": Object {
            "ContentEncoding": "GZIP",
          },
          "RetryOptions": Object {
            "DurationInSeconds": 60,
          },
          "RoleARN": Object {
            "Fn::GetAtt": Array [
              "AuroraLogFirehoseFirehoseRole56F7E896",
              "Arn",
            ],
          },
          "S3BackupMode": "FailedDataOnly",
          "S3Configuration": Object {
            "BucketARN": Object {
              "Fn::GetAtt": Array [
                "AuroraLogFirehoseFirehoseLogBucket8FB753CE",
                "Arn",
              ],
            },
            "CompressionFormat": "GZIP",
            "RoleARN": Object {
              "Fn::GetAtt": Array [
                "AuroraLogFirehoseFirehoseRole56F7E896",
                "Arn",
              ],
            },
          },
          "SecretsManagerConfiguration": Object {
            "Enabled": true,
            "RoleARN": Object {
              "Fn::GetAtt": Array [
                "AuroraLogFirehoseFirehoseRole56F7E896",
                "Arn",
              ],
            },
            "SecretARN": Object {
              "Fn::ImportValue": "GEVANNI-service-Secrets:ExportsOutputFnGetAttGEVANNIserviceNewRelicSecretIdF8C92B1E",
            },
          },
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::KinesisFirehose::DeliveryStream",
    },
    "AuroraLogToKinesisFirehoseRole0F9BD42D": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "logs.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/AmazonKinesisFirehoseFullAccess",
              ],
            ],
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "AuroraMysqlClusterAurora2CC0C6CE": Object {
      "DeletionPolicy": "Snapshot",
      "Properties": Object {
        "BacktrackWindow": 86400,
        "BackupRetentionPeriod": 1,
        "CopyTagsToSnapshot": true,
        "DBClusterParameterGroupName": Object {
          "Ref": "AuroraMysqlClusterAuroraClusterParameterGroupFA15B5FF",
        },
        "DBSubnetGroupName": Object {
          "Ref": "AuroraMysqlClusterSubnetGroup5BFB25C6",
        },
        "DatabaseName": "mydbname",
        "DeletionProtection": false,
        "EnableCloudwatchLogsExports": Array [
          "error",
          "general",
          "slowquery",
          "audit",
        ],
        "Engine": "aurora-mysql",
        "EngineVersion": "8.0.mysql_aurora.3.04.3",
        "KmsKeyId": Object {
          "Fn::ImportValue": "GEVANNI-service-ImportResources:ExportsOutputRefimportAppKeyArnParameterD7B87911",
        },
        "MasterUserPassword": Object {
          "Fn::Join": Array [
            "",
            Array [
              "{{resolve:secretsmanager:",
              Object {
                "Ref": "GEVANNIserviceDBAuroraAuroraMysqlClusterAuroraSecret131EB8833fdaad7efa858a3daf9490cf0a702aeb",
              },
              ":SecretString:password::}}",
            ],
          ],
        },
        "MasterUsername": "dbUser",
        "PreferredBackupWindow": "17:00-18:00",
        "PreferredMaintenanceWindow": "Wed:19:00-Wed:20:00",
        "StorageEncrypted": true,
        "Tags": Array [
          Object {
            "Key": "AWSBackup",
            "Value": "True",
          },
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
        "VpcSecurityGroupIds": Array [
          Object {
            "Fn::GetAtt": Array [
              "AuroraMysqlClusterSecurityGroup3D4B3D2A",
              "GroupId",
            ],
          },
        ],
      },
      "Type": "AWS::RDS::DBCluster",
      "UpdateReplacePolicy": "Snapshot",
    },
    "AuroraMysqlClusterAuroraClusterParameterGroupFA15B5FF": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "Description": "Aurora Cluster Parameter Group",
        "Family": "aurora-mysql8.0",
        "Parameters": Object {
          "binlog_format": "ROW",
          "character_set_client": "utf8mb4",
          "character_set_connection": "utf8mb4",
          "character_set_database": "utf8mb4",
          "character_set_results": "utf8mb4",
          "character_set_server": "utf8mb4",
          "init_connect": "SET NAMES utf8mb4",
          "time_zone": "Asia/Tokyo",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::RDS::DBClusterParameterGroup",
      "UpdateReplacePolicy": "Delete",
    },
    "AuroraMysqlClusterAuroraInstanceParameterGroupF88928D7": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "Description": "Aurora Instance Parameter Group",
        "Family": "aurora-mysql8.0",
        "Parameters": Object {
          "long_query_time": "10",
          "slow_query_log": "1",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::RDS::DBParameterGroup",
      "UpdateReplacePolicy": "Delete",
    },
    "AuroraMysqlClusterAuroraLogRetentionaudit151D70E1": Object {
      "Properties": Object {
        "LogGroupName": Object {
          "Fn::Join": Array [
            "",
            Array [
              "/aws/rds/cluster/",
              Object {
                "Ref": "AuroraMysqlClusterAurora2CC0C6CE",
              },
              "/audit",
            ],
          ],
        },
        "RetentionInDays": 90,
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aFD4BFC8A",
            "Arn",
          ],
        },
      },
      "Type": "Custom::LogRetention",
    },
    "AuroraMysqlClusterAuroraLogRetentionerror91B89A28": Object {
      "Properties": Object {
        "LogGroupName": Object {
          "Fn::Join": Array [
            "",
            Array [
              "/aws/rds/cluster/",
              Object {
                "Ref": "AuroraMysqlClusterAurora2CC0C6CE",
              },
              "/error",
            ],
          ],
        },
        "RetentionInDays": 90,
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aFD4BFC8A",
            "Arn",
          ],
        },
      },
      "Type": "Custom::LogRetention",
    },
    "AuroraMysqlClusterAuroraLogRetentiongeneralD09EE69A": Object {
      "Properties": Object {
        "LogGroupName": Object {
          "Fn::Join": Array [
            "",
            Array [
              "/aws/rds/cluster/",
              Object {
                "Ref": "AuroraMysqlClusterAurora2CC0C6CE",
              },
              "/general",
            ],
          ],
        },
        "RetentionInDays": 90,
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aFD4BFC8A",
            "Arn",
          ],
        },
      },
      "Type": "Custom::LogRetention",
    },
    "AuroraMysqlClusterAuroraLogRetentionslowquery4049F042": Object {
      "Properties": Object {
        "LogGroupName": Object {
          "Fn::Join": Array [
            "",
            Array [
              "/aws/rds/cluster/",
              Object {
                "Ref": "AuroraMysqlClusterAurora2CC0C6CE",
              },
              "/slowquery",
            ],
          ],
        },
        "RetentionInDays": 90,
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aFD4BFC8A",
            "Arn",
          ],
        },
      },
      "Type": "Custom::LogRetention",
    },
    "AuroraMysqlClusterAuroraSecretAttachment96B0BBA9": Object {
      "Properties": Object {
        "SecretId": Object {
          "Ref": "GEVANNIserviceDBAuroraAuroraMysqlClusterAuroraSecret131EB8833fdaad7efa858a3daf9490cf0a702aeb",
        },
        "TargetId": Object {
          "Ref": "AuroraMysqlClusterAurora2CC0C6CE",
        },
        "TargetType": "AWS::RDS::DBCluster",
      },
      "Type": "AWS::SecretsManager::SecretTargetAttachment",
    },
    "AuroraMysqlClusterAurorainstance138257E4A": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AutoMinorVersionUpgrade": false,
        "DBClusterIdentifier": Object {
          "Ref": "AuroraMysqlClusterAurora2CC0C6CE",
        },
        "DBInstanceClass": "db.t3.medium",
        "DBParameterGroupName": Object {
          "Ref": "AuroraMysqlClusterAuroraInstanceParameterGroupF88928D7",
        },
        "EnablePerformanceInsights": false,
        "Engine": "aurora-mysql",
        "PromotionTier": 0,
        "PubliclyAccessible": false,
        "Tags": Array [
          Object {
            "Key": "AWSBackup",
            "Value": "True",
          },
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::RDS::DBInstance",
      "UpdateReplacePolicy": "Delete",
    },
    "AuroraMysqlClusterAurorainstance24B4B09EE": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "AuroraMysqlClusterAurorainstance138257E4A",
      ],
      "Properties": Object {
        "AutoMinorVersionUpgrade": false,
        "DBClusterIdentifier": Object {
          "Ref": "AuroraMysqlClusterAurora2CC0C6CE",
        },
        "DBInstanceClass": "db.t3.medium",
        "DBParameterGroupName": Object {
          "Ref": "AuroraMysqlClusterAuroraInstanceParameterGroupF88928D7",
        },
        "EnablePerformanceInsights": false,
        "Engine": "aurora-mysql",
        "PromotionTier": 2,
        "PubliclyAccessible": false,
        "Tags": Array [
          Object {
            "Key": "AWSBackup",
            "Value": "True",
          },
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::RDS::DBInstance",
      "UpdateReplacePolicy": "Delete",
    },
    "AuroraMysqlClusterSecurityGroup3D4B3D2A": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "GroupDescription": "Aurora-SecurityGroup",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
        "VpcId": "vpc-12345",
      },
      "Type": "AWS::EC2::SecurityGroup",
      "UpdateReplacePolicy": "Delete",
    },
    "AuroraMysqlClusterSubnetGroup5BFB25C6": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "DBSubnetGroupDescription": "Aurora-SubnetGroup",
        "SubnetIds": Array [],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::RDS::DBSubnetGroup",
      "UpdateReplacePolicy": "Delete",
    },
    "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F": Object {
      "DependsOn": Array [
        "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": "cdk-hnb659fds-assets-123456789012-ap-northeast-1",
          "S3Key": "faa95a81ae7d7373f3e1f242268f904eb748d8d0fdd306e8a6fe515a1905a7d6.zip",
        },
        "Description": Object {
          "Fn::Join": Array [
            "",
            Array [
              "Lambda function for auto-deleting objects in ",
              Object {
                "Ref": "AuroraLogFirehoseFirehoseLogBucket8FB753CE",
              },
              " S3 bucket.",
            ],
          ],
        },
        "Handler": "index.handler",
        "MemorySize": 128,
        "Role": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
            "Arn",
          ],
        },
        "Runtime": "nodejs20.x",
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Sub": "arn:\${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "GEVANNIserviceDBAuroraAuroraMysqlClusterAuroraSecret131EB8833fdaad7efa858a3daf9490cf0a702aeb": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "Description": Object {
          "Fn::Join": Array [
            "",
            Array [
              "Generated by the CDK for stack: ",
              Object {
                "Ref": "AWS::StackName",
              },
            ],
          ],
        },
        "GenerateSecretString": Object {
          "ExcludeCharacters": " %+~\`#$&*()|[]{}:;<>?!'/@\\"\\\\",
          "GenerateStringKey": "password",
          "PasswordLength": 30,
          "SecretStringTemplate": "{\\"username\\":\\"dbUser\\"}",
        },
        "Tags": Array [
          Object {
            "Key": "AWSBackup",
            "Value": "True",
          },
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::SecretsManager::Secret",
      "UpdateReplacePolicy": "Delete",
    },
    "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aFD4BFC8A": Object {
      "DependsOn": Array [
        "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRoleDefaultPolicyADDA7DEB",
        "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRole9741ECFB",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": "cdk-hnb659fds-assets-123456789012-ap-northeast-1",
          "S3Key": "2819175352ad1ce0dae768e83fc328fb70fb5f10b4a8ff0ccbcb791f02b0716d.zip",
        },
        "Handler": "index.handler",
        "Role": Object {
          "Fn::GetAtt": Array [
            "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRole9741ECFB",
            "Arn",
          ],
        },
        "Runtime": "nodejs20.x",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRole9741ECFB": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
              ],
            ],
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRoleDefaultPolicyADDA7DEB": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "logs:PutRetentionPolicy",
                "logs:DeleteRetentionPolicy",
              ],
              "Effect": "Allow",
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRoleDefaultPolicyADDA7DEB",
        "Roles": Array [
          Object {
            "Ref": "LogRetentionaae0aa3c5b4d4f87b02d85b201efdd8aServiceRole9741ECFB",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;
