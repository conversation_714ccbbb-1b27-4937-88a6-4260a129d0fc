import * as cdk from 'aws-cdk-lib';
import { Template, Match } from 'aws-cdk-lib/assertions';
import { DbAuroraStack } from '../../lib/stack/db-aurora-stack';
import { IConfig } from 'params/interface';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as kms from 'aws-cdk-lib/aws-kms';
import * as rds from 'aws-cdk-lib/aws-rds';
import { ImportResourcesStack } from '../../lib/stack/import-resources-stack';
import { SecretStack } from '../../lib/stack/secret-stack';


const procEnv = {
    account: process.env.CDK_DEFAULT_ACCOUNT ?? '************', // Default account for testing
    region: process.env.CDK_DEFAULT_REGION ?? 'ap-northeast-1',
};

const pjCommonPrefix = 'GEVANNI-common';
const pjPrefix = 'GEVANNI-service';
let app: any;
let stack: any;

const envKey = 'dev01';
const serviceKey = 'sample01-dev01-ab';

const config: IConfig = require('../../params/' + envKey + '/' + serviceKey);

function getProcEnv() {
    if (config.Env.account && config.Env.region) {
        return {
            account: config.Env.account,
            region: config.Env.region,
        };
    } else {
        return procEnv;
    }
}
beforeEach(() => {
    app = new cdk.App();
    const envTagName = 'Environment';

    const secrets = new SecretStack(app, `${pjPrefix}-Secrets`, {
        prefix: pjPrefix,
        env: getProcEnv(),
    });

    // Import output from common CDK app
    const shareResources = new ImportResourcesStack(app, `${pjPrefix}-ImportResources`, {
        pjCommonPrefix,
        pjPrefix,
        env: getProcEnv(),
    });
    const vpc = shareResources.vpc;
    const appKey = shareResources.appKey;
    const alarmTopic = shareResources.alarmTopic;


    stack = new DbAuroraStack(app, `${pjPrefix}-DBAurora`, {
        pjPrefix,
        vpc: vpc,
        vpcSubnets: vpc.selectSubnets({
            subnetGroupName: 'Protected',
        }),
        // appServerSecurityGroup: ecs.app.backEcsApps.securityGroupForFargate,
        // bastionSecurityGroup: ecs.app.bastionApp.securityGroup,
        appServerSecurityGroup: undefined,
        bastionSecurityGroup: undefined,
        appKey: appKey,
        alarmTopic: alarmTopic,
        newrelicSecretArn: secrets.nrSecret.attrId,
        logRemovalPolicyParam: config.LogRemovalPolicyParam,
        codeBuildBucketLogLifecycleRules: config.s3BuildLogLifecycleRules,
        ...config.AuroraParam,
        env: getProcEnv(),
        terminationProtection: config.AuroraParam.stackTerminationProtection,
    });

    cdk.Tags.of(app).add(envTagName, envKey);
})

describe('DbAuroraStack', () => {
    test('Should match snapshot', () => {
        const template = Template.fromStack(stack);
        expect(template.toJSON()).toMatchSnapshot();
    });

    test('Should create an Aurora cluster', () => {
        const template = Template.fromStack(stack);
        template.hasResourceProperties('AWS::RDS::DBCluster', {
            Engine: 'aurora-mysql',
            DBClusterParameterGroupName: Match.anyValue(),
            MasterUsername: Match.anyValue(),
        });
    });


    test('Should create CloudWatch alarms for the Aurora cluster', () => {
        const template = Template.fromStack(stack);
        template.resourceCountIs('AWS::CloudWatch::Alarm', 1);
    });

    test('Should create a Firehose delivery stream for New Relic', () => {
        const template = Template.fromStack(stack);
        template.hasResourceProperties('AWS::KinesisFirehose::DeliveryStream', {
            DeliveryStreamType: 'DirectPut',
        });
    });

    test('Should create an S3 bucket for Firehose with lifecycle rules', () => {
        const template = Template.fromStack(stack);
        template.hasResourceProperties('AWS::S3::Bucket', {
            LifecycleConfiguration: {
                Rules: [
                    {
                        "NoncurrentVersionExpiration": {
                            "NewerNoncurrentVersions": 20,
                            "NoncurrentDays": 30,
                        },
                        "Status": "Enabled",
                    }
                ],
            },
        });
    });

    test('Should enable deletion protection if specified', () => {
        const template = Template.fromStack(stack);
        template.hasResourceProperties('AWS::RDS::DBCluster', {
            DeletionProtection: config.AuroraParam.stackTerminationProtection,
        });
    });

    // should be created 4 SubscriptionFilter for each log group
    test('Should create SubscriptionFilter for each log group', () => {
        const template = Template.fromStack(stack);
        template.resourceCountIs('AWS::Logs::SubscriptionFilter', 4);
        template.hasResourceProperties('AWS::Logs::SubscriptionFilter', {
            DestinationArn: {
                'Fn::GetAtt': [
                    Match.stringLikeRegexp('FirehoseStream'),
                    'Arn',
                ],
            },
            FilterPattern: '',
        });
    })

    // should enable aurora export log to cloudwatch
    test('Should enable Aurora export log to CloudWatch', () => {
        const template = Template.fromStack(stack);
        template.hasResourceProperties('AWS::RDS::DBCluster', {
            EnableCloudwatchLogsExports: [
                'error',
                'general',
                'slowquery',
                'audit',
            ],
        });
    });
    test('Should set backup retention days', () => {
        const template = Template.fromStack(stack);
        template.hasResourceProperties('AWS::RDS::DBCluster', {
            BackupRetentionPeriod: config.AuroraParam.backupRetentionDays,
        });
    });

    test('Should set backup preferred window', () => {
        const template = Template.fromStack(stack);
        template.hasResourceProperties('AWS::RDS::DBCluster', {
            PreferredBackupWindow: config.AuroraParam.backupPreferredWindow,
        });
    });

    test('Should set preferred maintenance window', () => {
        const template = Template.fromStack(stack);
        template.hasResourceProperties('AWS::RDS::DBCluster', {
            PreferredMaintenanceWindow: config.AuroraParam.preferredMaintenanceWindow,
        });
    });

    test('Should enable auto minor version upgrade', () => {
        const template = Template.fromStack(stack);
        template.hasResourceProperties('AWS::RDS::DBInstance', {
            AutoMinorVersionUpgrade: config.AuroraParam.autoMinorVersionUpgrade,
        });
    });

    // test stack should be create aurora postgres
    test('Should create Aurora PostgreSQL cluster when dbVersion is postgres engine', () => {
        const secrets = new SecretStack(app, `${pjPrefix}-Secrets-1`, {
            prefix: pjPrefix,
            env: getProcEnv(),
        });
    
        // Import output from common CDK app
        const shareResources = new ImportResourcesStack(app, `${pjPrefix}-ImportResources-1`, {
            pjCommonPrefix,
            pjPrefix,
            env: getProcEnv(),
        });
        const vpc = shareResources.vpc;
        const appKey = shareResources.appKey;
        const alarmTopic = shareResources.alarmTopic;

        const stackPostgres = new DbAuroraStack(app, `${pjPrefix}-DBAurora-postgress`, {
            pjPrefix,
            vpc: vpc,
            vpcSubnets: vpc.selectSubnets({
                subnetGroupName: 'Protected',
            }),
            // appServerSecurityGroup: ecs.app.backEcsApps.securityGroupForFargate,
            // bastionSecurityGroup: ecs.app.bastionApp.securityGroup,
            appServerSecurityGroup: undefined, // TODO: remove later
            bastionSecurityGroup: undefined, // TODO: remove later
            appKey: appKey,
            alarmTopic: alarmTopic,
            newrelicSecretArn: secrets.nrSecret.attrId,
            logRemovalPolicyParam: config.LogRemovalPolicyParam,
            codeBuildBucketLogLifecycleRules: config.s3BuildLogLifecycleRules,
            ...config.AuroraParam,
            dbVersion: rds.AuroraPostgresEngineVersion.VER_15_4,
            env: getProcEnv(),
            terminationProtection: config.AuroraParam.stackTerminationProtection,
        });

        const template = Template.fromStack(stackPostgres);
        template.hasResourceProperties('AWS::RDS::DBCluster', {
            Engine: 'aurora-postgresql',
        });
    });
    
});
