import * as cdk from 'aws-cdk-lib';
import { Template } from 'aws-cdk-lib/assertions';
import { IConfig } from '../params/interface';
import { WafAlbStack } from '../lib/stack/waf-alb-stack';
import { EcsAppStack } from '../lib/stack/ecs-app-stack';
import { ImportResourcesStack } from '../lib/stack/import-resources-stack';
import { MonitorStack } from '../lib/stack/monitor-stack';
import { RoleForAppTeamStack } from '../lib/stack/role-for-app-team-stack';
import { ElastiCacheStack } from '../lib/stack/elasticache-stack';
import { AcmStack } from '../lib/stack/acm-stack';
import { AlbAliasStack } from '../lib/stack/alb-aliasrecord-stack';
import { EfsStack } from '../lib/stack/efs-stack';
import { OpenSearchStack } from '../lib/stack/opensearch-stack';
import { SecretStack } from '../lib/stack/secret-stack';
import { NotifierStack } from '../lib/stack/notifier-stack';

// Account and Region on test
//  cdk.process.env.* returns undefined, and cdk.Stack.of(this).* returns ${Token[Region.4]} at test time.
//  In such case, RegionInfo.get(cdk.Stack.of(this).region) returns error and test will fail.
//  So we pass 'ap-northeast-1' as region code.
const procEnv = {
  account: process.env.CDK_DEFAULT_ACCOUNT,
  region: process.env.CDK_DEFAULT_REGION ?? 'ap-northeast-1',
};

const pjCommonPrefix = 'GEVANNI-common';
const pjPrefix = 'GEVANNI-service';
const app = new cdk.App();

const envKey = 'dev01';
const serviceKey = 'sample01-dev01-ab';

const config: IConfig = require('../params/' + envKey + '/' + serviceKey);

function getProcEnv() {
  if (config.Env.account && config.Env.region) {
    return {
      account: config.Env.account,
      region: config.Env.region,
    };
  } else {
    return procEnv;
  }
}

describe(`${pjPrefix} Guest Stacks`, () => {
  test('GuestAccount ECS App Stacks', () => {
    // Empty Secret Manager
    const secrets = new SecretStack(app, `${pjPrefix}-Secrets`, {
      prefix: pjPrefix,
      env: getProcEnv(),
    });
    // Share resources
    const shareResources = new ImportResourcesStack(app, `${pjCommonPrefix}-ImportResources`, {
      pjCommonPrefix,
      env: getProcEnv(),
    });

    const notifier = new NotifierStack(app, `${pjPrefix}-Notifier`, {
      pjPrefix,
      env: getProcEnv(),
      isCreateChatbot: config.NotifierParam.isCreateChatbot,
      notifyEmail: config.NotifierParam.monitoringNotifyEmail,
      workspaceId: config.NotifierParam.workspaceId,
      channelId: config.NotifierParam.channelIdMon,
    });

    const acm = new AcmStack(app, `${pjPrefix}-acm`, {
      ...config.ACMParam,
      prefix: pjPrefix,
      env: getProcEnv(),
      providerServiceToken: shareResources.ACMCustomeResourceProviderServiceToken,
    });

    const ecs = new EcsAppStack(app, `${pjPrefix}-ECS`, {
      serviceKey: serviceKey,
      vpc: shareResources.vpc,
      appKey: shareResources.appKey,
      alarmTopic: shareResources.alarmTopic,
      alarmTopicForAppTeam: notifier.alarmTopic,
      serviceId: serviceKey,
      pjCommonPrefix,
      prefix: pjPrefix,
      EcsFrontTask: config.EcsFrontTask,
      EcsBackTask: config.EcsBackTask,
      ecsFrontTaskRole: config.EcsFrontTaskRole,
      ecsBackTaskRole: config.EcsBackTaskRole,
      ecsBastionTasks: true,
      bastionTaskRole: config.BastionTaskRole,
      AlbAcm: config.ACMParam,
      env: getProcEnv(),
      crossRegionReferences: true,
      accessLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
      appLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
      buildLogBucketLifecycleRules: config.s3BuildLogLifecycleRules,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      otherRemovalPolicyParam: config.OtherRemovalPolicyParam,
      pipelineSourceBucketLifeCycleRules: config.pipelineSourceBucketLifeCycleRules,
      fireLensImageBase: shareResources.fluentbitImageUri,
      newrelicSecretArn: secrets.nrSecret.attrId,
      newrelicLayer: shareResources.newrelicLayer,
      EnvName: config.Env.envName,
      namespace: shareResources.namespace,
      namespaceArn: shareResources.namespaceArn,
      deployResource: config.DeployResource.DeployResource,
    });

    const alias = new AlbAliasStack(app, `${pjPrefix}-ALbAlias`, {
      AliasDomainName: config.ACMParam.AcmDomainName,
      Route53HostZoneId: config.ACMParam.HostZoneId,
      AssumeRoleArn: config.ACMParam.AssumeRoleArn,
      appAlb: ecs.app.frontAlb.appAlb,
      env: getProcEnv(),
      crossRegionReferences: true,
      providerServiceToken: shareResources.AliasCustomeResourceProviderServiceToken,
    });

    const wafAlb = new WafAlbStack(app, `${pjPrefix}-WafAlb`, {
      scope: 'REGIONAL',
      associations: [ecs.app.frontAlb.appAlb.loadBalancerArn, ecs.app.frontAlb.appAlb.loadBalancerArn],
      env: getProcEnv(),
      crossRegionReferences: true,
      ...config.WafAlbParam,
    });

    // Monitor stack: dashboard
    const monitor = new MonitorStack(app, `${pjPrefix}-MonitorStack`, {
      pjPrefix: `${pjPrefix}`,
      dashboardName: `${pjPrefix}-ECSApp`,
      albFullName: ecs.app.frontAlb.appAlb.loadBalancerFullName,
      appTargetGroupName: ecs.app.frontAlb.AlbTg.lbForAppTargetGroup.targetGroupName,
      isExistAlbTgUnHealthyHostCountAlarm: ecs.app.frontAlb.AlbTg.albTgUnHealthyHostCountAlarm ? true : false,
      ecsClusterName: ecs.app.ecsCommon.ecsCluster.clusterName,
      ecsAlbServiceName: ecs.app.frontEcsApps.ecsServiceName,
      ecsInternalServiceName: ecs.app.backEcsApps.ecsServiceName,
      // AutoScaleはCDK外で管理のため、固定値を修正要で設定
      ecsScaleOnRequestCount: 50,
      ecsTargetUtilizationPercent: 10000,
      env: getProcEnv(),
      deployResource: config.DeployResource.DeployResource,
    });

    const roleForAppTeam = new RoleForAppTeamStack(app, `${pjPrefix}-RoleForAppTeam`, {
      pjPrefix,
      pjCommonPrefix,
      ecsClusterName: ecs.app.ecsCommon.ecsCluster.clusterName,
      ...config.RoleForAppTeamParam,
      taskDefName: ecs.app.bastionApp.taskDef.family,
      frontsecretArn: ecs.app.frontEcsApps.secret.secretArn,
      backsecretArn: ecs.app.backEcsApps.secret.secretArn,
      frontAppName: ecs.app.frontEcsApps.appName,
      backAppName: ecs.app.backEcsApps.appName,
      frontSourceBucketArn: ecs.app.frontEcsAppPipeline.sourceBucket.bucketArn,
      backSourceBucketArn: ecs.app.backEcsAppPipeline.sourceBucket.bucketArn,
      bastionServiceTaskRoleArn: ecs.app.bastionApp.ecsTaskServiceTaskRole.roleArn,
      env: getProcEnv(),
      deployResource: config.DeployResource.DeployResource,
    });

    const opensearch = new OpenSearchStack(app, `${pjPrefix}-Opensearch`, {
      pjPrefix,
      myVpc: shareResources.vpc,
      backendServerSecurityGroup: ecs.app.backEcsApps.securityGroupForFargate,
      frontServerSecurityGroup: ecs.app.frontEcsApps.securityGroupForFargate,
      bastionSecurityGroup: ecs.app.bastionApp.securityGroup,
      env: getProcEnv(),
    });

    if (config.ElastiCacheParam) {
      const elasticache = new ElastiCacheStack(app, `${pjPrefix}-ElastiCache`, {
        pjPrefix,
        elastiCacheType: config.ElastiCacheTypeParam,
        myVpc: shareResources.vpc,
        appKey: shareResources.appKey,
        alarmTopic: shareResources.alarmTopic,
        backendServerSecurityGroup: ecs.app.backEcsApps.securityGroupForFargate,
        frontServerSecurityGroup: ecs.app.frontEcsApps.securityGroupForFargate,
        bastionSecurityGroup: ecs.app.bastionApp.securityGroup,
        ElastiCacheParam: config.ElastiCacheParam,
        env: getProcEnv(),
        logRemovalPolicy: config.LogRemovalPolicyParam?.removalPolicy,
      });
      expect(Template.fromStack(elasticache)).toMatchSnapshot();
    }

    if (config.EfsParam) {
      const efs = new EfsStack(app, `${pjPrefix}-Efs`, {
        pjPrefix,
        vpc: shareResources.vpc,
        appKey: shareResources.appKey,
        backendServerSecurityGroup: ecs.app.backEcsApps.securityGroupForFargate,
        frontServerSecurityGroup: ecs.app.frontEcsApps.securityGroupForFargate,
        ...config.EfsParam,
        env: getProcEnv(),
      });
    }

    // Tagging "Environment" tag to all resources in this app
    const envTagName = 'Environment';
    cdk.Tags.of(app).add(envTagName, envKey);

    // test with snapshot
    expect(Template.fromStack(shareResources)).toMatchSnapshot();
    expect(Template.fromStack(ecs)).toMatchSnapshot();
    expect(Template.fromStack(wafAlb)).toMatchSnapshot();
    expect(Template.fromStack(monitor)).toMatchSnapshot();
    expect(Template.fromStack(roleForAppTeam)).toMatchSnapshot();
    expect(Template.fromStack(acm)).toMatchSnapshot();
    expect(Template.fromStack(alias)).toMatchSnapshot();
    expect(Template.fromStack(opensearch)).toMatchSnapshot();
  });
});
