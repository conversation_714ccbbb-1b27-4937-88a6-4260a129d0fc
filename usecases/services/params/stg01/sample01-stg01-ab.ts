import * as cdk from 'aws-cdk-lib';
import * as inf from '../interface';
import * as appscaling from 'aws-cdk-lib/aws-applicationautoscaling';
import { aws_s3 as s3 } from 'aws-cdk-lib';
import * as efs from 'aws-cdk-lib/aws-efs';
import * as events from 'aws-cdk-lib/aws-events';
import * as rds from 'aws-cdk-lib/aws-rds';
import * as ec2 from 'aws-cdk-lib/aws-ec2';

export const DeployResource: inf.IDeployResource = {
  DeployResource: 'ALL',
  DeployControl: 'ROLLING',
};

export const EcspressoFrontDeployPipelineParam: inf.ICrossAccountDeployPipelineParam = {
  crossAccessAccountId: '',
  crossAccessEnvName: 'Stg',
  crossAccessPjPrefix: 'gevanni-cf-sample',
  // To remove the old Rolling pipeline deployed to the Gevanni account, comment in the following line and run cdk deploy.
  // deleteOldPipeline: true,
};

export const EcspressoBackDeployPipelineParam: inf.ICrossAccountDeployPipelineParam = {
  crossAccessAccountId: '',
  crossAccessEnvName: 'Stg',
  crossAccessPjPrefix: 'gevanni-cf-sample',
  // To remove the old Rolling pipeline deployed to the Gevanni account, comment in the following line and run cdk deploy.
  // deleteOldPipeline: true,
};

export const BlueGreenFrontDeployPipelineParam: inf.IBlueGreenPipelineParam = {
  crossAccessAccountId: '',
  crossAccessEnvName: 'Stg',
  crossAccessPjPrefix: 'gevanni-cf-sample',
  // CodeDeploy をデプロイするには、ECS Servce をデプロイ後に isEcsServiceDeployed を true に書き換えてデプロイする。
  isEcsServiceDeployed: false,
};

export const BlueGreenBackDeployPipelineParam: inf.IBlueGreenPipelineParam = {
  crossAccessAccountId: '',
  crossAccessEnvName: 'Stg',
  crossAccessPjPrefix: 'gevanni-cf-sample',
  // CodeDeploy をデプロイするには、ECS Servce をデプロイ後に isEcsServiceDeployed を true に書き換えてデプロイする。
  isEcsServiceDeployed: false,
};

export const SecretParam: inf.SecretParam = {
  stackTerminationProtection: false,
};
export const ShareResourcesParam: inf.ShareResourcesParam = {
  stackTerminationProtection: false,
};

export const ACMParam: inf.IACMParam = {
  AcmDomainName: 'test01.stg.gevanni.mynv.jp',
  HostZoneId: 'Z0931887TOBWM1VPZUSY',
  AssumeRoleArn: 'arn:aws:iam::************:role/LambdaCrossAccountRole',
  stackTerminationProtection: false,
};

export const WafAlbParam: inf.IWafParam = {
  allowIPList: ['***************/25'],
  preSharedKeys: ['Gevanni-preSharedKey-1', 'Gevanni-preSharedKey-2'],
  defaultAction: { block: {} },
  stackTerminationProtection: false,
};

// インターネットアクセス可能なECSサービス、タスクのパラメータを設定する
// ・appName：ECSサービスやタスク名に使用する名前
// ・portNumber：コンテナがリクエストを受け付けるポート（HTTPであれば80）
export const EcsFrontTask: inf.IEcsAlbParam = {
  appName: 'EcsApp',
  portNumber: 80,
  enableAlarm: false,
  appLanguage: 'ruby',
};

export const EcsFrontTaskRole: inf.IEcsTaskRoleParam = {
  policyStatements: [],
  managedPolicy: [],
  crossAccountRoleArns: [],
};

// VPC内でService Connectを経由して接続するECSサービス、タスクのパラメータを設定する
// ・appName：ECSサービスやタスク名に使用する名前
// ・portNumber：コンテナがリクエストを受け付けるポート（HTTPであれば80）
export const EcsBackTask: inf.IEcsParam = {
  appName: 'EcsBackend',
  portNumber: 8080,
  enableAlarm: false,
  AllowSGOtherECSApp: [
    {
      serviceId: 'sample02-dev01-cd',
      appName: 'EcsApp',
    },
  ], // App name of ecs connect ecs backend
  appLanguage: 'ruby',
};

export const EcsBackTaskRole: inf.IEcsTaskRoleParam = {
  policyStatements: [],
  managedPolicy: [],
  crossAccountRoleArns: [],
};

// インターネットアクセス可能なECSサービス、タスクのパラメータを設定する
// ・appName：ECSサービスやタスク名に使用する名前
// ・portNumber：コンテナがリクエストを受け付けるポート（HTTPであれば80）
export const EcsFrontBgTask: inf.IEcsAlbParam = {
  appName: 'EcsAppBg',
  portNumber: 80,
  enableAlarm: false,
  appLanguage: 'ruby',
};

export const EcsFrontBgTaskRole: inf.IEcsTaskRoleParam = {
  policyStatements: [],
  managedPolicy: [],
  crossAccountRoleArns: [],
};

// VPC内でService Connectを経由して接続するECSサービス、タスクのパラメータを設定する
// ・appName：ECSサービスやタスク名に使用する名前
// ・portNumber：コンテナがリクエストを受け付けるポート（HTTPであれば80）
export const EcsBackBgTask: inf.IEcsParam = {
  appName: 'EcsBackendBg',
  portNumber: 8080,
  enableAlarm: false,
  AllowSGOtherECSApp: [
    {
      serviceId: 'sample02-dev01-cd',
      appName: 'EcsAppBg',
    },
  ], // App name of ecs connect ecs backend
  appLanguage: 'ruby',
};

export const EcsBackBgTaskRole: inf.IEcsTaskRoleParam = {
  policyStatements: [],
  managedPolicy: [],
  crossAccountRoleArns: [],
};

export const BastionTaskRole: inf.IBastionTaskRoleParam = {
  policyStatements: [],
  managedPolicy: [],
};

export const Env: inf.IEnv = {
  prefix: 'GEVANNI',
  envName: 'Stg01',
  account: '************',
  region: 'ap-northeast-1',
};

export const NotifierParam: inf.INotifierParam = {
  isCreateChatbot: true,
  monitoringNotifyEmail: '<EMAIL>',
  slackChannelConfiguration: {
    workspaceId: 'T8XXXXXXX',
    channelId: 'C01YYYYYYYY',
  },
  teamsChannelConfiguration: {
    channelName: 'sample-channel',
    teamId: '********-1234-1234-1234-********9012',
    tenantId: '********-4321-4321-4321-************',
    teamsChannelId: '19%3A********90abcdef********90abcdef%40thread.tacv2',
  },
  stackTerminationProtection: false,
};

export const ElastiCacheParam: inf.IElastiCacheParam = {
  ElastiCacheSelfDesignedParam: {
    engine: 'valkey',
    engineVersion: '8.0',
    numNodeGroups: 3,
    replicasPerNodeGroup: 2,
    minCapacity: 3,
    maxCapacity: 12,
    targetValueToScale: 70,
    predefinedMetricToScale: appscaling.PredefinedMetric.ELASTICACHE_PRIMARY_ENGINE_CPU_UTILIZATION,
    enableAutoScale: false,
    cacheNodeTypeEnableAutoScale: 'cache.m5.large',
    cacheNodeTypeDisableAutoScale: 'cache.t3.small',
    elastiCacheCustomParam: {
      cacheParameterGroupFamily: 'valkey8',
      description: 'CustomParameterGroupForValkey',
      properties: {
        'cluster-enabled': 'yes',
      },
    },
    replicationGroupId: 'GEVANNI-sample01-stg01-ab-Valkey', // max length of replicationGroupId is 40 character
    preferredMaintenanceWindow: 'wed:19:00-wed:20:00',
    autoMinorVersionUpgrade: false,
  },
  ElastiCacheServerlessParam: {
    engine: 'valkey',
    engineVersion: '8',
    dataStorageMaximum: 123, // GB
    dataStorageMinimum: 0, // GB -- When this value is set, it will be charged even if you do not use it.
    ecpuPerSecondMaximum: 1000,
    ecpuPerSecondMinimum: 0, //When this value is set, it will be charged even if you do not use it.
    serverlessCacheName: 'sample01-stg01-ab-ValkeyServerless', // max length of serverlessCacheName is 40 character
  },
  stackTerminationProtection: false,
};

export const ElastiCacheTypeParam: inf.IElastiCacheTypeParam = {
  elastiCacheType: 'SERVERLESS',
};

export const ElastiCacheCrossServiceParam: inf.IElastiCacheCrossServiceParam = {
  allowSgCrossService: [
    {
      ssmParameterPath: '/sample02-dev01-cd/AnotherApp01', // SSM parameter path of another App01 SG
    },
    {
      ssmParameterPath: '/sample02-dev01-cd/AnotherApp02', // SSM parameter path of another App02 SG
    },
  ],
};

export const OidcParam: inf.IOidcParam = {
  OrganizationName: 'OrganizationName',
  RepositoryNames: {
    FrontEcspressoRepositoryName: 'FrontEcspressoRepositoryName',
    BackEcspressoRepositoryName: 'BackEcspressoRepositoryName',
    FrontBlueGreenRepositoryName: 'FrontBlueGreenRepositoryName',
    BackBlueGreenRepositoryName: 'BackBlueGreenRepositoryName',
    BatchAppRepositoryName: 'BatchAppRepositoryName',
  },
  stackTerminationProtection: false,
};

export const RoleForAppTeamParam: inf.IRoleForAppTeamParam = {
  allowUsers: ['<EMAIL>', '<EMAIL>'],
  hasRoleForVendor: false,
  vendorBastionAccountId: '************',
  stackTerminationProtection: false,
};

export const s3AuditLogLifecycleRules: s3.LifecycleRule[] = [
  {
    expiration: cdk.Duration.days(1825), //Store for 5 years
  },
  {
    abortIncompleteMultipartUploadAfter: cdk.Duration.days(30), //delete incomplete multipart uploads after 30 days
  },
];
export const LogRemovalPolicyParam = inf.DestroyRemovalPolicyParam;
export const OtherRemovalPolicyParam = inf.DestroyRemovalPolicyParam;

export const EfsParam: inf.IEfsParam = {
  lifecyclePolicy: efs.LifecyclePolicy.AFTER_7_DAYS,
  outOfInfrequentAccessPolicy: efs.OutOfInfrequentAccessPolicy.AFTER_1_ACCESS,
  throughputMode: efs.ThroughputMode.ELASTIC,
  performanceMode: efs.PerformanceMode.GENERAL_PURPOSE,
  removalPolicy: cdk.RemovalPolicy.DESTROY,
  // Please change isUseSharedTransferFamily to true after creating the EFS stack
  isUseSharedTransferFamily: false,
  // Please change the information accordingly
  sharedTransferFamilyAccountID: '************',
  hasBackup: true,
  backupParams: {
    // 17:00 UTC ~ 02:00 JST
    schedule: events.Schedule.cron({
      minute: '0',
      hour: '17',
      day: '*',
      month: '*',
      year: '*',
    }),
    retentionPeriod: cdk.Duration.days(7),
    removalPolicy: cdk.RemovalPolicy.DESTROY,
  },
  stackTerminationProtection: false,
};

export const pipelineSourceBucketLifeCycleRules: s3.LifecycleRule[] = [
  {
    noncurrentVersionExpiration: cdk.Duration.days(30),
    noncurrentVersionsToRetain: 20,
  },
];

export const s3BuildLogLifecycleRules: s3.LifecycleRule[] = [
  {
    noncurrentVersionExpiration: cdk.Duration.days(30),
    noncurrentVersionsToRetain: 20,
  },
];

export const openSearchParam: inf.IOpenSearchParam = {
  isCreate: true,
  stackTerminationProtection: false,
};

export const BatchParam: inf.IBatchParam[] = [
  {
    shouldConnectAppContainer: true,
    batchName: 'BatchApp',
    enableApiTrigger: true,
    taskParam: [
      {
        taskName: 'BatchTask',
        containerNames: ['BatchContainer'],
      },
    ],
    appLanguage: 'ruby',
    stackTerminationProtection: false,
  },
];

export const EcsAppParam: inf.EcsAppParam = {
  stackTerminationProtection: false,
};

export const MonitorParam: inf.MonitorParam = {
  stackTerminationProtection: false,
};

export const AuroraParam: inf.IAuroraParam = {
  isCreate: false,
  dbName: 'mydbname',
  dbUser: 'dbUser',
  dbVersion: rds.AuroraMysqlEngineVersion.VER_3_04_3,
  // dbVersion: rds.AuroraPostgresEngineVersion.VER_15_4,
  writerInstanceType: ec2.InstanceType.of(ec2.InstanceClass.T3, ec2.InstanceSize.MEDIUM),
  readers: [
    {
      instanceType: ec2.InstanceType.of(ec2.InstanceClass.T3, ec2.InstanceSize.MEDIUM),
      enablePerformanceInsights: false,
      autoMinorVersionUpgrade: false,
    },
  ],
  enablePerformanceInsights: false, // Change to false when cdk import
  auroraMinAcu: 2,
  auroraMaxAcu: 16,
  removalPolicy: cdk.RemovalPolicy.DESTROY,
  //ParameterGroupforMySQL
  clusterParameters: {
    time_zone: 'Asia/Tokyo',
    character_set_client: 'utf8mb4',
    character_set_connection: 'utf8mb4',
    character_set_database: 'utf8mb4',
    character_set_results: 'utf8mb4',
    character_set_server: 'utf8mb4',
    init_connect: 'SET NAMES utf8mb4',
    binlog_format: 'ROW',
  },
  instanceParameters: {
    slow_query_log: '1',
    long_query_time: '10',
  },
  //ParameterGroupforPostgreSQL
  // clusterParameters: {
  //   timezone: 'Asia/Tokyo',
  //   client_encoding: 'UTF8',
  // },
  // instanceParameters: {
  //   //「.」があるKey値はプロパティ扱いになるため「'」で括る
  //   shared_preload_libraries: 'auto_explain,pg_stat_statements,pg_hint_plan,pgaudit',
  //   log_statement: 'ddl',
  //   log_connections: '1',
  //   log_disconnections: '1',
  //   log_lock_waits: '1',
  //   log_min_duration_statement: '5000',
  //   'auto_explain.log_min_duration': '5000',
  //   'auto_explain.log_verbose': '1',
  //   log_rotation_age: '1440',
  //   log_rotation_size: '102400',
  //   'rds.log_retention_period': '10080',
  //   random_page_cost: '1',
  //   track_activity_query_size: '16384',
  //   idle_in_transaction_session_timeout: '7200000',
  //   statement_timeout: '7200000',
  //   search_path: '"$user",public',
  // },
  backupRetentionDays: 1,
  // 17:00-18:00 UTC ~ 02:00-03:00 JST
  backupPreferredWindow: '17:00-18:00',
  // Wed:19:00-Wed:20:00 UTC ~ Thu:04:00-Thu:05:00 JST
  preferredMaintenanceWindow: 'Wed:19:00-Wed:20:00',
  autoMinorVersionUpgrade: false,
  deletionProtection: false,
  stackTerminationProtection: false,
  enableRdsProxy: false, // Set to true to enable RDS Proxy for improved performance and connection pooling
  rdsProxyConfig: {
    maxConnectionsPercent: 100,
    maxIdleConnectionsPercent: 50,
    requireTLS: true,
    idleClientTimeout: 1800, // seconds (30 minutes)
  },
};
