import * as cdk from 'aws-cdk-lib';
import * as wafv2 from 'aws-cdk-lib/aws-wafv2';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import * as elasticache from 'aws-cdk-lib/aws-elasticache';
import * as appscaling from 'aws-cdk-lib/aws-applicationautoscaling';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as efs from 'aws-cdk-lib/aws-efs';
import * as events from 'aws-cdk-lib/aws-events';
import * as codedeploy from 'aws-cdk-lib/aws-codedeploy';
import { aws_s3 as s3 } from 'aws-cdk-lib';
import * as rds from 'aws-cdk-lib/aws-rds';
import * as ec2 from 'aws-cdk-lib/aws-ec2';

export interface IACMParam {
  AcmDomainName: string;
  HostZoneId: string;
  AssumeRoleArn: string;
  stackTerminationProtection: boolean;
}
export interface SecretParam {
  stackTerminationProtection: boolean;
}

export interface ShareResourcesParam {
  stackTerminationProtection: boolean;
}

export interface EcsAppParam {
  stackTerminationProtection: boolean;
}

export interface MonitorParam {
  stackTerminationProtection: boolean;
}

export interface IWafParam {
  defaultAction?: wafv2.CfnWebACL.DefaultActionProperty;
  ruleAction_IPsetRuleSet?: wafv2.CfnWebACL.RuleActionProperty;
  allowIPList?: string[];
  preSharedKeys?: string[];
  stackTerminationProtection: boolean;
}

export interface IEcsAlbParam {
  appName: string;
  portNumber: number;
  lifecycleRules?: ecr.LifecycleRule[];
  enableAlarm: boolean;
  appLanguage?: 'ruby' | 'python' | 'go' | 'json';
}
export interface IEcsParam {
  appName: string;
  portNumber: number;
  lifecycleRules?: ecr.LifecycleRule[];
  enableAlarm: boolean;
  AllowSGOtherECSApp?: [IAllowSGOtherECSApp];
  appLanguage?: 'ruby' | 'python' | 'go' | 'json';
}

export interface IAllowSGOtherECSApp {
  serviceId: string;
  appName: string;
}

export interface ITaskRoleParam {
  policyStatements: iam.PolicyStatement[];
  managedPolicy: string[];
}

export type IEcsTaskRoleParam = ITaskRoleParam & {
  crossAccountRoleArns: string[];
};

export type IBastionTaskRoleParam = ITaskRoleParam;

export interface IEnv {
  prefix: string;
  envName: string;
  account?: string;
  region?: string;
}

export interface INotifierParam {
  isCreateChatbot: boolean;
  slackChannelConfiguration?: {
    workspaceId: string;
    channelId: string;
  };
  teamsChannelConfiguration?: {
    channelName: string;
    teamId: string;
    tenantId: string;
    teamsChannelId: string;
  };
  monitoringNotifyEmail?: string;
  stackTerminationProtection: boolean;
}

export interface IOidcParam {
  OrganizationName: string;
  RepositoryNames: Record<string, string>;
  stackTerminationProtection: boolean;
}

export interface IRoleForAppTeamParam {
  allowUsers: string[];
  hasRoleForVendor?: boolean;
  vendorBastionAccountId?: string;
  stackTerminationProtection: boolean;
}

export interface IEfsParam {
  lifecyclePolicy?: efs.LifecyclePolicy;
  outOfInfrequentAccessPolicy?: efs.OutOfInfrequentAccessPolicy;
  throughputMode: efs.ThroughputMode;
  performanceMode: efs.PerformanceMode;
  removalPolicy?: cdk.RemovalPolicy;
  isUseSharedTransferFamily?: boolean;
  sharedTransferFamilyAccountID?: string;
  hasBackup?: boolean;
  backupParams?: {
    schedule: events.Schedule;
    retentionPeriod: cdk.Duration;
    removalPolicy?: cdk.RemovalPolicy;
  };
  stackTerminationProtection: boolean;
}

export interface IBatchParam {
  shouldConnectAppContainer: boolean;
  enableApiTrigger?: boolean;
  batchName: string;
  taskParam: {
    taskName: string;
    containerNames: string[];
    crossAccountRoleArns?: string[];
  }[];
  appLanguage?: 'ruby' | 'python' | 'go' | 'json';
  stackTerminationProtection: boolean;
}

export interface IElastiCacheParam {
  ElastiCacheSelfDesignedParam: {
    engine: 'redis' | 'valkey';
    engineVersion: string;
    numNodeGroups: number;
    replicasPerNodeGroup: number;
    minCapacity: number;
    maxCapacity: number;
    targetValueToScale: number;
    predefinedMetricToScale:
      | appscaling.PredefinedMetric.ELASTICACHE_PRIMARY_ENGINE_CPU_UTILIZATION
      | appscaling.PredefinedMetric.ELASTICACHE_REPLICA_ENGINE_CPU_UTILIZATION
      | appscaling.PredefinedMetric.ELASTICACHE_DATABASE_MEMORY_USAGE_COUNTED_FOR_EVICT_PERCENTAGE;
    enableAutoScale: boolean;
    cacheNodeTypeEnableAutoScale: string;
    cacheNodeTypeDisableAutoScale: string;
    elastiCacheCustomParam: elasticache.CfnParameterGroupProps;
    replicationGroupId: string;
    preferredMaintenanceWindow: string;
    autoMinorVersionUpgrade: boolean;
  };
  ElastiCacheServerlessParam: {
    engine: 'redis' | 'valkey';
    engineVersion: string;
    dataStorageMaximum: number;
    dataStorageMinimum: number;
    ecpuPerSecondMaximum: number;
    ecpuPerSecondMinimum: number;
    serverlessCacheName: string;
  };
  stackTerminationProtection: boolean;
}

export interface IElastiCacheTypeParam {
  elastiCacheType: 'SELFDESIGNED' | 'SERVERLESS';
}

export interface IAllowSgCrossService {
  ssmParameterPath: string;
}

export interface IElastiCacheCrossServiceParam {
  allowSgCrossService?: IAllowSgCrossService[];
}

export interface IOpenSearchParam {
  isCreate: boolean;
  stackTerminationProtection: boolean;
}

export interface IRemovalPolicyParam {
  removalPolicy: cdk.RemovalPolicy;
  autoDeleteObjects: boolean;
  emptyOnDelete: boolean;
}

export const DestroyRemovalPolicyParam: IRemovalPolicyParam = {
  removalPolicy: cdk.RemovalPolicy.DESTROY,
  autoDeleteObjects: true,
  emptyOnDelete: true,
};

export const RetainRemovalPolicyParam: IRemovalPolicyParam = {
  removalPolicy: cdk.RemovalPolicy.RETAIN,
  autoDeleteObjects: false,
  emptyOnDelete: false,
};

export interface IDeployResource {
  DeployResource: 'ALL' | 'FRONTEND' | 'BACKEND';
  DeployControl: 'ALL' | 'ROLLING' | 'BLUE_GREEN';
}

export interface ICrossAccountDeployPipelineParam {
  /**
   * Account ID to allow cross-account access.
   */
  crossAccessAccountId: string;
  /**
   * Environment name of the account for which you want to allow cross-accounting.
   * @example Dev
   */
  crossAccessEnvName: string;
  /**
   * project prefix of the account for which you want to allow cross-accounting.
   */
  crossAccessPjPrefix: string;
  /**
   * Event bus ARN to which the source bucket's events will be notified
   */
  eventBusArn?: string;
  /**
   * Flag to remove the old rolling pipeline deployed in the hogehoge account.
   */
  deleteOldPipeline?: boolean;
}

export interface IBlueGreenPipelineParam extends ICrossAccountDeployPipelineParam {
  /**
   * ECS Deploy Configuration.
   *
   * @default cdk.aws_codedeploy.EcsDeploymentConfig.ALL_AT_ONCE
   */
  deploymentConfig?: codedeploy.IEcsDeploymentConfig;
  /**
   * A flag indicating that the ECS Service has been deployed in advance of CodeDeploy.
   */
  isEcsServiceDeployed: boolean;
}

export interface IAuroraParam {
  isCreate: boolean;
  dbName: string;
  dbUser: string;
  dbVersion: rds.AuroraPostgresEngineVersion | rds.AuroraMysqlEngineVersion;
  writerInstanceType: ec2.InstanceType;
  /**
   * List of instance types for reader instances.
   * The number of reader instances will be determined by the array length.
   * Leave empty array [] or omit this property for Writer-only cluster.
   *
   * @example
   * // Writer-only cluster
   * readers: []
   *
   * // Multiple readers with different configurations
   * readers: [
   *   {
   *     instanceType: ec2.InstanceType.of(ec2.InstanceClass.T3, ec2.InstanceSize.MEDIUM),
   *     enablePerformanceInsights: true,
   *     autoMinorVersionUpgrade: false
   *   },
   *   {
   *     instanceType: ec2.InstanceType.of(ec2.InstanceClass.R6G, ec2.InstanceSize.LARGE),
   *     enablePerformanceInsights: false,
   *     autoMinorVersionUpgrade: true
   *   }
   * ]
   */
  readers?: Array<{
    instanceType: ec2.InstanceType;
    enablePerformanceInsights?: boolean;
    autoMinorVersionUpgrade?: boolean;
  }>;
  enablePerformanceInsights: boolean;
  removalPolicy: cdk.RemovalPolicy;
  auroraMinAcu: number;
  auroraMaxAcu: number;
  clusterParameters?: Record<string, string>;
  instanceParameters?: Record<string, string>;
  backupRetentionDays?: number;
  backupPreferredWindow?: string;
  preferredMaintenanceWindow?: string;
  autoMinorVersionUpgrade?: boolean;
  deletionProtection?: boolean;
  stackTerminationProtection?: boolean;
  /**
   * Whether to enable RDS Proxy for the Aurora cluster.
   * RDS Proxy provides connection pooling and improves scalability.
   *
   * @default false
   */
  enableRdsProxy?: boolean;
  /**
   * RDS Proxy configuration options.
   * Only used when enableRdsProxy is true.
   */
  rdsProxyConfig?: {
    /**
     * The maximum number of connections that the proxy can open to the database.
     *
     * @default 100
     */
    maxConnectionsPercent?: number;
    /**
     * The maximum percentage of idle connections allowed through the proxy.
     *
     * @default 50 (%)
     */
    maxIdleConnectionsPercent?: number;
    /**
     * Whether to require TLS for connections to the proxy.
     *
     * @default true
     */
    requireTLS?: boolean;
    /**
     * The amount of time for a connection to be idle before the proxy disconnects it.
     *
     * @default 1800 seconds (30 minutes)
     */
    idleClientTimeout?: number;
  };
}

export interface IConfig {
  ElastiCacheParam?: IElastiCacheParam;
  ACMParam: IACMParam;
  WafAlbParam: IWafParam;
  EcsFrontTask: IEcsAlbParam;
  EcsBackTask: IEcsParam;
  EcsFrontTaskRole: IEcsTaskRoleParam;
  EcsBackTaskRole: IEcsTaskRoleParam;
  EcsFrontBgTask?: IEcsAlbParam;
  EcsBackBgTask?: IEcsParam;
  EcsFrontBgTaskRole?: IEcsTaskRoleParam;
  EcsBackBgTaskRole?: IEcsTaskRoleParam;
  BastionTaskRole?: IBastionTaskRoleParam;
  Env: IEnv;
  NotifierParam: INotifierParam;
  RoleForAppTeamParam: IRoleForAppTeamParam;
  s3AuditLogLifecycleRules: s3.LifecycleRule[];
  s3BuildLogLifecycleRules: s3.LifecycleRule[];
  pipelineSourceBucketLifeCycleRules: s3.LifecycleRule[];
  BatchParam?: IBatchParam[];
  OidcParam: IOidcParam;
  LogRemovalPolicyParam?: IRemovalPolicyParam; // For LogGroup and S3 for log
  OtherRemovalPolicyParam?: IRemovalPolicyParam; // For ECR, Pipeline, etc.
  EfsParam?: IEfsParam;
  ElastiCacheTypeParam: IElastiCacheTypeParam;
  ElastiCacheCrossServiceParam: IElastiCacheCrossServiceParam;
  openSearchParam: IOpenSearchParam;
  DeployResource: IDeployResource;
  EcspressoFrontDeployPipelineParam?: ICrossAccountDeployPipelineParam;
  EcspressoBackDeployPipelineParam?: ICrossAccountDeployPipelineParam;
  BlueGreenFrontDeployPipelineParam?: IBlueGreenPipelineParam;
  BlueGreenBackDeployPipelineParam?: IBlueGreenPipelineParam;
  AuroraParam: IAuroraParam;
  SecretParam: SecretParam;
  ShareResourcesParam: ShareResourcesParam;
  EcsAppParam: EcsAppParam;
  MonitorParam: MonitorParam;
}
