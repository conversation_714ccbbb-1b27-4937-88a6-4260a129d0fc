import * as cdk from 'aws-cdk-lib';
import * as inf from '../interface';
import * as appscaling from 'aws-cdk-lib/aws-applicationautoscaling';
import { aws_s3 as s3 } from 'aws-cdk-lib';
import * as efs from 'aws-cdk-lib/aws-efs';
import * as events from 'aws-cdk-lib/aws-events';

export const DeployResource: inf.IDeployResource = {
  DeployResource: 'ALL',
};

export const ACMParam: inf.IACMParam = {
  AcmDomainName: 'locususer01-xg.prod.gevanni.mynv.jp',
  HostZoneId: 'Z02586805M90SV6SNCCE',
  AssumeRoleArn: 'arn:aws:iam::************:role/LambdaCrossAccountRole',
};

export const WafAlbParam: inf.IWafParam = {
  allowIPList: [
    '***************/25',
    '**************/32',  // NALS
    '************/32',  // NALS
    '*************/32',  // NALS
  ],
  preSharedKeys: ['3EnY1mqoLY'],
  defaultAction: { block: {} },
};

// インターネットアクセス可能なECSサービス、タスクのパラメータを設定する
// ・appName：ECSサービスやタスク名に使用する名前
// ・portNumber：コンテナがリクエストを受け付けるポート（HTTPであれば80）
// ・pathが存在しないタスクがデフォルトルールになる。1タスク必ず設定する（0でも2以上でもNG）
// ・path：デフォルトルールでないタスクの場合に必ず設定する、リクエストを振り分けるパスの文字列を設定
// 本サンプルでは2種類のECSサービスを定義し、EcsAppをデフォルトルールを設定している
export const EcsFrontTask: inf.IEcsAlbParam = {
  appName: 'EcsApp',
  portNumber: 3001,
  enableAlarm: false,
  appLanguage: 'json',
};

export const EcsFrontTaskRole: inf.IEcsTaskRoleParam = {
  policyStatements: [],
  managedPolicy: [],
  crossAccountRoleArns: [],
};

// VPC内でService Connectを経由して接続するECSサービス、タスクのパラメータを設定する
// ・appName：ECSサービスやタスク名に使用する名前
// ・portNumber：コンテナがリクエストを受け付けるポート（HTTPであれば80）
// 本サンプルでは2種類のECSサービスを定義している
export const EcsBackTask: inf.IEcsParam = {
  appName: 'EcsBackend',
  portNumber: 3000,
  enableAlarm: false,
  AllowSGOtherECSApp: [
    {
      serviceId: 'locusmanage01-prod01-sx',
      appName: 'EcsApp',
    },
  ], // App name of ecs connect ecs backend
};

export const EcsBackTaskRole: inf.IEcsTaskRoleParam = {
  policyStatements: [],
  managedPolicy: [],
  crossAccountRoleArns: ['arn:aws:iam::************:role/ProdLocus-user-backend-crossaccount-iamrole'],
};

export const BastionTaskRole: inf.IBastionTaskRoleParam = {
  policyStatements: [],
  managedPolicy: [],
};

export const Env: inf.IEnv = {
  prefix: 'GEVANNI',
  envName: 'Prod01',
  account: '************',
  region: 'ap-northeast-1',
};

export const NotifierParam: inf.INotifierParam = {
  isCreateChatbot: false,
  workspaceId: 'T8XXXXXXX',
  channelIdMon: 'C01YYYYYYYY',
  monitoringNotifyEmail: '',
};

export const ElastiCacheParam: inf.IElastiCacheParam = {
  ElastiCacheSelfDesignedParam: {
    engine: 'valkey',
    engineVersion: '7.2',
    numNodeGroups: 1,
    replicasPerNodeGroup: 1,
    minCapacity: 1,
    maxCapacity: 1,
    targetValueToScale: 70,
    predefinedMetricToScale: appscaling.PredefinedMetric.ELASTICACHE_PRIMARY_ENGINE_CPU_UTILIZATION,
    enableAutoScale: false,
    cacheNodeTypeEnableAutoScale: 'cache.m7g.large',
    cacheNodeTypeDisableAutoScale: 'cache.m7g.large',
    elastiCacheCustomParam: {
      cacheParameterGroupFamily: 'valkey7',
      description: 'CustomParameterGroupForValkey',
      properties: {
        'cluster-enabled': 'no',
      },
    },
    preferredMaintenanceWindow: 'wed:19:00-wed:20:00',
    autoMinorVersionUpgrade: false,
    replicationGroupId: 'GEVANNI-locususer01-prod01-xg-Valkey', // max length of replicationGroupId is 40 character
  },
  ElastiCacheServerlessParam: {
    engine: 'valkey',
    engineVersion: '7',
    dataStorageMaximum: 123, // GB
    dataStorageMinimum: 0, // GB -- When this value is set, it will be charged even if you do not use it.
    ecpuPerSecondMaximum: 1000,
    ecpuPerSecondMinimum: 0, //When this value is set, it will be charged even if you do not use it.
    serverlessCacheName: 'locususer01-prod01-xg-ValkeyServerless', // max length of serverlessCacheName is 40 character
  },
};

export const ElastiCacheTypeParam: inf.IElastiCacheTypeParam = {
  elastiCacheType: 'SELFDESIGNED',
};

export const ElastiCacheCrossServiceParam: inf.IElastiCacheCrossServiceParam = {
  allowSgCrossService: [
    {
      ssmParameterPath: '/locusmanage01-prod01-sx/Batch/BatchTaskSGId',
    },
    {
      ssmParameterPath: '/locusmanage01-prod01-sx/Batch/BatchConnectBackendSGId',
    },
  ],
};

export const OidcParam: inf.IOidcParam = {
  OrganizationName: 'mynavi-group',
  RepositoryNames: {
    FrontEcspressoRepositoryName: 'locus',
    BackEcspressoRepositoryName: 'locus',
  },
};

export const RoleForAppTeamParam: inf.IRoleForAppTeamParam = {
  allowUsers: [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ],
  hasRoleForVendor: true,
  vendorBastionAccountId: '************',
};

export const s3AuditLogLifecycleRules: s3.LifecycleRule[] = [
  {
    expiration: cdk.Duration.days(1825), //Store for 5 years
  },
  {
    abortIncompleteMultipartUploadAfter: cdk.Duration.days(30), //delete incomplete multipart uploads after 30 days
  },
];
export const LogRemovalPolicyParam = inf.RetainRemovalPolicyParam;
export const OtherRemovalPolicyParam = inf.DestroyRemovalPolicyParam;

export const pipelineSourceBucketLifeCycleRules: s3.LifecycleRule[] = [
  {
    noncurrentVersionExpiration: cdk.Duration.days(30),
    noncurrentVersionsToRetain: 20,
  },
];

export const s3BuildLogLifecycleRules: s3.LifecycleRule[] = [
  {
    noncurrentVersionExpiration: cdk.Duration.days(30),
    noncurrentVersionsToRetain: 20,
  },
];

export const openSearchParam: inf.IOpenSearchParam = {
  isCreate: false,
};
