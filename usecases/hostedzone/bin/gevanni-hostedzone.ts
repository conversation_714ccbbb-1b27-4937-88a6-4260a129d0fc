import * as cdk from 'aws-cdk-lib';
import * as fs from 'fs';
import { IConfig } from '../params/interface';
import { <PERSON>evanni<PERSON>oneStack } from '../lib/stack/hostedzone-stack'
const app = new cdk.App();

// ----------------------- Load context variables ------------------------------
// This context need to be specified in args
const argContextZone = 'zone';
const ZoneKey = app.node.tryGetContext(argContextZone);
if (ZoneKey == undefined)
  throw new Error(`Please specify environment with context option. ex) cdk deploy -c ${argContextZone}=dev`);
//Read Typescript Environment file
const TsEnvPath = './params/' + ZoneKey + '.ts';
if (!fs.existsSync(TsEnvPath)) throw new Error(`Can't find a ts environment file [../params/` + ZoneKey + `.ts]`);

//ESLintではrequireの利用が禁止されているため除外コメントを追加
//https://github.com/mynavi-group/csys-infra-baseline-environment-on-aws-change-homemade/issues/29#issuecomment-**********
const config: IConfig = require('../params/' + ZoneKey);

// Add envName to Stack for avoiding duplication of Stack names.
const pjPrefix = config.Env.envName + '-GEVANNI-Zone';

// ----------------------- Environment variables for stack ------------------------------
// Default environment
const procEnvDefault = {
  account: process.env.CDK_DEFAULT_ACCOUNT,
  region: process.env.CDK_DEFAULT_REGION,
};

// Define account id and region from context.
// If "env" isn't defined on the environment variable in context, use account and region specified by "--profile".
function getProcEnv() {
  if (config.Env.account && config.Env.region) {
    return {
      account: config.Env.account,
      region: config.Env.region,
    };
  } else {
    return procEnvDefault;
  }
}

// ----------------------- Guest System Stacks ------------------------------


const zone = new GevanniZoneStack(app, `${pjPrefix}-Route53`, {
  ...config.Route53Param,
  logRemovalPolicyParam: config.LogRemovalPolicyParam,
  env: {
    account: getProcEnv().account,
    region: 'us-east-1',
  },
});

// --------------------------------- Tagging  -------------------------------------

// Tagging "Environment" tag to all resources in this app
const envTagName = 'Environment';
cdk.Tags.of(app).add(envTagName, config.Env.envName);