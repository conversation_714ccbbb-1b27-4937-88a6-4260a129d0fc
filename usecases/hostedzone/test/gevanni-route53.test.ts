// import * as cdk from 'aws-cdk-lib';
// import { Template } from 'aws-cdk-lib/assertions';
// import * as GevanniRoute53 from '../lib/gevanni-route53-stack';

// example test. To run these tests, uncomment this file along with the
// example resource in lib/gevanni-route53-stack.ts
test('SQS Queue Created', () => {
//   const app = new cdk.App();
//     // WHEN
//   const stack = new GevanniRoute53.GevanniRoute53Stack(app, 'MyTestStack');
//     // THEN
//   const template = Template.fromStack(stack);

//   template.hasResourceProperties('AWS::SQS::Queue', {
//     VisibilityTimeout: 300
//   });
});
