import os
import boto3
import time

def acm_handler(event, context):
    request_type = event['RequestType']
    resource_properties = event.get("ResourceProperties")

    domain_name = resource_properties.get("DOMAIN_NAME")
    host_zone_id = resource_properties.get("HOSTED_ZONE_ID")
    sts_arn = resource_properties.get("ROLE_ARN")

    #イベントタイプがCreateの時create_resourceを実行
    if request_type == 'Create':
        return create_resource(domain_name, host_zone_id, sts_arn)

    #イベントタイプがDeleteの時delete_resourceを実行
    if request_type == 'Delete':
        return delete_resource(domain_name, host_zone_id, sts_arn)

    #ドメインの変更があった場合は別サービスとして扱う為、サービスを作りなおす。
    #その為イベントタイプがupdateの時はステータスコードを返却して処理を終了する。
    if request_type == 'Update':
        return {'statusCode': 200}

def create_resource(domain_name, host_zone_id, sts_arn):
    try:
        #ACMのリクエスト処理
        acm_client = boto3.client('acm')
        acm_response = acm_client.request_certificate(
            DomainName=domain_name,
            ValidationMethod='DNS'
        )

        #ACMのARNを取得10秒スリープでCNAMEレコードが払い出させるのを待つ
        certificate_arn = acm_response['CertificateArn']
        time.sleep(10)
        acm_details = acm_client.describe_certificate(CertificateArn=certificate_arn)

        #対象がDNS検証か確認
        dns_validation_options = next(
            (option for option in acm_details['Certificate']['DomainValidationOptions'] if option['ValidationMethod']=='DNS'),
            None
            )
        if not dns_validation_options:
            return {'statusCode': 500, 'body':'DNS検証ではありません。'}

        change_batch = {
            'Changes': [
                {
                    'Action': 'UPSERT',
                    'ResourceRecordSet': {
                        'Name': dns_validation_options['ResourceRecord']['Name'],
                        'Type': dns_validation_options['ResourceRecord']['Type'],
                        'TTL': 3600,
                        'ResourceRecords': [
                            {'Value': dns_validation_options['ResourceRecord']['Value']},
                        ],
                    },
                },
            ],
        }

        #一時認証取得
        sts = boto3.client('sts')
        access_info = sts.assume_role(
            RoleArn = sts_arn,
            RoleSessionName = 'cross_acct_lambda'
            )
        ACCESS_KEY = access_info['Credentials']['AccessKeyId']
        SECRET_KEY = access_info['Credentials']['SecretAccessKey']
        SESSION_TOKEN = access_info['Credentials']['SessionToken']

        #Route53に認証用レコードを登録
        route53 = boto3.client('route53',aws_access_key_id=ACCESS_KEY,aws_secret_access_key=SECRET_KEY,aws_session_token=SESSION_TOKEN,)
        route53.change_resource_record_sets(HostedZoneId=host_zone_id, ChangeBatch=change_batch)

        #ACM認証の疎通確認
        while True:
            acm_json = acm_client.describe_certificate(CertificateArn=certificate_arn)
            if acm_json['Certificate']['Status'] == 'ISSUED':
                break
            time.sleep(10)

        #ACMが正しく発行された場合、ARNを連携
        print('DNS検証が完了しました。')
        return {'statusCode': 200, 'body': 'DNS検証が完了しました。', 'Data':{'ACMARN': certificate_arn}}

    except Exception as e:
        print(f'Error: {e}')
        return {'statusCode': 500, 'body': '処理が失敗しました。'}

def delete_resource(domain_name, host_zone_id, sts_arn):
    try:
        #ACM認証用レコード取得
        acm_client = boto3.client('acm')
        acm_response = acm_client.list_certificates()
        for certificate in acm_response['CertificateSummaryList']:
            if domain_name == certificate['DomainName']:
                acm_details=acm_client.describe_certificate(CertificateArn=certificate['CertificateArn'])
                acm_record=acm_details['Certificate']['DomainValidationOptions'][0]['ResourceRecord']['Name']

        #一時認証取得
        sts = boto3.client('sts')
        access_info = sts.assume_role(
            RoleArn = sts_arn,
            RoleSessionName = 'cross_acct_lambda'
            )
        ACCESS_KEY = access_info['Credentials']['AccessKeyId']
        SECRET_KEY = access_info['Credentials']['SecretAccessKey']
        SESSION_TOKEN = access_info['Credentials']['SessionToken']

        #Route53から認証用レコードを削除
        route53 = boto3.client('route53',aws_access_key_id=ACCESS_KEY,aws_secret_access_key=SECRET_KEY,aws_session_token=SESSION_TOKEN,)
        route53_respons = route53.list_resource_record_sets(HostedZoneId=host_zone_id)
        for record in route53_respons['ResourceRecordSets']:
            if acm_record == record['Name'] and 'CNAME' == record['Type']:
                route53.change_resource_record_sets(
                    HostedZoneId=host_zone_id,
                    ChangeBatch={
                        'Changes': [
                            {
                                'Action': 'DELETE',
                                'ResourceRecordSet':record
                            }
                        ]
                    }
                )

        #ACM削除
        if domain_name == certificate['DomainName']:
            acm_client.delete_certificate(CertificateArn=certificate['CertificateArn'])

        print('ACMの削除が完了しました。')
        return {'statusCode': 200, 'body': 'ACMの削除が完了しました。'}
    except Exception as e:
        print(f'Error: {e}')
        return {'statusCode': 500, 'body': 'ACM削除処理に失敗しました。'}

