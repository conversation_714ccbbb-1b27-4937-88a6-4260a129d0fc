import boto3
import botocore
import os
import urllib.request
import urllib.parse
import logging
import json
from datetime import datetime
# https://docs.pingcap.com/tidbcloud/api/v1beta1/billing#tag/Billing/paths/~1billsCostExplorer/post

TIDB_SECRET_ARN = os.environ['TIDB_SECRET_ARN']
CW_METRICS_NAME = os.environ['CW_METRICS_NAME']
CW_METRICS_NAMESPACE = os.environ['CW_METRICS_NAMESPACE']

# create logger
logger = logging.getLogger()
logger.setLevel(logging.INFO)

def create_digest_auth_opener(username, password):
   # create DigestAuth
   # create a password manager
   password_mgr = urllib.request.HTTPPasswordMgrWithDefaultRealm()

   # Add the username and password.
   # If we knew the realm, we could use it instead of None.
   top_level_urls = [
      'https://billing.tidbapi.com/v1beta1/',
      'https://api.tidbcloud.com/api/v1beta/'
   ]
   password_mgr.add_password(None, top_level_urls, username, password)

   handler = urllib.request.HTTPDigestAuthHandler(password_mgr)

   # create "opener" (OpenerDirector instance)
   opener = urllib.request.build_opener(handler)
   return opener

def create_get_tidb_projects_request(page=1):
   endpoint = 'https://api.tidbcloud.com/api/v1beta/projects'
   query_params = urllib.parse.urlencode({
      'page': page,
      'page_size': 100,
   })
   headers = {
      'Content-Type': 'application/json'
   }
   req = urllib.request.Request(method='GET', url=f'{endpoint}?{query_params}', headers=headers)
   return req

def create_get_tidb_bills_request():
   billedMonth = datetime.now().strftime('%Y-%m')
   endpoint = f'https://billing.tidbapi.com/v1beta1/bills/{billedMonth}'
   headers = {
      'Content-Type': 'application/json'
   }
   req = urllib.request.Request(method='GET', url=endpoint, headers=headers)
   return req

def send_request(opener, req):
   res = opener.open(req)
   response = json.loads(res.read().decode('utf-8'))
   return response

def put_cost_to_cw_metrics(cost_by_project: dict):
   now = datetime.now()
   cw_client = boto3.client('cloudwatch')

   metric_data = []
   for project, cost in cost_by_project.items():
      metric_data.append({
         'MetricName': CW_METRICS_NAME,
         'Timestamp': now,
         'Value': cost,
         'Dimensions': [
            {
               'Name': 'Project',
               'Value': project
            }
         ],
         'Unit': 'None',
      })

   cw_client.put_metric_data(
      Namespace=CW_METRICS_NAMESPACE,
      MetricData=metric_data
   )
   return True

def lambda_handler(event, context):
   logger.info(f'boto3 version: {boto3.__version__}')
   logger.info(f'botocore version: {botocore.__version__}')

   secret_client = boto3.client('secretsmanager')
   get_secret_value_response = secret_client.get_secret_value(SecretId=TIDB_SECRET_ARN)
   tidb_secret = json.loads(get_secret_value_response['SecretString'])
   tidb_public_api_key = tidb_secret['publicKey']
   tidb_private_api_key = tidb_secret['privateKey']

   opener = create_digest_auth_opener(tidb_public_api_key, tidb_private_api_key)
   try:
      # get projects
      page = 1
      projects = []
      while True:
         get_tidb_projects_request = create_get_tidb_projects_request(page)
         get_tidb_projects_response = send_request(opener, get_tidb_projects_request)
         if 'items' in get_tidb_projects_response and get_tidb_projects_response['items'] != None:
            projects.extend(get_tidb_projects_response['items'])
         else:
            break
         
         if get_tidb_projects_response['total'] <= len(projects):
            break
         else:
            page += 1
         
      logger.info('Get Projects Request Successful')

      # get bills
      get_tidb_bills_request = create_get_tidb_bills_request()
      get_tidb_bills_response = send_request(opener, get_tidb_bills_request)
      logger.info('Get Bills Request Successful')

      cost_by_project = {}

      # set default cost to 0.0
      for project in projects:
         cost_by_project[project['name']] = 0.0
      
      # set cost by project
      if 'summaryByProject' in get_tidb_bills_response and get_tidb_bills_response['summaryByProject']['projects'] != None:
         for project in get_tidb_bills_response['summaryByProject']['projects']:
            cost_by_project[project['projectName']] = round(float(project['runningTotal']), 2)
      else:
         logger.info('No bills found')

      logger.info(f'cost_by_project: {cost_by_project}')
      put_cost_to_cw_metrics(cost_by_project)

      return {
         'statusCode': 200,
         'body': 'Success'
      }
   except Exception as e:
      logger.error(f'Error: {str(e)}')
      return {
         'statusCode': 500,
         'body': 'Internal Server Error'
      }
