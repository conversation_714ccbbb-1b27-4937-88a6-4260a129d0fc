import * as cdk from 'aws-cdk-lib';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import { aws_s3 as s3 } from 'aws-cdk-lib';
import * as lambda from 'aws-cdk-lib/aws-lambda';

export interface IlambdaIamParam {
  CrossAccountRoleArn: string;
}

export interface IShareResourcesParam {
  stackTerminationProtection?: boolean;
}

export interface ISecretParam {
  stackTerminationProtection?: boolean;
}

export interface IOidcParam {
  oidcProviderArn?: string;
  OrganizationName: string;
  RepositoryNames: Record<string, string>;
  stackTerminationProtection?: boolean;
}

export interface IVpcParam {
  cidr: string;
  maxAzs: number;
  natGateways: number;
}

export interface INotifierParam {
  isCreate: boolean;
  workspaceId: string;
  channelIdMon: string;
  monitoringNotifyEmail: string;
}

export interface IEnv {
  envName: string;
  prefix: string;
  account?: string;
  region?: string;
}

export interface IInfraResourcesPipelineParam {
  existingSlackChannelArn?: string;
  slackChannelName: string;
  slackWorkspaceId: string;
  slackChannelId: string;
  stackTerminationProtection?: boolean;
}

export interface IVpcForTiDBParam {
  /**
   * VPC Endpoint Service Name for TiDB
   */
  tidbEndpointServiceName: string;
  /**
   * VPC Endpoint Service Port for TiDB
   *
   * @default - 4000
   */
  tidbEndpointServicePort?: number;
  /**
   * VPC Endpoint Service Availability Zones for TiDB Cluster.
   *
   * Check the availability zone from Zone ID of a region,
   * use aws cli, ex: aws ec2 describe-availability-zones --region ap-northeast-1
   * @see https://docs.aws.amazon.com/ram/latest/userguide/working-with-az-ids.html
   *
   * @default no filtering on AZs is done
   * @example - ['ap-northeast-1a', 'ap-northeast-1c']
   */
  tidbClusterAvailabilityZones?: string[];
}

export interface ITiDBEndpointParam {
  tidbEndpoint: string;
}

export interface IBastionParam {
  lifecycleRules?: ecr.LifecycleRule[];
  stackTerminationProtection?: boolean;
}

export interface IFluentbitParam {
  repositoryName: string;
  lifecycleRules?: ecr.LifecycleRule[];
  repositoryDefault: string;
}

export interface IKmsKeyParam {
  pendingWindow: cdk.Duration;
}

export interface IRemovalPolicyParam {
  removalPolicy: cdk.RemovalPolicy;
  autoDeleteObjects: boolean;
  emptyOnDelete: boolean;
}

export const DestroyRemovalPolicyParam: IRemovalPolicyParam = {
  removalPolicy: cdk.RemovalPolicy.DESTROY,
  autoDeleteObjects: true,
  emptyOnDelete: true,
};

export const RetainRemovalPolicyParam: IRemovalPolicyParam = {
  removalPolicy: cdk.RemovalPolicy.RETAIN,
  autoDeleteObjects: false,
  emptyOnDelete: false,
};

export interface ILambdaCustomResourceParam {
  lambdaRuntime: lambda.Runtime;
}

export type ICustomResourceAcmParam = ILambdaCustomResourceParam & {
  stackTerminationProtection?: boolean;
};

export type ICustomResourceAliasParam = ILambdaCustomResourceParam & {
  stackTerminationProtection?: boolean;
};

export interface INewRelicParam {
  ExternalId: string;
  stackTerminationProtection?: boolean;
}

export interface IRoleForExternalTeamParam {
  externalBastionAccountId: string;
  externalTeamName: string;
  stackTerminationProtection?: boolean;
}
export interface ITiDBCostMetricsParam {
  stackTerminationProtection?: boolean;
}

export interface ICloudMapParam {
  stackTerminationProtection?: boolean;
}

export interface IConfig {
  lambdaIamParam: IlambdaIamParam;
  OidcParam: IOidcParam;
  VpcParam: IVpcParam;
  NotifierParam: INotifierParam;
  InfraResourcesPipelineParam: IInfraResourcesPipelineParam;
  Env: IEnv;
  VpcForTiDBParam: IVpcForTiDBParam;
  TiDBEndpoint: ITiDBEndpointParam;
  RegionalVpcForTiDBParam?: IVpcForTiDBParam;
  BastionParam: IBastionParam;
  s3AuditLogLifecycleRules: s3.LifecycleRule[];
  s3BuildLogLifecycleRules: s3.LifecycleRule[];
  KmsKeyParam?: IKmsKeyParam;
  LogRemovalPolicyParam?: IRemovalPolicyParam; // For LogGroup and S3 for log
  OtherRemovalPolicyParam?: IRemovalPolicyParam; // For ECR, Pipeline, etc.
  NewRelicParam: INewRelicParam;
  CustomResourceAcmParam: ICustomResourceAcmParam;
  CustomResourceAliasParam: ICustomResourceAliasParam;
  FluentbitParam: IFluentbitParam;
  RoleForExternalTeamParam: IRoleForExternalTeamParam;
  TiDBCostMetricsParam: ITiDBCostMetricsParam;
  CloudMapParam: ICloudMapParam;
  ShareResourcesParam: IShareResourcesParam;
  SecretParam: ISecretParam;
}
