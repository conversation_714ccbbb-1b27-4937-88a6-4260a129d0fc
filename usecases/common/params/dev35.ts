import * as cdk from 'aws-cdk-lib';
import * as inf from './interface';
import { aws_s3 as s3 } from 'aws-cdk-lib';
import * as lambda from 'aws-cdk-lib/aws-lambda';

export const lambdaIamParam: inf.IlambdaIamParam = {
  CrossAccountRoleArn: 'arn:aws:iam::************:role/LambdaCrossAccountRole',
};

export const OidcParam: inf.IOidcParam = {
  oidcProviderArn: '',
  OrganizationName: 'OrganizationName',
  RepositoryNames: {
    InfraRepositoryName: 'InfraRepositoryName',
    MasterBucketRepositoryName: 'MasterBucketRepositoryName',
  },
};

export const VpcParam: inf.IVpcParam = {
  cidr: '10.101.0.0/16',
  maxAzs: 3,
  natGateways: 3,
};

export const NotifierParam: inf.INotifierParam = {
  isCreate: true,
  workspaceId: 'T8XXXXXXX',
  channelIdMon: 'C01YYYYYYYY',
  monitoringNotifyEmail: '<EMAIL>',
};

export const Env: inf.IEnv = {
  envName: 'Dev35',
  // limit to 7 characters
  prefix: 'GEVANNI',
};

// CodeBuild完了後にSlackへのステータス通知を行う際に必要な情報
// slackChannelNameはSlackチャンネル名を入力
// slackWorkspaceIdはslackのワークスペースIDを入力
// slackChannelIdはSlackのチャンネルIDを入力
export const InfraResourcesPipelineParam: inf.IInfraResourcesPipelineParam = {
  slackChannelName: 'YOUR_CHANNEL_NAME',
  slackWorkspaceId: 'YOUR_SLACK_WORKSPACE_ID',
  slackChannelId: 'YOUR_SLACK_CHANNEL_ID',
  existingSlackChannelArn: '',
};

// Check the availability zone from Zone ID of a region
// use aws cli, ex: aws ec2 describe-availability-zones --region ap-northeast-1
// see: https://docs.aws.amazon.com/ram/latest/userguide/working-with-az-ids.html
export const VpcForTiDBParam: inf.IVpcForTiDBParam = {
  tidbEndpointServiceName: 'xxxxx',
  tidbEndpointServicePort: 4000,
  tidbClusterAvailabilityZones: ['ap-northeast-1a'], // same ZoneId=apne1-az4
};

export const TiDBEndpoint: inf.ITiDBEndpointParam = {
  tidbEndpoint: 'tidb_server_host',
};

export const BastionParam: inf.IBastionParam = {
  lifecycleRules: [
    {
      description: 'Keep last 5 images',
      maxImageCount: 5,
    },
  ],
};

export const FluentbitParam: inf.IFluentbitParam = {
  lifecycleRules: [
    {
      description: 'Keep last 5 images',
      maxImageCount: 5,
    },
  ],
  repositoryName: 'fluentbitrepo',
  repositoryDefault: 'public.ecr.aws/aws-observability/aws-for-fluent-bit:stable',
};

export const s3AuditLogLifecycleRules: s3.LifecycleRule[] = [
  {
    expiration: cdk.Duration.days(1825), //Store for 5 years
  },
  {
    abortIncompleteMultipartUploadAfter: cdk.Duration.days(30), //delete incomplete multipart uploads after 30 days
  },
];

export const s3BuildLogLifecycleRules: s3.LifecycleRule[] = [
  {
    noncurrentVersionExpiration: cdk.Duration.days(30),
    noncurrentVersionsToRetain: 20,
  },
];

export const KmsKeyParam: inf.IKmsKeyParam = {
  pendingWindow: cdk.Duration.days(7),
};

export const LogRemovalPolicyParam = inf.DestroyRemovalPolicyParam;
export const OtherRemovalPolicyParam = inf.DestroyRemovalPolicyParam;

export const CustomResourceAcmParam: inf.ICustomResourceAcmParam = {
  lambdaRuntime: lambda.Runtime.PYTHON_3_12,
};

export const CustomResourceAliasParam: inf.ICustomResourceAliasParam = {
  lambdaRuntime: lambda.Runtime.PYTHON_3_12,
};

export const NewRelicParam: inf.INewRelicParam = {
  ExternalId: '',
};

export const RoleForExternalTeamParam: inf.IRoleForExternalTeamParam = {
  externalBastionAccountId: '************',
  externalTeamName: 'Msp',
};
