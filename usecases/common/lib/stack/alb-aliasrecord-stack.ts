import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { CustomResourceAliasConstruct } from '../construct/lambda-customresouce-alias-construct';
import { IRole } from 'aws-cdk-lib/aws-iam';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as lambda from 'aws-cdk-lib/aws-lambda';

export interface AlbAliasProps extends cdk.StackProps {
  prefix: string;
  AliasFunctionlambdaRole: IRole;
  logRemovalPolicy?: cdk.RemovalPolicy;
  LambdaRuntime: lambda.Runtime;
}

export class AlbAliasStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: AlbAliasProps) {
    super(scope, id, props);

    // CustomResourceログ格納場所
    const LambdaLogGroup = new logs.LogGroup(this, 'function-logs', {
      logGroupName: `/aws/lambda/${props.prefix}/Alb-Alias-log`,
      retention: logs.RetentionDays.ONE_YEAR,
      removalPolicy: props.logRemovalPolicy ?? cdk.RemovalPolicy.DESTROY,
    });

    new CustomResourceAliasConstruct(this, `${props.prefix}-AlbAlias`, {
      prefix: props.prefix,
      AliasFunctionlambdaRole: props.AliasFunctionlambdaRole,
      LambdaLogGroup: LambdaLogGroup,
      LambdaRuntime: props.LambdaRuntime,
    });
  }
}
