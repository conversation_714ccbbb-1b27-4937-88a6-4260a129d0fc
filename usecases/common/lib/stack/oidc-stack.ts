import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { OidcIAMRoleConstruct } from '../construct/oidc-iamrole-construct';

export interface OidcStackProps extends cdk.StackProps {
  oidcProviderArn?: string;
  OrganizationName: string;
  RepositoryNames: Record<string, string>;
  pjPrefix: string;
}

export class OidcStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: OidcStackProps) {
    super(scope, id, props);

    // GithubActionsと接続するためのOpenIdConnectを作成

    let oidcProviderArn: string;
    if (props.oidcProviderArn) {
      oidcProviderArn = props.oidcProviderArn;
    } else {
      const oidcProvider = new iam.OpenIdConnectProvider(this, 'GithubActionsOidcProvider', {
        url: 'https://token.actions.githubusercontent.com',
        clientIds: ['sts.amazonaws.com'],
      });
      oidcProviderArn = oidcProvider.openIdConnectProviderArn;
    }

    // InfraResources用ロール
    new OidcIAMRoleConstruct(this, 'InfraResourcesRole', {
      OrganizationName: props.OrganizationName,
      RepositoryName: props.RepositoryNames.InfraRepositoryName,
      openIdConnectProviderArn: oidcProviderArn,
      statement: [
        {
          actions: ['cloudformation:DescribeStacks', 's3:PutObject'],
          resources: ['*'],
        },
      ],
    });

    // confmasterbucketアップロード用ロール
    new OidcIAMRoleConstruct(this, 'ConfMasterBucketRole', {
      OrganizationName: props.OrganizationName,
      RepositoryName: props.RepositoryNames.MasterBucketRepositoryName,
      openIdConnectProviderArn: oidcProviderArn,
      statement: [
        {
          actions: [
            's3:GetObject',
            's3:ListAllMyBuckets',
            's3:ListBucket',
            's3:PutObject',
            's3:DeleteObject',
            'ssm:PutParameter',
            'ssm:GetParameter',
          ],
          resources: ['*'],
        },
      ],
    });

    // batch master bucket アップロード用ロール
    new OidcIAMRoleConstruct(this, 'BatchMasterBucketRole', {
      OrganizationName: props.OrganizationName,
      RepositoryName: props.RepositoryNames.MasterBucketRepositoryName,
      openIdConnectProviderArn: oidcProviderArn,
      statement: [
        {
          actions: [
            's3:GetObject',
            's3:ListAllMyBuckets',
            's3:ListBucket',
            's3:PutObject',
            's3:DeleteObject',
            'ssm:PutParameter',
            'ssm:GetParameter',
          ],
          resources: ['*'],
        },
      ],
    });

    new ssm.StringParameter(this, 'SSMOidcProviderArn', {
      parameterName: `/${props.pjPrefix}/oidcProviderArn`,
      stringValue: oidcProviderArn,
    });
  }
}
