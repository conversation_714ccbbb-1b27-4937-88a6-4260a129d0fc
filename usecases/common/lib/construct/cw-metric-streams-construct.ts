import { Construct } from 'constructs';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as cloudwatch from 'aws-cdk-lib/aws-cloudwatch';
import * as firehose from 'aws-cdk-lib/aws-kinesisfirehose';

interface CwMetricStreamsProps {
  prefix: string;
  firehoseStream: firehose.CfnDeliveryStream;
}

export class CwMetricStreams extends Construct {
  public readonly streams: cloudwatch.CfnMetricStream;
  constructor(scope: Construct, id: string, props: CwMetricStreamsProps) {
    super(scope, id);

    const metricStreamsRole = new iam.Role(this, 'MetricStreamsRole', {
      assumedBy: new iam.ServicePrincipal('streams.metrics.cloudwatch.amazonaws.com'),
    });

    metricStreamsRole.addToPolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: ['firehose:PutRecord', 'firehose:PutRecordBatch'],
        resources: [props.firehoseStream.attrArn],
      }),
    );

    const metricStreams = new cloudwatch.CfnMetricStream(this, 'Metric-Streams', {
      name: `${props.prefix}-MetricStreams`,
      firehoseArn: props.firehoseStream.attrArn,
      roleArn: metricStreamsRole.roleArn,
      outputFormat: 'opentelemetry0.7',
      includeFilters: [
        {
          namespace: 'AWS/ApplicationELB',
          metricNames: [
            'RequestCount',
            'NewConnectionCount',
            'RejectedConnectionCount',
            'ClientTLSNegotiationErrorCount',
            'HTTPCode_ELB_5XX_Count',
            'HTTPCode_ELB_4XX_Count',
            'HTTPCode_Target_2XX_Count',
            'HTTPCode_Target_5XX_Count',
            'HTTPCode_Target_4XX_Count',
            'TargetConnectionErrorCount',
            'TargetTLSNegotiationErrorCount',
            'TargetResponseTime',
            'RequestCountPerTarget',
          ],
        },
        {
          namespace: 'AWS/ECS',
          metricNames: ['MemoryUtilization', 'CPUUtilization'],
        },
        {
          namespace: 'ECS/ContainerInsights',
          metricNames: ['DesiredTaskCount', 'RunningTaskCount', 'PendingTaskCount'],
        },
        {
          namespace: 'AWS/ElastiCache',
          metricNames: [
            'CPUUtilization',
            'EngineCPUUtilization',
            'ElastiCacheProcessingUnits',
            'SwapUsage',
            'Evictions',
            'CurrConnections',
            'FreeableMemory',
            'DatabaseMemoryUsagePercentage',
            'NetworkBytesIn',
            'NetworkBytesOut',
            'SuccessfulReadRequestLatency',
            'SuccessfulWriteRequestLatency',
            'ReplicationBytes',
          ],
        },
        {
          namespace: 'TiDB',
          metricNames: ['CostMetrics'],
        },
      ],
    });
    this.streams = metricStreams;
  }
}
