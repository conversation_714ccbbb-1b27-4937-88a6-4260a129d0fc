import * as cdk from 'aws-cdk-lib';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import { aws_ecr_assets as ecr_assets } from 'aws-cdk-lib';
import * as ecrdeploy from 'cdk-ecr-deployment';
import * as path from 'path';
import { Construct } from 'constructs';

export interface FluentbitDockerConstructProps {
  readonly prefix: string;
  /**
   * ECR repository name
   */
  readonly repositoryName: string;
  /**
   * ECR repository default
   */
  readonly repositoryDefault: string;
  /**
   * ECR lifecycle rule
   * @default Keep last 10 images
   */
  /**
   * ECR lifecycle rule
   * @default Keep last 10 images
   */
  readonly ecrLifecycleRules?: ecr.LifecycleRule[];
  /**
   * ECR removal policy parameters
   */
  ecrRemovalPolicyParam?: {
    removalPolicy?: cdk.RemovalPolicy;
    emptyOnDelete?: boolean;
  };
}

export class FluentbitDockerConstruct extends Construct {
  public readonly ecrRepository: ecr.Repository;

  constructor(scope: Construct, id: string, props: FluentbitDockerConstructProps) {
    super(scope, id);

    // ----------------------- ECR Repository -------------------------

    // Default ECR lifecycle rule
    const defaultLifecycleRules: ecr.LifecycleRule[] = [
      {
        description: 'Keep last 10 images',
        maxImageCount: 10,
      },
    ];

    // Create Fluentbit ECR Repository
    this.ecrRepository = new ecr.Repository(this, props.repositoryName, {
      imageScanOnPush: true,
      lifecycleRules: props.ecrLifecycleRules ?? defaultLifecycleRules,
      removalPolicy: props.ecrRemovalPolicyParam?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
      emptyOnDelete: props.ecrRemovalPolicyParam?.emptyOnDelete ?? false,
    });

    // Fluentbit docker image default
    new ecrdeploy.ECRDeployment(this, 'DeployImage', {
      src: new ecrdeploy.DockerImageName(props.repositoryDefault),
      dest: new ecrdeploy.DockerImageName(this.ecrRepository.repositoryUriForTag('fluentbitimage')),
    });

    // Fluentbit docker image for Ruby app

    // Create Image asset
    const fluentbitImageRuby = new ecr_assets.DockerImageAsset(this, 'FluentbitImageRuby', {
      directory: path.join(__dirname, '../../../../container/fluentbit_docker_image', 'ruby'),
      platform: ecr_assets.Platform.LINUX_AMD64,
    });

    // image Deployment
    new ecrdeploy.ECRDeployment(this, `fluentbitImageECRDeploymentRuby`, {
      src: new ecrdeploy.DockerImageName(fluentbitImageRuby.imageUri),
      dest: new ecrdeploy.DockerImageName(this.ecrRepository.repositoryUriForTag('fluentbitimageruby')),
    });

    // Fluentbit docker image for Go app

    // Create Image asset
    const fluentbitImageGo = new ecr_assets.DockerImageAsset(this, 'FluentbitImageGo', {
      directory: path.join(__dirname, '../../../../container/fluentbit_docker_image', 'go'),
    });

    // image Deployment
    new ecrdeploy.ECRDeployment(this, `fluentbitImageECRDeploymentGo`, {
      src: new ecrdeploy.DockerImageName(fluentbitImageGo.imageUri),
      dest: new ecrdeploy.DockerImageName(this.ecrRepository.repositoryUriForTag('fluentbitimagego')),
    });

    // Fluentbit docker image for python app

    // Create Image asset
    const fluentbitImagePython = new ecr_assets.DockerImageAsset(this, 'FluentbitImagePython', {
      directory: path.join(__dirname, '../../../../container/fluentbit_docker_image', 'python'),
    });

    // image Deployment
    new ecrdeploy.ECRDeployment(this, `fluentbitImageECRDeploymentPython`, {
      src: new ecrdeploy.DockerImageName(fluentbitImagePython.imageUri),
      dest: new ecrdeploy.DockerImageName(this.ecrRepository.repositoryUriForTag('fluentbitimagepython')),
    });

    // Fluentbit docker image for json format

    // Create Image asset
    const fluentbitImageJson = new ecr_assets.DockerImageAsset(this, 'FluentbitImageJson', {
      directory: path.join(__dirname, '../../../../container/fluentbit_docker_image', 'json'),
    });

    // image Deployment
    new ecrdeploy.ECRDeployment(this, `fluentbitImageECRDeploymentJson`, {
      src: new ecrdeploy.DockerImageName(fluentbitImageJson.imageUri),
      dest: new ecrdeploy.DockerImageName(this.ecrRepository.repositoryUriForTag('fluentbitimagejson')),
    });
  }
}
