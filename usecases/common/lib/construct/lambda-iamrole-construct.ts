import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as sns from 'aws-cdk-lib/aws-sns';

interface AcmLambdaIamRoleProps extends cdk.StackProps {
  pjPrefix: string;
  CrossAccountRoleArn: string;
}

export class AcmLambdaIamRole extends Construct {
  public readonly AcmFunctionlambdaRole: iam.Role;

  constructor(scope: Construct, id: string, props: AcmLambdaIamRoleProps) {
    super(scope, id);
    //lambda実行ロール
    const AcmFunctionlambdaRole = new iam.Role(this, 'AcmFunction-LambdaRole', {
      roleName: `${props.pjPrefix}-AcmFunctionLambdaActionRole`,
      assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
      managedPolicies: [
        iam.ManagedPolicy.fromManagedPolicyArn(
          this,
          'lambda',
          'arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole',
        ),
      ],
    });

    //ACM アクセス
    AcmFunctionlambdaRole.addToPolicy(
      new iam.PolicyStatement({
        actions: ['acm:RequestCertificate', 'acm:DescribeCertificate', 'acm:ListCertificates', 'acm:DeleteCertificate'],
        resources: ['*'],
      }),
    );

    //Route53 アクセス (Cross-Account)
    AcmFunctionlambdaRole.addToPolicy(
      new iam.PolicyStatement({
        actions: ['sts:AssumeRole'],
        resources: [props.CrossAccountRoleArn],
      }),
    );
    this.AcmFunctionlambdaRole = AcmFunctionlambdaRole;
  }
}
