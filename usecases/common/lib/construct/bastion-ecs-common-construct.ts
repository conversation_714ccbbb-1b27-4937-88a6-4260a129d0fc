import * as cdk from 'aws-cdk-lib';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as cwe from 'aws-cdk-lib/aws-events';
import * as cwet from 'aws-cdk-lib/aws-events-targets';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import { aws_ecr_assets as ecr_assets } from 'aws-cdk-lib';
import * as ecrdeploy from 'cdk-ecr-deployment';
import * as path from 'path';
import { Construct } from 'constructs';

export interface BastionEcsCommonConstructProps {
  /**
   * ECS application VPC
   */
  readonly vpc: ec2.Vpc;
  /**
   * SNS topic ARN for sending alarm
   */
  readonly alarmTopic: sns.Topic;
  /**
   * Project and environment prefix
   */
  readonly prefix: string;
  /**
   * ECR repository name
   */
  readonly repositoryName: string;
  /**
   * ECR lifecycle rule
   * @default Keep last 10 images
   */
  readonly ecrLifecycleRules?: ecr.LifecycleRule[];
  /**
   * ECR removal policy parameters
   */
  ecrRemovalPolicyParam?: {
    removalPolicy?: cdk.RemovalPolicy;
    emptyOnDelete?: boolean;
  };
}

export class BastionEcsCommonConstruct extends Construct {
  public readonly ecrRepository: ecr.Repository;
  public readonly ecsCluster: ecs.Cluster;
  public readonly ecsTaskExecutionRole: iam.Role;

  constructor(scope: Construct, id: string, props: BastionEcsCommonConstructProps) {
    super(scope, id);

    // ----------------------- ECR Repository -------------------------

    // Default ECR lifecycle rule
    const defaultLifecycleRules: ecr.LifecycleRule[] = [
      {
        description: 'Keep last 10 images',
        maxImageCount: 10,
      },
    ];

    // Create Bastion ECR Repository
    this.ecrRepository = new ecr.Repository(this, props.repositoryName, {
      imageScanOnPush: true,
      lifecycleRules: props.ecrLifecycleRules ?? defaultLifecycleRules,
      removalPolicy: props.ecrRemovalPolicyParam?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
      emptyOnDelete: props.ecrRemovalPolicyParam?.emptyOnDelete ?? false,
    });

    // Create Image asset
    const bastionImage = new ecr_assets.DockerImageAsset(this, 'BastionImage', {
      directory: path.join(__dirname, '../../../../container/bastion', 'docker'),
    });

    // image Deployment
    new ecrdeploy.ECRDeployment(this, `BastionECRDeployment`, {
      src: new ecrdeploy.DockerImageName(bastionImage.imageUri),
      dest: new ecrdeploy.DockerImageName(this.ecrRepository.repositoryUriForTag('bastionimage')),
    });

    // ----------------------- Execution Role -------------------------

    // Create Bastion ECS Task Execution Role
    // The task execution role grants the Amazon ECS container and Fargate agents permission to make AWS API calls on your behalf.
    // https://docs.aws.amazon.com/AmazonECS/latest/developerguide/task_execution_IAM_role.html
    this.ecsTaskExecutionRole = new iam.Role(this, 'BastionEcsTaskExecutionRole', {
      assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
      managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AmazonECSTaskExecutionRolePolicy')],
      inlinePolicies: {
        // For Service Connect, ECS Agent need to create CW Logs groups
        createLogs: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              actions: ['logs:CreateLogGroup'],
              resources: ['*'],
            }),
          ],
        }),
      },
    });

    // ----------------------- ECS Cluster -------------------------

    // Create Bastion ECS Cluster
    this.ecsCluster = new ecs.Cluster(this, 'BastionEcsCluster', {
      vpc: props.vpc,
      containerInsightsV2: ecs.ContainerInsights.ENHANCED,
      enableFargateCapacityProviders: true,
      clusterName: cdk.PhysicalName.GENERATE_IF_NEEDED,
    });

    // ----------------------- Event notification for Bastion ECS Cluster -------------------------

    // https://docs.aws.amazon.com/AmazonECS/latest/developerguide/ecs_cwe_events.html#ecs_service_events
    new cwe.Rule(this, 'BastionECSServiceActionEventRule', {
      description: 'CloudWatch Event Rule to send notification on Bastion ECS Service action events.',
      enabled: true,
      eventPattern: {
        source: ['aws.ecs'],
        detailType: ['ECS Service Action'],
        detail: {
          eventType: ['WARN', 'ERROR'],
        },
      },
      targets: [new cwet.SnsTopic(props.alarmTopic)],
    });

    // https://docs.aws.amazon.com/AmazonECS/latest/developerguide/ecs_cwe_events.html#ecs_service_deployment_events
    new cwe.Rule(this, 'BastionECSServiceDeploymentEventRule', {
      description: 'CloudWatch Event Rule to send notification on Bastion ECS Service deployment events.',
      enabled: true,
      eventPattern: {
        source: ['aws.ecs'],
        detailType: ['ECS Deployment State Change'],
        detail: {
          eventType: ['WARN', 'ERROR'],
        },
      },
      targets: [new cwet.SnsTopic(props.alarmTopic)],
    });
  }
}
