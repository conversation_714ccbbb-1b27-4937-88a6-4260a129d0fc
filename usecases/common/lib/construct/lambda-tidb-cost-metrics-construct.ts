import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as events from 'aws-cdk-lib/aws-events';
import * as targets from 'aws-cdk-lib/aws-events-targets';

export interface LambdaTidbCostMetricsProps {
  prefix: string;
  removalPolicy?: cdk.RemovalPolicy;
  tidbSecretArn: string;
}

export class LambdaTidbCostMetricsConstruct extends Construct {
  public readonly function: lambda.Function;
  constructor(scope: Construct, id: string, props: LambdaTidbCostMetricsProps) {
    super(scope, id);

    const lambdaLogGroup = new logs.LogGroup(this, 'FunctionLogGroup', {
      logGroupName: `/aws/lambda/${props.prefix}/TidbCostMetricsLog`,
      retention: logs.RetentionDays.ONE_YEAR,
      removalPolicy: props.removalPolicy ?? cdk.RemovalPolicy.DESTROY,
    });

    const lambdaFunction = new lambda.Function(this, 'function', {
      functionName: `${props.prefix}-tidb-cost-metrics`,
      runtime: lambda.Runtime.PYTHON_3_12,
      handler: 'lambda_function.lambda_handler',
      code: lambda.Code.fromAsset('lambda/tidb-cost-metrics'),
      timeout: cdk.Duration.seconds(900),
      logGroup: lambdaLogGroup,
      memorySize: 128,
      environment: {
        TIDB_SECRET_ARN: props.tidbSecretArn,
        CW_METRICS_NAME: 'CostMetrics',
        CW_METRICS_NAMESPACE: 'TiDB',
      },
    });
    this.function = lambdaFunction;

    new events.Rule(this, 'Rule', {
      schedule: events.Schedule.expression('rate(5 minutes)'),
      targets: [new targets.LambdaFunction(lambdaFunction)],
    });

    this.function.addToRolePolicy(
      new iam.PolicyStatement({
        actions: ['secretsmanager:GetSecretValue'],
        resources: [props.tidbSecretArn],
      }),
    );

    this.function.addToRolePolicy(
      new iam.PolicyStatement({
        actions: ['cloudwatch:PutMetricData'],
        resources: ['*'],
      }),
    );
  }
}
