// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Dev01 Guest Stacks GuestAccount ECS App Stacks 1`] = `
Object {
  "Outputs": Object {
    "AcmFunctionLambdaRoleArn": Object {
      "Export": Object {
        "Name": "Dev01-AcmFunctionLambdaRoleArn",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "Dev01LambdaRoleAcmFunctionLambdaRole0612C1D3",
          "Arn",
        ],
      },
    },
    "ExportsOutputFnGetAttDev01AppKey5F982BFEArnC4871D32": Object {
      "Export": Object {
        "Name": "Dev01-ShareResources:ExportsOutputFnGetAttDev01AppKey5F982BFEArnC4871D32",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "Dev01AppKey5F982BFE",
          "Arn",
        ],
      },
    },
    "ExportsOutputFnGetAttDev01LambdaRoleAcmFunctionLambdaRole0612C1D3Arn9D658D77": Object {
      "Export": Object {
        "Name": "Dev01-ShareResources:ExportsOutputFnGetAttDev01LambdaRoleAcmFunctionLambdaRole0612C1D3Arn9D658D77",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "Dev01LambdaRoleAcmFunctionLambdaRole0612C1D3",
          "Arn",
        ],
      },
    },
    "ExportsOutputRefDev01AlarmSNSTopic8457D16EDBE0EDE6": Object {
      "Export": Object {
        "Name": "Dev01-ShareResources:ExportsOutputRefDev01AlarmSNSTopic8457D16EDBE0EDE6",
      },
      "Value": Object {
        "Ref": "Dev01AlarmSNSTopic8457D16E",
      },
    },
    "FluentbitEcrRepositoryUri": Object {
      "Export": Object {
        "Name": "Dev01-FluentbitEcrRepositoryUri",
      },
      "Value": Object {
        "Fn::Join": Array [
          "",
          Array [
            Object {
              "Fn::Select": Array [
                4,
                Object {
                  "Fn::Split": Array [
                    ":",
                    Object {
                      "Fn::GetAtt": Array [
                        "FluentbitDockerfluentbitrepo09B43BE7",
                        "Arn",
                      ],
                    },
                  ],
                },
              ],
            },
            ".dkr.ecr.",
            Object {
              "Fn::Select": Array [
                3,
                Object {
                  "Fn::Split": Array [
                    ":",
                    Object {
                      "Fn::GetAtt": Array [
                        "FluentbitDockerfluentbitrepo09B43BE7",
                        "Arn",
                      ],
                    },
                  ],
                },
              ],
            },
            ".",
            Object {
              "Ref": "AWS::URLSuffix",
            },
            "/",
            Object {
              "Ref": "FluentbitDockerfluentbitrepo09B43BE7",
            },
          ],
        ],
      },
    },
    "alarmTopicArn": Object {
      "Export": Object {
        "Name": "Dev01-AlarmTopicArn",
      },
      "Value": Object {
        "Ref": "Dev01AlarmSNSTopic8457D16E",
      },
    },
    "appKeyArn": Object {
      "Export": Object {
        "Name": "Dev01-AppKeyArn",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "Dev01AppKey5F982BFE",
          "Arn",
        ],
      },
    },
    "vpcId": Object {
      "Export": Object {
        "Name": "Dev01-VpcId",
      },
      "Value": Object {
        "Ref": "Dev01Vpc877F445C",
      },
    },
  },
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "BatchMasterBucketAutoDeleteObjectsCustomResource2A5339A8": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "BatchMasterBucketPolicy63A1706F",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "BatchMasterBucketB198BB29",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "BatchMasterBucketB198BB29": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
        "VersioningConfiguration": Object {
          "Status": "Enabled",
        },
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "BatchMasterBucketPolicy63A1706F": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "BatchMasterBucketB198BB29",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "BatchMasterBucketB198BB29",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "BatchMasterBucketB198BB29",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "BatchSharedResourcesEventBridgeSchedulerServiceRole02B4EB8F": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Condition": Object {
                "StringEquals": Object {
                  "aws:SourceAccount": "************",
                },
              },
              "Effect": "Allow",
              "Principal": Object {
                "Service": "scheduler.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Path": "/service-role/",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "BatchSharedResourcesEventBridgeSchedulerServiceRoleDefaultPolicyA5A10CC9": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "lambda:InvokeFunction",
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "BatchSharedResourcesStateMachineExecutor26FF42B8",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "BatchSharedResourcesStateMachineExecutor26FF42B8",
                          "Arn",
                        ],
                      },
                      ":*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "BatchSharedResourcesEventBridgeSchedulerServiceRoleDefaultPolicyA5A10CC9",
        "Roles": Array [
          Object {
            "Ref": "BatchSharedResourcesEventBridgeSchedulerServiceRole02B4EB8F",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "BatchSharedResourcesStateMachineExecutor26FF42B8": Object {
      "DependsOn": Array [
        "BatchSharedResourcesStateMachineExecutorRole35C1A6DB",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": "cdk-hnb659fds-assets-************-ap-northeast-1",
          "S3Key": "0cb863c1fedefa23c10c6e52d4812c5b411f5cb24ed533e2043bc264be230478.zip",
        },
        "Handler": "app.lambda_handler",
        "Role": Object {
          "Fn::GetAtt": Array [
            "BatchSharedResourcesStateMachineExecutorRole35C1A6DB",
            "Arn",
          ],
        },
        "Runtime": "python3.12",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::Lambda::Function",
    },
    "BatchSharedResourcesStateMachineExecutorRole35C1A6DB": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
              ],
            ],
          },
        ],
        "Policies": Array [
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": "states:StartExecution",
                  "Effect": "Allow",
                  "Resource": "*",
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "stateMachineExecutionPolicy",
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "ConfMasterBucketA2D66739": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
        "VersioningConfiguration": Object {
          "Status": "Enabled",
        },
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "ConfMasterBucketAutoDeleteObjectsCustomResourceEF53A0DE": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "ConfMasterBucketPolicyB6E309CD",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "ConfMasterBucketA2D66739",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "ConfMasterBucketPolicyB6E309CD": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "ConfMasterBucketA2D66739",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ConfMasterBucketA2D66739",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ConfMasterBucketA2D66739",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "CustomCDKECRDeploymentbd07c930edb94112a20f03f096f53666512MiB28EAD8E4": Object {
      "DependsOn": Array [
        "CustomCDKECRDeploymentbd07c930edb94112a20f03f096f53666512MiBServiceRoleDefaultPolicy280095F8",
        "CustomCDKECRDeploymentbd07c930edb94112a20f03f096f53666512MiBServiceRole8C8B0491",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": "cdk-hnb659fds-assets-************-ap-northeast-1",
          "S3Key": "62494328fd645460d4bebd6302a3dae5e88a958d59832ae256df87891c628542.zip",
        },
        "Handler": "bootstrap",
        "MemorySize": 512,
        "Role": Object {
          "Fn::GetAtt": Array [
            "CustomCDKECRDeploymentbd07c930edb94112a20f03f096f53666512MiBServiceRole8C8B0491",
            "Arn",
          ],
        },
        "Runtime": "provided.al2023",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "CustomCDKECRDeploymentbd07c930edb94112a20f03f096f53666512MiBServiceRole8C8B0491": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
              ],
            ],
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "CustomCDKECRDeploymentbd07c930edb94112a20f03f096f53666512MiBServiceRoleDefaultPolicy280095F8": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "ecr:GetAuthorizationToken",
                "ecr:BatchCheckLayerAvailability",
                "ecr:GetDownloadUrlForLayer",
                "ecr:GetRepositoryPolicy",
                "ecr:DescribeRepositories",
                "ecr:ListImages",
                "ecr:DescribeImages",
                "ecr:BatchGetImage",
                "ecr:ListTagsForResource",
                "ecr:DescribeImageScanFindings",
                "ecr:InitiateLayerUpload",
                "ecr:UploadLayerPart",
                "ecr:CompleteLayerUpload",
                "ecr:PutImage",
              ],
              "Effect": "Allow",
              "Resource": "*",
            },
            Object {
              "Action": "s3:GetObject",
              "Effect": "Allow",
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "CustomCDKECRDeploymentbd07c930edb94112a20f03f096f53666512MiBServiceRoleDefaultPolicy280095F8",
        "Roles": Array [
          Object {
            "Ref": "CustomCDKECRDeploymentbd07c930edb94112a20f03f096f53666512MiBServiceRole8C8B0491",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F": Object {
      "DependsOn": Array [
        "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": "cdk-hnb659fds-assets-************-ap-northeast-1",
          "S3Key": "faa95a81ae7d7373f3e1f242268f904eb748d8d0fdd306e8a6fe515a1905a7d6.zip",
        },
        "Description": Object {
          "Fn::Join": Array [
            "",
            Array [
              "Lambda function for auto-deleting objects in ",
              Object {
                "Ref": "Dev01VpcFlowLogBucketE33D1C02",
              },
              " S3 bucket.",
            ],
          ],
        },
        "Handler": "index.handler",
        "MemorySize": 128,
        "Role": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
            "Arn",
          ],
        },
        "Runtime": "nodejs20.x",
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Sub": "arn:\${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "Dev01AlarmNotifyEmailF84A8C46": Object {
      "Properties": Object {
        "Endpoint": "<EMAIL>",
        "Protocol": "email",
        "TopicArn": Object {
          "Ref": "Dev01AlarmSNSTopic8457D16E",
        },
      },
      "Type": "AWS::SNS::Subscription",
    },
    "Dev01AlarmSNSTopic8457D16E": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::SNS::Topic",
    },
    "Dev01AlarmSNSTopicPolicyEDF42173": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sns:Publish",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "cloudwatch.amazonaws.com",
              },
              "Resource": Object {
                "Ref": "Dev01AlarmSNSTopic8457D16E",
              },
              "Sid": "0",
            },
            Object {
              "Action": "sns:Publish",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "events.amazonaws.com",
              },
              "Resource": Object {
                "Ref": "Dev01AlarmSNSTopic8457D16E",
              },
              "Sid": "1",
            },
          ],
          "Version": "2012-10-17",
        },
        "Topics": Array [
          Object {
            "Ref": "Dev01AlarmSNSTopic8457D16E",
          },
        ],
      },
      "Type": "AWS::SNS::TopicPolicy",
    },
    "Dev01AppKey5F982BFE": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "Description": "Custom KMS key",
        "EnableKeyRotation": true,
        "KeyPolicy": Object {
          "Statement": Array [
            Object {
              "Action": "kms:*",
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":iam::************:root",
                    ],
                  ],
                },
              },
              "Resource": "*",
            },
            Object {
              "Action": Array [
                "kms:Encrypt*",
                "kms:Decrypt*",
                "kms:ReEncrypt*",
                "kms:GenerateDataKey*",
                "kms:Describe*",
              ],
              "Condition": Object {
                "ArnLike": Object {
                  "kms:EncryptionContext:aws:logs:arn": "arn:aws:logs:ap-northeast-1:************:*",
                },
              },
              "Effect": "Allow",
              "Principal": Object {
                "Service": "logs.ap-northeast-1.amazonaws.com",
              },
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PendingWindowInDays": 7,
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::KMS::Key",
      "UpdateReplacePolicy": "Delete",
    },
    "Dev01AppKeyAlias152FB449": Object {
      "Properties": Object {
        "AliasName": "alias/Dev01-AppKey-for-app",
        "TargetKeyId": Object {
          "Fn::GetAtt": Array [
            "Dev01AppKey5F982BFE",
            "Arn",
          ],
        },
      },
      "Type": "AWS::KMS::Alias",
    },
    "Dev01ChatbotChatbotChannel863E252D": Object {
      "Properties": Object {
        "ConfigurationName": "Dev01-Chatbot-T8XXXXXXX",
        "IamRoleArn": Object {
          "Fn::GetAtt": Array [
            "Dev01ChatbotChatbotRoleE0FC3D70",
            "Arn",
          ],
        },
        "SlackChannelId": "C01YYYYYYYY",
        "SlackWorkspaceId": "T8XXXXXXX",
        "SnsTopicArns": Array [
          Object {
            "Ref": "Dev01AlarmSNSTopic8457D16E",
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::Chatbot::SlackChannelConfiguration",
    },
    "Dev01ChatbotChatbotRoleE0FC3D70": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "chatbot.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/ReadOnlyAccess",
              ],
            ],
          },
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/CloudWatchReadOnlyAccess",
              ],
            ],
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "Dev01LambdaRoleAcmFunctionLambdaRole0612C1D3": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
        ],
        "RoleName": "Dev01-AcmFunctionLambdaActionRole",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "Dev01LambdaRoleAcmFunctionLambdaRoleDefaultPolicyC4B003E7": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "acm:RequestCertificate",
                "acm:DescribeCertificate",
                "acm:ListCertificates",
                "acm:DeleteCertificate",
              ],
              "Effect": "Allow",
              "Resource": "*",
            },
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Resource": "arn:aws:iam::************:role/LambdaCrossAccountRole",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "Dev01LambdaRoleAcmFunctionLambdaRoleDefaultPolicyC4B003E7",
        "Roles": Array [
          Object {
            "Ref": "Dev01LambdaRoleAcmFunctionLambdaRole0612C1D3",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "Dev01Vpc877F445C": Object {
      "Properties": Object {
        "CidrBlock": "**********/16",
        "EnableDnsHostnames": true,
        "EnableDnsSupport": true,
        "InstanceTenancy": "default",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc",
          },
        ],
      },
      "Type": "AWS::EC2::VPC",
    },
    "Dev01VpcEc2EndpointForPrivate9791BE78": Object {
      "Properties": Object {
        "PrivateDnsEnabled": true,
        "SecurityGroupIds": Array [
          Object {
            "Fn::GetAtt": Array [
              "Dev01VpcEc2EndpointForPrivateSecurityGroup7C4287B7",
              "GroupId",
            ],
          },
        ],
        "ServiceName": "com.amazonaws.ap-northeast-1.ec2",
        "SubnetIds": Array [
          Object {
            "Ref": "Dev01VpcProtectedSubnet1Subnet23229EC2",
          },
          Object {
            "Ref": "Dev01VpcProtectedSubnet2SubnetE31E0056",
          },
          Object {
            "Ref": "Dev01VpcProtectedSubnet3SubnetA3EA6483",
          },
        ],
        "VpcEndpointType": "Interface",
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::VPCEndpoint",
    },
    "Dev01VpcEc2EndpointForPrivateSecurityGroup7C4287B7": Object {
      "Properties": Object {
        "GroupDescription": "Dev01-ShareResources/Dev01-Vpc/Vpc/Ec2EndpointForPrivate/SecurityGroup",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "SecurityGroupIngress": Array [
          Object {
            "CidrIp": Object {
              "Fn::GetAtt": Array [
                "Dev01Vpc877F445C",
                "CidrBlock",
              ],
            },
            "Description": Object {
              "Fn::Join": Array [
                "",
                Array [
                  "from ",
                  Object {
                    "Fn::GetAtt": Array [
                      "Dev01Vpc877F445C",
                      "CidrBlock",
                    ],
                  },
                  ":443",
                ],
              ],
            },
            "FromPort": 443,
            "IpProtocol": "tcp",
            "ToPort": 443,
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "Dev01VpcEc2MessagesEndpointForPrivate830A4A02": Object {
      "Properties": Object {
        "PrivateDnsEnabled": true,
        "SecurityGroupIds": Array [
          Object {
            "Fn::GetAtt": Array [
              "Dev01VpcEc2MessagesEndpointForPrivateSecurityGroup5D94B48D",
              "GroupId",
            ],
          },
        ],
        "ServiceName": "com.amazonaws.ap-northeast-1.ec2messages",
        "SubnetIds": Array [
          Object {
            "Ref": "Dev01VpcProtectedSubnet1Subnet23229EC2",
          },
          Object {
            "Ref": "Dev01VpcProtectedSubnet2SubnetE31E0056",
          },
          Object {
            "Ref": "Dev01VpcProtectedSubnet3SubnetA3EA6483",
          },
        ],
        "VpcEndpointType": "Interface",
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::VPCEndpoint",
    },
    "Dev01VpcEc2MessagesEndpointForPrivateSecurityGroup5D94B48D": Object {
      "Properties": Object {
        "GroupDescription": "Dev01-ShareResources/Dev01-Vpc/Vpc/Ec2MessagesEndpointForPrivate/SecurityGroup",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "SecurityGroupIngress": Array [
          Object {
            "CidrIp": Object {
              "Fn::GetAtt": Array [
                "Dev01Vpc877F445C",
                "CidrBlock",
              ],
            },
            "Description": Object {
              "Fn::Join": Array [
                "",
                Array [
                  "from ",
                  Object {
                    "Fn::GetAtt": Array [
                      "Dev01Vpc877F445C",
                      "CidrBlock",
                    ],
                  },
                  ":443",
                ],
              ],
            },
            "FromPort": 443,
            "IpProtocol": "tcp",
            "ToPort": 443,
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "Dev01VpcEcrDkrEndpointForPrivateF1C8A1F5": Object {
      "Properties": Object {
        "PrivateDnsEnabled": true,
        "SecurityGroupIds": Array [
          Object {
            "Fn::GetAtt": Array [
              "Dev01VpcEcrDkrEndpointForPrivateSecurityGroup6F318001",
              "GroupId",
            ],
          },
        ],
        "ServiceName": "com.amazonaws.ap-northeast-1.ecr.dkr",
        "SubnetIds": Array [
          Object {
            "Ref": "Dev01VpcProtectedSubnet1Subnet23229EC2",
          },
          Object {
            "Ref": "Dev01VpcProtectedSubnet2SubnetE31E0056",
          },
          Object {
            "Ref": "Dev01VpcProtectedSubnet3SubnetA3EA6483",
          },
        ],
        "VpcEndpointType": "Interface",
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::VPCEndpoint",
    },
    "Dev01VpcEcrDkrEndpointForPrivateSecurityGroup6F318001": Object {
      "Properties": Object {
        "GroupDescription": "Dev01-ShareResources/Dev01-Vpc/Vpc/EcrDkrEndpointForPrivate/SecurityGroup",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "SecurityGroupIngress": Array [
          Object {
            "CidrIp": Object {
              "Fn::GetAtt": Array [
                "Dev01Vpc877F445C",
                "CidrBlock",
              ],
            },
            "Description": Object {
              "Fn::Join": Array [
                "",
                Array [
                  "from ",
                  Object {
                    "Fn::GetAtt": Array [
                      "Dev01Vpc877F445C",
                      "CidrBlock",
                    ],
                  },
                  ":443",
                ],
              ],
            },
            "FromPort": 443,
            "IpProtocol": "tcp",
            "ToPort": 443,
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "Dev01VpcEcrEndpointForPrivateC89376C1": Object {
      "Properties": Object {
        "PrivateDnsEnabled": true,
        "SecurityGroupIds": Array [
          Object {
            "Fn::GetAtt": Array [
              "Dev01VpcEcrEndpointForPrivateSecurityGroupDEBC4996",
              "GroupId",
            ],
          },
        ],
        "ServiceName": "com.amazonaws.ap-northeast-1.ecr.api",
        "SubnetIds": Array [
          Object {
            "Ref": "Dev01VpcProtectedSubnet1Subnet23229EC2",
          },
          Object {
            "Ref": "Dev01VpcProtectedSubnet2SubnetE31E0056",
          },
          Object {
            "Ref": "Dev01VpcProtectedSubnet3SubnetA3EA6483",
          },
        ],
        "VpcEndpointType": "Interface",
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::VPCEndpoint",
    },
    "Dev01VpcEcrEndpointForPrivateSecurityGroupDEBC4996": Object {
      "Properties": Object {
        "GroupDescription": "Dev01-ShareResources/Dev01-Vpc/Vpc/EcrEndpointForPrivate/SecurityGroup",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "SecurityGroupIngress": Array [
          Object {
            "CidrIp": Object {
              "Fn::GetAtt": Array [
                "Dev01Vpc877F445C",
                "CidrBlock",
              ],
            },
            "Description": Object {
              "Fn::Join": Array [
                "",
                Array [
                  "from ",
                  Object {
                    "Fn::GetAtt": Array [
                      "Dev01Vpc877F445C",
                      "CidrBlock",
                    ],
                  },
                  ":443",
                ],
              ],
            },
            "FromPort": 443,
            "IpProtocol": "tcp",
            "ToPort": 443,
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "Dev01VpcFlowLogBucketAutoDeleteObjectsCustomResource3C1A4B87": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "Dev01VpcFlowLogBucketPolicy4934D2DC",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "Dev01VpcFlowLogBucketE33D1C02",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "Dev01VpcFlowLogBucketE33D1C02": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "KMSMasterKeyID": Object {
                  "Fn::GetAtt": Array [
                    "Dev01VpcKey2A43D420",
                    "Arn",
                  ],
                },
                "SSEAlgorithm": "aws:kms",
              },
            },
          ],
        },
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "ExpirationInDays": 1825,
              "Status": "Enabled",
            },
            Object {
              "AbortIncompleteMultipartUpload": Object {
                "DaysAfterInitiation": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "Dev01VpcFlowLogBucketPolicy4934D2DC": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "Dev01VpcFlowLogBucketE33D1C02",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "Dev01VpcFlowLogBucketE33D1C02",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "Dev01VpcFlowLogBucketE33D1C02",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "Dev01VpcFlowLogBucketE33D1C02",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "Dev01VpcFlowLogBucketE33D1C02",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "Dev01VpcFlowLogsFlowLog3658F6DC": Object {
      "DependsOn": Array [
        "Dev01VpcFlowLogBucketAutoDeleteObjectsCustomResource3C1A4B87",
        "Dev01VpcFlowLogBucketPolicy4934D2DC",
        "Dev01VpcFlowLogBucketE33D1C02",
      ],
      "Properties": Object {
        "LogDestination": Object {
          "Fn::GetAtt": Array [
            "Dev01VpcFlowLogBucketE33D1C02",
            "Arn",
          ],
        },
        "LogDestinationType": "s3",
        "ResourceId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
        "ResourceType": "VPC",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/FlowLogs",
          },
        ],
        "TrafficType": "ALL",
      },
      "Type": "AWS::EC2::FlowLog",
    },
    "Dev01VpcIGW30EA9F4B": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc",
          },
        ],
      },
      "Type": "AWS::EC2::InternetGateway",
    },
    "Dev01VpcKey2A43D420": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "Description": "for VPC Flow log",
        "EnableKeyRotation": true,
        "KeyPolicy": Object {
          "Statement": Array [
            Object {
              "Action": "kms:*",
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":iam::************:root",
                    ],
                  ],
                },
              },
              "Resource": "*",
            },
            Object {
              "Action": Array [
                "kms:Encrypt*",
                "kms:Decrypt*",
                "kms:ReEncrypt*",
                "kms:GenerateDataKey*",
                "kms:Describe*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "Service": "delivery.logs.amazonaws.com",
              },
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PendingWindowInDays": 7,
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::KMS::Key",
      "UpdateReplacePolicy": "Delete",
    },
    "Dev01VpcKeyAlias1229DA5D": Object {
      "Properties": Object {
        "AliasName": "alias/Dev01-Vpc-for-flowlog",
        "TargetKeyId": Object {
          "Fn::GetAtt": Array [
            "Dev01VpcKey2A43D420",
            "Arn",
          ],
        },
      },
      "Type": "AWS::KMS::Alias",
    },
    "Dev01VpcLogsEndpointForPrivateEF3DD14B": Object {
      "Properties": Object {
        "PrivateDnsEnabled": true,
        "SecurityGroupIds": Array [
          Object {
            "Fn::GetAtt": Array [
              "Dev01VpcLogsEndpointForPrivateSecurityGroup91443D9C",
              "GroupId",
            ],
          },
        ],
        "ServiceName": "com.amazonaws.ap-northeast-1.logs",
        "SubnetIds": Array [
          Object {
            "Ref": "Dev01VpcProtectedSubnet1Subnet23229EC2",
          },
          Object {
            "Ref": "Dev01VpcProtectedSubnet2SubnetE31E0056",
          },
          Object {
            "Ref": "Dev01VpcProtectedSubnet3SubnetA3EA6483",
          },
        ],
        "VpcEndpointType": "Interface",
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::VPCEndpoint",
    },
    "Dev01VpcLogsEndpointForPrivateSecurityGroup91443D9C": Object {
      "Properties": Object {
        "GroupDescription": "Dev01-ShareResources/Dev01-Vpc/Vpc/LogsEndpointForPrivate/SecurityGroup",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "SecurityGroupIngress": Array [
          Object {
            "CidrIp": Object {
              "Fn::GetAtt": Array [
                "Dev01Vpc877F445C",
                "CidrBlock",
              ],
            },
            "Description": Object {
              "Fn::Join": Array [
                "",
                Array [
                  "from ",
                  Object {
                    "Fn::GetAtt": Array [
                      "Dev01Vpc877F445C",
                      "CidrBlock",
                    ],
                  },
                  ":443",
                ],
              ],
            },
            "FromPort": 443,
            "IpProtocol": "tcp",
            "ToPort": 443,
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "Dev01VpcNaclPrivate6079F8AF": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/NaclPrivate",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::NetworkAcl",
    },
    "Dev01VpcNaclPrivateDefaultAssociationDev01ShareResourcesDev01VpcPrivateSubnet1C5BB03BCDF975B04": Object {
      "Properties": Object {
        "NetworkAclId": Object {
          "Ref": "Dev01VpcNaclPrivate6079F8AF",
        },
        "SubnetId": Object {
          "Ref": "Dev01VpcPrivateSubnet1SubnetBB806C4A",
        },
      },
      "Type": "AWS::EC2::SubnetNetworkAclAssociation",
    },
    "Dev01VpcNaclPrivateDefaultAssociationDev01ShareResourcesDev01VpcPrivateSubnet2BF3FEDF09DA4E05E": Object {
      "Properties": Object {
        "NetworkAclId": Object {
          "Ref": "Dev01VpcNaclPrivate6079F8AF",
        },
        "SubnetId": Object {
          "Ref": "Dev01VpcPrivateSubnet2SubnetCD93D031",
        },
      },
      "Type": "AWS::EC2::SubnetNetworkAclAssociation",
    },
    "Dev01VpcNaclPrivateDefaultAssociationDev01ShareResourcesDev01VpcPrivateSubnet304A4289EE39885F2": Object {
      "Properties": Object {
        "NetworkAclId": Object {
          "Ref": "Dev01VpcNaclPrivate6079F8AF",
        },
        "SubnetId": Object {
          "Ref": "Dev01VpcPrivateSubnet3Subnet665BA903",
        },
      },
      "Type": "AWS::EC2::SubnetNetworkAclAssociation",
    },
    "Dev01VpcNaclPrivateNaclEgressPrivate0CFF05CC": Object {
      "Properties": Object {
        "CidrBlock": "0.0.0.0/0",
        "Egress": true,
        "NetworkAclId": Object {
          "Ref": "Dev01VpcNaclPrivate6079F8AF",
        },
        "Protocol": -1,
        "RuleAction": "allow",
        "RuleNumber": 100,
      },
      "Type": "AWS::EC2::NetworkAclEntry",
    },
    "Dev01VpcNaclPrivateNaclIngressPrivate34B8DF1B": Object {
      "Properties": Object {
        "CidrBlock": "0.0.0.0/0",
        "Egress": false,
        "NetworkAclId": Object {
          "Ref": "Dev01VpcNaclPrivate6079F8AF",
        },
        "Protocol": -1,
        "RuleAction": "allow",
        "RuleNumber": 120,
      },
      "Type": "AWS::EC2::NetworkAclEntry",
    },
    "Dev01VpcNaclPublic460E32A4": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/NaclPublic",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::NetworkAcl",
    },
    "Dev01VpcNaclPublicDefaultAssociationDev01ShareResourcesDev01VpcPublicSubnet1B7DDEF134F0D2906": Object {
      "Properties": Object {
        "NetworkAclId": Object {
          "Ref": "Dev01VpcNaclPublic460E32A4",
        },
        "SubnetId": Object {
          "Ref": "Dev01VpcPublicSubnet1SubnetADDE854C",
        },
      },
      "Type": "AWS::EC2::SubnetNetworkAclAssociation",
    },
    "Dev01VpcNaclPublicDefaultAssociationDev01ShareResourcesDev01VpcPublicSubnet25DEC827DA3D16C0A": Object {
      "Properties": Object {
        "NetworkAclId": Object {
          "Ref": "Dev01VpcNaclPublic460E32A4",
        },
        "SubnetId": Object {
          "Ref": "Dev01VpcPublicSubnet2Subnet2ED475D5",
        },
      },
      "Type": "AWS::EC2::SubnetNetworkAclAssociation",
    },
    "Dev01VpcNaclPublicDefaultAssociationDev01ShareResourcesDev01VpcPublicSubnet373582C2B1446A2C4": Object {
      "Properties": Object {
        "NetworkAclId": Object {
          "Ref": "Dev01VpcNaclPublic460E32A4",
        },
        "SubnetId": Object {
          "Ref": "Dev01VpcPublicSubnet3SubnetAAA122AE",
        },
      },
      "Type": "AWS::EC2::SubnetNetworkAclAssociation",
    },
    "Dev01VpcNaclPublicNaclEgressPublicDB18733E": Object {
      "Properties": Object {
        "CidrBlock": "0.0.0.0/0",
        "Egress": true,
        "NetworkAclId": Object {
          "Ref": "Dev01VpcNaclPublic460E32A4",
        },
        "Protocol": -1,
        "RuleAction": "allow",
        "RuleNumber": 100,
      },
      "Type": "AWS::EC2::NetworkAclEntry",
    },
    "Dev01VpcNaclPublicNaclIngressPublic40F0EEA4": Object {
      "Properties": Object {
        "CidrBlock": "0.0.0.0/0",
        "Egress": false,
        "NetworkAclId": Object {
          "Ref": "Dev01VpcNaclPublic460E32A4",
        },
        "Protocol": -1,
        "RuleAction": "allow",
        "RuleNumber": 100,
      },
      "Type": "AWS::EC2::NetworkAclEntry",
    },
    "Dev01VpcPrivateSubnet1DefaultRoute13489FDA": Object {
      "Properties": Object {
        "DestinationCidrBlock": "0.0.0.0/0",
        "NatGatewayId": Object {
          "Ref": "Dev01VpcPublicSubnet1NATGatewayBF18FE7A",
        },
        "RouteTableId": Object {
          "Ref": "Dev01VpcPrivateSubnet1RouteTable45D55E2C",
        },
      },
      "Type": "AWS::EC2::Route",
    },
    "Dev01VpcPrivateSubnet1RouteTable45D55E2C": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/PrivateSubnet1",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::RouteTable",
    },
    "Dev01VpcPrivateSubnet1RouteTableAssociationD7D40D87": Object {
      "Properties": Object {
        "RouteTableId": Object {
          "Ref": "Dev01VpcPrivateSubnet1RouteTable45D55E2C",
        },
        "SubnetId": Object {
          "Ref": "Dev01VpcPrivateSubnet1SubnetBB806C4A",
        },
      },
      "Type": "AWS::EC2::SubnetRouteTableAssociation",
    },
    "Dev01VpcPrivateSubnet1SubnetBB806C4A": Object {
      "Properties": Object {
        "AvailabilityZone": "dummy1a",
        "CidrBlock": "**********/18",
        "MapPublicIpOnLaunch": false,
        "Tags": Array [
          Object {
            "Key": "aws-cdk:subnet-name",
            "Value": "Private",
          },
          Object {
            "Key": "aws-cdk:subnet-type",
            "Value": "Private",
          },
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/PrivateSubnet1",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::Subnet",
    },
    "Dev01VpcPrivateSubnet2DefaultRoute528CAEED": Object {
      "Properties": Object {
        "DestinationCidrBlock": "0.0.0.0/0",
        "NatGatewayId": Object {
          "Ref": "Dev01VpcPublicSubnet2NATGatewayB0823BF6",
        },
        "RouteTableId": Object {
          "Ref": "Dev01VpcPrivateSubnet2RouteTable96F7BA4D",
        },
      },
      "Type": "AWS::EC2::Route",
    },
    "Dev01VpcPrivateSubnet2RouteTable96F7BA4D": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/PrivateSubnet2",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::RouteTable",
    },
    "Dev01VpcPrivateSubnet2RouteTableAssociationE73F0253": Object {
      "Properties": Object {
        "RouteTableId": Object {
          "Ref": "Dev01VpcPrivateSubnet2RouteTable96F7BA4D",
        },
        "SubnetId": Object {
          "Ref": "Dev01VpcPrivateSubnet2SubnetCD93D031",
        },
      },
      "Type": "AWS::EC2::SubnetRouteTableAssociation",
    },
    "Dev01VpcPrivateSubnet2SubnetCD93D031": Object {
      "Properties": Object {
        "AvailabilityZone": "dummy1b",
        "CidrBlock": "***********/18",
        "MapPublicIpOnLaunch": false,
        "Tags": Array [
          Object {
            "Key": "aws-cdk:subnet-name",
            "Value": "Private",
          },
          Object {
            "Key": "aws-cdk:subnet-type",
            "Value": "Private",
          },
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/PrivateSubnet2",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::Subnet",
    },
    "Dev01VpcPrivateSubnet3DefaultRoute2CE3B53D": Object {
      "Properties": Object {
        "DestinationCidrBlock": "0.0.0.0/0",
        "NatGatewayId": Object {
          "Ref": "Dev01VpcPublicSubnet3NATGatewayD3BE3B5C",
        },
        "RouteTableId": Object {
          "Ref": "Dev01VpcPrivateSubnet3RouteTable682B23FC",
        },
      },
      "Type": "AWS::EC2::Route",
    },
    "Dev01VpcPrivateSubnet3RouteTable682B23FC": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/PrivateSubnet3",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::RouteTable",
    },
    "Dev01VpcPrivateSubnet3RouteTableAssociation5D475234": Object {
      "Properties": Object {
        "RouteTableId": Object {
          "Ref": "Dev01VpcPrivateSubnet3RouteTable682B23FC",
        },
        "SubnetId": Object {
          "Ref": "Dev01VpcPrivateSubnet3Subnet665BA903",
        },
      },
      "Type": "AWS::EC2::SubnetRouteTableAssociation",
    },
    "Dev01VpcPrivateSubnet3Subnet665BA903": Object {
      "Properties": Object {
        "AvailabilityZone": "dummy1c",
        "CidrBlock": "************/18",
        "MapPublicIpOnLaunch": false,
        "Tags": Array [
          Object {
            "Key": "aws-cdk:subnet-name",
            "Value": "Private",
          },
          Object {
            "Key": "aws-cdk:subnet-type",
            "Value": "Private",
          },
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/PrivateSubnet3",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::Subnet",
    },
    "Dev01VpcProtectedSubnet1RouteTable00E42F31": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/ProtectedSubnet1",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::RouteTable",
    },
    "Dev01VpcProtectedSubnet1RouteTableAssociation123EAADF": Object {
      "Properties": Object {
        "RouteTableId": Object {
          "Ref": "Dev01VpcProtectedSubnet1RouteTable00E42F31",
        },
        "SubnetId": Object {
          "Ref": "Dev01VpcProtectedSubnet1Subnet23229EC2",
        },
      },
      "Type": "AWS::EC2::SubnetRouteTableAssociation",
    },
    "Dev01VpcProtectedSubnet1Subnet23229EC2": Object {
      "Properties": Object {
        "AvailabilityZone": "dummy1a",
        "CidrBlock": "************/22",
        "MapPublicIpOnLaunch": false,
        "Tags": Array [
          Object {
            "Key": "aws-cdk:subnet-name",
            "Value": "Protected",
          },
          Object {
            "Key": "aws-cdk:subnet-type",
            "Value": "Isolated",
          },
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/ProtectedSubnet1",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::Subnet",
    },
    "Dev01VpcProtectedSubnet2RouteTableA3A90132": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/ProtectedSubnet2",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::RouteTable",
    },
    "Dev01VpcProtectedSubnet2RouteTableAssociation9EAE1D97": Object {
      "Properties": Object {
        "RouteTableId": Object {
          "Ref": "Dev01VpcProtectedSubnet2RouteTableA3A90132",
        },
        "SubnetId": Object {
          "Ref": "Dev01VpcProtectedSubnet2SubnetE31E0056",
        },
      },
      "Type": "AWS::EC2::SubnetRouteTableAssociation",
    },
    "Dev01VpcProtectedSubnet2SubnetE31E0056": Object {
      "Properties": Object {
        "AvailabilityZone": "dummy1b",
        "CidrBlock": "************/22",
        "MapPublicIpOnLaunch": false,
        "Tags": Array [
          Object {
            "Key": "aws-cdk:subnet-name",
            "Value": "Protected",
          },
          Object {
            "Key": "aws-cdk:subnet-type",
            "Value": "Isolated",
          },
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/ProtectedSubnet2",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::Subnet",
    },
    "Dev01VpcProtectedSubnet3RouteTableAssociation545B5936": Object {
      "Properties": Object {
        "RouteTableId": Object {
          "Ref": "Dev01VpcProtectedSubnet3RouteTableD4EAFABE",
        },
        "SubnetId": Object {
          "Ref": "Dev01VpcProtectedSubnet3SubnetA3EA6483",
        },
      },
      "Type": "AWS::EC2::SubnetRouteTableAssociation",
    },
    "Dev01VpcProtectedSubnet3RouteTableD4EAFABE": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/ProtectedSubnet3",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::RouteTable",
    },
    "Dev01VpcProtectedSubnet3SubnetA3EA6483": Object {
      "Properties": Object {
        "AvailabilityZone": "dummy1c",
        "CidrBlock": "************/22",
        "MapPublicIpOnLaunch": false,
        "Tags": Array [
          Object {
            "Key": "aws-cdk:subnet-name",
            "Value": "Protected",
          },
          Object {
            "Key": "aws-cdk:subnet-type",
            "Value": "Isolated",
          },
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/ProtectedSubnet3",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::Subnet",
    },
    "Dev01VpcPublicSubnet1DefaultRoute94BF465D": Object {
      "DependsOn": Array [
        "Dev01VpcVPCGW038CF5D7",
      ],
      "Properties": Object {
        "DestinationCidrBlock": "0.0.0.0/0",
        "GatewayId": Object {
          "Ref": "Dev01VpcIGW30EA9F4B",
        },
        "RouteTableId": Object {
          "Ref": "Dev01VpcPublicSubnet1RouteTable05D70546",
        },
      },
      "Type": "AWS::EC2::Route",
    },
    "Dev01VpcPublicSubnet1EIPF340CBF4": Object {
      "Properties": Object {
        "Domain": "vpc",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/PublicSubnet1",
          },
        ],
      },
      "Type": "AWS::EC2::EIP",
    },
    "Dev01VpcPublicSubnet1NATGatewayBF18FE7A": Object {
      "DependsOn": Array [
        "Dev01VpcPublicSubnet1DefaultRoute94BF465D",
        "Dev01VpcPublicSubnet1RouteTableAssociation04CAB82E",
      ],
      "Properties": Object {
        "AllocationId": Object {
          "Fn::GetAtt": Array [
            "Dev01VpcPublicSubnet1EIPF340CBF4",
            "AllocationId",
          ],
        },
        "SubnetId": Object {
          "Ref": "Dev01VpcPublicSubnet1SubnetADDE854C",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/PublicSubnet1",
          },
        ],
      },
      "Type": "AWS::EC2::NatGateway",
    },
    "Dev01VpcPublicSubnet1RouteTable05D70546": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/PublicSubnet1",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::RouteTable",
    },
    "Dev01VpcPublicSubnet1RouteTableAssociation04CAB82E": Object {
      "Properties": Object {
        "RouteTableId": Object {
          "Ref": "Dev01VpcPublicSubnet1RouteTable05D70546",
        },
        "SubnetId": Object {
          "Ref": "Dev01VpcPublicSubnet1SubnetADDE854C",
        },
      },
      "Type": "AWS::EC2::SubnetRouteTableAssociation",
    },
    "Dev01VpcPublicSubnet1SubnetADDE854C": Object {
      "Properties": Object {
        "AvailabilityZone": "dummy1a",
        "CidrBlock": "************/24",
        "MapPublicIpOnLaunch": true,
        "Tags": Array [
          Object {
            "Key": "aws-cdk:subnet-name",
            "Value": "Public",
          },
          Object {
            "Key": "aws-cdk:subnet-type",
            "Value": "Public",
          },
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/PublicSubnet1",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::Subnet",
    },
    "Dev01VpcPublicSubnet2DefaultRouteF3C49B05": Object {
      "DependsOn": Array [
        "Dev01VpcVPCGW038CF5D7",
      ],
      "Properties": Object {
        "DestinationCidrBlock": "0.0.0.0/0",
        "GatewayId": Object {
          "Ref": "Dev01VpcIGW30EA9F4B",
        },
        "RouteTableId": Object {
          "Ref": "Dev01VpcPublicSubnet2RouteTable39958795",
        },
      },
      "Type": "AWS::EC2::Route",
    },
    "Dev01VpcPublicSubnet2EIP8F62E16A": Object {
      "Properties": Object {
        "Domain": "vpc",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/PublicSubnet2",
          },
        ],
      },
      "Type": "AWS::EC2::EIP",
    },
    "Dev01VpcPublicSubnet2NATGatewayB0823BF6": Object {
      "DependsOn": Array [
        "Dev01VpcPublicSubnet2DefaultRouteF3C49B05",
        "Dev01VpcPublicSubnet2RouteTableAssociation3A849813",
      ],
      "Properties": Object {
        "AllocationId": Object {
          "Fn::GetAtt": Array [
            "Dev01VpcPublicSubnet2EIP8F62E16A",
            "AllocationId",
          ],
        },
        "SubnetId": Object {
          "Ref": "Dev01VpcPublicSubnet2Subnet2ED475D5",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/PublicSubnet2",
          },
        ],
      },
      "Type": "AWS::EC2::NatGateway",
    },
    "Dev01VpcPublicSubnet2RouteTable39958795": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/PublicSubnet2",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::RouteTable",
    },
    "Dev01VpcPublicSubnet2RouteTableAssociation3A849813": Object {
      "Properties": Object {
        "RouteTableId": Object {
          "Ref": "Dev01VpcPublicSubnet2RouteTable39958795",
        },
        "SubnetId": Object {
          "Ref": "Dev01VpcPublicSubnet2Subnet2ED475D5",
        },
      },
      "Type": "AWS::EC2::SubnetRouteTableAssociation",
    },
    "Dev01VpcPublicSubnet2Subnet2ED475D5": Object {
      "Properties": Object {
        "AvailabilityZone": "dummy1b",
        "CidrBlock": "************/24",
        "MapPublicIpOnLaunch": true,
        "Tags": Array [
          Object {
            "Key": "aws-cdk:subnet-name",
            "Value": "Public",
          },
          Object {
            "Key": "aws-cdk:subnet-type",
            "Value": "Public",
          },
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/PublicSubnet2",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::Subnet",
    },
    "Dev01VpcPublicSubnet3DefaultRouteE472929B": Object {
      "DependsOn": Array [
        "Dev01VpcVPCGW038CF5D7",
      ],
      "Properties": Object {
        "DestinationCidrBlock": "0.0.0.0/0",
        "GatewayId": Object {
          "Ref": "Dev01VpcIGW30EA9F4B",
        },
        "RouteTableId": Object {
          "Ref": "Dev01VpcPublicSubnet3RouteTable0634D0B7",
        },
      },
      "Type": "AWS::EC2::Route",
    },
    "Dev01VpcPublicSubnet3EIP77180147": Object {
      "Properties": Object {
        "Domain": "vpc",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/PublicSubnet3",
          },
        ],
      },
      "Type": "AWS::EC2::EIP",
    },
    "Dev01VpcPublicSubnet3NATGatewayD3BE3B5C": Object {
      "DependsOn": Array [
        "Dev01VpcPublicSubnet3DefaultRouteE472929B",
        "Dev01VpcPublicSubnet3RouteTableAssociation2315A1AE",
      ],
      "Properties": Object {
        "AllocationId": Object {
          "Fn::GetAtt": Array [
            "Dev01VpcPublicSubnet3EIP77180147",
            "AllocationId",
          ],
        },
        "SubnetId": Object {
          "Ref": "Dev01VpcPublicSubnet3SubnetAAA122AE",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/PublicSubnet3",
          },
        ],
      },
      "Type": "AWS::EC2::NatGateway",
    },
    "Dev01VpcPublicSubnet3RouteTable0634D0B7": Object {
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/PublicSubnet3",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::RouteTable",
    },
    "Dev01VpcPublicSubnet3RouteTableAssociation2315A1AE": Object {
      "Properties": Object {
        "RouteTableId": Object {
          "Ref": "Dev01VpcPublicSubnet3RouteTable0634D0B7",
        },
        "SubnetId": Object {
          "Ref": "Dev01VpcPublicSubnet3SubnetAAA122AE",
        },
      },
      "Type": "AWS::EC2::SubnetRouteTableAssociation",
    },
    "Dev01VpcPublicSubnet3SubnetAAA122AE": Object {
      "Properties": Object {
        "AvailabilityZone": "dummy1c",
        "CidrBlock": "************/24",
        "MapPublicIpOnLaunch": true,
        "Tags": Array [
          Object {
            "Key": "aws-cdk:subnet-name",
            "Value": "Public",
          },
          Object {
            "Key": "aws-cdk:subnet-type",
            "Value": "Public",
          },
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc/PublicSubnet3",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::Subnet",
    },
    "Dev01VpcS3EndpointForPrivateE247CD04": Object {
      "Properties": Object {
        "RouteTableIds": Array [
          Object {
            "Ref": "Dev01VpcPrivateSubnet1RouteTable45D55E2C",
          },
          Object {
            "Ref": "Dev01VpcPrivateSubnet2RouteTable96F7BA4D",
          },
          Object {
            "Ref": "Dev01VpcPrivateSubnet3RouteTable682B23FC",
          },
          Object {
            "Ref": "Dev01VpcProtectedSubnet1RouteTable00E42F31",
          },
          Object {
            "Ref": "Dev01VpcProtectedSubnet2RouteTableA3A90132",
          },
          Object {
            "Ref": "Dev01VpcProtectedSubnet3RouteTableD4EAFABE",
          },
        ],
        "ServiceName": Object {
          "Fn::Join": Array [
            "",
            Array [
              "com.amazonaws.",
              Object {
                "Ref": "AWS::Region",
              },
              ".s3",
            ],
          ],
        },
        "VpcEndpointType": "Gateway",
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::VPCEndpoint",
    },
    "Dev01VpcSsmEndpointForPrivate3EAACC25": Object {
      "Properties": Object {
        "PrivateDnsEnabled": true,
        "SecurityGroupIds": Array [
          Object {
            "Fn::GetAtt": Array [
              "Dev01VpcSsmEndpointForPrivateSecurityGroupD15D6C88",
              "GroupId",
            ],
          },
        ],
        "ServiceName": "com.amazonaws.ap-northeast-1.ssm",
        "SubnetIds": Array [
          Object {
            "Ref": "Dev01VpcProtectedSubnet1Subnet23229EC2",
          },
          Object {
            "Ref": "Dev01VpcProtectedSubnet2SubnetE31E0056",
          },
          Object {
            "Ref": "Dev01VpcProtectedSubnet3SubnetA3EA6483",
          },
        ],
        "VpcEndpointType": "Interface",
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::VPCEndpoint",
    },
    "Dev01VpcSsmEndpointForPrivateSecurityGroupD15D6C88": Object {
      "Properties": Object {
        "GroupDescription": "Dev01-ShareResources/Dev01-Vpc/Vpc/SsmEndpointForPrivate/SecurityGroup",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "SecurityGroupIngress": Array [
          Object {
            "CidrIp": Object {
              "Fn::GetAtt": Array [
                "Dev01Vpc877F445C",
                "CidrBlock",
              ],
            },
            "Description": Object {
              "Fn::Join": Array [
                "",
                Array [
                  "from ",
                  Object {
                    "Fn::GetAtt": Array [
                      "Dev01Vpc877F445C",
                      "CidrBlock",
                    ],
                  },
                  ":443",
                ],
              ],
            },
            "FromPort": 443,
            "IpProtocol": "tcp",
            "ToPort": 443,
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "Dev01VpcSsmMessagesEndpointForPrivate660D4848": Object {
      "Properties": Object {
        "PrivateDnsEnabled": true,
        "SecurityGroupIds": Array [
          Object {
            "Fn::GetAtt": Array [
              "Dev01VpcSsmMessagesEndpointForPrivateSecurityGroupA61F2FC6",
              "GroupId",
            ],
          },
        ],
        "ServiceName": "com.amazonaws.ap-northeast-1.ssmmessages",
        "SubnetIds": Array [
          Object {
            "Ref": "Dev01VpcProtectedSubnet1Subnet23229EC2",
          },
          Object {
            "Ref": "Dev01VpcProtectedSubnet2SubnetE31E0056",
          },
          Object {
            "Ref": "Dev01VpcProtectedSubnet3SubnetA3EA6483",
          },
        ],
        "VpcEndpointType": "Interface",
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::VPCEndpoint",
    },
    "Dev01VpcSsmMessagesEndpointForPrivateSecurityGroupA61F2FC6": Object {
      "Properties": Object {
        "GroupDescription": "Dev01-ShareResources/Dev01-Vpc/Vpc/SsmMessagesEndpointForPrivate/SecurityGroup",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "SecurityGroupIngress": Array [
          Object {
            "CidrIp": Object {
              "Fn::GetAtt": Array [
                "Dev01Vpc877F445C",
                "CidrBlock",
              ],
            },
            "Description": Object {
              "Fn::Join": Array [
                "",
                Array [
                  "from ",
                  Object {
                    "Fn::GetAtt": Array [
                      "Dev01Vpc877F445C",
                      "CidrBlock",
                    ],
                  },
                  ":443",
                ],
              ],
            },
            "FromPort": 443,
            "IpProtocol": "tcp",
            "ToPort": 443,
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
          Object {
            "Key": "Name",
            "Value": "Dev01-ShareResources/Dev01-Vpc/Vpc",
          },
        ],
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "Dev01VpcVPCGW038CF5D7": Object {
      "Properties": Object {
        "InternetGatewayId": Object {
          "Ref": "Dev01VpcIGW30EA9F4B",
        },
        "VpcId": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::EC2::VPCGatewayAttachment",
    },
    "FluentbitDockerDeployImageCustomResourceC0B08756": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "DestImage": Object {
          "Fn::Join": Array [
            "",
            Array [
              "docker://",
              Object {
                "Fn::Select": Array [
                  4,
                  Object {
                    "Fn::Split": Array [
                      ":",
                      Object {
                        "Fn::GetAtt": Array [
                          "FluentbitDockerfluentbitrepo09B43BE7",
                          "Arn",
                        ],
                      },
                    ],
                  },
                ],
              },
              ".dkr.ecr.",
              Object {
                "Fn::Select": Array [
                  3,
                  Object {
                    "Fn::Split": Array [
                      ":",
                      Object {
                        "Fn::GetAtt": Array [
                          "FluentbitDockerfluentbitrepo09B43BE7",
                          "Arn",
                        ],
                      },
                    ],
                  },
                ],
              },
              ".",
              Object {
                "Ref": "AWS::URLSuffix",
              },
              "/",
              Object {
                "Ref": "FluentbitDockerfluentbitrepo09B43BE7",
              },
              ":fluentbitimage",
            ],
          ],
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomCDKECRDeploymentbd07c930edb94112a20f03f096f53666512MiB28EAD8E4",
            "Arn",
          ],
        },
        "SrcImage": "docker://public.ecr.aws/aws-observability/aws-for-fluent-bit:stable",
      },
      "Type": "Custom::CDKBucketDeployment",
      "UpdateReplacePolicy": "Delete",
    },
    "FluentbitDockerfluentbitImageECRDeploymentGoCustomResource5142C11E": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "DestImage": Object {
          "Fn::Join": Array [
            "",
            Array [
              "docker://",
              Object {
                "Fn::Select": Array [
                  4,
                  Object {
                    "Fn::Split": Array [
                      ":",
                      Object {
                        "Fn::GetAtt": Array [
                          "FluentbitDockerfluentbitrepo09B43BE7",
                          "Arn",
                        ],
                      },
                    ],
                  },
                ],
              },
              ".dkr.ecr.",
              Object {
                "Fn::Select": Array [
                  3,
                  Object {
                    "Fn::Split": Array [
                      ":",
                      Object {
                        "Fn::GetAtt": Array [
                          "FluentbitDockerfluentbitrepo09B43BE7",
                          "Arn",
                        ],
                      },
                    ],
                  },
                ],
              },
              ".",
              Object {
                "Ref": "AWS::URLSuffix",
              },
              "/",
              Object {
                "Ref": "FluentbitDockerfluentbitrepo09B43BE7",
              },
              ":fluentbitimagego",
            ],
          ],
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomCDKECRDeploymentbd07c930edb94112a20f03f096f53666512MiB28EAD8E4",
            "Arn",
          ],
        },
        "SrcImage": Object {
          "Fn::Join": Array [
            "",
            Array [
              "docker://",
              Object {
                "Fn::Sub": "************.dkr.ecr.ap-northeast-1.\${AWS::URLSuffix}/cdk-hnb659fds-container-assets-************-ap-northeast-1:5837adffd71a0ee1e2e256ce8824e81194633ec9d861e3410847b53cb433f539",
              },
            ],
          ],
        },
      },
      "Type": "Custom::CDKBucketDeployment",
      "UpdateReplacePolicy": "Delete",
    },
    "FluentbitDockerfluentbitImageECRDeploymentJsonCustomResourceED0606D0": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "DestImage": Object {
          "Fn::Join": Array [
            "",
            Array [
              "docker://",
              Object {
                "Fn::Select": Array [
                  4,
                  Object {
                    "Fn::Split": Array [
                      ":",
                      Object {
                        "Fn::GetAtt": Array [
                          "FluentbitDockerfluentbitrepo09B43BE7",
                          "Arn",
                        ],
                      },
                    ],
                  },
                ],
              },
              ".dkr.ecr.",
              Object {
                "Fn::Select": Array [
                  3,
                  Object {
                    "Fn::Split": Array [
                      ":",
                      Object {
                        "Fn::GetAtt": Array [
                          "FluentbitDockerfluentbitrepo09B43BE7",
                          "Arn",
                        ],
                      },
                    ],
                  },
                ],
              },
              ".",
              Object {
                "Ref": "AWS::URLSuffix",
              },
              "/",
              Object {
                "Ref": "FluentbitDockerfluentbitrepo09B43BE7",
              },
              ":fluentbitimagejson",
            ],
          ],
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomCDKECRDeploymentbd07c930edb94112a20f03f096f53666512MiB28EAD8E4",
            "Arn",
          ],
        },
        "SrcImage": Object {
          "Fn::Join": Array [
            "",
            Array [
              "docker://",
              Object {
                "Fn::Sub": "************.dkr.ecr.ap-northeast-1.\${AWS::URLSuffix}/cdk-hnb659fds-container-assets-************-ap-northeast-1:d6f621e4172ab5d741ca27ea967c43fd22e102d7fadbdf0ef3f25c87ff2519cd",
              },
            ],
          ],
        },
      },
      "Type": "Custom::CDKBucketDeployment",
      "UpdateReplacePolicy": "Delete",
    },
    "FluentbitDockerfluentbitImageECRDeploymentPythonCustomResource235A9700": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "DestImage": Object {
          "Fn::Join": Array [
            "",
            Array [
              "docker://",
              Object {
                "Fn::Select": Array [
                  4,
                  Object {
                    "Fn::Split": Array [
                      ":",
                      Object {
                        "Fn::GetAtt": Array [
                          "FluentbitDockerfluentbitrepo09B43BE7",
                          "Arn",
                        ],
                      },
                    ],
                  },
                ],
              },
              ".dkr.ecr.",
              Object {
                "Fn::Select": Array [
                  3,
                  Object {
                    "Fn::Split": Array [
                      ":",
                      Object {
                        "Fn::GetAtt": Array [
                          "FluentbitDockerfluentbitrepo09B43BE7",
                          "Arn",
                        ],
                      },
                    ],
                  },
                ],
              },
              ".",
              Object {
                "Ref": "AWS::URLSuffix",
              },
              "/",
              Object {
                "Ref": "FluentbitDockerfluentbitrepo09B43BE7",
              },
              ":fluentbitimagepython",
            ],
          ],
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomCDKECRDeploymentbd07c930edb94112a20f03f096f53666512MiB28EAD8E4",
            "Arn",
          ],
        },
        "SrcImage": Object {
          "Fn::Join": Array [
            "",
            Array [
              "docker://",
              Object {
                "Fn::Sub": "************.dkr.ecr.ap-northeast-1.\${AWS::URLSuffix}/cdk-hnb659fds-container-assets-************-ap-northeast-1:dbfeb3e1b5db269da19226de54a3605ae989f843897fb17ff8bdecb5a789a1bf",
              },
            ],
          ],
        },
      },
      "Type": "Custom::CDKBucketDeployment",
      "UpdateReplacePolicy": "Delete",
    },
    "FluentbitDockerfluentbitrepo09B43BE7": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "EmptyOnDelete": true,
        "ImageScanningConfiguration": Object {
          "ScanOnPush": true,
        },
        "LifecyclePolicy": Object {
          "LifecyclePolicyText": "{\\"rules\\":[{\\"rulePriority\\":1,\\"description\\":\\"Keep last 5 images\\",\\"selection\\":{\\"tagStatus\\":\\"any\\",\\"countType\\":\\"imageCountMoreThan\\",\\"countNumber\\":5},\\"action\\":{\\"type\\":\\"expire\\"}}]}",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::ECR::Repository",
      "UpdateReplacePolicy": "Delete",
    },
    "LambdaLayerNewRelicLayerD9FD4CB9": Object {
      "Properties": Object {
        "CompatibleRuntimes": Array [
          "python3.12",
        ],
        "Content": Object {
          "S3Bucket": "cdk-hnb659fds-assets-************-ap-northeast-1",
          "S3Key": "0f7bd35990988c95baeaada08452a07271c6d19d56531d75f3f409cbadf7dffc.zip",
        },
        "LayerName": "Dev01-NewRelicLayer",
      },
      "Type": "AWS::Lambda::LayerVersion",
    },
    "SSMAcmFunctionLambdaRoleArnD1640F6F": Object {
      "Properties": Object {
        "Name": "/Dev01/AcmFunctionLambdaRoleArn",
        "Tags": Object {
          "Environment": "dev01",
        },
        "Type": "String",
        "Value": Object {
          "Fn::GetAtt": Array [
            "Dev01LambdaRoleAcmFunctionLambdaRole0612C1D3",
            "Arn",
          ],
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "SSMAlarmTopicArnAD924D2F": Object {
      "Properties": Object {
        "Name": "/Dev01/alarmTopicArn",
        "Tags": Object {
          "Environment": "dev01",
        },
        "Type": "String",
        "Value": Object {
          "Ref": "Dev01AlarmSNSTopic8457D16E",
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "SSMAppKeyArnC0399964": Object {
      "Properties": Object {
        "Name": "/Dev01/appKeyArn",
        "Tags": Object {
          "Environment": "dev01",
        },
        "Type": "String",
        "Value": Object {
          "Fn::GetAtt": Array [
            "Dev01AppKey5F982BFE",
            "Arn",
          ],
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "SSMBatchMasterBucketNameC49CA1CE": Object {
      "Properties": Object {
        "Name": "/Dev01/BatchMasterBucketName",
        "Tags": Object {
          "Environment": "dev01",
        },
        "Type": "String",
        "Value": Object {
          "Ref": "BatchMasterBucketB198BB29",
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "SSMConfMasterBucketName34A96FE4": Object {
      "Properties": Object {
        "Name": "/Dev01/ConfMasterBucketName",
        "Tags": Object {
          "Environment": "dev01",
        },
        "Type": "String",
        "Value": Object {
          "Ref": "ConfMasterBucketA2D66739",
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "SSMEventBridgeServiceRoleArnAF0BB66F": Object {
      "Properties": Object {
        "Name": "/Dev01/role/eventBridgeServiceRoleArn",
        "Tags": Object {
          "Environment": "dev01",
        },
        "Type": "String",
        "Value": Object {
          "Fn::GetAtt": Array [
            "BatchSharedResourcesEventBridgeSchedulerServiceRole02B4EB8F",
            "Arn",
          ],
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "SSMFluentbitEcrRepositoryUri5C5F4227": Object {
      "Properties": Object {
        "Name": "/Dev01/fluentbit/ecr/repository-uri",
        "Tags": Object {
          "Environment": "dev01",
        },
        "Type": "String",
        "Value": Object {
          "Fn::Join": Array [
            "",
            Array [
              Object {
                "Fn::Select": Array [
                  4,
                  Object {
                    "Fn::Split": Array [
                      ":",
                      Object {
                        "Fn::GetAtt": Array [
                          "FluentbitDockerfluentbitrepo09B43BE7",
                          "Arn",
                        ],
                      },
                    ],
                  },
                ],
              },
              ".dkr.ecr.",
              Object {
                "Fn::Select": Array [
                  3,
                  Object {
                    "Fn::Split": Array [
                      ":",
                      Object {
                        "Fn::GetAtt": Array [
                          "FluentbitDockerfluentbitrepo09B43BE7",
                          "Arn",
                        ],
                      },
                    ],
                  },
                ],
              },
              ".",
              Object {
                "Ref": "AWS::URLSuffix",
              },
              "/",
              Object {
                "Ref": "FluentbitDockerfluentbitrepo09B43BE7",
              },
            ],
          ],
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "SSMNewRelicLayerArn98E33162": Object {
      "Properties": Object {
        "Name": "/Dev01/NewRelicLayerArn",
        "Tags": Object {
          "Environment": "dev01",
        },
        "Type": "String",
        "Value": Object {
          "Ref": "LambdaLayerNewRelicLayerD9FD4CB9",
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "SSMPrivateSubnetIdE326F0D2": Object {
      "Properties": Object {
        "Name": "/Dev01/privateSubnetId",
        "Tags": Object {
          "Environment": "dev01",
        },
        "Type": "String",
        "Value": Object {
          "Ref": "Dev01VpcPrivateSubnet1SubnetBB806C4A",
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "SSMStateMachineExecutorArn311CB060": Object {
      "Properties": Object {
        "Name": "/Dev01/lambda/stateMachineExecutorArn",
        "Tags": Object {
          "Environment": "dev01",
        },
        "Type": "String",
        "Value": Object {
          "Fn::GetAtt": Array [
            "BatchSharedResourcesStateMachineExecutor26FF42B8",
            "Arn",
          ],
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "SSMVpcId02E027C2": Object {
      "Properties": Object {
        "Name": "/Dev01/vpcId",
        "Tags": Object {
          "Environment": "dev01",
        },
        "Type": "String",
        "Value": Object {
          "Ref": "Dev01Vpc877F445C",
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`Dev01 Guest Stacks GuestAccount ECS App Stacks 2`] = `
Object {
  "Outputs": Object {
    "BuildProjectArn": Object {
      "Export": Object {
        "Name": "Dev01-BuildProjectArn",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "buildProjectB3CE3E56",
          "Arn",
        ],
      },
    },
    "BuildProjectName": Object {
      "Export": Object {
        "Name": "Dev01-BuildProjectName",
      },
      "Value": Object {
        "Ref": "buildProjectB3CE3E56",
      },
    },
    "PipelineName": Object {
      "Export": Object {
        "Name": "Dev01-PipelineName",
      },
      "Value": Object {
        "Ref": "ProjectC78D97AD",
      },
    },
    "SourceBucketName": Object {
      "Export": Object {
        "Name": "Dev01-SourceBucketName",
      },
      "Value": Object {
        "Ref": "SourceBucketDDD2130A",
      },
    },
  },
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "BucketNotificationsHandler050a0587b7544547bf325f094a3db8347ECC3691": Object {
      "DependsOn": Array [
        "BucketNotificationsHandler050a0587b7544547bf325f094a3db834RoleDefaultPolicy2CF63D36",
        "BucketNotificationsHandler050a0587b7544547bf325f094a3db834RoleB6FB88EC",
      ],
      "Properties": Object {
        "Code": Object {
          "ZipFile": "import boto3  # type: ignore
import json
import logging
import urllib.request

s3 = boto3.client(\\"s3\\")

EVENTBRIDGE_CONFIGURATION = 'EventBridgeConfiguration'
CONFIGURATION_TYPES = [\\"TopicConfigurations\\", \\"QueueConfigurations\\", \\"LambdaFunctionConfigurations\\"]

def handler(event: dict, context):
  response_status = \\"SUCCESS\\"
  error_message = \\"\\"
  try:
    props = event[\\"ResourceProperties\\"]
    notification_configuration = props[\\"NotificationConfiguration\\"]
    managed = props.get('Managed', 'true').lower() == 'true'
    skipDestinationValidation = props.get('SkipDestinationValidation', 'false').lower() == 'true'
    stack_id = event['StackId']
    old = event.get(\\"OldResourceProperties\\", {}).get(\\"NotificationConfiguration\\", {})
    if managed:
      config = handle_managed(event[\\"RequestType\\"], notification_configuration)
    else:
      config = handle_unmanaged(props[\\"BucketName\\"], stack_id, event[\\"RequestType\\"], notification_configuration, old)
    s3.put_bucket_notification_configuration(Bucket=props[\\"BucketName\\"], NotificationConfiguration=config, SkipDestinationValidation=skipDestinationValidation)
  except Exception as e:
    logging.exception(\\"Failed to put bucket notification configuration\\")
    response_status = \\"FAILED\\"
    error_message = f\\"Error: {str(e)}. \\"
  finally:
    submit_response(event, context, response_status, error_message)

def handle_managed(request_type, notification_configuration):
  if request_type == 'Delete':
    return {}
  return notification_configuration

def handle_unmanaged(bucket, stack_id, request_type, notification_configuration, old):
  def get_id(n):
    n['Id'] = ''
    strToHash=json.dumps(n, sort_keys=True).replace('\\"Name\\": \\"prefix\\"', '\\"Name\\": \\"Prefix\\"').replace('\\"Name\\": \\"suffix\\"', '\\"Name\\": \\"Suffix\\"')
    return f\\"{stack_id}-{hash(strToHash)}\\"
  def with_id(n):
    n['Id'] = get_id(n)
    return n

  external_notifications = {}
  existing_notifications = s3.get_bucket_notification_configuration(Bucket=bucket)
  for t in CONFIGURATION_TYPES:
    if request_type == 'Update':
        old_incoming_ids = [get_id(n) for n in old.get(t, [])]
        external_notifications[t] = [n for n in existing_notifications.get(t, []) if not get_id(n) in old_incoming_ids]      
    elif request_type == 'Delete':
        external_notifications[t] = [n for n in existing_notifications.get(t, []) if not n['Id'].startswith(f\\"{stack_id}-\\")]
    elif request_type == 'Create':
        external_notifications[t] = [n for n in existing_notifications.get(t, [])]
  if EVENTBRIDGE_CONFIGURATION in existing_notifications:
    external_notifications[EVENTBRIDGE_CONFIGURATION] = existing_notifications[EVENTBRIDGE_CONFIGURATION]

  if request_type == 'Delete':
    return external_notifications

  notifications = {}
  for t in CONFIGURATION_TYPES:
    external = external_notifications.get(t, [])
    incoming = [with_id(n) for n in notification_configuration.get(t, [])]
    notifications[t] = external + incoming

  if EVENTBRIDGE_CONFIGURATION in notification_configuration:
    notifications[EVENTBRIDGE_CONFIGURATION] = notification_configuration[EVENTBRIDGE_CONFIGURATION]
  elif EVENTBRIDGE_CONFIGURATION in external_notifications:
    notifications[EVENTBRIDGE_CONFIGURATION] = external_notifications[EVENTBRIDGE_CONFIGURATION]

  return notifications

def submit_response(event: dict, context, response_status: str, error_message: str):
  response_body = json.dumps(
    {
      \\"Status\\": response_status,
      \\"Reason\\": f\\"{error_message}See the details in CloudWatch Log Stream: {context.log_stream_name}\\",
      \\"PhysicalResourceId\\": event.get(\\"PhysicalResourceId\\") or event[\\"LogicalResourceId\\"],
      \\"StackId\\": event[\\"StackId\\"],
      \\"RequestId\\": event[\\"RequestId\\"],
      \\"LogicalResourceId\\": event[\\"LogicalResourceId\\"],
      \\"NoEcho\\": False,
    }
  ).encode(\\"utf-8\\")
  headers = {\\"content-type\\": \\"\\", \\"content-length\\": str(len(response_body))}
  try:
    req = urllib.request.Request(url=event[\\"ResponseURL\\"], headers=headers, data=response_body, method=\\"PUT\\")
    with urllib.request.urlopen(req) as response:
      print(response.read().decode(\\"utf-8\\"))
    print(\\"Status code: \\" + response.reason)
  except Exception as e:
      print(\\"send(..) failed executing request.urlopen(..): \\" + str(e))",
        },
        "Description": "AWS CloudFormation handler for \\"Custom::S3BucketNotifications\\" resources (@aws-cdk/aws-s3)",
        "Handler": "index.handler",
        "Role": Object {
          "Fn::GetAtt": Array [
            "BucketNotificationsHandler050a0587b7544547bf325f094a3db834RoleB6FB88EC",
            "Arn",
          ],
        },
        "Runtime": "python3.11",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
        "Timeout": 300,
      },
      "Type": "AWS::Lambda::Function",
    },
    "BucketNotificationsHandler050a0587b7544547bf325f094a3db834RoleB6FB88EC": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
              ],
            ],
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "BucketNotificationsHandler050a0587b7544547bf325f094a3db834RoleDefaultPolicy2CF63D36": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:PutBucketNotification",
              "Effect": "Allow",
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "BucketNotificationsHandler050a0587b7544547bf325f094a3db834RoleDefaultPolicy2CF63D36",
        "Roles": Array [
          Object {
            "Ref": "BucketNotificationsHandler050a0587b7544547bf325f094a3db834RoleB6FB88EC",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F": Object {
      "DependsOn": Array [
        "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": "cdk-hnb659fds-assets-************-ap-northeast-1",
          "S3Key": "faa95a81ae7d7373f3e1f242268f904eb748d8d0fdd306e8a6fe515a1905a7d6.zip",
        },
        "Description": Object {
          "Fn::Join": Array [
            "",
            Array [
              "Lambda function for auto-deleting objects in ",
              Object {
                "Ref": "SourceBucketDDD2130A",
              },
              " S3 bucket.",
            ],
          ],
        },
        "Handler": "index.handler",
        "MemorySize": 128,
        "Role": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
            "Arn",
          ],
        },
        "Runtime": "nodejs20.x",
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Sub": "arn:\${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "Log95422804": Object {
      "DeletionPolicy": "Retain",
      "Properties": Object {
        "KmsKeyId": Object {
          "Fn::ImportValue": "Dev01-ShareResources:ExportsOutputFnGetAttDev01AppKey5F982BFEArnC4871D32",
        },
        "RetentionInDays": 30,
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::Logs::LogGroup",
      "UpdateReplacePolicy": "Retain",
    },
    "MetricFirehoseCfnSubscriptionFilter1CB8AA27": Object {
      "Properties": Object {
        "DestinationArn": Object {
          "Fn::GetAtt": Array [
            "MetricFirehoseFirehoseStreamA8A8DF15",
            "Arn",
          ],
        },
        "FilterPattern": "",
        "LogGroupName": Object {
          "Ref": "Log95422804",
        },
        "RoleArn": Object {
          "Fn::GetAtt": Array [
            "MetricFirehoseToKinesisFirehoseRoleD4E71AEF",
            "Arn",
          ],
        },
      },
      "Type": "AWS::Logs::SubscriptionFilter",
    },
    "MetricFirehoseCwLogGroup3F32A32A": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "LogGroupName": "/aws/firehose/Dev01/buildlogs",
        "RetentionInDays": 90,
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::Logs::LogGroup",
      "UpdateReplacePolicy": "Delete",
    },
    "MetricFirehoseCwLogStream3CC5B4E7": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "LogGroupName": Object {
          "Ref": "MetricFirehoseCwLogGroup3F32A32A",
        },
      },
      "Type": "AWS::Logs::LogStream",
      "UpdateReplacePolicy": "Delete",
    },
    "MetricFirehoseFirehoseLogBucket585BDB75": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "NoncurrentVersionExpiration": Object {
                "NewerNoncurrentVersions": 20,
                "NoncurrentDays": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "MetricFirehoseFirehoseLogBucketAutoDeleteObjectsCustomResource33FF24D5": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "MetricFirehoseFirehoseLogBucketPolicy545A50A7",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "MetricFirehoseFirehoseLogBucket585BDB75",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "MetricFirehoseFirehoseLogBucketPolicy545A50A7": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "MetricFirehoseFirehoseLogBucket585BDB75",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "MetricFirehoseFirehoseLogBucket585BDB75",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "MetricFirehoseFirehoseLogBucket585BDB75",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "MetricFirehoseFirehoseLogBucket585BDB75",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "MetricFirehoseFirehoseLogBucket585BDB75",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "MetricFirehoseFirehoseRoleC9BBE682": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "firehose.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "MetricFirehoseFirehoseRoleDefaultPolicyDD4F05D7": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:PutObject",
                "s3:GetBucketLocation",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "MetricFirehoseFirehoseLogBucket585BDB75",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "MetricFirehoseFirehoseLogBucket585BDB75",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": "secretsmanager:GetSecretValue",
              "Effect": "Allow",
              "Resource": Object {
                "Fn::ImportValue": "Dev01-Secrets:ExportsOutputRefNewRelicSecret6D36392C61C5E3B2",
              },
            },
            Object {
              "Action": "logs:PutLogEvents",
              "Effect": "Allow",
              "Resource": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    Object {
                      "Fn::GetAtt": Array [
                        "MetricFirehoseCwLogGroup3F32A32A",
                        "Arn",
                      ],
                    },
                    ":*",
                  ],
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "MetricFirehoseFirehoseRoleDefaultPolicyDD4F05D7",
        "Roles": Array [
          Object {
            "Ref": "MetricFirehoseFirehoseRoleC9BBE682",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "MetricFirehoseFirehoseStreamA8A8DF15": Object {
      "Properties": Object {
        "DeliveryStreamName": "Dev01-BuildLog-Stream",
        "DeliveryStreamType": "DirectPut",
        "HttpEndpointDestinationConfiguration": Object {
          "BufferingHints": Object {
            "IntervalInSeconds": 60,
            "SizeInMBs": 1,
          },
          "CloudWatchLoggingOptions": Object {
            "Enabled": true,
            "LogGroupName": Object {
              "Ref": "MetricFirehoseCwLogGroup3F32A32A",
            },
            "LogStreamName": Object {
              "Ref": "MetricFirehoseCwLogStream3CC5B4E7",
            },
          },
          "EndpointConfiguration": Object {
            "Name": "New Relic",
            "Url": "https://aws-api.newrelic.com/firehose/v1",
          },
          "RequestConfiguration": Object {
            "ContentEncoding": "GZIP",
          },
          "RetryOptions": Object {
            "DurationInSeconds": 60,
          },
          "RoleARN": Object {
            "Fn::GetAtt": Array [
              "MetricFirehoseFirehoseRoleC9BBE682",
              "Arn",
            ],
          },
          "S3BackupMode": "FailedDataOnly",
          "S3Configuration": Object {
            "BucketARN": Object {
              "Fn::GetAtt": Array [
                "MetricFirehoseFirehoseLogBucket585BDB75",
                "Arn",
              ],
            },
            "CompressionFormat": "GZIP",
            "RoleARN": Object {
              "Fn::GetAtt": Array [
                "MetricFirehoseFirehoseRoleC9BBE682",
                "Arn",
              ],
            },
          },
          "SecretsManagerConfiguration": Object {
            "Enabled": true,
            "RoleARN": Object {
              "Fn::GetAtt": Array [
                "MetricFirehoseFirehoseRoleC9BBE682",
                "Arn",
              ],
            },
            "SecretARN": Object {
              "Fn::ImportValue": "Dev01-Secrets:ExportsOutputRefNewRelicSecret6D36392C61C5E3B2",
            },
          },
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::KinesisFirehose::DeliveryStream",
    },
    "MetricFirehoseToKinesisFirehoseRoleD4E71AEF": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "logs.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/AmazonKinesisFirehoseFullAccess",
              ],
            ],
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "PipelineTriggerEventRule0AE0646E": Object {
      "Properties": Object {
        "EventPattern": Object {
          "account": Array [
            "************",
          ],
          "detail": Object {
            "bucket": Object {
              "name": Array [
                Object {
                  "Ref": "SourceBucketDDD2130A",
                },
              ],
            },
            "object": Object {
              "key": Array [
                "image.zip",
              ],
            },
          },
          "detail-type": Array [
            "Object Created",
          ],
          "source": Array [
            "aws.s3",
          ],
        },
        "State": "ENABLED",
        "Targets": Array [
          Object {
            "Arn": Object {
              "Fn::Join": Array [
                "",
                Array [
                  "arn:",
                  Object {
                    "Ref": "AWS::Partition",
                  },
                  ":codepipeline:ap-northeast-1:************:",
                  Object {
                    "Ref": "ProjectC78D97AD",
                  },
                ],
              ],
            },
            "Id": "Target0",
            "RoleArn": Object {
              "Fn::GetAtt": Array [
                "ProjectEventsRole256975EC",
                "Arn",
              ],
            },
          },
        ],
      },
      "Type": "AWS::Events::Rule",
    },
    "ProjectArtifactsBucketA004C8A7": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "KMSMasterKeyID": Object {
                  "Fn::GetAtt": Array [
                    "ProjectArtifactsBucketEncryptionKey9C4C3F23",
                    "Arn",
                  ],
                },
                "SSEAlgorithm": "aws:kms",
              },
            },
          ],
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "ProjectArtifactsBucketEncryptionKey9C4C3F23": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "KeyPolicy": Object {
          "Statement": Array [
            Object {
              "Action": "kms:*",
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":iam::************:root",
                    ],
                  ],
                },
              },
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::KMS::Key",
      "UpdateReplacePolicy": "Delete",
    },
    "ProjectArtifactsBucketEncryptionKeyAlias9B199404": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AliasName": "alias/codepipeline-dev01pipelineprojectc103e338",
        "TargetKeyId": Object {
          "Fn::GetAtt": Array [
            "ProjectArtifactsBucketEncryptionKey9C4C3F23",
            "Arn",
          ],
        },
      },
      "Type": "AWS::KMS::Alias",
      "UpdateReplacePolicy": "Delete",
    },
    "ProjectArtifactsBucketPolicyB28439E8": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "ProjectArtifactsBucketA004C8A7",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ProjectArtifactsBucketA004C8A7",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ProjectArtifactsBucketA004C8A7",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "ProjectBuildCodeBuildCodePipelineActionRole39C24142": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":iam::************:root",
                    ],
                  ],
                },
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "ProjectBuildCodeBuildCodePipelineActionRoleDefaultPolicyE7B213A3": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "codebuild:BatchGetBuilds",
                "codebuild:StartBuild",
                "codebuild:StopBuild",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "buildProjectB3CE3E56",
                  "Arn",
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ProjectBuildCodeBuildCodePipelineActionRoleDefaultPolicyE7B213A3",
        "Roles": Array [
          Object {
            "Ref": "ProjectBuildCodeBuildCodePipelineActionRole39C24142",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ProjectC78D97AD": Object {
      "DependsOn": Array [
        "ProjectRoleDefaultPolicy7F29461B",
        "ProjectRole4CCB274E",
      ],
      "Properties": Object {
        "ArtifactStore": Object {
          "EncryptionKey": Object {
            "Id": Object {
              "Fn::GetAtt": Array [
                "ProjectArtifactsBucketEncryptionKey9C4C3F23",
                "Arn",
              ],
            },
            "Type": "KMS",
          },
          "Location": Object {
            "Ref": "ProjectArtifactsBucketA004C8A7",
          },
          "Type": "S3",
        },
        "RoleArn": Object {
          "Fn::GetAtt": Array [
            "ProjectRole4CCB274E",
            "Arn",
          ],
        },
        "Stages": Array [
          Object {
            "Actions": Array [
              Object {
                "ActionTypeId": Object {
                  "Category": "Source",
                  "Owner": "AWS",
                  "Provider": "S3",
                  "Version": "1",
                },
                "Configuration": Object {
                  "PollForSourceChanges": false,
                  "S3Bucket": Object {
                    "Ref": "SourceBucketDDD2130A",
                  },
                  "S3ObjectKey": "image.zip",
                },
                "Name": "SourceBucket",
                "OutputArtifacts": Array [
                  Object {
                    "Name": "SourceArtifact",
                  },
                ],
                "RoleArn": Object {
                  "Fn::GetAtt": Array [
                    "ProjectSourceSourceBucketCodePipelineActionRole30AADFBE",
                    "Arn",
                  ],
                },
                "RunOrder": 1,
              },
            ],
            "Name": "Source",
          },
          Object {
            "Actions": Array [
              Object {
                "ActionTypeId": Object {
                  "Category": "Build",
                  "Owner": "AWS",
                  "Provider": "CodeBuild",
                  "Version": "1",
                },
                "Configuration": Object {
                  "ProjectName": Object {
                    "Ref": "buildProjectB3CE3E56",
                  },
                },
                "InputArtifacts": Array [
                  Object {
                    "Name": "SourceArtifact",
                  },
                ],
                "Name": "CodeBuild",
                "OutputArtifacts": Array [
                  Object {
                    "Name": "Artifact_Build_CodeBuild",
                  },
                ],
                "RoleArn": Object {
                  "Fn::GetAtt": Array [
                    "ProjectBuildCodeBuildCodePipelineActionRole39C24142",
                    "Arn",
                  ],
                },
                "RunOrder": 1,
              },
            ],
            "Name": "Build",
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::CodePipeline::Pipeline",
    },
    "ProjectEventsRole256975EC": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "events.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "ProjectEventsRoleDefaultPolicy00ED7125": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "codepipeline:StartPipelineExecution",
              "Effect": "Allow",
              "Resource": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    "arn:",
                    Object {
                      "Ref": "AWS::Partition",
                    },
                    ":codepipeline:ap-northeast-1:************:",
                    Object {
                      "Ref": "ProjectC78D97AD",
                    },
                  ],
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ProjectEventsRoleDefaultPolicy00ED7125",
        "Roles": Array [
          Object {
            "Ref": "ProjectEventsRole256975EC",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ProjectRole4CCB274E": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "codepipeline.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "ProjectRoleDefaultPolicy7F29461B": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:GetObject*",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
                "s3:PutObject",
                "s3:PutObjectLegalHold",
                "s3:PutObjectRetention",
                "s3:PutObjectTagging",
                "s3:PutObjectVersionTagging",
                "s3:Abort*",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ProjectArtifactsBucketA004C8A7",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ProjectArtifactsBucketA004C8A7",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "kms:Decrypt",
                "kms:DescribeKey",
                "kms:Encrypt",
                "kms:ReEncrypt*",
                "kms:GenerateDataKey*",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ProjectArtifactsBucketEncryptionKey9C4C3F23",
                  "Arn",
                ],
              },
            },
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ProjectSourceSourceBucketCodePipelineActionRole30AADFBE",
                  "Arn",
                ],
              },
            },
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ProjectBuildCodeBuildCodePipelineActionRole39C24142",
                  "Arn",
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ProjectRoleDefaultPolicy7F29461B",
        "Roles": Array [
          Object {
            "Ref": "ProjectRole4CCB274E",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ProjectSourceSourceBucketCodePipelineActionRole30AADFBE": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":iam::************:root",
                    ],
                  ],
                },
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "ProjectSourceSourceBucketCodePipelineActionRoleDefaultPolicyBD09B9B2": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:GetObject*",
                "s3:GetBucket*",
                "s3:List*",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "SourceBucketDDD2130A",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "SourceBucketDDD2130A",
                          "Arn",
                        ],
                      },
                      "/image.zip",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:DeleteObject*",
                "s3:PutObject",
                "s3:PutObjectLegalHold",
                "s3:PutObjectRetention",
                "s3:PutObjectTagging",
                "s3:PutObjectVersionTagging",
                "s3:Abort*",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ProjectArtifactsBucketA004C8A7",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ProjectArtifactsBucketA004C8A7",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "kms:Encrypt",
                "kms:ReEncrypt*",
                "kms:GenerateDataKey*",
                "kms:Decrypt",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ProjectArtifactsBucketEncryptionKey9C4C3F23",
                  "Arn",
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ProjectSourceSourceBucketCodePipelineActionRoleDefaultPolicyBD09B9B2",
        "Roles": Array [
          Object {
            "Ref": "ProjectSourceSourceBucketCodePipelineActionRole30AADFBE",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "SlackChannel425D424B": Object {
      "Properties": Object {
        "ConfigurationName": "Dev01-YOUR_CHANNEL_NAME",
        "IamRoleArn": Object {
          "Fn::GetAtt": Array [
            "SlackChannelConfigurationRole7EE950F2",
            "Arn",
          ],
        },
        "SlackChannelId": "YOUR_SLACK_CHANNEL_ID",
        "SlackWorkspaceId": "YOUR_SLACK_WORKSPACE_ID",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::Chatbot::SlackChannelConfiguration",
    },
    "SlackChannelConfigurationRole7EE950F2": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "chatbot.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "SourceBucketAutoDeleteObjectsCustomResourceC68FC040": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "SourceBucketPolicy703DFBF9",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "SourceBucketDDD2130A",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "SourceBucketDDD2130A": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
        "VersioningConfiguration": Object {
          "Status": "Enabled",
        },
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "SourceBucketNotifications0A6F2084": Object {
      "DependsOn": Array [
        "SourceBucketPolicy703DFBF9",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "SourceBucketDDD2130A",
        },
        "Managed": true,
        "NotificationConfiguration": Object {
          "EventBridgeConfiguration": Object {},
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "BucketNotificationsHandler050a0587b7544547bf325f094a3db8347ECC3691",
            "Arn",
          ],
        },
        "SkipDestinationValidation": false,
      },
      "Type": "Custom::S3BucketNotifications",
    },
    "SourceBucketPolicy703DFBF9": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "SourceBucketDDD2130A",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "SourceBucketDDD2130A",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "SourceBucketDDD2130A",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "buildProjectB3CE3E56": Object {
      "Properties": Object {
        "Artifacts": Object {
          "Type": "CODEPIPELINE",
        },
        "Cache": Object {
          "Type": "NO_CACHE",
        },
        "EncryptionKey": Object {
          "Fn::GetAtt": Array [
            "ProjectArtifactsBucketEncryptionKey9C4C3F23",
            "Arn",
          ],
        },
        "Environment": Object {
          "ComputeType": "BUILD_GENERAL1_SMALL",
          "Image": "aws/codebuild/standard:5.0",
          "ImagePullCredentialsType": "CODEBUILD",
          "PrivilegedMode": false,
          "Type": "LINUX_CONTAINER",
        },
        "LogsConfig": Object {
          "CloudWatchLogs": Object {
            "GroupName": Object {
              "Ref": "Log95422804",
            },
            "Status": "ENABLED",
          },
        },
        "Name": "Dev01-Pipeline-BuildProject",
        "ServiceRole": Object {
          "Fn::GetAtt": Array [
            "buildProjectRoleAF642118",
            "Arn",
          ],
        },
        "Source": Object {
          "BuildSpec": "{
  \\"version\\": \\"0.2\\",
  \\"phases\\": {
    \\"install\\": {
      \\"commands\\": []
    },
    \\"build\\": {
      \\"commands\\": [
        \\"npm install\\",
        \\"npx cdk deploy --require-approval never --all -c environment=dev01\\"
      ]
    }
  }
}",
          "Type": "CODEPIPELINE",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
        "TimeoutInMinutes": 180,
      },
      "Type": "AWS::CodeBuild::Project",
    },
    "buildProjectNotifyOnBuildSucceeded0CCFF51E": Object {
      "Properties": Object {
        "DetailType": "FULL",
        "EventTypeIds": Array [
          "codebuild-project-build-state-succeeded",
        ],
        "Name": "Dev01PipelinebuildProjectNotifyOnBuildSucceeded13A1967D",
        "Resource": Object {
          "Fn::GetAtt": Array [
            "buildProjectB3CE3E56",
            "Arn",
          ],
        },
        "Tags": Object {
          "Environment": "dev01",
        },
        "Targets": Array [
          Object {
            "TargetAddress": Object {
              "Ref": "SlackChannel425D424B",
            },
            "TargetType": "AWSChatbotSlack",
          },
        ],
      },
      "Type": "AWS::CodeStarNotifications::NotificationRule",
    },
    "buildProjectNotifyOnBuildfailed2BA6AA1C": Object {
      "Properties": Object {
        "DetailType": "FULL",
        "EventTypeIds": Array [
          "codebuild-project-build-state-failed",
        ],
        "Name": "Dev01PipelinebuildProjectNotifyOnBuildfailed4F143AC8",
        "Resource": Object {
          "Fn::GetAtt": Array [
            "buildProjectB3CE3E56",
            "Arn",
          ],
        },
        "Tags": Object {
          "Environment": "dev01",
        },
        "Targets": Array [
          Object {
            "TargetAddress": Object {
              "Ref": "SlackChannel425D424B",
            },
            "TargetType": "AWSChatbotSlack",
          },
        ],
      },
      "Type": "AWS::CodeStarNotifications::NotificationRule",
    },
    "buildProjectRoleAF642118": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "codebuild.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "buildProjectRoleDefaultPolicy1C8C8518": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "logs:CreateLogStream",
                "logs:PutLogEvents",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "Log95422804",
                  "Arn",
                ],
              },
            },
            Object {
              "Action": Array [
                "logs:CreateLogGroup",
                "logs:CreateLogStream",
                "logs:PutLogEvents",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":logs:ap-northeast-1:************:log-group:/aws/codebuild/",
                      Object {
                        "Ref": "buildProjectB3CE3E56",
                      },
                    ],
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":logs:ap-northeast-1:************:log-group:/aws/codebuild/",
                      Object {
                        "Ref": "buildProjectB3CE3E56",
                      },
                      ":*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "codebuild:CreateReportGroup",
                "codebuild:CreateReport",
                "codebuild:UpdateReport",
                "codebuild:BatchPutTestCases",
                "codebuild:BatchPutCodeCoverages",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    "arn:",
                    Object {
                      "Ref": "AWS::Partition",
                    },
                    ":codebuild:ap-northeast-1:************:report-group/",
                    Object {
                      "Ref": "buildProjectB3CE3E56",
                    },
                    "-*",
                  ],
                ],
              },
            },
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Resource": "arn:aws:iam::*:role/cdk-*",
            },
            Object {
              "Action": Array [
                "s3:GetObject*",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
                "s3:PutObject",
                "s3:PutObjectLegalHold",
                "s3:PutObjectRetention",
                "s3:PutObjectTagging",
                "s3:PutObjectVersionTagging",
                "s3:Abort*",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ProjectArtifactsBucketA004C8A7",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ProjectArtifactsBucketA004C8A7",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "kms:Decrypt",
                "kms:DescribeKey",
                "kms:Encrypt",
                "kms:ReEncrypt*",
                "kms:GenerateDataKey*",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ProjectArtifactsBucketEncryptionKey9C4C3F23",
                  "Arn",
                ],
              },
            },
            Object {
              "Action": Array [
                "kms:Decrypt",
                "kms:Encrypt",
                "kms:ReEncrypt*",
                "kms:GenerateDataKey*",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ProjectArtifactsBucketEncryptionKey9C4C3F23",
                  "Arn",
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "buildProjectRoleDefaultPolicy1C8C8518",
        "Roles": Array [
          Object {
            "Ref": "buildProjectRoleAF642118",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`Dev01 Guest Stacks GuestAccount ECS App Stacks 3`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "BatchMasterBucketRole8B540744": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRoleWithWebIdentity",
              "Condition": Object {
                "StringEquals": Object {
                  "token.actions.githubusercontent.com:aud": "sts.amazonaws.com",
                },
                "StringLike": Object {
                  "token.actions.githubusercontent.com:sub": "repo:OrganizationName/MasterBucketRepositoryName:*",
                },
              },
              "Effect": "Allow",
              "Principal": Object {
                "Federated": Object {
                  "Ref": "GithubActionsOidcProviderF9E986BE",
                },
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "BatchMasterBucketRoleDefaultPolicy0B0EF068": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:GetObject",
                "s3:ListAllMyBuckets",
                "s3:ListBucket",
                "s3:PutObject",
                "s3:DeleteObject",
                "ssm:PutParameter",
                "ssm:GetParameter",
              ],
              "Effect": "Allow",
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "BatchMasterBucketRoleDefaultPolicy0B0EF068",
        "Roles": Array [
          Object {
            "Ref": "BatchMasterBucketRole8B540744",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ConfMasterBucketRole38A4F887": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRoleWithWebIdentity",
              "Condition": Object {
                "StringEquals": Object {
                  "token.actions.githubusercontent.com:aud": "sts.amazonaws.com",
                },
                "StringLike": Object {
                  "token.actions.githubusercontent.com:sub": "repo:OrganizationName/MasterBucketRepositoryName:*",
                },
              },
              "Effect": "Allow",
              "Principal": Object {
                "Federated": Object {
                  "Ref": "GithubActionsOidcProviderF9E986BE",
                },
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "ConfMasterBucketRoleDefaultPolicyFA559AA8": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:GetObject",
                "s3:ListAllMyBuckets",
                "s3:ListBucket",
                "s3:PutObject",
                "s3:DeleteObject",
                "ssm:PutParameter",
                "ssm:GetParameter",
              ],
              "Effect": "Allow",
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ConfMasterBucketRoleDefaultPolicyFA559AA8",
        "Roles": Array [
          Object {
            "Ref": "ConfMasterBucketRole38A4F887",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "CustomAWSCDKOpenIdConnectProviderCustomResourceProviderHandlerF2C543E0": Object {
      "DependsOn": Array [
        "CustomAWSCDKOpenIdConnectProviderCustomResourceProviderRole517FED65",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": "cdk-hnb659fds-assets-************-ap-northeast-1",
          "S3Key": "977fc1649d2dbcce16e23f6332faef6fa0f48aa74a0afe35f4a3467754e20cd8.zip",
        },
        "Handler": "__entrypoint__.handler",
        "MemorySize": 128,
        "Role": Object {
          "Fn::GetAtt": Array [
            "CustomAWSCDKOpenIdConnectProviderCustomResourceProviderRole517FED65",
            "Arn",
          ],
        },
        "Runtime": "nodejs20.x",
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "CustomAWSCDKOpenIdConnectProviderCustomResourceProviderRole517FED65": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Sub": "arn:\${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
          },
        ],
        "Policies": Array [
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": Array [
                    "iam:CreateOpenIDConnectProvider",
                    "iam:DeleteOpenIDConnectProvider",
                    "iam:UpdateOpenIDConnectProviderThumbprint",
                    "iam:AddClientIDToOpenIDConnectProvider",
                    "iam:RemoveClientIDFromOpenIDConnectProvider",
                  ],
                  "Effect": "Allow",
                  "Resource": "*",
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "Inline",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "GithubActionsOidcProviderF9E986BE": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "ClientIDList": Array [
          "sts.amazonaws.com",
        ],
        "CodeHash": "977fc1649d2dbcce16e23f6332faef6fa0f48aa74a0afe35f4a3467754e20cd8",
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomAWSCDKOpenIdConnectProviderCustomResourceProviderHandlerF2C543E0",
            "Arn",
          ],
        },
        "Url": "https://token.actions.githubusercontent.com",
      },
      "Type": "Custom::AWSCDKOpenIdConnectProvider",
      "UpdateReplacePolicy": "Delete",
    },
    "InfraResourcesRoleDefaultPolicy650E5DA3": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "cloudformation:DescribeStacks",
                "s3:PutObject",
              ],
              "Effect": "Allow",
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "InfraResourcesRoleDefaultPolicy650E5DA3",
        "Roles": Array [
          Object {
            "Ref": "InfraResourcesRoleE6C761A4",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "InfraResourcesRoleE6C761A4": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRoleWithWebIdentity",
              "Condition": Object {
                "StringEquals": Object {
                  "token.actions.githubusercontent.com:aud": "sts.amazonaws.com",
                },
                "StringLike": Object {
                  "token.actions.githubusercontent.com:sub": "repo:OrganizationName/InfraRepositoryName:*",
                },
              },
              "Effect": "Allow",
              "Principal": Object {
                "Federated": Object {
                  "Ref": "GithubActionsOidcProviderF9E986BE",
                },
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "SSMOidcProviderArn34EEA24A": Object {
      "Properties": Object {
        "Name": "/Dev01/oidcProviderArn",
        "Tags": Object {
          "Environment": "dev01",
        },
        "Type": "String",
        "Value": Object {
          "Ref": "GithubActionsOidcProviderF9E986BE",
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`Dev01 Guest Stacks GuestAccount ECS App Stacks 4`] = `
Object {
  "Outputs": Object {
    "bastionEcrRepositoryName": Object {
      "Export": Object {
        "Name": "Dev01-BastionEcrRepositoryName",
      },
      "Value": Object {
        "Ref": "BastionEcsCommonConstructbastionrepo39941F80",
      },
    },
    "bastionEcsClusterName": Object {
      "Export": Object {
        "Name": "Dev01-BastionEcsClusterName",
      },
      "Value": Object {
        "Ref": "BastionEcsCommonConstructBastionEcsCluster3733E5BE",
      },
    },
    "bastionEcsTaskExecutionRoleArn": Object {
      "Export": Object {
        "Name": "Dev01-BastionEcsTaskExecutionRoleArn",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "BastionEcsCommonConstructBastionEcsTaskExecutionRoleCFF6097D",
          "Arn",
        ],
      },
    },
  },
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "BastionEcrRepositoryNameParameter356C9BC0": Object {
      "Properties": Object {
        "Name": "/Dev01/bastion/ecr/repository-name",
        "Tags": Object {
          "Environment": "dev01",
        },
        "Type": "String",
        "Value": Object {
          "Ref": "BastionEcsCommonConstructbastionrepo39941F80",
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "BastionEcsClusterNameParameter33BEB638": Object {
      "Properties": Object {
        "Name": "/Dev01/bastion/ecs/cluster-name",
        "Tags": Object {
          "Environment": "dev01",
        },
        "Type": "String",
        "Value": Object {
          "Ref": "BastionEcsCommonConstructBastionEcsCluster3733E5BE",
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "BastionEcsCommonConstructBastionECRDeploymentCustomResource2BE7124F": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "DestImage": Object {
          "Fn::Join": Array [
            "",
            Array [
              "docker://",
              Object {
                "Fn::Select": Array [
                  4,
                  Object {
                    "Fn::Split": Array [
                      ":",
                      Object {
                        "Fn::GetAtt": Array [
                          "BastionEcsCommonConstructbastionrepo39941F80",
                          "Arn",
                        ],
                      },
                    ],
                  },
                ],
              },
              ".dkr.ecr.",
              Object {
                "Fn::Select": Array [
                  3,
                  Object {
                    "Fn::Split": Array [
                      ":",
                      Object {
                        "Fn::GetAtt": Array [
                          "BastionEcsCommonConstructbastionrepo39941F80",
                          "Arn",
                        ],
                      },
                    ],
                  },
                ],
              },
              ".",
              Object {
                "Ref": "AWS::URLSuffix",
              },
              "/",
              Object {
                "Ref": "BastionEcsCommonConstructbastionrepo39941F80",
              },
              ":bastionimage",
            ],
          ],
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomCDKECRDeploymentbd07c930edb94112a20f03f096f53666512MiB28EAD8E4",
            "Arn",
          ],
        },
        "SrcImage": Object {
          "Fn::Join": Array [
            "",
            Array [
              "docker://",
              Object {
                "Fn::Sub": "************.dkr.ecr.ap-northeast-1.\${AWS::URLSuffix}/cdk-hnb659fds-container-assets-************-ap-northeast-1:29062584fa1aedb878c07da20097120b97d89322621f3f0613d2a83903289ab5",
              },
            ],
          ],
        },
      },
      "Type": "Custom::CDKBucketDeployment",
      "UpdateReplacePolicy": "Delete",
    },
    "BastionEcsCommonConstructBastionECSServiceActionEventRuleA80B7488": Object {
      "Properties": Object {
        "Description": "CloudWatch Event Rule to send notification on Bastion ECS Service action events.",
        "EventPattern": Object {
          "detail": Object {
            "eventType": Array [
              "WARN",
              "ERROR",
            ],
          },
          "detail-type": Array [
            "ECS Service Action",
          ],
          "source": Array [
            "aws.ecs",
          ],
        },
        "State": "ENABLED",
        "Targets": Array [
          Object {
            "Arn": Object {
              "Fn::ImportValue": "Dev01-ShareResources:ExportsOutputRefDev01AlarmSNSTopic8457D16EDBE0EDE6",
            },
            "Id": "Target0",
          },
        ],
      },
      "Type": "AWS::Events::Rule",
    },
    "BastionEcsCommonConstructBastionECSServiceDeploymentEventRule82AE6645": Object {
      "Properties": Object {
        "Description": "CloudWatch Event Rule to send notification on Bastion ECS Service deployment events.",
        "EventPattern": Object {
          "detail": Object {
            "eventType": Array [
              "WARN",
              "ERROR",
            ],
          },
          "detail-type": Array [
            "ECS Deployment State Change",
          ],
          "source": Array [
            "aws.ecs",
          ],
        },
        "State": "ENABLED",
        "Targets": Array [
          Object {
            "Arn": Object {
              "Fn::ImportValue": "Dev01-ShareResources:ExportsOutputRefDev01AlarmSNSTopic8457D16EDBE0EDE6",
            },
            "Id": "Target0",
          },
        ],
      },
      "Type": "AWS::Events::Rule",
    },
    "BastionEcsCommonConstructBastionEcsCluster3733E5BE": Object {
      "Properties": Object {
        "ClusterSettings": Array [
          Object {
            "Name": "containerInsights",
            "Value": "enabled",
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::ECS::Cluster",
    },
    "BastionEcsCommonConstructBastionEcsClusterC14D53B8": Object {
      "Properties": Object {
        "CapacityProviders": Array [
          "FARGATE",
          "FARGATE_SPOT",
        ],
        "Cluster": Object {
          "Ref": "BastionEcsCommonConstructBastionEcsCluster3733E5BE",
        },
        "DefaultCapacityProviderStrategy": Array [],
      },
      "Type": "AWS::ECS::ClusterCapacityProviderAssociations",
    },
    "BastionEcsCommonConstructBastionEcsTaskExecutionRoleCFF6097D": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "ecs-tasks.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy",
              ],
            ],
          },
        ],
        "Policies": Array [
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": "logs:CreateLogGroup",
                  "Effect": "Allow",
                  "Resource": "*",
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "createLogs",
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "BastionEcsCommonConstructbastionrepo39941F80": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "EmptyOnDelete": true,
        "ImageScanningConfiguration": Object {
          "ScanOnPush": true,
        },
        "LifecyclePolicy": Object {
          "LifecyclePolicyText": "{\\"rules\\":[{\\"rulePriority\\":1,\\"description\\":\\"Keep last 5 images\\",\\"selection\\":{\\"tagStatus\\":\\"any\\",\\"countType\\":\\"imageCountMoreThan\\",\\"countNumber\\":5},\\"action\\":{\\"type\\":\\"expire\\"}}]}",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::ECR::Repository",
      "UpdateReplacePolicy": "Delete",
    },
    "BastionEcsTaskExecutionRoleArnParameter39B9A254": Object {
      "Properties": Object {
        "Name": "/Dev01/bastion/ecs/task-execution-role-arn",
        "Tags": Object {
          "Environment": "dev01",
        },
        "Type": "String",
        "Value": Object {
          "Fn::GetAtt": Array [
            "BastionEcsCommonConstructBastionEcsTaskExecutionRoleCFF6097D",
            "Arn",
          ],
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "BastionTiDBEndpointParameter3DB7334D": Object {
      "Properties": Object {
        "Name": "/Dev01/bastion/tidb/endpoint",
        "Tags": Object {
          "Environment": "dev01",
        },
        "Type": "String",
        "Value": "tidb_server_host",
      },
      "Type": "AWS::SSM::Parameter",
    },
    "CustomCDKECRDeploymentbd07c930edb94112a20f03f096f53666512MiB28EAD8E4": Object {
      "DependsOn": Array [
        "CustomCDKECRDeploymentbd07c930edb94112a20f03f096f53666512MiBServiceRoleDefaultPolicy280095F8",
        "CustomCDKECRDeploymentbd07c930edb94112a20f03f096f53666512MiBServiceRole8C8B0491",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": "cdk-hnb659fds-assets-************-ap-northeast-1",
          "S3Key": "62494328fd645460d4bebd6302a3dae5e88a958d59832ae256df87891c628542.zip",
        },
        "Handler": "bootstrap",
        "MemorySize": 512,
        "Role": Object {
          "Fn::GetAtt": Array [
            "CustomCDKECRDeploymentbd07c930edb94112a20f03f096f53666512MiBServiceRole8C8B0491",
            "Arn",
          ],
        },
        "Runtime": "provided.al2023",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "CustomCDKECRDeploymentbd07c930edb94112a20f03f096f53666512MiBServiceRole8C8B0491": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
              ],
            ],
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "CustomCDKECRDeploymentbd07c930edb94112a20f03f096f53666512MiBServiceRoleDefaultPolicy280095F8": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "ecr:GetAuthorizationToken",
                "ecr:BatchCheckLayerAvailability",
                "ecr:GetDownloadUrlForLayer",
                "ecr:GetRepositoryPolicy",
                "ecr:DescribeRepositories",
                "ecr:ListImages",
                "ecr:DescribeImages",
                "ecr:BatchGetImage",
                "ecr:ListTagsForResource",
                "ecr:DescribeImageScanFindings",
                "ecr:InitiateLayerUpload",
                "ecr:UploadLayerPart",
                "ecr:CompleteLayerUpload",
                "ecr:PutImage",
              ],
              "Effect": "Allow",
              "Resource": "*",
            },
            Object {
              "Action": "s3:GetObject",
              "Effect": "Allow",
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "CustomCDKECRDeploymentbd07c930edb94112a20f03f096f53666512MiBServiceRoleDefaultPolicy280095F8",
        "Roles": Array [
          Object {
            "Ref": "CustomCDKECRDeploymentbd07c930edb94112a20f03f096f53666512MiBServiceRole8C8B0491",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`Dev01 Guest Stacks GuestAccount ECS App Stacks 5`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "Dev01CustomResourceAcmAcmServiceTokenF54FA94E": Object {
      "Properties": Object {
        "Name": "/Dev01/AcmServiceToken",
        "Tags": Object {
          "Environment": "dev01",
        },
        "Type": "String",
        "Value": Object {
          "Fn::GetAtt": Array [
            "Dev01CustomResourceAcmProviderframeworkonEvent54F0D509",
            "Arn",
          ],
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "Dev01CustomResourceAcmProviderframeworkonEvent54F0D509": Object {
      "DependsOn": Array [
        "Dev01CustomResourceAcmProviderframeworkonEventServiceRoleDefaultPolicy3D9D3094",
        "Dev01CustomResourceAcmProviderframeworkonEventServiceRole824349FF",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": "cdk-hnb659fds-assets-************-ap-northeast-1",
          "S3Key": "4dc48ffba382f93077a1e6824599bbd4ceb6f91eb3d9442eca3b85bdb1a20b1e.zip",
        },
        "Description": "AWS CDK resource provider framework - onEvent (Dev01-acm/Dev01-CustomResource-Acm/Provider)",
        "Environment": Object {
          "Variables": Object {
            "USER_ON_EVENT_FUNCTION_ARN": Object {
              "Fn::GetAtt": Array [
                "Dev01CustomResourceAcmfunction66B79E1F",
                "Arn",
              ],
            },
          },
        },
        "Handler": "framework.onEvent",
        "Role": Object {
          "Fn::GetAtt": Array [
            "Dev01CustomResourceAcmProviderframeworkonEventServiceRole824349FF",
            "Arn",
          ],
        },
        "Runtime": "nodejs20.x",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "Dev01CustomResourceAcmProviderframeworkonEventServiceRole824349FF": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
              ],
            ],
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "Dev01CustomResourceAcmProviderframeworkonEventServiceRoleDefaultPolicy3D9D3094": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "lambda:InvokeFunction",
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "Dev01CustomResourceAcmfunction66B79E1F",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "Dev01CustomResourceAcmfunction66B79E1F",
                          "Arn",
                        ],
                      },
                      ":*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "Dev01CustomResourceAcmProviderframeworkonEventServiceRoleDefaultPolicy3D9D3094",
        "Roles": Array [
          Object {
            "Ref": "Dev01CustomResourceAcmProviderframeworkonEventServiceRole824349FF",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "Dev01CustomResourceAcmfunction66B79E1F": Object {
      "Properties": Object {
        "Code": Object {
          "S3Bucket": "cdk-hnb659fds-assets-************-ap-northeast-1",
          "S3Key": "60beba43fe89a85f0409d390dfbba9ac6f6027c34ce3c8924b7703a21de5e7f6.zip",
        },
        "FunctionName": "Dev01-ACM-functions",
        "Handler": "AlbAcm.acm_handler",
        "LoggingConfig": Object {
          "LogGroup": Object {
            "Ref": "functionlogsBE268F5F",
          },
        },
        "Role": Object {
          "Fn::ImportValue": "Dev01-ShareResources:ExportsOutputFnGetAttDev01LambdaRoleAcmFunctionLambdaRole0612C1D3Arn9D658D77",
        },
        "Runtime": "python3.12",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
        "Timeout": 300,
      },
      "Type": "AWS::Lambda::Function",
    },
    "functionlogsBE268F5F": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "LogGroupName": "/aws/lambda/Dev01/ACM-functions-log",
        "RetentionInDays": 365,
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::Logs::LogGroup",
      "UpdateReplacePolicy": "Delete",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`Dev01 Guest Stacks GuestAccount ECS App Stacks 6`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "Dev01AlbAliasAliasServiceToken78CAD2FE": Object {
      "Properties": Object {
        "Name": "/Dev01/AliasServiceToken",
        "Tags": Object {
          "Environment": "dev01",
        },
        "Type": "String",
        "Value": Object {
          "Fn::GetAtt": Array [
            "Dev01AlbAliasProviderframeworkonEvent9BF6DD58",
            "Arn",
          ],
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "Dev01AlbAliasProviderframeworkonEvent9BF6DD58": Object {
      "DependsOn": Array [
        "Dev01AlbAliasProviderframeworkonEventServiceRoleDefaultPolicyA044D15E",
        "Dev01AlbAliasProviderframeworkonEventServiceRole295EC58F",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": "cdk-hnb659fds-assets-************-ap-northeast-1",
          "S3Key": "4dc48ffba382f93077a1e6824599bbd4ceb6f91eb3d9442eca3b85bdb1a20b1e.zip",
        },
        "Description": "AWS CDK resource provider framework - onEvent (Dev01-ALbAlias/Dev01-AlbAlias/Provider)",
        "Environment": Object {
          "Variables": Object {
            "USER_ON_EVENT_FUNCTION_ARN": Object {
              "Fn::GetAtt": Array [
                "Dev01AlbAliasfunction5C314CA8",
                "Arn",
              ],
            },
          },
        },
        "Handler": "framework.onEvent",
        "Role": Object {
          "Fn::GetAtt": Array [
            "Dev01AlbAliasProviderframeworkonEventServiceRole295EC58F",
            "Arn",
          ],
        },
        "Runtime": "nodejs20.x",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "Dev01AlbAliasProviderframeworkonEventServiceRole295EC58F": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
              ],
            ],
          },
        ],
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "Dev01AlbAliasProviderframeworkonEventServiceRoleDefaultPolicyA044D15E": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "lambda:InvokeFunction",
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "Dev01AlbAliasfunction5C314CA8",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "Dev01AlbAliasfunction5C314CA8",
                          "Arn",
                        ],
                      },
                      ":*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "Dev01AlbAliasProviderframeworkonEventServiceRoleDefaultPolicyA044D15E",
        "Roles": Array [
          Object {
            "Ref": "Dev01AlbAliasProviderframeworkonEventServiceRole295EC58F",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "Dev01AlbAliasfunction5C314CA8": Object {
      "Properties": Object {
        "Code": Object {
          "S3Bucket": "cdk-hnb659fds-assets-************-ap-northeast-1",
          "S3Key": "26e046e0008fda0a76d8297914a82802df4d975d1cde7d44b7fb3fbae87fd68b.zip",
        },
        "FunctionName": "Dev01-Alb-Alias",
        "Handler": "AlbAliasRecode.alias_handler",
        "LoggingConfig": Object {
          "LogGroup": Object {
            "Ref": "functionlogsBE268F5F",
          },
        },
        "Role": Object {
          "Fn::ImportValue": "Dev01-ShareResources:ExportsOutputFnGetAttDev01LambdaRoleAcmFunctionLambdaRole0612C1D3Arn9D658D77",
        },
        "Runtime": "python3.12",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
        "Timeout": 300,
      },
      "Type": "AWS::Lambda::Function",
    },
    "functionlogsBE268F5F": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "LogGroupName": "/aws/lambda/Dev01/Alb-Alias-log",
        "RetentionInDays": 365,
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev01",
          },
        ],
      },
      "Type": "AWS::Logs::LogGroup",
      "UpdateReplacePolicy": "Delete",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;
