import * as cdk from 'aws-cdk-lib';
import { Template } from 'aws-cdk-lib/assertions';
import { IConfig } from '../params/interface';
import { OidcStack } from '../lib/stack/oidc-stack';
import { InfraResourcesPipelineStack } from '../lib/stack/pipeline-infraresources-stack';
import { ShareResourcesStack } from '../lib/stack/share-resources-stack';
import { BastionEcsCommonStack } from '../lib/stack/bastion-ecs-common-stack';
import { AcmStack } from '../lib/stack/acm-stack';
import { AlbAliasStack } from '../lib/stack/alb-aliasrecord-stack';
import { SecretStack } from '../lib/stack/secret-stack';

// Account and Region on test
//  cdk.process.env.* returns undefined, and cdk.Stack.of(this).* returns ${Token[Region.4]} at test time.
//  In such case, RegionInfo.get(cdk.Stack.of(this).region) returns error and test will fail.
//  So we pass 'ap-northeast-1' as region code.
const procEnv = {
  account: process.env.CDK_DEFAULT_ACCOUNT,
  region: process.env.CDK_DEFAULT_REGION ?? 'ap-northeast-1',
};

const app = new cdk.App();

const envKey = 'dev01';

const config: IConfig = require('../params/' + envKey);
const pjPrefix = config.Env.envName + config.Env.prefix;

function getProcEnv() {
  if (config.Env.account && config.Env.region) {
    return {
      account: config.Env.account,
      region: config.Env.region,
    };
  } else {
    return procEnv;
  }
}

describe(`${pjPrefix} Guest Stacks`, () => {
  test('GuestAccount ECS App Stacks', () => {
    // Empty Secret Manager
    const secrets = new SecretStack(app, `${pjPrefix}-Secrets`, {
      prefix: pjPrefix,
      env: getProcEnv(),
    });

    // Share resources
    const shareResources = new ShareResourcesStack(app, `${pjPrefix}-ShareResources`, {
      pjPrefix,
      isCreateChatbot: config.NotifierParam.isCreate,
      notifyEmail: config.NotifierParam.monitoringNotifyEmail,
      workspaceId: config.NotifierParam.workspaceId,
      channelId: config.NotifierParam.channelIdMon,
      myVpcCidr: config.VpcParam.cidr,
      myVpcMaxAzs: config.VpcParam.maxAzs,
      natGateways: config.VpcParam.natGateways,
      ...config.VpcForTiDBParam,
      env: getProcEnv(),
      CrossAccountRoleArn: config.lambdaIamParam.CrossAccountRoleArn,
      myFlowLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      otherRemovalPolicyParam: config.OtherRemovalPolicyParam,
      kmsPendingWindow: config.KmsKeyParam?.pendingWindow,
      fluentbitRepositoryName: 'fluentbitrepo',
      fluentbitEcrLifecycleRules: config.FluentbitParam.lifecycleRules,
      fluentbitEcrRemovalPolicyParam: config.OtherRemovalPolicyParam,
      fluentbitRepositoryDefault: config.FluentbitParam.repositoryDefault,
    });

    // Infrastructure pipeline
    const infraPipeline = new InfraResourcesPipelineStack(app, `${pjPrefix}-Pipeline`, {
      ...config.InfraResourcesPipelineParam,
      pjPrefix,
      envKey,
      env: getProcEnv(),
      appKey: shareResources.appKey,
      pipelineBucketRemovalPolicyParam: config.OtherRemovalPolicyParam,
      newrelicSecretArn: secrets.nrSecret.secretArn,
      buildLogBucketLifecycleRules: config.s3BuildLogLifecycleRules,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
    });

    const oidc = new OidcStack(app, `${pjPrefix}-OIDC`, {
      oidcProviderArn: config.OidcParam.oidcProviderArn,
      OrganizationName: config.OidcParam.OrganizationName,
      RepositoryNames: config.OidcParam.RepositoryNames,
      env: getProcEnv(),
      pjPrefix,
    });

    const bastionEcsCommon = new BastionEcsCommonStack(app, `${pjPrefix}-BastionEcsCommon`, {
      pjPrefix,
      vpc: shareResources.vpc.myVpc,
      alarmTopic: shareResources.alarmTopic.topic,
      repositoryName: 'bastionrepo',
      ecrLifecycleRules: config.BastionParam.lifecycleRules,
      env: getProcEnv(),
      ecrRemovalPolicyParam: config.OtherRemovalPolicyParam,
      tidbEndpoint: config.TiDBEndpoint.tidbEndpoint,
    });

    const acm = new AcmStack(app, `${pjPrefix}-acm`, {
      prefix: pjPrefix,
      AcmFunctionlambdaRole: shareResources.lambdaRole.AcmFunctionlambdaRole,
      env: getProcEnv(),
      logRemovalPolicy: config.LogRemovalPolicyParam?.removalPolicy,
      LambdaRuntime: config.CustomResourceAcmParam.lambdaRuntime,
    });

    const alias = new AlbAliasStack(app, `${pjPrefix}-ALbAlias`, {
      AliasFunctionlambdaRole: shareResources.lambdaRole.AcmFunctionlambdaRole,
      prefix: pjPrefix,
      env: getProcEnv(),
      crossRegionReferences: true,
      logRemovalPolicy: config.LogRemovalPolicyParam?.removalPolicy,
      LambdaRuntime: config.CustomResourceAliasParam.lambdaRuntime,
    });

    // Tagging "Environment" tag to all resources in this app
    const envTagName = 'Environment';
    cdk.Tags.of(app).add(envTagName, envKey);

    // test with snapshot
    expect(Template.fromStack(shareResources)).toMatchSnapshot();
    expect(Template.fromStack(infraPipeline)).toMatchSnapshot();
    expect(Template.fromStack(oidc)).toMatchSnapshot();
    expect(Template.fromStack(bastionEcsCommon)).toMatchSnapshot();
    expect(Template.fromStack(acm)).toMatchSnapshot();
    expect(Template.fromStack(alias)).toMatchSnapshot();
  });
});
