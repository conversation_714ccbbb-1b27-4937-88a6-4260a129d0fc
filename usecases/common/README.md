# Common resource

- Include 3 stack
  - ShareResourcesStack
  - InfraResourcesPipelineStack
  - OidcStack

### First init

- Move to `common` folder: `cd usecases/common`
- Install node modules: `npm install`
- Check CDK version: `cdk --version`

### Useful commands

- Move to `common` folder: `cd usecases/common`

- List stack:

```
cdk ls -c environment=<env name>

# ex
cdk ls -c environment=dev01
```

- Compare deployed stack with current state:

```
cdk diff -c environment=<env name>

# ex
cdk diff -c environment=dev01
```

- Deploy stacks to your AWS account/region

```
cdk deploy --all -c environment=<env name>

# ex
cdk deploy --all -c environment=dev01
```

- Perform the jest unit tests

```
npm test

# run unit tests and generate snapshot
npm test -- -u
```
