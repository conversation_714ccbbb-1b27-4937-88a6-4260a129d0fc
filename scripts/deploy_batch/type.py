from typing import Literal, Optional, TypedDict, Union


class Config(TypedDict):
    env: "EnvironmentParameter"
    tasks: dict[str, "TaskParameter"]


class EnvironmentParameter(TypedDict):
    name: str
    service_prefix: str


class TaskParameter(TypedDict):
    definition: "TaskDefinitionParameter"
    build: dict[str, "DockerParameter"]


class TaskDefinitionParameter(TypedDict):
    name: str
    spec: "TaskSpecParameter"


class TaskSpecParameter(TypedDict):
    cpu: int
    memory: int


class DockerParameter(TypedDict):
    context: str
    dockerfile: Optional[str]
    image_placeholder: str


class Triggers(TypedDict):
    version: str
    triggers: dict[str, "Trigger"]


Trigger = Union["CronTrigger", "APITrigger"]


class CronTrigger(TypedDict):
    type: Literal["cron"]
    name: str
    cron: "Cron"
    state: Literal["ENABLED", "DISABLED"]
    inputs: dict[str, "Input"]


class Cron(TypedDict):
    minute: Optional[str]
    hour: Optional[str]
    date: Optional[str]
    month: Optional[str]
    dayOfWeek: Optional[str]
    year: Optional[str]


class Input(TypedDict):
    commands: list["Command"]


class Command(TypedDict):
    containerName: str
    passedCommand: list[str]


class APITrigger(TypedDict):
    type: Literal["api"]
    name: str
    apiPath: str
