[MULTILINE_PARSER]
    name          multiline-regex-ruby
    type          regex
    flush_timeout 1000
    #
    # Regex rules for multiline parsing
    # ---------------------------------
    #
    # configuration hints:
    #
    #  - first state always has the name: start_state
    #  - every field in the rule must be inside double quotes
    #
    # rules |   state name  | regex pattern                  | next state
    # ------|---------------|--------------------------------------------
    rule "start_state"  "^[A-Z], \[\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+ #[0-9]+\]" "cont"
    rule "cont"         "^\s+.*"  "cont"
