{
  "deploymentConfiguration": {
    "deploymentCircuitBreaker": {
      "enable": true,
      "rollback": true
    },
    "maximumPercent": 200,
    "minimumHealthyPercent": 50
  },
  "deploymentController": {
    "type": "ECS"
  },
  "enableECSManagedTags": false,
  "enableExecuteCommand": true,
  "launchType": "",
  "networkConfiguration": {
    "awsvpcConfiguration": {
      "assignPublicIp": "DISABLED",
      "securityGroups": ["{{ must_env `SECURITY_GROUP` }}"],
      "subnets": ["{{ must_env `SUBNET_1` }}", "{{ must_env `SUBNET_2` }}", "{{ must_env `SUBNET_3` }}"]
    }
  },
  "tags":[
    {
    "key": "{{ must_env `CM_BILLING_GROUP_TAG_KEY` }}",
    "value": "{{ must_env `CM_BILLING_GROUP_TAG` }}"
    },
    {
    "key": "ServiceID",
    "value": "{{ must_env `ServiceID_TAG` }}"
    }
  ],
  "platformFamily": "Linux",
  "platformVersion": "LATEST",
  "propagateTags": "{{ must_env `PROPAGATE_TAG` }}",
  "schedulingStrategy": "REPLICA",
  "availabilityZoneRebalancing": "ENABLED",
  "serviceRegistries":[
    {
      "registryArn": "{{must_env `CLOUDMAP_SERVICE_ARN`}}"
    }
  ],
  "serviceConnectConfiguration": {
    "enabled": true,
    "namespace": "{{ must_env `NAMESPACE` }}",
    "services": [
      {
        "portName": "backend",
        "discoveryName": "{{must_env `DISCOVERY_NAME`}}",
        "clientAliases": [
          {
            "port": {{ must_env `PORT_NUMBER` }}
          }
        ]
      }
    ],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "{{ must_env `LOG_GROUP_SERVICE_CONNECT` }}",
        "awslogs-region": "ap-northeast-1",
        "awslogs-stream-prefix": "ecs"
      }
    }
  }
}
