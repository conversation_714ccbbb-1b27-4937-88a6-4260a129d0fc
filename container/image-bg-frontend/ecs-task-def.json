{
  "containerDefinitions": [
    {
      "essential": true,
      "image": "{{ must_env `FIRELENS_IMAGE_NAME` }}",
      "name": "logRouter",
      "firelensConfiguration" : {
        "type": "fluentbit"
      },
      "logConfiguration" : {
        "logDriver": "awslogs",
        "options" : {
          "awslogs-group": "{{ must_env `LOG_GROUP_FIRELENS` }}",
          "awslogs-region" : "ap-northeast-1",
          "awslogs-create-group": "true",
          "awslogs-stream-prefix" : "EcsFrontendBg-FireLens-"
        }
      },
      "memoryReservation": 50
    },
    {
      "cpu": 0,
      "dockerLabels": {},
      "essential": true,
      "image": "{{ must_env `IMAGE1_NAME` }}",
      "logConfiguration": {
        "logDriver": "awsfirelens",
        "options": {
          "Name": "firehose",
          "region": "ap-northeast-1",
          "delivery_stream" : "{{ must_env `STREAM_NAME`}}",
          "retry_limit": "2"
        }
      },
      "memoryReservation": 100,
      "name": "EcsAppBg",
      "portMappings": [
        {
          "appProtocol": "",
          "containerPort": {{ must_env `PORT_NUMBER` }},
          "hostPort": {{ must_env `PORT_NUMBER` }},
          "protocol": "tcp"
        }
      ],
      "secrets": [],
      "linuxParameters": {
        "initProcessEnabled": true
      }
    }
  ],
  "cpu": "256",
  "executionRoleArn": "{{ must_env `EXECUTION_ROLE_ARN` }}",
  "taskRoleArn": "{{must_env `TASK_ROLE`}}",
  "family": "{{ must_env `FAMILY` }}",
  "ipcMode": "",
  "memory": "1024",
  "networkMode": "awsvpc",
  "pidMode": "",
  "requiresCompatibilities": ["FARGATE"]
}
