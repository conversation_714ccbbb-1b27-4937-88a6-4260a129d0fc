import boto3
import os
import sys
import configparser
from botocore.exceptions import ClientError

autoscaling = boto3.client("application-autoscaling")
elbv2 = boto3.client("elbv2")
ecs = boto3.client("ecs")

ecs_cluster = os.environ["ECS_CLUSTER"]
ecs_service = os.environ["ECS_SERVICE"]
blue_target_group_arn = os.environ["BLUE_TARGET_GROUP_ARN"]
green_target_group_arn = os.environ["GREEN_TARGET_GROUP_ARN"]

script_dir = os.path.dirname(os.path.abspath(__file__))


# 設定ファイル読み込み
def read_param_file():
    param_file = os.path.join(script_dir, "param.ini")
    if not os.path.isfile(param_file):
        sys.exit("Configuration file 'param.ini' not found.")

    param = configparser.ConfigParser()
    param.read(param_file)
    return param


# スケーラブルターゲットの設定
# https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/application-autoscaling/client/register_scalable_target.html
def register_scalable_target(min_capacity, max_capacity):
    try:
        response = autoscaling.register_scalable_target(
            ServiceNamespace="ecs",
            ScalableDimension="ecs:service:DesiredCount",
            ResourceId=f"service/{ecs_cluster}/{ecs_service}",
            MinCapacity=int(min_capacity),
            MaxCapacity=int(max_capacity),
        )
        print("Scalable target registered:", response)
    except ClientError as e:
        print(f"Error registering scalable target: {e}")


# ターゲット追跡スケーリングポリシーの設定
# https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/application-autoscaling/client/put_scaling_policy.html
def put_scaling_policy(target_value):
    try:
        response = autoscaling.put_scaling_policy(
            ServiceNamespace="ecs",
            ScalableDimension="ecs:service:DesiredCount",
            ResourceId=f"service/{ecs_cluster}/{ecs_service}",
            PolicyName="Test-target-tracking-scaling-policy",
            PolicyType="TargetTrackingScaling",
            TargetTrackingScalingPolicyConfiguration={
                "TargetValue": int(target_value),
                "PredefinedMetricSpecification": {
                    "PredefinedMetricType": "ECSServiceAverageCPUUtilization"
                },
            },
        )
        print("Scaling policy put:", response)
    except ClientError as e:
        print(f"Error putting scaling policy: {e}")


# ALBヘルスチェックの設定
# https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/elbv2/client/modify_target_group.html
def modify_target_group(
    health_path,
    interval,
    timeout,
    healthy_threshold_count,
    unhealthy_threshold_count,
):
    target_groups = [blue_target_group_arn, green_target_group_arn]

    for target_group_arn in target_groups:
        try:
            response = elbv2.modify_target_group(
                TargetGroupArn=target_group_arn,
                HealthCheckPath=health_path,
                HealthCheckIntervalSeconds=int(interval),
                HealthCheckTimeoutSeconds=int(timeout),
                HealthyThresholdCount=int(healthy_threshold_count),
                UnhealthyThresholdCount=int(unhealthy_threshold_count),
            )
            print(f"Target group {target_group_arn} modified:", response)
        except ClientError as e:
            print(f"Error modifying target group {target_group_arn}: {e}")


def main():

    # 設定ファイル読み込み
    param = read_param_file()

    try:
        min_capacity = param["AutoScale"]["MIN_CAPACITY"]
        max_capacity = param["AutoScale"]["MAX_CAPACITY"]
        target_value = param["AutoScale"]["TARGET_VALUE"]
        health_path = param["HealthCheck"]["HEALTH_PATH"]
        interval = param["HealthCheck"]["INTERVAL"]
        timeout = param["HealthCheck"]["TIMEOUT"]
        healthy_threshold_count = param["HealthCheck"]["HEALTHY_THRESHOLD_COUNT"]
        unhealthy_threshold_count = param["HealthCheck"]["UNHEALTHY_THRESHOLD_COUNT"]

    except KeyError as e:
        print(f"KeyError: The key '{e.args[0]}' is not found")

    # AutoScalingの更新
    register_scalable_target(min_capacity, max_capacity)
    put_scaling_policy(target_value)

    # ALB HealthCheckの更新
    modify_target_group(
        health_path,
        interval,
        timeout,
        healthy_threshold_count,
        unhealthy_threshold_count,
    )


if __name__ == "__main__":
    main()
