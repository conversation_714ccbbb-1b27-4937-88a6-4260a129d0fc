# 名前解決の設計方針

## 概要

**ECS サービス間通信**

- Service Connect を利用して異なる ECS サービス間の名前解決を行う。
- Cloud Map の名前空間は、同一環境内 (stg01/prod01) のすべてのサービスで同一にする。

**踏み台/バッチコンテナ通信とバックエンドコンテナ間通信**

- 踏み台コンテナやバッチコンテナなどの単発タスクは、Service Discovery を利用して名前解決を行う。
- 接続先の ECS サービスから Service Discovery を有効化することで、Route 53 を利用した名前解決を行う。

## 構成

### ECS サービス間の名前解決

![](/docs/images/service-connect-name-resolve-architecture.dio.svg)

### 踏み台/バッチ コンテナとバックエンドコンテナ間の名前解決

![](/docs/images/service-discovery-name-resolve-architecture.dio.svg)

### リソース一覧

- Cloud Map
- Route 53
- ECS
  - フロントエンドコンテナ
  - バックエンドコンテナ
  - 踏み台コンテナ
  - バッチコンテナ

## 設計方針

**ECS サービス間通信**

- 主なユースケース

  - 以下、通信元サービスを A、通信先サービスを B と呼称する。
  - フロントエンド(A) -> バックエンド(B)
    - ある案件の社内管理画面をサービス A、ユーザー画面をサービス B として作成する。
    - フロントエンドは異なるサービスとしてデプロイするが、バックエンドは共通とする。
    - バックエンドは B の方に作成する。
    - この時、フロントエンド(A)からバックエンド(B)へのサービス間通信が必要になる。  
      ![](../images/name-resolution-usecase1.dio.svg)
  - フロントエンド(A) -> フロントエンド(B)
    - バックエンドの API には、VPC 内のフロントエンドからだけでなく VPC 外からも接続が必要である。
      - 例) ネイティブアプリのバックエンド API としても使用するケース
    - その対応として、バックエンドの API を別サービス(B)のフロントエンドとしてデプロイする。
    - この時、VPC 内のフロントエンド(A)から、API 用のフロントエンド(B)へのサービス間通信が必要になる。  
      ![](../images/name-resolution-usecase2.dio.svg)

- Cloud Map の名前空間は専有リソースではなく、共有リソースとして作成する。
  - 共有リソースとした背景は以下の通り。
    - 案件によってはバックエンドコンテナに VPC 外からの接続が必要なケースがある場合は、スタックを分割して別 ECS サービスのフロントエンドコンテナとしてデプロイを行う。
    - 異なるスタック間リソースの名前解決が必要となるために、Cloud Map 名前空間はスタックごとの専有リソースではなく、共有リソースとし、同一環境内 (stg01/prod01...) の全てのサービスで同一とする。
- 異なるスタックの ECS サービス同士の名前解決が可能となっているが、サービス間の通信許可はセキュリティグループで制限するため、セキュリティ上の問題はない。
  - 通信制御の仕様は以下の通り。
    - 接続元 ECS サービスのフロントエンドの SG ID を SSM パラメーターストアに格納。
    - 接続先 ECS サービスのパラメーターファイルで、接続元 ECS サービス ID を指定する。
    - パラメーターファイルで指定された ECS サービス ID の SG からの通信を許可。

**踏み台/バッチコンテナ通信とバックエンドコンテナ間通信**

- 踏み台やバッチコンテナなどの単発タスクでは、Service Connect が利用できないため Service Discovery を利用して名前解決を行う。
  - Service Connect が利用できない背景として以下の理由がある。
    - 単発タスクは必要時に起動するタスクのため、ECS サービスのように常時起動のタスク数を指定する方法が困難である。
    - ※ Desired を 0 にすることもできるが、この場合だと 0 のためサイドカーを持ったタスクを立ち上げるのが不可。
    - Service Connect は Service Connect Agent によるサイドカーコンテナ経由で名前解決するため、ECS サービス上でタスクを起動させる必要がある背景から、Service Connect が利用できない。
  - 従って、接続先 ECS サービスから Service Discovery を有効化し Route 53 経由で名前解決をする。
- Service Descovery 用 Cloud Map サービスは専有リソースとして管理する。
  - バックエンドサービス定義に Cloud Map サービス ID を CodeBuild から取得し、ECS サービス定義に Cloud Map サービス ID を追加する。
- バックエンドコンテナのインバウンドルールに踏み台/バッチコンテナの SG を追加して通信を許可する。
