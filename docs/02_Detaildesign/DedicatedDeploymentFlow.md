# サービス専有リソースデプロイフロー

## 概要

- アプリケーションチーム からの AWS リソース 作成/更新 申請から、承認・デプロイまでのフローについて記載する。
- CDK デプロイ時に利用される CI/CD はベースコードの CodePipeline リソースから流用している。
- デプロイフローは 新規作成 / 個別変更 / 一括変更 の 3 つ存在する。
  - 新規作成 と 個別変更はデプロイフローは基本的に同一だが申請内容が異なる。
  - 一括変更は上記2つとフローが異なるため、詳細は検討中。

## 構成

![](/docs/images/application-flow-for-creating-aws-resource.dio.svg)

### リソース一覧

- Google Forms
  - 申請フォーム。
- Backlog
  - デプロイ承認チケット。
- Gevanni アカウント
  - S3 バケット
    - Step Functions ワークフローのトリガーとなる。
  - Step Functions ワークフロー
    - Lambda Function
      - Backlog チケット起票。
      - GitHub プルリクエストの作成。
  - CodePipeline
    - S3 バケット
      - ソースバケット、CodePipeline 実行のトリガーとなる。
    - CodeBuild
      - AWS CDK CLI によって CDK デプロイを実行する。
- GitHub Actions ワークフロー
  - cdk diff コマンドによる差分確認。
  - S3 バケットへ CDK コード配置。
- マケプレ購入アカウント
  - TiDB

## デプロイフロー

### 前提

#### 役割 (ロール) 

- フローに関わる役割 (ロール) が、申請者、承認者、作業者の 3 つ存在する。
  - 申請者
    - 内製案件のインフラとして、必要な AWS リソースを申請するアプリケーションチーム。
  - 承認者
    - 用途が適切かどうか、申請内容の妥当性を確認する。
  - 作業者
    - 自動生成された CDK コードの確認。
    - CDK 管理外の設定を行う。
    - GitHub プルリクエストをマージする。

**申請者**

- 内製案件のインフラとして、必要な AWS リソースを申請するアプリケーションチーム。
- Google Forms から AWS リソースの作成申請を行う。

**承認者**

- 用途が適切かどうか、申請内容の妥当性を確認する。
  - 例) イントラサイト系は NG
- CDK コードレベルの内部実装は感知しない。
- (2025/01 時点) メイン承認者は 1 課課長(小原)、サブ承認者は Gevanni リーダー(乳井)。
  - メイン承認者の不在時はサブ承認者が代理承認する。
  - メイン承認者は後から確認すること。

**作業者**

- CDK 管理外の設定を行う。
  - 例) TiDB のプロジェクト作成。
- 自動生成された CDK コードの確認。
  - `cdk diff` コマンドによる結果を確認する。
- GitHub プルリクエストをマージする。
  - プルリクのマージがトリガーになって、`cdk deploy` が自動実行される。
- (2025/01 時点) Gevanni 運用チームメンバー全員想定、MSP

#### Backlog

- 申請内容を Backlog の課題チケットで管理する。
- チケット追加時にチャット通知を行う。
  - (2024 年 時点): Slack チャンネルに通知を行う。
    - 将来的に Teams に変更の可能性あり。
- 通知を分けるため、Backlog プロジェクトは Gevanni 専用のものを使用する。

### サービス専有リソース新規作成のデプロイ

- デプロイフローの詳細を記載する。
- 【xx 者】は人が行う必要のある手順。
- (xxx) は自動化されている作業。

1. 【利用者】 Google Forms に申請内容を記載し送信する。
1. (GAS) Google Forms から S3 バケットへ 申請内容をアップロード。
   - 並行して、申請者に自動返信メールが送信される。
1. (EventBridge) S3 バケットにオブジェクトが配置されたことをトリガーに Step Functions を実行。
   - Step Functions 内のワークフロー
   1. (Lambda) Backlog に申請チケットを起票する。
      - チケットの内容は以下の通り。
        - 申請内容。
        - Step Functions トリガー用のリンク。
          - 誤クリックしないように折りたたみ記法を用いて記載。
   1. 【承認者】申請内容を確認して Backlog 上で承認する。
   - 申請内容に問題がある場合、チケットにコメントする。
   1. 【作業者】申請チケットの 承認/非承認 に従いチケット記載のリンクをクリックする。
      - 承認の場合。
        - 承認用トリガーのリンクをクリックする。
      - 非承認の場合。
        - 申請者に問い合わせをする。
        - 最新性になった場合は、非承認トリガーのリンクをクリックする。
   1. (Lambda) 承認用のリンクをトリガーに GitHub プルリクエストを作成する。
      - 新規サービスのパラメーターファイルを作成する。
1. プルリク自動作成後のデプロイフロー
   1. (GHA) プルリク作成をトリガーに cdk diff を実行する。
   - cdk コマンドのオプションでサービス ID の指定が必要となる。
   - GitHub Actions 内では GITHUB_HEAD_REF 変数でプルリク元ブランチ名を参照できるため、Lambda からの push 時に作成するブランチ名をサービス ID にする。
     - 参考: https://docs.github.com/ja/actions/writing-workflows/choosing-what-your-workflow-does/store-information-in-variables
   1. 【作業者】プルリクとテスト結果を確認し、問題なければ承認してマージする。
   1. (GHA) マージをトリガーにデプロイ実行のワークフローを実行する。
      - 設定ファイルを ZIP にして S3 にアップロード。
      - S3 へのアップロードをトリガーに CodePipeline を実行する。
   1. 【作業者】自動化できない手動作業を行う。
      - TiDB Cloud のプロジェクト作成など。
   1. 【作業差】申請者に完了通知メールを送信する。

### サービス専有リソースの個別変更のデプロイフロー

- 基本的に新規作成と同一だが、以下が異なる。
  - 申請フォームからサービス ID の指定が必須
    - 入力ミスを懸念して、選択式とする。
    - サービス新規作成時に、サービス ID のマスタを更新する仕組みが必要
      - 詳細は別途検討
  - パラメータファイル編集の排他制御
    - 特定サービスに同じタイミングに申請がきた場合、パラメータファイルを同時編集しないように、1 申請 1 プルリクとして、順番に処理する。
    - 実装案
      - 前提
        - プルリク元ブランチ名がサービス ID
        - マージしたら、プルリク元ブランチは即削除
          - リポジトリの設定
      - プルリク自動生成時に、マージ前のブランチがあるかチェック
      - マージ前のブランチが存在すれば、処理を戻す。
        - 1 つ目の申請のプルリクマージ後に、作業者は 2 つ目のリンクを再度クリック
  - 申請者への通知方法
    - メールではなく Pager Duty 経由も検討

### サービス専有リソースの一括変更のデプロイフロー

- 新規作成と個別変更とはフローが異なるので、詳細は別途検討。
- 想定ケース 1
  - サービス専有リソースの Construct ファイルを更新してから個々のサービスに適用する場合
  - パラメータファイルは更新しないため、個別変更とは別フローを設計する必要がある。
  - 更新適用の有無の判別をどうするか？
    - 以下のようなケースでは、更新適用したサービスとしていないサービスが混在する。
      - Construct ファイルを更新
      - 既存サービスに変更適用する前に新規サービスを新しい Construct ファイルで作成
