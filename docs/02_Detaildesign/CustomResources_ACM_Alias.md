# カスタムリソース設計

## 概要

- CDK でサポートされていない挙動についてはカスタムリソースを使用する。
- <PERSON><PERSON><PERSON><PERSON> では主に `ACM レコード設定`、`Alias レコード設定` について CDK デプロイ時にクロスアカウントアクセスが発生するためカスタムリソースを利用する。

## 構成

![](/docs/images/custom-resource-architecture.dio.svg)

### リソース一覧

- <PERSON><PERSON><PERSON>i アカウント (サービス用アカウント)
  - カスタムリソース
    - `acm-stack.ts`
      - ACM 認証レコード設定カスタムリソース。
    - `alb-aliasrecord-stack.ts`
      - ALB 用 Alias レコード設定カスタムリソース。
- Route 53 用アカウント
  - Route 53
    - CNAME レコード、Alias レコードを登録。

## ACM 認証レコード設定カスタムリソース設計

- カスタムリソースの利用目的
  - 未認証の ACM を CDK のみで作成しようとするとスタックの作成が完了せず、認証用 CNAME レコードを払い出せない。
  - ACM を発行するアカウントと CNAME レコードを登録する Route 53 のホストゾーンアカウントが別で、CDK デプロイ時にクロスアカウントアクセスが発生するため。
  
### 前提

- ACM 認証を CDK 上で実施する前に以下の処理が完了していることを確認する。
- 認証用レコードを追加するホストゾーンを CDK で作成している。
- 移譲用の NS レコード登録が完了している。
  - ※ Backlog で管理し手動で対応。
- サービス専有リソースパラメータの `IACMParam` に下記が正しく設定されていること。
  - `AcmDomainName`
  - `HostZoneId`
  - `AssumeRoleArn`

### 処理フロー

- `環境名-acm` スタックを CDK デプロイすることでカスタムリソースとして処理が実行される。
- スタックのデプロイが開始されると関数 (`ALBAcm.py`) が実行される。
- カスタムリソース処理
  - スタックの作成/更新/削除(Create/Update/Delete)をトリガーとしている。以下はそれぞれのイベントの処理内容。

1. 作成
   1. ACM リクエストを作成 (サービス用アカウント)
   1. CNAME レコードを取得 (サービス用アカウント)
   1. CNAME レコードをホストゾーンに追加 (Route53用アカウント)
   1. ACM 認証確認 (サービス用アカウント)

1. 更新
   1. ステータスコード `200` を返して処理を終了する。
      - ※ドメインが変わる (更新処理) の場合サービスとしては別物扱いのため更新によるドメイン変更はないものする。

1. 削除
   1. 対象 ACM の CNAME レコードを取得 (サービス用アカウント)
   1. ホストゾーンから対象の認証用レコードを削除 (Route53用アカウント)
   1. 対象 ACM を削除 (サービス用アカウント)

## ALB 用 Alias レコード設定カスタムリソース設計

- カスタムリソース利用目的。
  - Route 53 のホストゾーンが存在するアカウントと ALB が存在するアカウントが別で、CDK デプロイ時クロスアカウントアクセスが発生するため。
  
### 前提

- Alias レコード登録用のカスタムリソース実行前に以下の内容を確認する。
  - Alias レコードを追加するホストゾーンが作成されている。
  - `環境名-ALbAlias` スタックにて、対象サービスの ALB が作成されている。

### 処理フロー

- `環境名-ALbAlias` スタックを CDK デプロイすることでカスタムリソースとして処理が実行される。
- スタックのデプロイが開始されると関数 (`AlbAliasRecode.py`) が実行される。
- カスタムリソース処理
  - スタックの 作成/更新/削除 (Create/Update/Delete) をトリガーとしている。

- それぞれのイベントの処理内容は以下の通り。
1. 作成
   1. ALB の Alias レコードをホストゾーンに追加 (Route 53 用アカウント)
1. 更新
   1. ステータスコード `200` を返して処理を終了する。
      - ※ドメインが変わる (更新処理) の場合サービスとしては別物扱いのため更新によるドメイン変更はないものする。
1. 削除
   1. ホストゾーンから対象の Alias レコードを削除 (Route 53 用アカウント)
