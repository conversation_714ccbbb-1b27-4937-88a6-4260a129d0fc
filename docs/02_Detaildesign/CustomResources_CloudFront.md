# CloudFront 継続的デプロイメントポリシーのカスタムリソース設計

## 概要

- Blue/Green デプロイにおいて、新バージョンの動作確認を ALB のテスト用ポート（Green ポート）で行いたいが、カスタムヘッダーを付与しているため、ALB ドメイン経由でポートを指定することは不可能。そこで、テスト用ポートへ接続可能な別ディストリビューションを用意し、CloudFront の継続的デプロイメントポリシーで接続先ディストリビューションを制御する。
- CloudFormation では、ディストリビューション作成時に継続的デプロイメントポリシーを同時にアタッチすることがサポートされていないため、カスタムリソースを用いて実装する。

## 継続的デプロイメントポリシー

- 一般ユーザがアクセスするディストリビューションを Primary Distribution、ALB のテスト用ポート接続用のディストリビューションを Staging Distribution とする。
- Primary Distribution に継続的デプロイメントポリシーをアタッチし、ポリシーで定義した条件を満たしたアクセスは、Staging Distribution トラフィックさせる。

### 構成

![](./../images/cloudfront-continuousdeployment.dio.svg)

### Blue/Green デプロイにおける新バージョンのテスト

- BlueGreen デプロイは、本番稼働している環境（Blue 環境）とは別に、新しいバージョンをデプロイした環境（Green 環境）を用意する。
- Green 環境で動作確認を行い、問題がなければトラフィックを Blue 環境から Green 環境に切り替えることで、一般ユーザが新バージョンにアクセスできるようにする。

![](./../images/bluegreen-deployment-flow.dio.svg)

#### テスト用ポートへのアクセス経路

- 課題
  - フロントエンドの ALB は、CloudFront とのカスタムヘッダーを付与しているため、直接、テストポートを指定したアクセスができない。
  - CloudFront のビヘイビアは Blue ポートで ALB に接続する設定になっており、テストポートに接続できない。
- 解決策
  - ALB にテストポートで接続するディストリビューションを作成する。
  - 継続的デプロイメントポリシーで特定のヘッダー情報の有無に応じて接続先ディストリビューションを制御する。  
    ※ディストリビューションは異なるが、同じドメインを共有している

### 詳細

- 継続的デプロイメントポリシーのルールで、リクエストに以下のヘッダー情報を含む場合は、Staging Distribution へ遷移する。
  - key: `aws-cf-cd-bluegreen-deployment`
  - value: `green`
- Primary Distribution と Staging Distribution の差分は以下の通り
  ||Pimary Distribution|Staging Distribution|
  |-|-|-|
  |作成方法|CDK|Lambda|
  |オリジンの ALB の接続ポート|Blue ポート(80 または 443)|Green ポート(8080 または 8443)|
  |遷移の条件|デフォルト|BlueGreen 用ヘッダーあり|
  |ステージング|False|True|
- ALB の接続ポート以外のビヘイビア情報やキャッシュポリシーなどは Primary Distribution の設定値をそのまま Staging Distribution も引き継いでいる。

## カスタムリソース

### リソース一覧

- CloudFront
- Lambda
- SSM パラメータストア
- カスタムリソース
  - トリガー
    - `cloudfront-stack.ts`
  - カスタムリソース
    - `cloudfront-customresource-construct.ts`

### 構成

![](./../images/cloudfront-customresource.dio.svg)

### 設計方針

- カスタムリソースにはディストリビューション ID をパラメータとして渡しているが、ID が変更されない場合は再実行されないため、任意のランダム文字列を付加してスタック更新ごとに確実に再実行されるよう制御している。
- 毎回カスタムリソースが実行されるため、前回の実行から Primary Distribution に変更があったか、バージョン ID（ETag）で判断する
- カスタムリソースの Lambda 関数内のローカル変数の値は、実行が完了すると消えるため、リソースの情報は適宜 SSM パラメータストアに格納する。

### 処理フロー

- CloudFront スタックを CDK デプロイすることでカスタムリソースとして処理が実行される。
- スタックのデプロイが開始されると関数 (`ContinuousDeployment.py`) が実行される。
- カスタムリソース処理
  - スタックの作成/更新/削除(Create/Update/Delete)をトリガーとしている。以下はそれぞれのイベントの処理内容。

1. 作成

   1. CloudFront スタックの作成をトリガーに Lambda が実行される。
   1. Primary Distribution の設定情報を取得する。
   1. その情報を基に Staging Distribution を作成する。
   1. 継続的デプロイメントポリシーを作成し、Primary Distribution に継続的デプロイメントポリシーをアタッチする。
   1. SSM パラメータストアを作成し、Primary Distribution のバージョン ID（ETag）を格納する。

1. 更新

   1. CloudFront スタックの更新をトリガーに Lambda が実行される。
   1. Primary Distribution に前回のカスタムリソース実行時から変更があったか確認する。
      1. 前回実行時に保管した Primary Distribution の ETag と現状の Primary Distribution の ETag を取得する。
      1. 両者に差分があるか確認する。
         - 値が同じだった場合、処理は終了
         - 値が異なっている（ディストリビューションに更新があった）場合は、次の処理へ
   1. Primary Distribution の設定情報を取得する。
   1. Staging Distribution に Primary Distribution の変更を反映する。
   1. Primary Distribution の ETag を SSM パラメータストアに格納する。

1. 削除
   1. CloudFront スタックの削除をトリガーに Lambda が実行される。
   1. Primary Distribution から継続的デプロイメントポリシーをデタッチする。
   1. 継続的デプロイメントポリシーを無効化した後、削除する。
   1. Staging Distribution を無効化した後、削除する。
   1. SSM パラメータストアを削除する。
