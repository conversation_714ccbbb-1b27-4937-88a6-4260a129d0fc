<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" style="background: #ffffff; background-color: light-dark(#ffffff, #121212);" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="922px" height="521px" viewBox="-0.5 -0.5 922 521" content="&lt;mxfile&gt;&lt;diagram id=&quot;eN2EK5niKOSlhqgC7PYa&quot; name=&quot;250219&quot;&gt;zZlZd6LKFoB/DWvd8+BdDDI9MhnnGHHkjaEYFCgFFPTX3yqBqMF0232S7ktMLHZVbar2t4fSEIwSFS+JufNH0AEhQZNOQTAqQaOL5NAblpxKiUhVAi8JnFJEXQV6cAaVkKykh8AB6d3ADMIwC3b3QhvGMbCzO5mZJDC/H+bC8P6pO9MDDYFum2FTugyczC+lAs1f5V0QeH79ZIoTyx7LtLdeAg9x9TyCZtzLVXZHZq2r2mjqmw7Mb0SMRjBKAmFWtqJCASG2bW22cl7nk16KLdUezfBQbeVmRdXaExBnTylrN5RVi85OtaFyP8iAvjNtfJ8jZyAY2c+iEN1RqOkGYajAECaX0cgUgLNtJE+zBG7BTY/DixZJoh7HTH3gVNMhUhxk2IdY3FctByQZKD5wuu6pNEG1oxcAI5AlJzSmnlFbPr9ybbcrmX/LlKuEZuVL3ruuq81QozLbJyZkGiaUljqhMYRA4pfGE5JGSBRuiEjCNQyMHGSHm0F0cVkZ7z5Ajjo0LRBOYBpkAYxRfxQ4Dp4jh7hDfode2ziG8e10KQy8u2lmuisDyQ0KbH/58kCplpK1BLUdMzMJRipv6c4u9ghaCRby6zQnBy8elNA11ue+NvdQSxXwPRqnSCPUkD0PqBkWSauxPiV7UpK2be4NC6bx25ySJUkpNvlRWL/NsfA814r5VH87TjY9QQ+zjbUbrGLRWmMK3ZOAWVxf8ixkkQPI232yR2851Afbs9hfRGEOOLrI0s3wTFLLdWyIfvGqU5eFSH1b8w07l6ROoaEl2lPJhpFwxivvGvp8Ki86fpc3VpZ9SD1VW7LLRZEh9RP0e+QdXuHmrypjoHiRHZttz/R1pIw6L1t/dRYmai5oIBf9U3qY2Xz3bE4jVSUPKyVis/OrcV4sz054Xm+Xnc4UKZiuR/nhhbMH1HGyM6xRlBeFT/nkih6NGRPv6TDa5jPbCJSAUk/ekswXM+Xgr9d8zzes4djwWZVvU/OTuU34eNsbxW9MsNpKM6ZwtAWyUjacdBbOetw2k8jl1ByCbRGT+vEkK0LiD/OxCiXX32Tr156x33JzOC3UPj/yB4WUYpAuChG6A5Nze5rCCTk7RtueZa2EN0Ff9Jm9IexydwmPniDMzGE+gLmX+Cd31i1cSZxGfNxZoV0oiykoXtQ96/XEnjlY9+S53FaXb6rG7ZNN2JZYZwjz2bI3tgM8ft/dD2LhDT14fVJ8w+lY0ngzYpe20h+qnc4Mozhtujt2SY/HlAKdyayruOTKOOixcQDZxkcZWtbR/D2nob+biMa7cNuD2fZVOI+UdbEaTVYBkq02yeT1uFNXKAHIwkjs2wu9q5iRdZQPh2SCwUfjTTgccjw1iXvCSY3ovk6pPD9Uh2rkTuMQeyKkM2oVqpRq2wAwL+4goo7G0j0asWa5FNpTZyskDJvOl2uxAKt5FBinjhuWbjwsEksMdXpzQJVDXrMe5uakcDk6CsYl6PbzwelAoQQotyebHIfBq7AW8u5GkkQ7luKdeYkpuT+ds1qy7XuehzMKfpU54iZ5JGXak80qK4TAxbcujDO9SkXUdyVf5kHuZb4i9XJfXb0cEwjuw+rF2QKw3J9Ury+3ZuvdnNUpiyab5hXpR/alv8LAfNPAuLAxhKhdGjQhtSuJINVdzBM1D9kka5i5KmC1i9rISiD5AKkac4Pw03r3CP2lYF7w/T1ewneFQwNWAlJ4SGx0zP7FcyHdUCUPNVQYyIq6zFSwZaXGj3IvF2KmTnBETQ83/2OZKQiDGLRAfAwSGEfo+S0Yt8w8/acej1ZxM+WXAxgjLD9cUHQD6e8cT5/zgx8nQe7eC9oPciL3wAuEL/CC5nG0jkvxnhmiKD6B00readrpKW0FsZuYrR+Abdm+GXug5aO1R6YD/vlmpE/k7G9A+keZNj/yvYCjGcfBcxH5GKFXqijvvh0SCwSn/QiSQFsMx30NpJbwMe7+IKRmrUxAaF6OX7+YfcXHIYyKrdyI3M4nBRl1SQ2mwPFADalZSG+RUg+QlujqL0vod2RY66fA+MfAStMc7+c9sn41dQIDpPEdNNO+B019rJ9l3atm3Zr6J4oahTgzEw9kDUUXZ3jfz3Plmfw7WJvYHhx7fodhHXTkf0my/lbq33Jl7nEwv8uVoj4oEp/jelVUD4Sum4J/zb55NiO0NiHLhMjVpPmqIak1aaFmz9eN2i0E9GGQVNTBz/L/956lWPxTjbuRl9fDgny5ns31P3a799z+N3I91Twg/7kU/X+aktkvCl2W/73Q/XlYotvr1+7l8Ov/Nhjtfw==&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <rect fill="#ffffff" width="100%" height="100%" x="0" y="0" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
    <g>
        <g>
            <rect x="240" y="0" width="440" height="160" fill-opacity="0.5" fill="#ffe6cc" stroke="#d79b00" stroke-opacity="0.5" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <image x="239.5" y="-0.5" width="30" height="30" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD8AAAA/CAMAAABggeDtAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURSQvPjI8SltjbpKXn9bY2/Hy8////+Tl55+kqrq+woSKkz9JVmlwe62xtsjLz01WYnZ9hxOS1AAAAAAJcEhZcwAAFxEAABcRAcom8z8AAAHZSURBVFhH7ZXbcusgDEW5WVxt+P+v7d7C6UOD3Z4+dc54TSYmCMFGkhXz8PDw8Eew9hysuTc7HzaRmDD0uXCm5tzOZzVWzdlzYkWFFR+RYMwuG6cK1vPpZbMmwxxh1h0X2MN3a+wuMkwTcZiCi1DygW0wVTCuhYY7IhZbLNZh5D741Uyakr7nkIMnQ3iXkAT3xdEV/tLPFVdYN4armf6Dwoekxn0SvyBC8rhLwNDoAfhjtYOIZqk6UoRxDJ7MhKxAsA/fht/orz4RsoP0TvmkIbbX8Q8SVJ3qZ8pwfWovg88TaLwKowgrhyfTHxfA9Rm7nFX+CVRexGDq5gI9IaKYIBUB4DWQDE6qrjl4A5dLowTWINciX+qHqKn8Q/YyChZdFbANjM5WcCJLDFFTP+yjHp5W2NN1CodPTHCf0XZuSnZuetjh9+S/q6E/jm2t/f4KTkO8zMCYxXMPXi/XjrPOvpDX0+8M9JcVSHCambvBuurPt+mNjlLL671PnI8yDm2JS1D77LDLDmfRnlF+o2t1XlD1Dccee3GfKm1vZWfvZnEbf1X/k8r35sUWY9xeXUmiZyFfl/+Lxrb9lS2juf+Y5g/tdsoWMv8Z/pnqUKyu11+4Pjw8/O8Y8wHjAA9cnAnpaQAAAABJRU5ErkJggg==" preserveAspectRatio="none"/>
        </g>
        <g>
            <g fill="#000000" font-family="&quot;Helvetica&quot;" font-weight="bold" font-size="12px" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));">
                <text x="271.5" y="19.5">
                    AWS　管理
                </text>
            </g>
        </g>
        <g>
            <rect x="0" y="200" width="920" height="320" fill-opacity="0.5" fill="#dae8fc" stroke="#6c8ebf" stroke-opacity="0.5" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <rect x="0" y="200" width="80" height="30" fill="none" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 215px; margin-left: 1px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">
                                    マイナビ管理
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="40" y="219" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle" font-weight="bold">
                        マイナビ管理
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="280" y="40" width="360" height="80" fill="#ffe6cc" stroke="#d79b00" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 358px; height: 1px; padding-top: 80px; margin-left: 281px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">
                                    BLEA コード
                                    <div>
                                        (baseline-environment-on-aws)
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="460" y="84" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle" font-weight="bold">
                        BLEA コード...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="280" y="240" width="360" height="80" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 358px; height: 1px; padding-top: 280px; margin-left: 281px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">
                                    ベースコード
                                    <br/>
                                    (csys-infra-baseline-environment-on-aws-change-homemade)
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="460" y="284" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle" font-weight="bold">
                        ベースコード
(csys-infra-baseline-environment-on-aws-change-homemade)
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="40" y="400" width="360" height="80" fill="#d5e8d4" stroke="#82b366" pointer-events="all" style="fill: light-dark(rgb(213, 232, 212), rgb(31, 47, 30)); stroke: light-dark(rgb(130, 179, 102), rgb(68, 110, 44));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 358px; height: 1px; padding-top: 440px; margin-left: 41px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">
                                    Gevanni コード
                                    <br/>
                                    (csys-infra-gevanni-infra)
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="220" y="444" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle" font-weight="bold">
                        Gevanni コード...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 460 130 L 460 221.76" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 460 227.76 L 456 219.76 L 460 221.76 L 464 219.76 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 181px; margin-left: 460px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    カスタマイズ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="460" y="184" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle" font-weight="bold">
                        カスタマイズ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 450 330 L 237.95 387.83" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 232.16 389.41 L 238.82 383.45 L 237.95 387.83 L 240.93 391.17 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 360px; margin-left: 340px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    カスタマイズ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="340" y="363" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle" font-weight="bold">
                        カスタマイズ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="520" y="400" width="360" height="80" fill="#f5f5f5" stroke="#666666" pointer-events="all" style="fill: light-dark(rgb(245, 245, 245), rgb(26, 26, 26)); stroke: light-dark(rgb(102, 102, 102), rgb(149, 149, 149));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 358px; height: 1px; padding-top: 440px; margin-left: 521px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #333333; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#333333, #c1c1c1); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">
                                    他プロジェクト CDK コード
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="700" y="444" fill="#333333" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle" font-weight="bold">
                        他プロジェクト CDK コード
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 470 330 L 682.05 387.83" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 687.84 389.41 L 679.07 391.17 L 682.05 387.83 L 681.18 383.45 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 360px; margin-left: 580px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    カスタマイズ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="580" y="363" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle" font-weight="bold">
                        カスタマイズ
                    </text>
                </switch>
            </g>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>