<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1402px" height="1032px" viewBox="-0.5 -0.5 1402 1032" content="&lt;mxfile&gt;&lt;diagram id=&quot;cqQ8FBySK3ytqSc-xZce&quot; name=&quot;250204&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <path d="M 841.82 72.21 L 725.85 22.51" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 721.03 20.44 L 728.84 19.98 L 725.85 22.51 L 726.08 26.41 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 20px; margin-left: 801px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 13px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                issue の作成
                                <br style="font-size: 13px;"/>
                                担当者のアサイン
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="801" y="24" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="13px" text-anchor="middle" font-weight="bold">
                    issue の作成...
                </text>
            </switch>
        </g>
        <path d="M 841.82 87.79 L 725.85 137.49" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 721.03 139.56 L 726.08 133.59 L 725.85 137.49 L 728.84 140.02 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 140px; margin-left: 801px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 13px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                実施タスクを決定
                                <br style="font-size: 13px;"/>
                                スプリントを計画
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="801" y="144" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="13px" text-anchor="middle" font-weight="bold">
                    実施タスクを決定
スプリントを計画
                </text>
            </switch>
        </g>
        <rect x="840" y="60" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 843.66 98.18 C 844.11 89.17 851.27 81.98 860 81.98 C 862.95 81.98 865.85 82.81 868.37 84.38 C 873.05 87.3 876.05 92.53 876.34 98.18 Z M 851.08 70.8 C 851.08 65.85 855.08 61.82 860 61.82 C 864.92 61.82 868.92 65.85 868.92 70.8 C 868.92 75.75 864.92 79.78 860 79.78 C 855.08 79.78 851.08 75.75 851.08 70.8 Z M 869.33 82.84 C 867.75 81.86 866.05 81.15 864.27 80.7 C 868.07 79.04 870.73 75.23 870.73 70.8 C 870.73 64.84 865.92 60 860 60 C 854.08 60 849.27 64.84 849.27 70.8 C 849.27 75.23 851.94 79.05 855.74 80.71 C 847.77 82.71 841.82 90.18 841.82 99.09 C 841.82 99.59 842.22 100 842.73 100 L 877.27 100 C 877.77 100 878.18 99.59 878.18 99.09 C 878.18 92.47 874.79 86.25 869.33 82.84 Z" fill="#232f3d" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 107px; margin-left: 860px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                PM
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="860" y="119" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    PM
                </text>
            </switch>
        </g>
        <path d="M 558.18 72.21 L 674.15 22.51" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 678.97 20.44 L 673.92 26.41 L 674.15 22.51 L 671.16 19.98 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 20px; margin-left: 599px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 13px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                issue を作成
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="599" y="24" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="13px" text-anchor="middle" font-weight="bold">
                    issue を作成
                </text>
            </switch>
        </g>
        <path d="M 558.18 87.79 L 674.15 137.49" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 678.97 139.56 L 671.16 140.02 L 674.15 137.49 L 673.92 133.59 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 139px; margin-left: 590px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 13px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                実施タスクを相談・報告
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="590" y="143" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="13px" text-anchor="middle" font-weight="bold">
                    実施タスクを相談・報告
                </text>
            </switch>
        </g>
        <rect x="520" y="60" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 523.66 98.18 C 524.11 89.17 531.27 81.98 540 81.98 C 542.95 81.98 545.85 82.81 548.37 84.38 C 553.05 87.3 556.05 92.53 556.34 98.18 Z M 531.08 70.8 C 531.08 65.85 535.08 61.82 540 61.82 C 544.92 61.82 548.92 65.85 548.92 70.8 C 548.92 75.75 544.92 79.78 540 79.78 C 535.08 79.78 531.08 75.75 531.08 70.8 Z M 549.33 82.84 C 547.75 81.86 546.05 81.15 544.27 80.7 C 548.07 79.04 550.73 75.23 550.73 70.8 C 550.73 64.84 545.92 60 540 60 C 534.08 60 529.27 64.84 529.27 70.8 C 529.27 75.23 531.94 79.05 535.74 80.71 C 527.77 82.71 521.82 90.18 521.82 99.09 C 521.82 99.59 522.22 100 522.73 100 L 557.27 100 C 557.77 100 558.18 99.59 558.18 99.09 C 558.18 92.47 554.79 86.25 549.33 82.84 Z" fill="#232f3d" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 107px; margin-left: 540px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Worker
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="540" y="119" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Worker
                </text>
            </switch>
        </g>
        <image x="679.5" y="-0.5" width="40" height="40" xlink:href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iOTYiIHdpZHRoPSI5OCIgdmlld0JveD0iMCAwIDk4IDk2Ij48cGF0aCBmaWxsPSIjMjQyOTJmIiBkPSJNNDguODU0IDBDMjEuODM5IDAgMCAyMiAwIDQ5LjIxN2MwIDIxLjc1NiAxMy45OTMgNDAuMTcyIDMzLjQwNSA0Ni42OSAyLjQyNy40OSAzLjMxNi0xLjA1OSAzLjMxNi0yLjM2MiAwLTEuMTQxLS4wOC01LjA1Mi0uMDgtOS4xMjctMTMuNTkgMi45MzQtMTYuNDItNS44NjctMTYuNDItNS44NjctMi4xODQtNS43MDQtNS40Mi03LjE3LTUuNDItNy4xNy00LjQ0OC0zLjAxNS4zMjQtMy4wMTUuMzI0LTMuMDE1IDQuOTM0LjMyNiA3LjUyMyA1LjA1MiA3LjUyMyA1LjA1MiA0LjM2NyA3LjQ5NiAxMS40MDQgNS4zNzggMTQuMjM1IDQuMDc0LjQwNC0zLjE3OCAxLjY5OS01LjM3OCAzLjA3NC02LjYtMTAuODM5LTEuMTQxLTIyLjI0My01LjM3OC0yMi4yNDMtMjQuMjgzIDAtNS4zNzggMS45NC05Ljc3OCA1LjAxNC0xMy4yLS40ODUtMS4yMjItMi4xODQtNi4yNzUuNDg2LTEzLjAzOCAwIDAgNC4xMjUtMS4zMDQgMTMuNDI2IDUuMDUyYTQ2Ljk3IDQ2Ljk3IDAgMCAxIDEyLjIxNC0xLjYzYzQuMTI1IDAgOC4zMy41NzEgMTIuMjEzIDEuNjMgOS4zMDItNi4zNTYgMTMuNDI3LTUuMDUyIDEzLjQyNy01LjA1MiAyLjY3IDYuNzYzLjk3IDExLjgxNi40ODUgMTMuMDM4IDMuMTU1IDMuNDIyIDUuMDE1IDcuODIyIDUuMDE1IDEzLjIgMCAxOC45MDUtMTEuNDA0IDIzLjA2LTIyLjMyNCAyNC4yODMgMS43OCAxLjU0OCAzLjMxNiA0LjQ4MSAzLjMxNiA5LjEyNiAwIDYuNi0uMDggMTEuODk3LS4wOCAxMy41MjYgMCAxLjMwNC44OSAyLjg1MyAzLjMxNiAyLjM2NCAxOS40MTItNi41MiAzMy40MDUtMjQuOTM1IDMzLjQwNS00Ni42OTFDOTcuNzA3IDIyIDc1Ljc4OCAwIDQ4Ljg1NCAweiIgY2xpcC1ydWxlPSJldmVub2RkIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="661" y="48" width="80" height="15" stroke-width="0"/>
            <text x="699.5" y="57.5">
                GitHub Project
            </text>
        </g>
        <image x="679.5" y="119.5" width="40" height="40" xlink:href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMTI3IiB3aWR0aD0iMTI3IiB2aWV3Qm94PSIwIDAgMTI3IDEyNyI+CiAgPHBhdGggZmlsbD0iI0UwMUU1QSIgZD0iTTI3LjIgODBjMCA3LjMtNS45IDEzLjItMTMuMiAxMy4yQzYuNyA5My4yLjggODcuMy44IDgwYzAtNy4zIDUuOS0xMy4yIDEzLjItMTMuMmgxMy4yVjgwem02LjYgMGMwLTcuMyA1LjktMTMuMiAxMy4yLTEzLjIgNy4zIDAgMTMuMiA1LjkgMTMuMiAxMy4ydjMzYzAgNy4zLTUuOSAxMy4yLTEzLjIgMTMuMi03LjMgMC0xMy4yLTUuOS0xMy4yLTEzLjJWODB6Ii8+CiAgPHBhdGggZmlsbD0iIzM2QzVGMCIgZD0iTTQ3IDI3Yy03LjMgMC0xMy4yLTUuOS0xMy4yLTEzLjJDMzMuOCA2LjUgMzkuNy42IDQ3IC42YzcuMyAwIDEzLjIgNS45IDEzLjIgMTMuMlYyN0g0N3ptMCA2LjdjNy4zIDAgMTMuMiA1LjkgMTMuMiAxMy4yIDAgNy4zLTUuOSAxMy4yLTEzLjIgMTMuMkgxMy45QzYuNiA2MC4xLjcgNTQuMi43IDQ2LjljMC03LjMgNS45LTEzLjIgMTMuMi0xMy4ySDQ3eiIvPgogIDxwYXRoIGZpbGw9IiMyRUI2N0QiIGQ9Ik05OS45IDQ2LjljMC03LjMgNS45LTEzLjIgMTMuMi0xMy4yIDcuMyAwIDEzLjIgNS45IDEzLjIgMTMuMiAwIDcuMy01LjkgMTMuMi0xMy4yIDEzLjJIOTkuOVY0Ni45em0tNi42IDBjMCA3LjMtNS45IDEzLjItMTMuMiAxMy4yLTcuMyAwLTEzLjItNS45LTEzLjItMTMuMlYxMy44QzY2LjkgNi41IDcyLjguNiA4MC4xLjZjNy4zIDAgMTMuMiA1LjkgMTMuMiAxMy4ydjMzLjF6Ii8+CiAgPHBhdGggZmlsbD0iI0VDQjIyRSIgZD0iTTgwLjEgOTkuOGM3LjMgMCAxMy4yIDUuOSAxMy4yIDEzLjIgMCA3LjMtNS45IDEzLjItMTMuMiAxMy4yLTcuMyAwLTEzLjItNS45LTEzLjItMTMuMlY5OS44aDEzLjJ6bTAtNi42Yy03LjMgMC0xMy4yLTUuOS0xMy4yLTEzLjIgMC03LjMgNS45LTEzLjIgMTMuMi0xMy4yaDMzLjFjNy4zIDAgMTMuMiA1LjkgMTMuMiAxMy4yIDAgNy4zLTUuOSAxMy4yLTEzLjIgMTMuMkg4MC4xeiIvPgo8L3N2Zz4=" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="685" y="168" width="31" height="15" stroke-width="0"/>
            <text x="699.5" y="177.5">
                Slack
            </text>
        </g>
        <path d="M 770 300 L 924.3 377.15" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 929 379.5 L 921.17 379.5 L 924.3 377.15 L 924.3 373.24 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 930 700 L 775.7 777.15" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 771 779.5 L 775.7 773.24 L 775.7 777.15 L 778.83 779.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 940 460 L 940 613.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 940 618.88 L 936.5 611.88 L 940 613.63 L 943.5 611.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 470 380 L 624.3 302.85" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 629 300.5 L 624.3 306.76 L 624.3 302.85 L 621.17 300.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 630 780 L 475.7 702.85" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 471 700.5 L 478.83 700.5 L 475.7 702.85 L 475.7 706.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <image x="1199.5" y="639.5" width="40" height="40" xlink:href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMTI3IiB3aWR0aD0iMTI3IiB2aWV3Qm94PSIwIDAgMTI3IDEyNyI+CiAgPHBhdGggZmlsbD0iI0UwMUU1QSIgZD0iTTI3LjIgODBjMCA3LjMtNS45IDEzLjItMTMuMiAxMy4yQzYuNyA5My4yLjggODcuMy44IDgwYzAtNy4zIDUuOS0xMy4yIDEzLjItMTMuMmgxMy4yVjgwem02LjYgMGMwLTcuMyA1LjktMTMuMiAxMy4yLTEzLjIgNy4zIDAgMTMuMiA1LjkgMTMuMiAxMy4ydjMzYzAgNy4zLTUuOSAxMy4yLTEzLjIgMTMuMi03LjMgMC0xMy4yLTUuOS0xMy4yLTEzLjJWODB6Ii8+CiAgPHBhdGggZmlsbD0iIzM2QzVGMCIgZD0iTTQ3IDI3Yy03LjMgMC0xMy4yLTUuOS0xMy4yLTEzLjJDMzMuOCA2LjUgMzkuNy42IDQ3IC42YzcuMyAwIDEzLjIgNS45IDEzLjIgMTMuMlYyN0g0N3ptMCA2LjdjNy4zIDAgMTMuMiA1LjkgMTMuMiAxMy4yIDAgNy4zLTUuOSAxMy4yLTEzLjIgMTMuMkgxMy45QzYuNiA2MC4xLjcgNTQuMi43IDQ2LjljMC03LjMgNS45LTEzLjIgMTMuMi0xMy4ySDQ3eiIvPgogIDxwYXRoIGZpbGw9IiMyRUI2N0QiIGQ9Ik05OS45IDQ2LjljMC03LjMgNS45LTEzLjIgMTMuMi0xMy4yIDcuMyAwIDEzLjIgNS45IDEzLjIgMTMuMiAwIDcuMy01LjkgMTMuMi0xMy4yIDEzLjJIOTkuOVY0Ni45em0tNi42IDBjMCA3LjMtNS45IDEzLjItMTMuMiAxMy4yLTcuMyAwLTEzLjItNS45LTEzLjItMTMuMlYxMy44QzY2LjkgNi41IDcyLjguNiA4MC4xLjZjNy4zIDAgMTMuMiA1LjkgMTMuMiAxMy4ydjMzLjF6Ii8+CiAgPHBhdGggZmlsbD0iI0VDQjIyRSIgZD0iTTgwLjEgOTkuOGM3LjMgMCAxMy4yIDUuOSAxMy4yIDEzLjIgMCA3LjMtNS45IDEzLjItMTMuMiAxMy4yLTcuMyAwLTEzLjItNS45LTEzLjItMTMuMlY5OS44aDEzLjJ6bTAtNi42Yy03LjMgMC0xMy4yLTUuOS0xMy4yLTEzLjIgMC03LjMgNS45LTEzLjIgMTMuMi0xMy4yaDMzLjFjNy4zIDAgMTMuMiA1LjkgMTMuMiAxMy4yIDAgNy4zLTUuOSAxMy4yLTEzLjIgMTMuMkg4MC4xeiIvPgo8L3N2Zz4=" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="1205" y="688" width="31" height="15" stroke-width="0"/>
            <text x="1219.5" y="697.5">
                Slack
            </text>
        </g>
        <path d="M 1362.32 660 L 1246.37 660" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1241.12 660 L 1248.12 656.5 L 1246.37 660 L 1248.12 663.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 640px; margin-left: 1299px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 13px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                進捗確認
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1299" y="644" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="13px" text-anchor="middle" font-weight="bold">
                    進捗確認
                </text>
            </switch>
        </g>
        <rect x="1360.5" y="640" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 1364.16 678.18 C 1364.61 669.17 1371.77 661.98 1380.5 661.98 C 1383.45 661.98 1386.35 662.81 1388.87 664.38 C 1393.55 667.3 1396.55 672.53 1396.84 678.18 Z M 1371.58 650.8 C 1371.58 645.85 1375.58 641.82 1380.5 641.82 C 1385.42 641.82 1389.42 645.85 1389.42 650.8 C 1389.42 655.75 1385.42 659.78 1380.5 659.78 C 1375.58 659.78 1371.58 655.75 1371.58 650.8 Z M 1389.83 662.84 C 1388.25 661.86 1386.55 661.15 1384.77 660.7 C 1388.57 659.04 1391.23 655.23 1391.23 650.8 C 1391.23 644.84 1386.42 640 1380.5 640 C 1374.58 640 1369.77 644.84 1369.77 650.8 C 1369.77 655.23 1372.44 659.05 1376.24 660.71 C 1368.27 662.71 1362.32 670.18 1362.32 679.09 C 1362.32 679.59 1362.72 680 1363.23 680 L 1397.77 680 C 1398.27 680 1398.68 679.59 1398.68 679.09 C 1398.68 672.47 1395.29 666.25 1389.83 662.84 Z" fill="#232f3d" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 687px; margin-left: 1381px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                PM
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1381" y="699" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    PM
                </text>
            </switch>
        </g>
        <path d="M 1078.18 660 L 1193.63 660" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1198.88 660 L 1191.88 663.5 L 1193.63 660 L 1191.88 656.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 640px; margin-left: 1140px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 13px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                進捗報告
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1140" y="644" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="13px" text-anchor="middle" font-weight="bold">
                    進捗報告
                </text>
            </switch>
        </g>
        <rect x="1040" y="640" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 1043.66 678.18 C 1044.11 669.17 1051.27 661.98 1060 661.98 C 1062.95 661.98 1065.85 662.81 1068.37 664.38 C 1073.05 667.3 1076.05 672.53 1076.34 678.18 Z M 1051.08 650.8 C 1051.08 645.85 1055.08 641.82 1060 641.82 C 1064.92 641.82 1068.92 645.85 1068.92 650.8 C 1068.92 655.75 1064.92 659.78 1060 659.78 C 1055.08 659.78 1051.08 655.75 1051.08 650.8 Z M 1069.33 662.84 C 1067.75 661.86 1066.05 661.15 1064.27 660.7 C 1068.07 659.04 1070.73 655.23 1070.73 650.8 C 1070.73 644.84 1065.92 640 1060 640 C 1054.08 640 1049.27 644.84 1049.27 650.8 C 1049.27 655.23 1051.94 659.05 1055.74 660.71 C 1047.77 662.71 1041.82 670.18 1041.82 679.09 C 1041.82 679.59 1042.22 680 1042.73 680 L 1077.27 680 C 1077.77 680 1078.18 679.59 1078.18 679.09 C 1078.18 672.47 1074.79 666.25 1069.33 662.84 Z" fill="#232f3d" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 687px; margin-left: 1060px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Worker
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1060" y="699" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Worker
                </text>
            </switch>
        </g>
        <image x="679.5" y="969.5" width="40" height="40" xlink:href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iOTYiIHdpZHRoPSI5OCIgdmlld0JveD0iMCAwIDk4IDk2Ij48cGF0aCBmaWxsPSIjMjQyOTJmIiBkPSJNNDguODU0IDBDMjEuODM5IDAgMCAyMiAwIDQ5LjIxN2MwIDIxLjc1NiAxMy45OTMgNDAuMTcyIDMzLjQwNSA0Ni42OSAyLjQyNy40OSAzLjMxNi0xLjA1OSAzLjMxNi0yLjM2MiAwLTEuMTQxLS4wOC01LjA1Mi0uMDgtOS4xMjctMTMuNTkgMi45MzQtMTYuNDItNS44NjctMTYuNDItNS44NjctMi4xODQtNS43MDQtNS40Mi03LjE3LTUuNDItNy4xNy00LjQ0OC0zLjAxNS4zMjQtMy4wMTUuMzI0LTMuMDE1IDQuOTM0LjMyNiA3LjUyMyA1LjA1MiA3LjUyMyA1LjA1MiA0LjM2NyA3LjQ5NiAxMS40MDQgNS4zNzggMTQuMjM1IDQuMDc0LjQwNC0zLjE3OCAxLjY5OS01LjM3OCAzLjA3NC02LjYtMTAuODM5LTEuMTQxLTIyLjI0My01LjM3OC0yMi4yNDMtMjQuMjgzIDAtNS4zNzggMS45NC05Ljc3OCA1LjAxNC0xMy4yLS40ODUtMS4yMjItMi4xODQtNi4yNzUuNDg2LTEzLjAzOCAwIDAgNC4xMjUtMS4zMDQgMTMuNDI2IDUuMDUyYTQ2Ljk3IDQ2Ljk3IDAgMCAxIDEyLjIxNC0xLjYzYzQuMTI1IDAgOC4zMy41NzEgMTIuMjEzIDEuNjMgOS4zMDItNi4zNTYgMTMuNDI3LTUuMDUyIDEzLjQyNy01LjA1MiAyLjY3IDYuNzYzLjk3IDExLjgxNi40ODUgMTMuMDM4IDMuMTU1IDMuNDIyIDUuMDE1IDcuODIyIDUuMDE1IDEzLjIgMCAxOC45MDUtMTEuNDA0IDIzLjA2LTIyLjMyNCAyNC4yODMgMS43OCAxLjU0OCAzLjMxNiA0LjQ4MSAzLjMxNiA5LjEyNiAwIDYuNi0uMDggMTEuODk3LS4wOCAxMy41MjYgMCAxLjMwNC44OSAyLjg1MyAzLjMxNiAyLjM2NCAxOS40MTItNi41MiAzMy40MDUtMjQuOTM1IDMzLjQwNS00Ni42OTFDOTcuNzA3IDIyIDc1Ljc4OCAwIDQ4Ljg1NCAweiIgY2xpcC1ydWxlPSJldmVub2RkIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="661" y="1018" width="80" height="15" stroke-width="0"/>
            <text x="699.5" y="1027.5">
                GitHub Project
            </text>
        </g>
        <image x="679.5" y="849.5" width="40" height="40" xlink:href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMTI3IiB3aWR0aD0iMTI3IiB2aWV3Qm94PSIwIDAgMTI3IDEyNyI+CiAgPHBhdGggZmlsbD0iI0UwMUU1QSIgZD0iTTI3LjIgODBjMCA3LjMtNS45IDEzLjItMTMuMiAxMy4yQzYuNyA5My4yLjggODcuMy44IDgwYzAtNy4zIDUuOS0xMy4yIDEzLjItMTMuMmgxMy4yVjgwem02LjYgMGMwLTcuMyA1LjktMTMuMiAxMy4yLTEzLjIgNy4zIDAgMTMuMiA1LjkgMTMuMiAxMy4ydjMzYzAgNy4zLTUuOSAxMy4yLTEzLjIgMTMuMi03LjMgMC0xMy4yLTUuOS0xMy4yLTEzLjJWODB6Ii8+CiAgPHBhdGggZmlsbD0iIzM2QzVGMCIgZD0iTTQ3IDI3Yy03LjMgMC0xMy4yLTUuOS0xMy4yLTEzLjJDMzMuOCA2LjUgMzkuNy42IDQ3IC42YzcuMyAwIDEzLjIgNS45IDEzLjIgMTMuMlYyN0g0N3ptMCA2LjdjNy4zIDAgMTMuMiA1LjkgMTMuMiAxMy4yIDAgNy4zLTUuOSAxMy4yLTEzLjIgMTMuMkgxMy45QzYuNiA2MC4xLjcgNTQuMi43IDQ2LjljMC03LjMgNS45LTEzLjIgMTMuMi0xMy4ySDQ3eiIvPgogIDxwYXRoIGZpbGw9IiMyRUI2N0QiIGQ9Ik05OS45IDQ2LjljMC03LjMgNS45LTEzLjIgMTMuMi0xMy4yIDcuMyAwIDEzLjIgNS45IDEzLjIgMTMuMiAwIDcuMy01LjkgMTMuMi0xMy4yIDEzLjJIOTkuOVY0Ni45em0tNi42IDBjMCA3LjMtNS45IDEzLjItMTMuMiAxMy4yLTcuMyAwLTEzLjItNS45LTEzLjItMTMuMlYxMy44QzY2LjkgNi41IDcyLjguNiA4MC4xLjZjNy4zIDAgMTMuMiA1LjkgMTMuMiAxMy4ydjMzLjF6Ii8+CiAgPHBhdGggZmlsbD0iI0VDQjIyRSIgZD0iTTgwLjEgOTkuOGM3LjMgMCAxMy4yIDUuOSAxMy4yIDEzLjIgMCA3LjMtNS45IDEzLjItMTMuMiAxMy4yLTcuMyAwLTEzLjItNS45LTEzLjItMTMuMlY5OS44aDEzLjJ6bTAtNi42Yy03LjMgMC0xMy4yLTUuOS0xMy4yLTEzLjIgMC03LjMgNS45LTEzLjIgMTMuMi0xMy4yaDMzLjFjNy4zIDAgMTMuMiA1LjkgMTMuMiAxMy4yIDAgNy4zLTUuOSAxMy4yLTEzLjIgMTMuMkg4MC4xeiIvPgo8L3N2Zz4=" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="685" y="898" width="31" height="15" stroke-width="0"/>
            <text x="699.5" y="907.5">
                Slack
            </text>
        </g>
        <path d="M 841.82 922.21 L 725.85 872.51" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 721.03 870.44 L 728.84 869.98 L 725.85 872.51 L 726.08 876.41 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 870px; margin-left: 801px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 13px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                トリアージ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="801" y="874" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="13px" text-anchor="middle" font-weight="bold">
                    トリアージ
                </text>
            </switch>
        </g>
        <rect x="840" y="910" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 843.66 948.18 C 844.11 939.17 851.27 931.98 860 931.98 C 862.95 931.98 865.85 932.81 868.37 934.38 C 873.05 937.3 876.05 942.53 876.34 948.18 Z M 851.08 920.8 C 851.08 915.85 855.08 911.82 860 911.82 C 864.92 911.82 868.92 915.85 868.92 920.8 C 868.92 925.75 864.92 929.78 860 929.78 C 855.08 929.78 851.08 925.75 851.08 920.8 Z M 869.33 932.84 C 867.75 931.86 866.05 931.15 864.27 930.7 C 868.07 929.04 870.73 925.23 870.73 920.8 C 870.73 914.84 865.92 910 860 910 C 854.08 910 849.27 914.84 849.27 920.8 C 849.27 925.23 851.94 929.05 855.74 930.71 C 847.77 932.71 841.82 940.18 841.82 949.09 C 841.82 949.59 842.22 950 842.73 950 L 877.27 950 C 877.77 950 878.18 949.59 878.18 949.09 C 878.18 942.47 874.79 936.25 869.33 932.84 Z" fill="#232f3d" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 957px; margin-left: 860px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                PM
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="860" y="969" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    PM
                </text>
            </switch>
        </g>
        <path d="M 558.18 922.21 L 674.15 872.51" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 678.97 870.44 L 673.92 876.41 L 674.15 872.51 L 671.16 869.98 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 880px; margin-left: 600px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 13px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                期日調整
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="600" y="884" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="13px" text-anchor="middle" font-weight="bold">
                    期日調整
                </text>
            </switch>
        </g>
        <path d="M 558.18 938.48 L 674.11 992.55" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 678.87 994.77 L 671.04 994.98 L 674.11 992.55 L 674 988.64 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 995px; margin-left: 600px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 13px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                優先度・期日を反映
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="600" y="999" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="13px" text-anchor="middle" font-weight="bold">
                    優先度・期日を反映
                </text>
            </switch>
        </g>
        <rect x="520" y="910" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 523.66 948.18 C 524.11 939.17 531.27 931.98 540 931.98 C 542.95 931.98 545.85 932.81 548.37 934.38 C 553.05 937.3 556.05 942.53 556.34 948.18 Z M 531.08 920.8 C 531.08 915.85 535.08 911.82 540 911.82 C 544.92 911.82 548.92 915.85 548.92 920.8 C 548.92 925.75 544.92 929.78 540 929.78 C 535.08 929.78 531.08 925.75 531.08 920.8 Z M 549.33 932.84 C 547.75 931.86 546.05 931.15 544.27 930.7 C 548.07 929.04 550.73 925.23 550.73 920.8 C 550.73 914.84 545.92 910 540 910 C 534.08 910 529.27 914.84 529.27 920.8 C 529.27 925.23 531.94 929.05 535.74 930.71 C 527.77 932.71 521.82 940.18 521.82 949.09 C 521.82 949.59 522.22 950 522.73 950 L 557.27 950 C 557.77 950 558.18 949.59 558.18 949.09 C 558.18 942.47 554.79 936.25 549.33 932.84 Z" fill="#232f3d" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 957px; margin-left: 540px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Worker
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="540" y="969" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Worker
                </text>
            </switch>
        </g>
        <image x="159.5" y="399.5" width="40" height="40" xlink:href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMTI3IiB3aWR0aD0iMTI3IiB2aWV3Qm94PSIwIDAgMTI3IDEyNyI+CiAgPHBhdGggZmlsbD0iI0UwMUU1QSIgZD0iTTI3LjIgODBjMCA3LjMtNS45IDEzLjItMTMuMiAxMy4yQzYuNyA5My4yLjggODcuMy44IDgwYzAtNy4zIDUuOS0xMy4yIDEzLjItMTMuMmgxMy4yVjgwem02LjYgMGMwLTcuMyA1LjktMTMuMiAxMy4yLTEzLjIgNy4zIDAgMTMuMiA1LjkgMTMuMiAxMy4ydjMzYzAgNy4zLTUuOSAxMy4yLTEzLjIgMTMuMi03LjMgMC0xMy4yLTUuOS0xMy4yLTEzLjJWODB6Ii8+CiAgPHBhdGggZmlsbD0iIzM2QzVGMCIgZD0iTTQ3IDI3Yy03LjMgMC0xMy4yLTUuOS0xMy4yLTEzLjJDMzMuOCA2LjUgMzkuNy42IDQ3IC42YzcuMyAwIDEzLjIgNS45IDEzLjIgMTMuMlYyN0g0N3ptMCA2LjdjNy4zIDAgMTMuMiA1LjkgMTMuMiAxMy4yIDAgNy4zLTUuOSAxMy4yLTEzLjIgMTMuMkgxMy45QzYuNiA2MC4xLjcgNTQuMi43IDQ2LjljMC03LjMgNS45LTEzLjIgMTMuMi0xMy4ySDQ3eiIvPgogIDxwYXRoIGZpbGw9IiMyRUI2N0QiIGQ9Ik05OS45IDQ2LjljMC03LjMgNS45LTEzLjIgMTMuMi0xMy4yIDcuMyAwIDEzLjIgNS45IDEzLjIgMTMuMiAwIDcuMy01LjkgMTMuMi0xMy4yIDEzLjJIOTkuOVY0Ni45em0tNi42IDBjMCA3LjMtNS45IDEzLjItMTMuMiAxMy4yLTcuMyAwLTEzLjItNS45LTEzLjItMTMuMlYxMy44QzY2LjkgNi41IDcyLjguNiA4MC4xLjZjNy4zIDAgMTMuMiA1LjkgMTMuMiAxMy4ydjMzLjF6Ii8+CiAgPHBhdGggZmlsbD0iI0VDQjIyRSIgZD0iTTgwLjEgOTkuOGM3LjMgMCAxMy4yIDUuOSAxMy4yIDEzLjIgMCA3LjMtNS45IDEzLjItMTMuMiAxMy4yLTcuMyAwLTEzLjItNS45LTEzLjItMTMuMlY5OS44aDEzLjJ6bTAtNi42Yy03LjMgMC0xMy4yLTUuOS0xMy4yLTEzLjIgMC03LjMgNS45LTEzLjIgMTMuMi0xMy4yaDMzLjFjNy4zIDAgMTMuMiA1LjkgMTMuMiAxMy4yIDAgNy4zLTUuOSAxMy4yLTEzLjIgMTMuMkg4MC4xeiIvPgo8L3N2Zz4=" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="165" y="448" width="31" height="15" stroke-width="0"/>
            <text x="179.5" y="457.5">
                Slack
            </text>
        </g>
        <path d="M 322.32 420 L 206.37 420" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 201.12 420 L 208.12 416.5 L 206.37 420 L 208.12 423.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 400px; margin-left: 259px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 13px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                進捗確認
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="259" y="404" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="13px" text-anchor="middle" font-weight="bold">
                    進捗確認
                </text>
            </switch>
        </g>
        <rect x="320.5" y="400" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 324.16 438.18 C 324.61 429.17 331.77 421.98 340.5 421.98 C 343.45 421.98 346.35 422.81 348.87 424.38 C 353.55 427.3 356.55 432.53 356.84 438.18 Z M 331.58 410.8 C 331.58 405.85 335.58 401.82 340.5 401.82 C 345.42 401.82 349.42 405.85 349.42 410.8 C 349.42 415.75 345.42 419.78 340.5 419.78 C 335.58 419.78 331.58 415.75 331.58 410.8 Z M 349.83 422.84 C 348.25 421.86 346.55 421.15 344.77 420.7 C 348.57 419.04 351.23 415.23 351.23 410.8 C 351.23 404.84 346.42 400 340.5 400 C 334.58 400 329.77 404.84 329.77 410.8 C 329.77 415.23 332.44 419.05 336.24 420.71 C 328.27 422.71 322.32 430.18 322.32 439.09 C 322.32 439.59 322.72 440 323.23 440 L 357.77 440 C 358.27 440 358.68 439.59 358.68 439.09 C 358.68 432.47 355.29 426.25 349.83 422.84 Z" fill="#232f3d" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 447px; margin-left: 341px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                PM
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="341" y="459" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    PM
                </text>
            </switch>
        </g>
        <path d="M 38.18 420 L 153.63 420" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 158.88 420 L 151.88 423.5 L 153.63 420 L 151.88 416.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 400px; margin-left: 100px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 13px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                進捗報告
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="100" y="404" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="13px" text-anchor="middle" font-weight="bold">
                    進捗報告
                </text>
            </switch>
        </g>
        <rect x="0" y="400" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 3.66 438.18 C 4.11 429.17 11.27 421.98 20 421.98 C 22.95 421.98 25.85 422.81 28.37 424.38 C 33.05 427.3 36.05 432.53 36.34 438.18 Z M 11.08 410.8 C 11.08 405.85 15.08 401.82 20 401.82 C 24.92 401.82 28.92 405.85 28.92 410.8 C 28.92 415.75 24.92 419.78 20 419.78 C 15.08 419.78 11.08 415.75 11.08 410.8 Z M 29.33 422.84 C 27.75 421.86 26.05 421.15 24.27 420.7 C 28.07 419.04 30.73 415.23 30.73 410.8 C 30.73 404.84 25.92 400 20 400 C 14.08 400 9.27 404.84 9.27 410.8 C 9.27 415.23 11.94 419.05 15.74 420.71 C 7.77 422.71 1.82 430.18 1.82 439.09 C 1.82 439.59 2.22 440 2.73 440 L 37.27 440 C 37.77 440 38.18 439.59 38.18 439.09 C 38.18 432.47 34.79 426.25 29.33 422.84 Z" fill="#232f3d" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 447px; margin-left: 20px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Worker
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="20" y="459" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Worker
                </text>
            </switch>
        </g>
        <rect x="400" y="390" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 420px; margin-left: 401px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <b>
                                    <font style="font-size: 16px;">
                                        進捗報告
                                    </font>
                                </b>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="460" y="424" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    進捗報告
                </text>
            </switch>
        </g>
        <rect x="640" y="270" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 300px; margin-left: 641px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <span style="font-size: 16px;">
                                    <b>
                                        プランニング
                                    </b>
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="700" y="304" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    プランニング
                </text>
            </switch>
        </g>
        <rect x="880" y="390" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 420px; margin-left: 881px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <span style="font-size: 16px;">
                                    <b>
                                        作業
                                    </b>
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="940" y="424" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    作業
                </text>
            </switch>
        </g>
        <rect x="880" y="630" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 660px; margin-left: 881px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <span style="font-size: 16px;">
                                    <b>
                                        進捗報告
                                    </b>
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="940" y="664" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    進捗報告
                </text>
            </switch>
        </g>
        <rect x="640" y="750" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 780px; margin-left: 641px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <span style="font-size: 16px;">
                                    <b>
                                        期日調整
                                        <br/>
                                        トリアージ
                                        <br/>
                                    </b>
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="700" y="784" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    期日調整トリアージ...
                </text>
            </switch>
        </g>
        <path d="M 460 620 L 460 466.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 460 461.12 L 463.5 468.12 L 460 466.37 L 456.5 468.12 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="400" y="630" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 660px; margin-left: 401px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <span style="font-size: 16px;">
                                    <b>
                                        作業
                                    </b>
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="460" y="664" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    作業
                </text>
            </switch>
        </g>
        <rect x="620" y="510" width="160" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 540px; margin-left: 621px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <span style="font-size: 16px;">
                                    <b>
                                        スプリントサイクル
                                    </b>
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="700" y="544" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    スプリントサイクル
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>