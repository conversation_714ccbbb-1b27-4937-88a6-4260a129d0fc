<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="521px" height="421px" viewBox="-0.5 -0.5 521 421" content="&lt;mxfile&gt;&lt;diagram id=&quot;RhQ7oAhi7uXaoNpWmrsy&quot; name=&quot;250207&quot;&gt;7Vrdk6I4EP9r5vEsQvjQR0Gd3dvb27314fbNihCBGiBUiF/z118iAYGgo6XOjFc6WiadTtPp/nWnk/EJusnmmaIs/E58HD/pmr95gqMnXQcaAPxLULYFxbL6BSGgkS+Z9oRp9IrLmZK6jHycNxgZITGLsibRI2mKPdagIUrJusm2IHHzqRkKsEKYeihWqf9GPgsLal+39/QvOArC8snAGhQjCSqZ5UryEPlkXSPB8RN0KSGsaCUbF8fCeKVdinmTA6NGIXWF4qVUleIYsYik8nFsW66hVJXilJ0iOghH+p/P/8zg+tvkBxqZ2q/X33/YtiIX+9xUsksoC0lAUhSP91QnZEnMBwFv5oySl8qGfPWOqpZck5Bbs75U8hmTBDO65QzFSldNFyHp6aDiq6b+JBF/hK5JVEJDukRi0izBVorIyZJ6WM6qG+oNQYbdEsQQDTBTBPFGbT170s4PZ/ikbyogGHoezvOjrkpJekPfSNNoPU3TQMM6ZSBc6D3TbBm97b0DRle9ZyuCetrgTCSUjGSxyPFtnGx9oDcv9VX/jUg71VdtQUqkHXDU1ZxgK5H2N0owp/zCOYm5ga6cHHNuGTYUG9jex1cISrNpxetEZDsN6iemwTcFwcFpXuZ2QtsaWyYY8tMVhkA7T68mP28UGtwyN1gKAN2YLH1O+o4yBX35C2aewJLGcSPNwQdMh7+57m7xMTmrKyg93ewgdtFslQhUNv4Fup7QJnbRbJUIVDbRK7VuErtotqlq3J4NOmaD1mz+hg5ZsjhKsVtVnMLGC5Iyl8SE7uwP+d9E+NEJKPIj3BgbGOZootfGRhHlgkTlJkKdihzgLKI4rs0xh1BzzCpT1EYWuxcf8VEeYl+qs8KURbyK/QvNcfyT5JEUPyeMkaTGMIyjQAwwknEqkj2Pa4VpM0eJFcr6HOhlXyJOPBLlWWGORbQReji84M3EYLIJxNmgh9a50aO4iOGvntDH4d2i1eTyBLRnCcqOZT2xBFweNI7mPai3EpQm++t9SV+GeFir5o1W+qmnwkYoH4lbqMRtaQIlaNWVHq/TgSp7iukqEqI1bjcr5mKcOeWtQLQ4YhniyKXH08VF+JZbVQu+I4NHoq3AVzJ/OuTuEiam4xUu8iY4hGavtOgMdCIVnotU02oVTPppSNXNXh9eDtbuk98xmA0fMLtDmLUTogkGvQHUqhf4eNCp9xt70JWYUaA3DLCsDB+wuwPYGSfuwzcFmnqH8gDavQOtvY2+M9CAAqk58l4CSpapf3HVB1XAKjLXYcTwNEOe6K/pro6ue7IGKwke5VxRYU3VD5zrjqreLs/PHVVNVenU/QG1K1Tg4P4MZrYNZqkGA1YXgK9xZDF0xWBjd3o82z3uGD7VHcPE7o8147w7hpFmukDdXc6+Y0gi399dcLa3mGqg3GVivGAfd8mAPY5XJ25pT4tgeq+0Bzuz3uUxbKox3LgbuL/K5V1hdXLpwkE0y6VhD4NpR3eqGqAeqLvX++0autGD/dtAjvjjb79pMvJnr/PEYD8sf+t0XR589s1Xtz5y8+224rHT8PARzf/HaD4FhteLZt7d/xSl+PfU/gc9cPwf&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-945df2-1-5a30b5-1-s-0">
            <stop offset="0%" style="stop-color: rgb(90, 48, 181); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(148, 93, 242); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f78e04-1-d05c17-1-s-0">
            <stop offset="0%" style="stop-color: rgb(208, 92, 23); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(247, 142, 4); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <path d="M 140 310 L 140 278.24" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 140 272.24 L 144 280.24 L 140 278.24 L 136 280.24 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 170 210.09 L 341.76 210" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 347.76 210 L 339.77 214.01 L 341.76 210 L 339.76 206.01 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 190px; margin-left: 260px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Access
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="260" y="193" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Access
                </text>
            </switch>
        </g>
        <path d="M 380 270 L 380 301.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 380 307.76 L 376 299.76 L 380 301.76 L 384 299.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 140 190 L 140 120 Q 140 110 140 100 L 140 78.24" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 140 72.24 L 144 80.24 L 140 78.24 L 136 80.24 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 100px; margin-left: 100px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Name Resolve
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="100" y="103" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Name Resolve
                </text>
            </switch>
        </g>
        <path d="M 120 0 L 160 0 L 160 40 L 120 40 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 147.43 21.97 C 147.62 21.97 147.79 21.88 147.9 21.72 C 148.26 21.2 151.43 16.55 151.43 14.45 C 151.43 12.15 149.71 10.41 147.43 10.41 C 145.15 10.41 143.43 12.15 143.43 14.45 C 143.43 16.55 146.6 21.2 146.96 21.72 C 147.07 21.88 147.24 21.97 147.43 21.97 Z M 147.43 11.56 C 149.06 11.56 150.29 12.8 150.29 14.45 C 150.29 15.72 148.52 18.69 147.43 20.36 C 146.34 18.7 144.57 15.72 144.57 14.45 C 144.57 12.8 145.8 11.56 147.43 11.56 Z M 149.72 14.45 C 149.72 13.24 148.63 12.14 147.43 12.14 C 146.23 12.14 145.14 13.24 145.14 14.45 C 145.14 15.66 146.23 16.77 147.43 16.77 C 148.63 16.77 149.72 15.66 149.72 14.45 Z M 146.29 14.45 C 146.29 13.88 146.86 13.3 147.43 13.3 C 148 13.3 148.57 13.88 148.57 14.45 C 148.57 15.03 148 15.61 147.43 15.61 C 146.86 15.61 146.29 15.03 146.29 14.45 Z M 151.62 18.83 L 151.24 19.92 C 152.2 20.26 153.62 21 154.36 22.55 L 145.14 22.55 C 144.83 22.55 144.57 22.8 144.57 23.12 L 144.57 30.06 L 142.86 30.05 L 142.86 27.75 C 142.86 27.43 142.6 27.17 142.29 27.17 L 138.29 27.17 C 137.97 27.17 137.72 27.43 137.72 27.75 L 137.72 30.05 L 136 30.05 L 136 23.7 C 136 23.47 135.87 23.26 135.66 23.17 C 135.46 23.08 135.21 23.12 135.05 23.28 L 128.4 29.44 C 128.02 29.25 127.66 29.02 127.33 28.76 L 135.81 21.25 C 135.93 21.14 136 20.98 136 20.81 L 136 19.08 C 136 18.76 135.75 18.5 135.43 18.5 L 129.14 18.5 C 129.14 18.45 129.14 18.41 129.14 18.37 C 129.14 18.24 129.15 18.12 129.15 18 C 129.15 17.41 129.25 16.79 129.43 16.19 L 135.43 16.19 C 135.75 16.19 136 15.93 136 15.61 L 136 10.68 C 136.58 10.65 137.15 10.69 137.72 10.8 L 137.72 20.23 C 137.72 20.55 137.97 20.81 138.29 20.81 L 142.29 20.81 C 142.6 20.81 142.86 20.55 142.86 20.23 L 142.86 16.77 L 141.72 16.77 L 141.72 19.66 L 138.86 19.66 L 138.86 11.12 C 139.94 11.53 140.97 12.2 141.88 13.13 L 142.69 12.31 C 140 9.58 136.44 8.78 133.17 10.18 C 130.27 11.43 128 14.86 128 18 C 128 18.1 128 18.21 128 18.33 C 127.99 18.43 127.99 18.54 127.99 18.65 C 126.33 19.2 124.01 20.65 124.01 24.51 L 124 24.63 C 124 24.71 124 24.78 124.01 24.89 C 124.2 28.02 126.86 30.73 130.2 31.19 C 130.22 31.19 130.25 31.2 130.28 31.2 L 138.23 31.2 C 138.25 31.21 138.27 31.22 138.29 31.22 L 142.29 31.22 C 142.3 31.22 142.32 31.21 142.33 31.21 L 150.28 31.22 L 150.28 31.22 C 150.3 31.22 150.32 31.22 150.34 31.21 C 150.39 31.21 156 30.56 156 24.86 C 156 20.81 152.94 19.29 151.62 18.83 Z M 138.86 28.33 L 141.72 28.33 L 141.72 30.05 L 138.86 30.05 Z M 133.62 11.25 C 134.03 11.07 134.44 10.94 134.86 10.85 L 134.86 15.03 L 129.88 15.03 C 130.68 13.37 132.06 11.92 133.62 11.25 Z M 125.15 24.67 L 125.15 24.51 C 125.15 21.92 126.33 20.28 128.65 19.66 L 134.86 19.66 L 134.86 20.55 L 126.49 27.96 C 125.71 27.08 125.22 25.98 125.15 24.82 C 125.14 24.75 125.14 24.71 125.15 24.67 Z M 129.61 29.89 L 134.86 25.02 L 134.86 30.05 L 130.32 30.04 C 130.07 30 129.84 29.95 129.61 29.89 Z M 150.25 30.06 L 145.72 30.06 L 145.72 23.7 L 154.75 23.7 C 154.82 24.06 154.86 24.44 154.86 24.86 C 154.86 29.38 150.66 30.01 150.25 30.06 Z M 142.29 21.39 L 138.25 21.39 C 137.93 21.39 137.67 21.65 137.67 21.97 L 137.67 26.01 C 137.67 26.33 137.93 26.59 138.25 26.59 L 142.29 26.59 C 142.6 26.59 142.86 26.33 142.86 26.01 L 142.86 21.97 C 142.86 21.65 142.6 21.39 142.29 21.39 Z M 138.82 25.44 L 138.82 22.55 L 141.72 22.55 L 141.72 25.44 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 47px; margin-left: 140px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Cloud Map
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="140" y="59" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Cloud...
                </text>
            </switch>
        </g>
        <rect x="360" y="320" width="40" height="25.83" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 399.09 320.07 L 360.91 320.07 C 360.41 320.07 360 320.48 360 320.98 L 360 344.85 C 360 345.35 360.41 345.76 360.91 345.76 L 399.09 345.76 C 399.59 345.76 400 345.35 400 344.85 L 400 320.98 C 400 320.48 399.59 320.07 399.09 320.07 Z M 361.82 343.94 L 361.82 321.89 L 398.18 321.89 L 398.18 343.94 Z M 364.77 341.67 L 366.59 341.67 L 366.59 324.17 L 364.77 324.17 Z M 369.55 341.67 L 371.36 341.67 L 371.36 324.17 L 369.55 324.17 Z M 374.32 341.67 L 376.14 341.67 L 376.14 324.17 L 374.32 324.17 Z M 379.09 341.67 L 380.91 341.67 L 380.91 324.17 L 379.09 324.17 Z M 383.86 341.67 L 385.68 341.67 L 385.68 324.17 L 383.86 324.17 Z M 388.64 341.67 L 390.45 341.67 L 390.45 324.17 L 388.64 324.17 Z M 393.41 341.67 L 395.23 341.67 L 395.23 324.17 L 393.41 324.17 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 353px; margin-left: 380px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Service B
                                <br/>
                                Container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="380" y="365" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Servic...
                </text>
            </switch>
        </g>
        <rect x="120" y="319.93" width="40" height="25.83" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 159.09 320 L 120.91 320 C 120.41 320 120 320.41 120 320.91 L 120 344.78 C 120 345.28 120.41 345.69 120.91 345.69 L 159.09 345.69 C 159.59 345.69 160 345.28 160 344.78 L 160 320.91 C 160 320.41 159.59 320 159.09 320 Z M 121.82 343.87 L 121.82 321.82 L 158.18 321.82 L 158.18 343.87 Z M 124.77 341.6 L 126.59 341.6 L 126.59 324.1 L 124.77 324.1 Z M 129.55 341.6 L 131.36 341.6 L 131.36 324.1 L 129.55 324.1 Z M 134.32 341.6 L 136.14 341.6 L 136.14 324.1 L 134.32 324.1 Z M 139.09 341.6 L 140.91 341.6 L 140.91 324.1 L 139.09 324.1 Z M 143.86 341.6 L 145.68 341.6 L 145.68 324.1 L 143.86 324.1 Z M 148.64 341.6 L 150.45 341.6 L 150.45 324.1 L 148.64 324.1 Z M 153.41 341.6 L 155.23 341.6 L 155.23 324.1 L 153.41 324.1 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 353px; margin-left: 140px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Service A
                                <br/>
                                Container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="140" y="365" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Servic...
                </text>
            </switch>
        </g>
        <rect x="120" y="200" width="40" height="25.83" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 159.09 200.07 L 120.91 200.07 C 120.41 200.07 120 200.48 120 200.98 L 120 224.85 C 120 225.35 120.41 225.76 120.91 225.76 L 159.09 225.76 C 159.59 225.76 160 225.35 160 224.85 L 160 200.98 C 160 200.48 159.59 200.07 159.09 200.07 Z M 121.82 223.94 L 121.82 201.89 L 158.18 201.89 L 158.18 223.94 Z M 124.77 221.67 L 126.59 221.67 L 126.59 204.17 L 124.77 204.17 Z M 129.55 221.67 L 131.36 221.67 L 131.36 204.17 L 129.55 204.17 Z M 134.32 221.67 L 136.14 221.67 L 136.14 204.17 L 134.32 204.17 Z M 139.09 221.67 L 140.91 221.67 L 140.91 204.17 L 139.09 204.17 Z M 143.86 221.67 L 145.68 221.67 L 145.68 204.17 L 143.86 204.17 Z M 148.64 221.67 L 150.45 221.67 L 150.45 204.17 L 148.64 204.17 Z M 153.41 221.67 L 155.23 221.67 L 155.23 204.17 L 153.41 204.17 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 233px; margin-left: 140px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Service Connect
                                <br/>
                                Agent
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="140" y="245" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Servic...
                </text>
            </switch>
        </g>
        <rect x="360" y="200" width="40" height="25.83" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 399.09 200.07 L 360.91 200.07 C 360.41 200.07 360 200.48 360 200.98 L 360 224.85 C 360 225.35 360.41 225.76 360.91 225.76 L 399.09 225.76 C 399.59 225.76 400 225.35 400 224.85 L 400 200.98 C 400 200.48 399.59 200.07 399.09 200.07 Z M 361.82 223.94 L 361.82 201.89 L 398.18 201.89 L 398.18 223.94 Z M 364.77 221.67 L 366.59 221.67 L 366.59 204.17 L 364.77 204.17 Z M 369.55 221.67 L 371.36 221.67 L 371.36 204.17 L 369.55 204.17 Z M 374.32 221.67 L 376.14 221.67 L 376.14 204.17 L 374.32 204.17 Z M 379.09 221.67 L 380.91 221.67 L 380.91 204.17 L 379.09 204.17 Z M 383.86 221.67 L 385.68 221.67 L 385.68 204.17 L 383.86 204.17 Z M 388.64 221.67 L 390.45 221.67 L 390.45 204.17 L 388.64 204.17 Z M 393.41 221.67 L 395.23 221.67 L 395.23 204.17 L 393.41 204.17 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 233px; margin-left: 380px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Service Connect
                                <br/>
                                Agent
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="380" y="245" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Servic...
                </text>
            </switch>
        </g>
        <rect x="0" y="120" width="520" height="300" fill="none" stroke="#d45b07" pointer-events="all"/>
        <rect x="300" y="160" width="160" height="240" fill="none" stroke="#d45b07" pointer-events="all"/>
        <path d="M 0 120 L 30 120 L 30 150 L 0 150 Z" fill="url(#mx-gradient-f78e04-1-d05c17-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 24.89 138.26 L 21.52 136.24 L 21.52 131.43 C 21.52 131.28 21.44 131.15 21.31 131.07 L 16.47 128.25 L 16.47 124.18 L 24.89 129.15 Z M 25.52 128.55 L 16.27 123.08 C 16.14 123 15.98 123 15.84 123.07 C 15.71 123.15 15.63 123.29 15.63 123.44 L 15.63 128.49 C 15.63 128.64 15.71 128.78 15.84 128.85 L 20.68 131.68 L 20.68 136.48 C 20.68 136.63 20.76 136.77 20.88 136.84 L 25.09 139.37 C 25.16 139.41 25.23 139.43 25.31 139.43 C 25.38 139.43 25.45 139.41 25.51 139.37 C 25.65 139.3 25.73 139.16 25.73 139.01 L 25.73 128.91 C 25.73 128.76 25.65 128.62 25.52 128.55 Z M 14.98 146.1 L 5.11 140.86 L 5.11 129.15 L 13.53 124.18 L 13.53 128.26 L 9.09 131.08 C 8.97 131.16 8.9 131.29 8.9 131.43 L 8.9 138.59 C 8.9 138.74 8.99 138.89 9.13 138.96 L 14.79 141.9 C 14.91 141.97 15.05 141.97 15.17 141.9 L 20.66 139.07 L 24.04 141.09 Z M 25.1 140.75 L 20.9 138.22 C 20.77 138.15 20.62 138.14 20.49 138.21 L 14.98 141.06 L 9.74 138.33 L 9.74 131.66 L 14.17 128.84 C 14.3 128.77 14.37 128.63 14.37 128.49 L 14.37 123.44 C 14.37 123.29 14.29 123.15 14.16 123.07 C 14.03 123 13.86 123 13.73 123.08 L 4.48 128.55 C 4.35 128.62 4.27 128.76 4.27 128.91 L 4.27 141.11 C 4.27 141.27 4.36 141.41 4.49 141.48 L 14.78 146.95 C 14.84 146.98 14.91 147 14.98 147 C 15.05 147 15.12 146.98 15.18 146.95 L 25.09 141.48 C 25.22 141.41 25.3 141.27 25.31 141.12 C 25.31 140.97 25.23 140.83 25.1 140.75 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 135px; margin-left: 32px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ECS
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="32" y="139" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    ECS
                </text>
            </switch>
        </g>
        <rect x="300" y="160" width="24.38" height="30" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 312.99 184.69 L 321.68 184.69 L 321.68 183.33 L 312.99 183.33 Z M 312.99 177.93 L 321.68 177.93 L 321.68 176.57 L 312.99 176.57 Z M 312.99 171.18 L 321.68 171.18 L 321.68 169.81 L 312.99 169.81 Z M 309.33 184.78 L 309.33 183.24 L 310.86 183.24 L 310.86 184.78 Z M 308.65 186.14 L 311.55 186.14 C 311.92 186.14 312.23 185.83 312.23 185.46 L 312.23 182.56 C 312.23 182.18 311.92 181.88 311.55 181.88 L 308.65 181.88 C 308.27 181.88 307.97 182.18 307.97 182.56 L 307.97 185.46 C 307.97 185.83 308.27 186.14 308.65 186.14 Z M 309.33 178.02 L 309.33 176.49 L 310.86 176.49 L 310.86 178.02 Z M 308.65 179.38 L 311.55 179.38 C 311.92 179.38 312.23 179.08 312.23 178.7 L 312.23 175.8 C 312.23 175.43 311.92 175.12 311.55 175.12 L 308.65 175.12 C 308.27 175.12 307.97 175.43 307.97 175.8 L 307.97 178.7 C 307.97 179.08 308.27 179.38 308.65 179.38 Z M 309.33 171.26 L 309.33 169.73 L 310.86 169.73 L 310.86 171.26 Z M 308.65 172.63 L 311.55 172.63 C 311.92 172.63 312.23 172.32 312.23 171.94 L 312.23 169.05 C 312.23 168.67 311.92 168.37 311.55 168.37 L 308.65 168.37 C 308.27 168.37 307.97 168.67 307.97 169.05 L 307.97 171.94 C 307.97 172.32 308.27 172.63 308.65 172.63 Z M 306.92 188.64 L 306.92 166.83 L 322.93 166.83 L 322.93 188.64 Z M 305.56 166.15 L 305.56 185.74 L 304.02 185.74 L 304.02 163.94 L 320.04 163.94 L 320.04 165.47 L 306.24 165.47 C 305.86 165.47 305.56 165.78 305.56 166.15 Z M 302.66 163.26 L 302.66 183.17 L 301.45 183.17 L 301.45 161.36 L 317.46 161.36 L 317.46 162.57 L 303.34 162.57 C 302.96 162.57 302.66 162.88 302.66 163.26 Z M 323.61 165.47 L 321.4 165.47 L 321.4 163.26 C 321.4 162.88 321.09 162.57 320.72 162.57 L 318.82 162.57 L 318.82 160.68 C 318.82 160.31 318.52 160 318.14 160 L 300.77 160 C 300.39 160 300.09 160.31 300.09 160.68 L 300.09 183.85 C 300.09 184.23 300.39 184.53 300.77 184.53 L 302.66 184.53 L 302.66 186.42 C 302.66 186.8 302.96 187.1 303.34 187.1 L 305.56 187.1 L 305.56 189.32 C 305.56 189.7 305.86 190 306.24 190 L 323.61 190 C 323.99 190 324.29 189.7 324.29 189.32 L 324.29 166.15 C 324.29 165.78 323.99 165.47 323.61 165.47 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 175px; margin-left: 326px;">
                        <div data-drawio-colors="color: #232F3E; background-color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Service B
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="326" y="179" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    Servi...
                </text>
            </switch>
        </g>
        <rect x="60" y="160" width="160" height="240" fill="none" stroke="#d45b07" pointer-events="all"/>
        <rect x="60" y="160" width="24.38" height="30" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 72.99 184.69 L 81.68 184.69 L 81.68 183.33 L 72.99 183.33 Z M 72.99 177.93 L 81.68 177.93 L 81.68 176.57 L 72.99 176.57 Z M 72.99 171.18 L 81.68 171.18 L 81.68 169.81 L 72.99 169.81 Z M 69.33 184.78 L 69.33 183.24 L 70.86 183.24 L 70.86 184.78 Z M 68.65 186.14 L 71.55 186.14 C 71.92 186.14 72.23 185.83 72.23 185.46 L 72.23 182.56 C 72.23 182.18 71.92 181.88 71.55 181.88 L 68.65 181.88 C 68.27 181.88 67.97 182.18 67.97 182.56 L 67.97 185.46 C 67.97 185.83 68.27 186.14 68.65 186.14 Z M 69.33 178.02 L 69.33 176.49 L 70.86 176.49 L 70.86 178.02 Z M 68.65 179.38 L 71.55 179.38 C 71.92 179.38 72.23 179.08 72.23 178.7 L 72.23 175.8 C 72.23 175.43 71.92 175.12 71.55 175.12 L 68.65 175.12 C 68.27 175.12 67.97 175.43 67.97 175.8 L 67.97 178.7 C 67.97 179.08 68.27 179.38 68.65 179.38 Z M 69.33 171.26 L 69.33 169.73 L 70.86 169.73 L 70.86 171.26 Z M 68.65 172.63 L 71.55 172.63 C 71.92 172.63 72.23 172.32 72.23 171.94 L 72.23 169.05 C 72.23 168.67 71.92 168.37 71.55 168.37 L 68.65 168.37 C 68.27 168.37 67.97 168.67 67.97 169.05 L 67.97 171.94 C 67.97 172.32 68.27 172.63 68.65 172.63 Z M 66.92 188.64 L 66.92 166.83 L 82.93 166.83 L 82.93 188.64 Z M 65.56 166.15 L 65.56 185.74 L 64.02 185.74 L 64.02 163.94 L 80.04 163.94 L 80.04 165.47 L 66.24 165.47 C 65.86 165.47 65.56 165.78 65.56 166.15 Z M 62.66 163.26 L 62.66 183.17 L 61.45 183.17 L 61.45 161.36 L 77.46 161.36 L 77.46 162.57 L 63.34 162.57 C 62.96 162.57 62.66 162.88 62.66 163.26 Z M 83.61 165.47 L 81.4 165.47 L 81.4 163.26 C 81.4 162.88 81.09 162.57 80.72 162.57 L 78.82 162.57 L 78.82 160.68 C 78.82 160.31 78.52 160 78.14 160 L 60.77 160 C 60.39 160 60.09 160.31 60.09 160.68 L 60.09 183.85 C 60.09 184.23 60.39 184.53 60.77 184.53 L 62.66 184.53 L 62.66 186.42 C 62.66 186.8 62.96 187.1 63.34 187.1 L 65.56 187.1 L 65.56 189.32 C 65.56 189.7 65.86 190 66.24 190 L 83.61 190 C 83.99 190 84.29 189.7 84.29 189.32 L 84.29 166.15 C 84.29 165.78 83.99 165.47 83.61 165.47 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 175px; margin-left: 86px;">
                        <div data-drawio-colors="color: #232F3E; background-color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Service A
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="86" y="179" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    Servi...
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>