<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" style="background: transparent; background-color: transparent;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="2579px" height="736px" viewBox="-0.5 -0.5 2579 736" content="&lt;mxfile&gt;&lt;diagram id=&quot;t1SR_PXq1gYllvhI1J37&quot; name=&quot;ページ1&quot;&gt;7X1Zd6LKF++nyVr3PpyzmIfHKkBFRcERfbmLWZxQQEA//d3lkI7RpMcM/T/pbju4KYqqPf72roI8sMqqqqfOZmYkfrB8YCi/emDVB4ahJVaAH4SyP1N4XjpRojT2TzTqG6EfH4Jzwwt1F/tBdqadSHmSLPN4c030kvU68PIrmpOmSXndLEyW/hVh40TBDaHvOctb6jj289mJKjHiN3ojiKPZ+c68xJ5OrJxL2/NEspnjJ+UTEqs9sEqaJPnpaFUpwZIw75ottRfOPo4rDdb5D10wNlZJp0oFhguyfEnvNWb/z0UYhbPcnSd8Hm2+v3AgSpPd5twsSPOgusd3x700p24HRj9OF/QkSFZBnu6hybkjljpfclYRnuZO38sn/BbObWZPeC2y/FnOZxlHj31/4wMcnFlxny1Bxqo1ndObon/wF+J2ULrjfyT5+2wBrqz9gPRCPbC4nMV50N84Hjlbgi0AbZav4K4qDYcvsu+KTS9L6JZ5H8ccmbphTj0onPU6/j0eOcs4WsPxMghhDpgwKwY7RGdynmzehJHnbhiBvtJCmrlRQl6Qb/nM0vRb8Zm+VUKNfZCYB3Q6YB9k8XyA0N/Ne+aK9yzP32G+dIf5F4n8eeYzn94DXJgnS9fuk7/VXE5ibplHXxr+eeaxN7wKfIiq56/rZB1cMwcYkO5twsh/+cvXyZmvxy9qdfVt//SbGaQxjDtIz8TTvckNf4XPMOpkl3rBK9OjLwErd9IoyF9t+ULoS4Olk8fF9Qj/vBy4/3E50D8sB+ZD5cD/j8uB+WE5sB8qB+GX5XAlhW9C+VRyuASCHxAD9aFiEL8fW/8iuXyX3fILAfyHuX2+1ExiGNBj4Oev4z4tCNc9nPTlfNHTxPBZP88BxE1HpwnedHSU/eN0fkMdbnPQepw3di7QkJfHyTq7UY8LQG07brA0kywmzeCUm+R5srpWlvtg9oJ4PRAyUQWc5WmyCJRkmaTfdC6Ml8sL6YFkqlirMaTxzNmQoayqiNQ9/nUOuzT4N4rz2c79f17ik2s3hGFBqhVwh+ytUaAkXsuQo29AIH8nS7zQ/ryNyx9p0kEV5/alYzh+0hV8+9YT+bJ/8uUzu2zuI132I+S9TUaxes5BsXKhyJf0lLpQ6DMF/p0PpBv9yBZB7s3OnD8az3EWPIZ/cHfl9OGhqUIo/zL8HeI9mnhLpG+bwQ/63h2eE+/RxFsifduMfLuM+pp4jybytyN+fjV952r62dXwj8XJLl/Ga/BulyIp4XGYrPMnzg3+1ohKPHd6IkI0Ldx4SDgTHv/AGd/JZo+J8Hd98w865CfOgYz0XBqmmcv3s+aQWzrZ5jStMK7IOG79c5lx/6bByR51j4wHw9fT0XWrjIVzSzJ27HiL6JjkX+bsB6GzW+bvmc5/uCd/zLKemP4xpuE0Jo7xy4z/DjPWRJoX8X/GjAOiou5JRT+TPTMc+9H2fFva/LLnL3v+sudfsmfuYk4fZ8/sjT0rkIGa8SYgyvVl0H+JQSsywx/N6r9h0KRKsrno6Gey6HsLaO9s0dxthFZ6X4b8lxiypoqkXvJfMeTASz+V/X58wszfqZVJD4h7kMTjAUuKZq+VR5M0nyVRsnaW2jfqVbX0XOD8VtR8XB58scCZ5U6aI7IljijN0smy2LuQa/HyWyHWvzQ6V2mBcj7/WHB9LK5+K9SKn3NRjL7sz/n1VbGznv0Dc+RY7jqbO337zZUcln+290h+1sULKzAgJ2f/pNk5Brx4H459VlsSpWfafurx2dXn1v88G1QShlnwJitCNPVeC7WfSv1+d5nwN73WD6zK/oGtoL/g4Hnh2uget4I98fA0e8fF08zb+fg7G2fPKxuXPXnfNuepl3173PMVEkm4WTy5XRj5qX1m97HCLEnjA2AA56e2o72mKK/uSH13adzu1yXphbuLl/4XbP5LYPN/MP89KeibWONjyL9ynZdN4R+GjS+PdXy+KMOK16z6DFHmAh5+YtX9Lw8u9AsY/COFcLteAhlTHDoeAcfuzlucUfBXkPn8QeZrM8OvlmZ+zl4v4ef60a6PDz+3ayVfQPFvs+EvoPjni6gCL19bqvTR2xTo33h4RRavipP/UP9S9C+VJ/9QzfTh7ctMZ8/6/c3ozH09eKcq02WcX5tP/moX/LX55EOWukSO+Wxe+qcr9ZeVq4ff25r/0jLUZ12F+lH/fIaoH+egxVcF+v2VyRfkwvzWgxzX65z0T2vLOwhY/lEBf+zDdzT9wsoF84AvD2fI3LfC0T1lOHrxh7svEHj01M8d+ir2/aOugIuND+fq3W1UV19znz9S+ntNq19b2uXYy5LOry7mvsO6KH27znERmXZTBqxdV/++Hsf534BeXxWs90RcHH29KvDhu/cvPu/zLaDwz3exXPZBPa3d88Its+jLFN6AW3cWUP48t16T1OtLGe/NjtuljBt2/Pm32rwmms/FntvqsKb0Hxjh6GOyjbO+YpWw3ZGXt2H30TH94508EyJAMHXW2YUN+FtrOIqOP897Qi5hGsmvRfDzGGBep2Gc+/gK2n9H0P5TW4IfUfSL8PrZm7w+bE9w9nBnYXj5bDrpyazf2I28c4C+3bz/PL96PHhcoZdu39924X68Or6h8vs47rsg6QWg91zsxxuiC5W6UEhXTu6Aczt9ZWqbdQQGEo9wt1dSrXqUIPjT6Q9n2jCCIy2D/3AbIwN+qq1JPZySBsju9HuUjtKM8wSLEHpra0hjhJRqXhbSxBoSYjnUqmGPnO/KJepW508OH3oPn+r04c8f0mZzOQ8ffn9spzQEWZiQ/iy/1uwOS2QN6xitiYyZWngQpcjQcZlykl/WaZs0VGysj20YM/Z6ZLSa1Wg1C4NsjiXDWiw1a9TTqwcGsyPT4rDVixFnB7Y9AJJbWN25FXcqNKpzEd1WUt1JGlo93jRzq6vrXDRMY7Ta9HY6ahutIlxMcVLMg+l2yjebS9OY7Qdycws3G/u5NcNcjen0W1q0MZauOKFWfarf30zpLCajh8iED/AJ+Knd3bfojVAFPXqwzJ3Ucj1lthq0odnBasrNztSW1tv6pvTKhtSU0cbf7BfNWZlYTeiAF2ubZFTMF4NVvPLHVIdMNrBY2vVSOC3TGfyfbcNGy23MOanLDxv9fTYe9zx7fFD3zaZd+uZeXel9S7A3O+Mo5v5w1O21eGWi60Shf8/IX19lvrwucH9t209fgHcHSHDCm7kA/saa33x3/0uvDHssw32ueuil2PK0XPZSfvGbu+sF5nppk5aZf/nrTv7Q/vrnd2JE+Wl3N+1FmX6t/fV+/D9WRGKEmwAF4XoDkTxLgOwHm2WyvwBOPy4uYPP//FAY04QHGT9g7nggPGDq/z7Brk96u2shf19h8WTqL3om6l/hkq5cnqI4f/1Npf5HuPsgyjvUINk7OaTGkAKkxNYb6KgJ9IN0yW8kmaRQp2P5x/bIv4SEQOhLAOHfz0o3T9zZ+aInHg4U4wSemKO3ZKW3rB9d1scuYrqzp1S4l+RS/Msa8XvBib1Ncp3NhrjiwFm9nlD+VAYE2YAfB9/O3X+j2rG5epMYnRt/ukLmvXe63U2TdtnxXm+nV9dlSfpOXfJ9MQ97J+05O/4ZfbdssoKgG6//OUqJFEyoTfVyoQSizb2Db6GF3OSFyAK8f5YK3/MfCUgpXB738MwgpATH7PaqFvbdnPtRxd5M7IJ87fYF6lbu0rumu5ctnVfhQCRLiAQeSA8Ik1jwGhy+3SPwAvz8Pqpk76DKF1rKvymQd1qtZX91f8XvvvrwDz9R+X3Zcb+7DPGbiIp5jqjkZzbzxq9BpdnXd178IVG/w5tzf8BOf3izxPtpxW/a6e2uii9U9VaoyiM1ZWDZtR6+Bcq6zt0YQfj3dk3znXHW7RaQepJEy6CWpHcU7W8vIpMP0pGCSBFXpYLOQLgq0qJsAP+1tVJDq005+eECMx4w/WEPjxoRFn26F9dsdu2pFVsxy5FzmHaRtD0kxmohenRaFJEVdenarEUPRK3oM0gtp77sENg53qSYdzZFf7MXJqtNYImL9sCKmjNb0M1+1VprzVyZ+VHD6AuUyhi0gVu19XCRbkcdB3QJi/KB6fWdQ2tzyKvDsS6NwwBQHfbUjjSvdYdBv34uV0unn6SoKy566Nf+lPo8xZS6AU3B5sgXKkcIhO4h3IUKY+jqeixK9hDvSQ1YLEMyGjgCdSaLYrUA2H/YW2iWzeOoI/syO2PTRhbV5kkjybuyyRG1NopwRmTbmDVIeZocBuR3gtQKWkROpaP2wMusbsG6QNtJ/C4Y7+baYihSeC1GBksNUXds6xFkBTgqcbIdtMs5w3JlKtdytjCWA9Er/ExJMMIZ0mE2ej1cxm63bEUaapQtf1JL/ZrLHaQyL/hDSvnUHG416LuagWSGFM0HEG1x25QZp4AzTI6IB69Jo7kkSofhoTS5FcJJ7HhmEa7VwTY8THZwhZ0Wm5k64JYIJT3UCEpcw1PGX4exPSyYADWInLRNv1TYrCpR46Cpw5o2JUzxY7FFB7OA6iSKPF6h2mQaT5AZ66i1XBzZBTeosXiECKv2HaTMNKRbaGu0I1Vi+Lgkt1Q3ygRhshwxU0lZntuTkVj1zbwcKrnACXItIrdz9Py8yrEiAtx1RWStpqgXR5sSLjspms8mFTZwpzsJppEyXlmLqGlppd4dzkyeTaCR0ke9LRUf76Fuxwg5i5xoLZwaRjifzldoZJRINdSYJnPXFhZycimZRvXRCi1mKlJ7mTAEv1Zb98yZ5kqlVs+MSNO1ha4vI31KT3aHadbNwlnCLpHmBKg+tzKrU7A+3IgsTzR0VFKRZnR0zdLNeifb234o+8gqU81TdS1CHWHLymZlowxmM1f6LsK7fW4uC1EDByCPG4OJElYZTD/hppHuUmQdfRgodWW4Qstog2I024CG4BmvLdAsmqItmikyUxVW3dBnDdQH76EO5gxjkitFuy80ilJNqEwzTAvuYY593y2UlTqMLaW+inBitcMB2xshHLdQ0h+ieo6IAVlwk2mMlPoC2lRbDhkzDauYwqiDQj1RZqD1RO0XnmaoloY0IinSZwR0QZV6VjdrgsqvYoSdAUyuo3kaVUsU1MJTPcFj4AY69Ayc6xx4ucjQhkqqgP8sexrQJoSWaUN0phl4pHgIdRMLjSzQaXWrWbWhXsK9Zjqc08i5AbL20AgnuGogCy1QZE4soJE1sqR2pEWE1kODXhe8LLUo42ho6Shp06DBFHgqNW9GzWQG7schc+hIMAd1Bo4gaYHnNtQKzJaaw7X1GK5tlxYqTdRDzp4sGU4GwMNOD8ZidNq4jbcowtFOX6DRgiwVlkTtPCRrMOqE0oAfEukygS69DrDQqG3g0K9B7wZ4JMSC/wI9GyhddFAsBVH9oTJuQu+AYYCDZhNO9zaSzmED+I+6IBW4A5GLNEM4JLfpWjGozQLmtdcsTUe6sgD27gkb2lXdWsGo9hqlNRUSaRBWLGgKFrQgjK0rU0WbkduVXWhLeMJpHHgBDSNDWUN/8wHcK8N6zzB0aKeEPatvtGAoZUeK0cKB8amRm8RI64MI2jNXXymaAv2IyVCvYzjUsdi0mnUSPCfqSCubCKKkki2jaDKJdBjk0ELDTgto5ShuRgrIFJkbjFE5RRZGBRcjva/DPKO1NkALUH7QyVp/osxIeG6hpr5Vl4Q6gGm3+6BV9bgPOrohV1MgcKUkM55iOEm0JBpqPRSXZDzTJtDI7CNKI+2AVgftmw6bQAPt00YKzM8B7Vtax3MD1BvCUFSYtmUQziYKTWTQJLQe0GJCqx9px3Z9ZFEEFFC1SZxojRQxihbWRbkm6O68ic9e0UZ74sTEw6FjrqO50Ieb6Yw94CXPtrl9X5xLWjj2mmQlk9jUghzpikZm3SRDVNAMkR9EthqoMgjaisgPvX+mokgH9btz6bFDDSsE3Rwvvenw6aXHDhGomfK/NpZc5SOlfpjkKkfi3649XnX2h2Z/EhRgcphxO9KkfvDy0K6KsJivVw0Sq6dZ5cHZNW3YfNz2JTkgsX0dBKUCYFC1ABgz7mQu91i3XmTeypyJpUgPAvYQ813bpeydzZp+TQTkNRy0D/x+PTDsaWYilW+iDm7xk5QggWZ3IDl2wLUlp2kNWgQBMYMRAQ3UXu62s9Kil1PMC91GFXV2JFbJ61FUA7cDMBFnuyOswOGcgDe3Xs9H00hmZH7Stdfzld2xIQ6FzNzwBmTfgNTdNSnQbMdF9dXISNgwlYiWjgfLcTdwuvjgNZCWkb57abE3w3Ve2ASd1abBeNIjWObgn/wgCHK8bRFoEIbivpC7+wNgToZaYQg1EGVaU68TLrhjIFu3upFIH9DYWqNWvBsYu8JPSa/Opt+JphMl6oJ77gz97RrXCjKjqrSGDQtDDGolW4GAFrGxYXTLz1nPaAYgfRoN8WhXkaHV5+Nh6UpSo0RRq6yhUWATaFLYXdFbSlaNU6LWhjEbOKRVvRK8GjrR0np+nI0gy618tU99RkBMBCHKt7247g30pjK9KBrQpFUuOnalGcOmgZJWOgU+19u2aQsbyk3UCbZqI7stM/p45awE/kKzB6LE19u+8aTd0A7ZlcwsU6lyTXARKCWzmYbE/XVAXSJ2uhO2m06kQRRK1G24buyLXkxRgmxAEF8R2pRs3/Dnbd+0kyVuBYHjWx7YCcA3POoWh1JedIr9fr7sElwcsWiq+OBTR/t1KilHELrNNb1leDu+BKvqoHg6ItswKvKewxoLSGhZ7xI8ru6YrQ0HqGnB5elCCB2zMMpGG7S0drQoZJY1kevO/cmGaO66UbAao845vh7uTFHf0y0MQsX7QlCYNJSO6j2J6SDcVRFywU07reF+0gw7wW44Lns6scpK9D0fDLDWlYn/ZOxgNZ0RseYxPSBWbJvGIAfAB+xMVJD6lh9jSbbnBHCaw5joapedyNncW8jcoVGV3cmEFbmqs6ocOFfEY+TK0tZXjU1v155XzhF393BU1yD+Rq0+JRlFO+XQSs1dBPDvE3q2V8eyTEIDWYxbS0KVlSuHOuyYnj5QZc4ei9NirjPuck78TYfkg5In7eXw0Fm1C4CteEO2/hiVm4O2+JJ4oNAIsginPcyZg6sRsGiIE1mea5VkEuzsbUWS6XksLsWAaOb+uOEnKWx/58RuENaHAr8VO7hUwibguY3drhEPMG/uw1mfJGjgw0DyfGgz+35O9j3t7E01WimrRaqb5qAMDDU/FGzmLFAO+j9uTcwlWZ7CJbhO1p2AlqVGY8fmbbyXJE8Qt66B1UBy57Hjs1iuuqimDAChU/3NGuJ0GBVC6W2r6lBxXRdxaHnslYwKHz2dnBPmqET7mvtDywwZN+fUCACSQvfo4iCGAbGADTQaT6PmkAEAtNABb8/c9iyV1uBuFnwlNYKdpey1Eg9HgrsciYUbHuxWtcBtsHmGWLK6HHN6fkm6cVdRkKB0ANCNlqHNhRO5W7XUvHuYzdqlYq0iJdrmjOnqlLPf4XAdSaAIQ+KR9q4pCzxxiQszRgXxKd1K4NrMdroyexHo3JHGyTuXONlR0RztGHqQsVadIj5y62b9g9JT9cFZRwktXNCF6Eg63s6aqKaORDNjE97hHYJuuupIcVFL2W55f9dTqAN1RRVsf53w7ka5oh5tU7JXVH6MxlOhrFtLC2Y1b3NrHtRlqIkd1h41IQXoWDXkuxIHV4w4g9kt7U1kREZJqKbtZq7bJOFBS5q9wwG8K1hMl0SfDU1KAo1slgfB0NuwIrUqYV6RaimrIfEoyWgd5qMEN3vzJWsApMPJYnCWAjaLNSMtUJZUxPElPiNDQlUrsY6tVu6tXbEn8yvE90lzoiAcJv4xD2eUmy5bBcXJElcvg3CtH474YLyxOiFDtMUKaKLs20lxMMbpgaaMqA1ZTrvXr7tqe5rS+5hBzchmTDGdp2ueJYIKSnF46LZNeTduEaPZ83zsZ0WJct1DScduleZ6K03ySshGITEdvpFn7mJd1CdBHR+O2CCs44xp27S053BYLHsJn7c61dBr0+xe0A2lJHnPzBmu8ZL3Q1Qv/fkEAyb/fH7t1bGscrUiYM/J5yewpw4h0O6bsRUW8Z4FqOfWB8MsZPns6H7WKzaVpgZPgFVDBqi3a/uZ7M+LwgOUAUCvPXCWK8qdkADWW7uNIpusTJItl+Iy9NjBCpCZSxM1sWlT7nAiid+uyO0bgAz4qIsGlY7yWovnCHwEJNCTtoOQS6lJMwqJ7hLV6Y+6JgtwT8+sIXWEe4F6iPwj3PPn46gW1ZDAD7C0y85wD7wwaJV9BHubiQFgb2n7AzdKBuxcArC3Kzu7Zqkk+/4UcNrI4AhYALAnj+cA9XaBiQ8WQD3o1+EtAkQFM2QJ2LPlaTdwjw6w8k3kkURo3KoXp2KOS6CeuR8A1FuViyPUy7X2VCJQr012b667HYB6JWJAKNu4GEjbwhddyZmOu9EGEvgusdyJt7WbjSJkq7K0GlYvaqFttiF23OYbTaZtyTPGy7AHcqcggbR3PXFaV8c2eHqpAVraQjU8CtwTyOuIxoyggGUJHmRGmSwO6IFWCQbkw0da0TjDvJzXcxKktn4dgB7xhJ1BGDOWpU+VyUmdCA3sE6+IZU9tjExIGJXEKVx5RTfabHsk8KWTDCxs0f6o3RFa41Ww/EYL+2IG7WT9STtvFNpbAutrJCesRHMIWXuChrXhsV4ndw5eP1oD4HNmRrRoJihpbwDwlQXesVQLAN8CHGHSTqfOIZyL0449Waqt3dj1SZGkWUK88Y1iQMoTizytAPAZJye2oADyecjRx/tjQqMYorztoHYr8zIejA8kF0+XR7QtSxKrRY3lijhqer5itw1GAeyNAe6JoWDmRmm2RsUxE0DmvpZSAPa8I9grasVaZ9QBJdbPqGDPtmbpCe6p4HFIQZQMxwXA5+3wArsKws7WOkym7gnwzQwC+Hps6MknwMeKzNpZTaupnO/53mk2BO6RdQuMOhsV8qftNAC4F85rXdOOU8ZkdTqfe5nMDdR92fUAEXBlZ8VPyBBXNmrT0rbTMDfVrjWfjbMz1BuDQ5ll7b5Wdo9Qb6zuXPAwn9CvfY3lbxtLgtb+IfIgPZGphM2WgpphSsfdqgtYTZGE9jZxdAs5lLkr7Ygj1dit7gos58qQozuGkmcT+wAeKfSxl6FBf6YdBvGK87sHdiiwYaZGHTutotAs7GXSlzE7jaC3ZL1bWTgauFtfJGqu9qIYVcXcQtvK58VxpFktGO8Ozi7QRGOELMDxNtaURNHWbDHfTuFmqFXYsSdMpbW/oVCEqRbedSiuhlq7vdZhQnysttIkj+qa61Bu0wjtEO3OvTWBz2osRHVwqfX1YH5Yklp0TEopBY+6KCBJHfa6pkHXaNQ5VtUlvujWtTHqKoHd5cmSECsVThOY05xNSZWR4lhu65HC9qFnwI0hA/Z08M4ND0yegJuuaWVUY47h5LK2QoO49FSLsJTiWnC+Vfp1hA66NAgILNy6rjhKIIrMMHH2JPwFIrMh3ZvYMqN9ghoivdNYdrep8xRCUrvW7UhcSPyfaZnlRJ2U06is87WcqgrbguRhSSILPVpz4zm2QBJbgAWcPKLnRjgdi3wgolhh9yJ51SZZ5qpN5IHMoS2oF0hLasmArUGXElVTTWHEWB1rCMierGdIeJdTXKPETb/VESuC+KsVWhZzUIUgFL3BxC870awxFNnAP8PXdDahgLtbDM7Z5mTL9OuNbNNGcAMhNALM02tWFhUdjUmsixiutbBQppQCS/yl0JjuzCP0WJPMcEbWklmnZynUqLOIGHNnDql2tN9rWlSvzbm6JCMf76tSkHwSTSaziG508xq/nxla5E5SEhgO9d3KWBPoC0Gfl4jmdPIOQvvexs/qHIWpqhJMLer6YkHhYc0s+kQs7PzQlWPeOuSHLTvI11PzUEIqhUQ5n28Kl9KZ9nhHySueaq/oTUjCB2dwyKkpLNELRQQbgvSFHYkjoUnsCa/Lu8uV3/3TUojUynBKiXN3FMr97XHNF/KHIa+li2YURWQt/Lgd4t02Djz7dbyPv6v5ybYBgb3dNnCh/fltA5d9NF+/JOrvfpD0P/juw5/5JVHvtzNIerZbUODubA163ydPL7/45s6TGRwxdvX0ZM8nfhaDcBz7cRp4ZzUtQSDvKdbLvq3L+9U4+Uamd5/XuGzLfgOhMj+5K/SFTZm/9OzeG221FZ/tq6Mvr1r/81tt+dt9mU9M4SvqfUW9Txr1zo9hvrjj9xdD4Q+7gRvn+vJLKYVrp3nvWaQ/FAjha5qQh4O+eQPCNQP4RVr8fw==&lt;/diagram&gt;&lt;diagram id=&quot;1DPB1elWb0_-w5CuIvj_&quot; name=&quot;ページ2&quot;&gt;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&lt;/diagram&gt;&lt;diagram id=&quot;eNdSHP6MPUNh7K3EtPhw&quot; name=&quot;ページ3&quot;&gt;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&lt;/diagram&gt;&lt;diagram id=&quot;1dO9kirlTEsgIiyZ5COf&quot; name=&quot;ページ4&quot;&gt;7b3ZlqLK1gb6NDXGORf/HvTNZQSgoiKgqOjNGfSiIioI6NOfGaZWZWNmtdnUqqy9cicZBNHN7osZzfzCKlnT3nvbhZGH0foLQ4XNF1b9wjAMxVHwi6QcLykszd2lJPs0vEujviWM0lN0l0hfUw9pGBWXtLukMs/XZbp9mBjkm00UlA/SvP0+rx9mi/N1+CBh6yXRk4RR4K2fpk7TsFzcpUqM+C29E6XJ4lIzL7F3LzLvmvfSkWLhhXl9L4nVvrDKPs/Lu6esUaI1GbyHw9J65u3Xdu2jTfkjH7jcZJKGa6U97UXmepFKuST836WUylsfLv29NLY8Xgcg2eeH7SVbtC+j5tawe/41O/W0XfTX3gKbRHkWlfsjZLkUxFKXOi8c8n+MeOGQ+t54C5dyF/fGWmT5C50vNE6+Fv5tHODhMhQ/MSzM94cFRmUTRqQQ6guL60VaRqOtF5C3NYgCpC3KDCpVaXh8dvjuD9PzBHo6du83NOyToWlHlbfZpL83Qt46TTbwvI5i6AImQ5WCEKJLcplvX2MYr2pKoB+wIM08YUBekJ+OMkvTrzTK3FMG1NgvEvMF3T2wX2Tx8oDQXz3yD4Wf5fkbQy/dGPorPf740PNPhjMKwSJc/tzkm+jh+EH390eXjPX/+Oufs8vQn/9Qmwd/He//ZUX7FJod7S+Jd3WTCn9hlKHR+WEfRC/kEy4W1Nsn0Uvlibepto/WXplWDxv3xykgPMf8WL3wPFauKfJVHKhrCn1Jgf8uD9ITiharqAwWlzHf5ummPPeBx/AfaFPl7oeHrApJ+R/D30i8lSY+TaSfZoNf9K0aHifeShOfJtJPs5G/rq1+mHgrTeSftvjx1/SNr+lHX8N/LM4P5TrdRMpXPEbGOM43pZKv8/15/Fn4X4swBI7T9fpeuogQTQuQXpT7fBXdexOf/8Gb0CsWX1XbVVn1PT9aW3mRlmlOlJafl2WePavNriovAKEiondfnElLLygUzMDl7wvnkCq9YnvXrThtSDsw4LoteZk1CYHA//PqgvvfProTRT0g7cHw593Tw1wFC+/WpO3YC1bJWW1f+xxGsXdYl6+pe2XpodXj6Ke69wa2uKb9cbkXn8i9VkEHMcwLQB9+yvDfIcOaSPMi/mdkOCIs6t+x6AcSZoZj31eYpafCrAw/hfhvEWJVpCnq3xHiYP+RhPe9DbF8G4ADytYe4m54aD2E5J9I/L+hAD6R+BvKP0ezD+X/nW331ff+um7hXxgonn/kqKOeuotoXng6VPS1B39+rN7Ehf4CmV50Dr/1YHwgx/n3PedvPThPXeeaMvrCCGfdUmy9zYOBEnYHsjSE/a8K6f+CO42EiAdv722K6yDgb7nhKTn/vriLr+YZyS9Z7ksboFt3zbiU8Wms/w5j/afQepaGIaH1E2P99cWjxYJ3g+sFqTzfpyeoxbs2Yf2oO/s7qX5dJfK2hvnWuhDzRaa/oCvqRl9Xir7qAOEq6Pdw+N0rGTKLX2TuC4IU4YvEfZHutAL9Rb7qCemGHriQDjhkDXz7fTW+vbfMcfno3soHqJI7mjGEV/4ns68IteSH603c1djeNwvcjaU+muFei6b8DZqeiULoJRE63iDBy2tSX155GYn+0XUk+l0XkuinK0nedkvaHXnZy9btp9QxqKYwjb69uxDkkZY+Z1efaOlL5g83mzrb92h/dsEXl0Ju6uxDca7r1ST2IeLnuP89xfzcDSB3TfvzXPV0naKd58k6auX7G1x1GbI0O280+j5pvzsLfYb2j+l3rhBdU6lrCinKKz1AkXd/Mq3tJgEkkk6wOaypXjvJEfwbjMYLbZzAE/lBOlLQDH6rVDRwBJKiuFifugY8FQ78X1+rNZRta5IJIXcwGlI62hdcINgkYbixxzSGr5plXUkze4wQdpjReIgnnQSLIT1MWy67CdSGbZj1xDvNTSTtTrmRrcSA3ldVYicm3Vr0aEfUqhGD1Hoeyh5BrNPtHvPethptj8Is20a2uOo7dtJduIJujZreRuuWyiJMOsZIoFTGoA3ca23Gq/1uMvCAlbAon5jhyDv1tqeyORHeYHAcAVjHgTqQli1zHI3a5+RWLN39hv9O4mqIfu1frS/3mFK3wCnYmoRC4wmRYJ7iQ6wwhq5upqLkjvFxALWIdUxaA0/AzQQ4tiIY/tPRRotimSYDOZTZBbvvFElrmXfy0pQtjnC1UcULQtvOogPfReQxArvKtCpaRF6jo74TFLZZsT6kHST+EE0PS201Fim8ERODpcbInLp6AlMNnNQ43zn9esmwXL2XWyVbGWtHDKqwUHKMcIF06I3ejtepb9a9REOduhfOWvuw5XMnqS4r/rSnQmoJVTkjXzOQzMAwYwfkCPctmfEqeMOUiCWjK02Wkiidxqfa4jKE89QLrCreqM4uPs0O8IW7r7YL1eHWCOVD1Ilq3MJzJtzEqTuumAh1CJ207ahW2KKpUeekqeOWNieDEqZij44WETXIFXmaodZsns6Qleqot16dhwsqaLF4gshQHQdIWWhIt9HO6CeqxPBpTapUt8oMYQNyLNQCGsQdSUvs9nZZj5VS4AS5lZDqPL3c3PFKRgh4MEVkZ3M0TJNtDZ/dMVrI5g028MCcRfNEmWb2KunaWq2b44XFszlkUkZouKPScx3qboqQtyoJ18KrcYLL+TJDE6NGqqGmNOm7trKRV0r5PGlPMrRaqEgdFsIY1FprM7QWmi/VWrswEk3XVrq+TvQ5PTuc5oVZxIucXSPNi1B7aRf2oGJDqKgLPx0d1VSiGQNds3WrPSiObhjLIbLrvRaoupaggbBjZatxUQG9WSojH+HDsbTWlaiBApCnHWemxE0B3c+5eaL7FJlrjiOlrYwztE62KEWLLXAIXvDaCi2SOdqhhSIzTWW3DX3RQSPQHqqzZBiLfCm6I6FT1WpOFZph2VCHNQ1Dv1IydZzaSjtLcG73Y4cdThBOeygfjVG7RESAbKhkniKlvYI8zY5DxkLDKqYwGqBYz5UFcD1h+1WgGaqtIY1QipSZQLqgSkPbLLrA8lmKsOdA5wZaoFGtXEE9PNdzPIXRQKehgUudAy2XGNpY2SugP+uhBmkzklZoY3RJM/BECRAycxtNbOBpdafZrbFeQ10LHd5p5J2D7CNkwjluOshGK5RYMxvSDJLWOqclJG2InKEJWpZa1WkytnWU92ngYAo0lVp2k26+APXjkT4MJOiDugBFkPdAcxtqA2JLLeHbdgrf9msb1RYaIu84h09mDozhYAhtMQZ93Mc7lODkoK/QZDUmCo2wXYBkDVqdUxqMh0SKzKHIYABDaLS28Bi2oHQDNBJiQX8BnzmKiU6KrSBqNFamXSgdAAuMoNWF18OtpHPYgPFHJlAFaiB0kRYIx6Qa006BbVbQr6NmazrSlRUM75EMQ79p2xm06qhRWlchlgZhxYasIEErMrBtZa5oC1JdbUJeMiacxoEW0DAylA2Ut3SgrgLrQ8PQIZ8SD+2R0YOm1AMpRSsP2qcmfp4ibQQk6C98PVM0BcoR87HexvCoY7Frd9vEeM7UiVZ3EVhJpVgnyWyW6NDIsY3Ggx6k1ZO0myhAU2RtMUb1HNkYVVyK9JEO/Uw2moNWwPzAk63RTFkQ89xDXX2nrkmqA93uj4Cr2ukIeHRLvqaA4EpNejzH8JJwSTLWhiitSXvmXUgjvU8ojeSDtDZw33zchTTgPm2iQP884L61fX7noOEYmqJCt22DjGyu0IQGXZI2hLSUpLXPaed8I2RTBBRQrVmaa509YhQtbotyS9D9ZRdftKKLjkSJiafTwNokS2EElemM6/BS4LrccSQuJS2eBlALGhGZWpEnXdFIr7ukiQpaIPKL0FYDVgZC2wn5pY8uqSjRgf1ufHouUMMKQTfnT58UeP/Tc4EI2Ez5r7WlVPlEaZ9mpcoR+3foT7PB8dQdzaIKRA4z/kCatU9BGbtNFVfLTdYhtnpeNAG83dCGy6f9UJIjYts3UVQrAAZVG4Ax48+W8pD121URZNZCrEXaidhTypuuT7kHl7XClgjIa+z0T/xx4xjuvLCQynfRAPf42Z4gga7pSJ4bcX3J69pOjyAgxpkQ0EAdZbNf1Da9nmNeMDtNMjgQWyVvJkkL1A7ARFwczrACx0sC3vx2u5zME5mR+ZnpbpaZO3DBDsXM0ggcBz6VzEOXAs72fNTOJkbOxnuJcOnUWU/NyDPxKeggrSBlD/fV0Yo3ZeUSdNaaR9PZkGCZU3inB4GQ012PQIM4Fo+VbB5PgDkZKsNgasDK9ObBIF5xZ0O26ZmJSJ/Q1N6gXnpwjEMV7kmp3nY0SOYzJTFBPQ/G4W6DWxXpUVPb446NwQb18p1AQIvY2TK6HZZsYHQjoD6NxnhyaEjT2svpuPYlqVOjpFe30CRyCTSpXFMM1pLd4pSkt2WsDo5pVW+EoIXu0vbt8twbQZZ7ZXbch4yAmARMVOgGaTtw9K4yvzIapElZKXpuoxnjroHy3n4O49zuu5YrbCk/V2fYbk3cvszo08zLBP6a5jqixLf7oXEv39iN2Uxm1nup8S1QEWhPejOPifobALsk7Pwg7LaDRAMrlKu7eNM5VsOUogTZACOekbT5DjKHy35oufka96LIC+0A5ATgG56Y1amWV4PqeFyuTYKLExbNlRB06uS42UvKGYTuSk3vGcGBr0GqBiidT4AjcUPW6VssIKF12yR4XD0wOxceUNeGz/crIfasyqg7feDS1lmikFW3RM5chrMt4dxNp2I1Rl1yfDs+WKJ+pHsYiIqPlaAw+1g6s/cspaP40CTIBzXt9cbHWTceRIfxtB7qRCobMQxCEMCWKRP9ybhRNl8QspYp7RApdi3DKQHwwXDmKlB9x0+xJLtLAjitcUp41WRncrEMVjJ36jS1OZuxItcMssaDd1U6Rb4s7ULV2A4P/WXjnXH3ECdtDexv0htRklH19xzK1NJHAP8+oGZ7sS3rPDaQzfitPFZZufGo04EZ6o4qc+5UnFdLnfHXS6JvBmQ+KAXSUY5Pg6xfAWzF25TMJRq/BG4JJfFEoQnMIrz+uGROvkbAoiHOZHmpNZJFsHOwE8lML2BxLUaEM4/wFy/mlRsevNSP4vZY4HfiANdK3AU8t3X7LaIBlt1jvBiRCRroMKA8H7vMcVSCXOODu20mmZKt9rplOXVkqOWpYgtvhUrg/2lvZq3JahOuQXWy/gy4bG90DmzZx0dJCgRx5xtYjSR/mXohi+XGRC3FAYROjbYbsNNxUgl1sGuaU8OZPuLQ+lwqaRU+azq5JIOjEu7rHk89K2b8klMTAEgKPaSrkxhHRAK2kGk6T7pjBgDQSge8vfD7i720AXWz4hupEx1s5ajVeDwR/PVErPz45PaaFe6DzDNEktX1lNPL66Qbm4qCBGUAgG6yjl0unslm01NL87RY9GvFzhIl2ZWM5euUdzzgeJNIwAhjopGOviULPFGJKytFFdEpZiNwfWY3z6xhAjx3TuPkg0+U7KTqTg4M7RSs3aaIjtz5xeikDFXdufAoSYtXdCV6ko53iy5qqRPRKtic93iPoBtTnSg+6im7HR8ehgp1oh6kCm64yXl/qzxIPcum5GZUebbGc6Fu22sberXscxse2GWsiQPWnXRhCjCwWyj0JQ6+mHAGc1i728RIjJqkWq5f+H6XmAct7w5PJ9CuIDEmsT5bmrgEOsWijKJxsGVFKquhX4lqK9mYaJR8sonLSY67w+WaNQDS4XzlXKiArWrDSCtU5A1RfHnIyDChatVYx3avDDa+OJT5DPEjkp0wCIeJfizjBeXv172K4mSJa9dRvNFPZ3ww3dqDmCHcYkc0YfbdrDoZ0/2JpoykD7Oc/nDU9tX+fE8fUwZ1E5exxP1yv+FZQqioFscns2/Jh2mPCM2R59OwqGpU6gHKB26vtjY7aVY2QjGJiejwnbLwV5uqPYva+HTGBnEbF0zfpaUjh+NqPcz5sjdoxkGfZo+Cbig1mfcsvPEGr/kwRu06XM4wYPKPp9debEtWqg0Be165vAN76hgM7bGb2nGVHlmAen7bGRcxyxdn9bPJ2L00N3gCrDoyQL1DPyzkcFlVAaAMAHp9x1tnlD8jBmy48TtVMcssMluuxXUcsE4GyMynCZu4tCUPOJHYb1/kjh1ABnxiIqfRUdnq8RyBj4AEhtLOibk9NesmMeFdwjqjiWmxAPf0wh5TZ7gXqackPMO9cDlNWkkLCbyDpUNxgXughYGr3DPY284MAHtrN3T8JHfYpQRg71APDt1ayY+jOeC0icERsABgT54uAeodIgufbIB6UK7H2wSIClbMErDnynMz8s8KsAktFJCJ0LTXru6cOT6BetbRAaiX1asz1Cu1/lwiUK8PWYqNOQCoVyMGiLJLK0faVaHoS958aiZbmMCbRHJnwc7tdqqYbera7tjDpId2xZbIcZ/vdJm+LS+YoMAB0J2CCaR7GIrztjp1QdNLHeDSHmrhSeTfgbyBaCwICljXoEEWlMXiiHa0RjBgPnxOqzoXmFfyekmM1C5sA9AjmnDgxClj2/pcmd2xE0kD+cQZkey5i5EFE0Yl9ypfzuhOn+1PBL72csfGNh1O+gOhN82i9be0eCQWkE/W7+ULJrG7I7C+ReaEjWiNYdaeo3FrfPbXyYNTMEo2APi8hZGsujnK+1sAfHWFDyzVA8C3AkWY9/dz7xQvxfnAna3V3mHqh8RJ0q3B3oRG5RD3xKrcNwD4jDsltqIA8gXI06fH84RGMUR5N0D9XhEUPAgfUC6dr89oW5YkVks664woanqZsbsOowD2xgD3xFiwSqO2epPqPBNA1rG1pwDsBWewV7Wqjc6oDiW2L6jgyPYW+zu4p4LGIQ5R0hwfAF9wwCvsKwh7O/s0m/t3gG9hEMA3ZONAvgN8rMhsvGzezOXyyA/vekPgHlm2wGiwVWH+tJtHAPfiZcu03HTPWKxOl8ugkDlHPdZmAIiAqwcZPyNNzFzUp6XdoGNtm0NvuZgWF6g3BYWyKPojrTbPUG+qHnzQMB9Qr3225W9rS4424SkJYHoiUzlbrAW1wJSOzcYErKZIQn+Xe7qNPMo61G7CEW/sTvcFlvNlmKN7hlIWM/cEGikOcVAgZ7TQTk6acaF5YscCGxdqMnD3TRJblbvORzJm5wmUlm8OmY0Tx9+FImFzdZikqKmWNto1IS9OE83uQXsP8HaFZhojFBFOd6mm5Iq2Yavlbg6VoV7lpoEwlzbhlkIJpnr4MKC4FuodjtqAifHZ20qTeZRpbWK5TyN0QLS/DDYEPqupkLRBpbY3zvK0Jr7olLhSKh6ZKCKTOhyYlkG3aDQ4e9UlvjLb2hSZSuSaPFkSYqXK68LgdBdz4mWkOJbbBcSxfRoaUDHMgAMdtHMnAJEn4Ma07ILqLDG8XLcy5KR1oNpkSCmuB+97ddhG6KRLTkRg4c73xUkOVmSBibIn5i8SmS0p3sK2lRxz1BHpg8ayh22bpxCS+i1zIHEx0X+WbdUzdVbPk7rNt0qqqVwbJg9rYlnoyYabLrENlNgBLODkCb004vlU5CMRpQp7FMlmG7LM1ZrJjsyhHbAXUEvqyYCtgZdyVVMtYcLYA3sMyJ6sZ0j4UFJcp8bdsDcQG4L4mwytqyWwQhSLgTML60Gy6IxFNgov8HW/mFEwujsMytnlZNsK251i20dQgRAbEebpDSuLio6mxNYlDNdb2ahQaoEl+lLozA/WGXpsyMxwQZaSWW9oK9RksEoY62CNqX5yPGpa0m4tubYkoxAfm1qQQmJNZouE7phliz8uDC3xZ3tiGE7tQ2ZsCPQFo89LhHMG5QCh43AbFm2OwlTTCJaWmKFYUXjcsqoRIQu7PJlyytun8rRjnXIzt041TKWQKJfLbeVTOtOfHig546l+Rm9jYj44g0NeS2EJXygiyBBMX9iJOBG6RJ7wpr65XPndfz2FUK2O55S49CexPNqd13xh/jDmtf2qmyQJWQs/7314rV0CEvdwmwArPNkkILBPNwlc0/78JoGnRx2+c4y8SUv33vP5CPndiXL469sZcvLH8d4fj0+QfzuO/uX+YfRvZ9OfOY7+6luG5B/cMsS+646hp0dflDyMrHQbke1Anzti/5IdsYrM8Hc7lv6J4ysB8Oj2yqPveJCFlh6dz+CYG7u13nTL7Nfrl15VEf+i0n2XO0Cu+9e+v3lTfk9VfC34xl5n+nrl0E9veaZJnstphzCtHpyWOF+l852LRu62TN+VeL2A59tNPOq1eu7eiYr71bztRmpReEVRvwrs15NYT8T89fZR7+Z2yscwkl5znAv9VmSYyUe4yoymhYej8sZ3md0cl3c5kvU8hd7pLrObDfrwd5n9zDB+hLvMbrb3r7zL7JdG/uHZkre9y+xmg28cLflYsv/MBQg8/5RrOYm5YU6uGf/40AlPRupjXQP3kg28DwFv57tovvsQ8HZG7jbd/jgEvFm7+J+mAfejNODfkwY/7cf6q2jA/ygNhPekgfzLNHib6ejv0YD+URq8lWfwdu0/cCfG30OV7w/2Mwb7hwf78qlFfKL3Zo2PrhwUHh3Su+OWy0ffKPaknCd3Fz4u6K6DTwo6k/5rd36DG57ONttp2Tn4kIYCMgUvnnDHd32M93jlB92NN0+SPnKFUhTWWswt/+LpsI/+l6Tl4uD/f8SD+Myhz9cCfZL4kIRveOvV7da+q0D/qfWfj6Ou3+ro94sI9/MS4X9u7efz6rJfXfH5AzP399fiT91Nn7cI/31C/HmL8EeQ5re8ifDFCfqnNH9K86c0/6Y0c9dIS+8mzU/vY/rcXfUXivPn7qoPIc+3VsneVp5vROz4vOT/rxHjz0v+39Uav/dE+WmEjvN1k9x5Zxs8sMRV9pJHNN+XizzJN95a+5Z6ayvlNz/m15XAZ32aRentS0QCdxKWWXtFkQbX5Fa6/uZ7Da+ZLo5ZSLm8f7Aj86uj9s6hKn7INTDmh9eCn1kDu/DY/0EHOfbhUYiLjfjNhRv28c3r8qMinllwASJ5x3vZLur/2Xo49pE/SZQecfpdiY++vu63e9SoPI6L6HUWgN5qTfbDsN5zex/fxp9/3Yz5uts7f0Gt88JDcfu6w+v+Tlj2hmKnmdfS7Dd3Tv/AnuVHqyGS8GSh5OkiyE9tH7uND57c4/3rFvheYOVnL/1/Y1owT2hBphP+IV2Hn0D5LwHK/+B8945BX0MWvxr6B2rzusf7vdAwc2O9+EPYF1Z8OFAfwb48u6P7hXMzf7VZeQZ1vyMJnq6LwPwojb2AoGH/EKwusPfTvHx88/K5YeGPumGek9ar4Xl4ROvdDc9vHDSQxQfeDZj9U/Qv+Tf+kNPly2vPVW+cXH3+3Mu7TVU/41j/F7Ty55r1e3jJRY55qJ6ld96BwvzqRS5Xz/ZvX+TyyIP9MR3Yv7vZ97Y3WKQeRv/hKOlhEa+8zZ952cv7/QWQZ2jI/NYW8YfLKfRPc9ZrMwP7w1vE3/VEz3XGdmMae931LXP3YmTeYISzrv9y8wjyV33+bCxFUMTp6eIueGr7X7ws65d9DcwzV3p8Wz/i2KuM/arsvv7iy5XBPgOU/7vw7HPS/Iao7B0DlN9WAE9XVZ4I7vusBr5rgPLbY/V+nu07Mr3sN33jwbjhuv4t3/OvSxz7A07lNx6cG5vtPwOUfxrrj7PT8F8OUP6rSuRtDfPTHf6P51TfruS7LgRKT299+uuj5moFuQG5jxGJkKv2Zu14fr4r+Uci4yJUj7VmPCTvTblGZnP5KeGHPsJPc/fDX35Inu31Pfzwx3M+pSPIwjkgrx22uua4Rva4jdEl/F58EqXE0HG956SwbtMuyfg1qi8OSHRbV7M7vW5Foiacm7Vaa/ZkqJM4KuzEsjlsD1PEuZHrkhBqfmWbSzsdNGjS5hK6r+x1L+9o7XTbLW1T17lkvE9Rth0edNQ3elW8muO8Wkbz3ZzvdteWsTg6cncHlU3D0l5grsUMRj0t2RprX5xR2YgajbZzuiDxl+IRCbhGbizn56557NFboYmGtLMuvb3tB8oic/rkbm27K3cHc1fa7NrbOqg7UldG23B7XHUXdW53z5GYWtt8Ui1XTpZm4ZQid6yjyGZpP9iTUEc0uSq+2MWdnt9ZcpLJjzujYzGdDgN3elKP3a5bh9ZRzfSRLbjbg3Em82g8MYc9XpnpOmHo3xLxZ0DUV8T5AHBeJfsNgmPfbu1P39n0C3uEn7tj6KuX7b32Uv6o44v93Wu1brtLhcfB0uWv1+++kcOUfbqpHGzhFsxkkUNyGG3X+fHGBaz/zw9ZCY0nHhpZJQ8yPjt7OLLnRFa+aML5FfX//uDNq3+l9459xst+KZ76n3CdG3y9f/TLR3flPb1w/XzLr0a44YVrIv/wzb8y/XAb7P3K5MevLkX/XXcBy+d+76PggpzqqHjV0y7CQ7MkP7VLX+9sejC7pfjnufP3riH9gR3kD+aPPzXhAfAfptG3d7fvYjpnV5/Mgy6ZP5y/8tZtUDdnRYfiXNdrMRP9iJlo4cat8m8Kc66Ht352i/2vYaMPHBPkt/AS9wy0/U0Y9PgiXp57NN19ZRB03cLwecz97/Za/YPb/j/CMXeafjiL4WnuDYOI3JZo9mc19we66/P7apj6wWnrb+vr3yPCrWMHd1MFtt1BF5QvXWcCkkwWL34OqL8pJIe58ptBJubGoZE3ht83Tixst4TrIi/7BOJ/CRB/uA2AvrEP4G1h+I3lhstse0HfXKzMQMelm/8704gsU1Lb5suzy5Mw1b/18G0+Typ5ZjoPI/9oAeqW7siBRvH6vKN+kYZhdDbPD9afv7vS9ZXBXovogvzwhLNAPaW69KbW+OkG+76X+aH3iaz/EmT9j908s77jzlcU0Uf3qrPXcKhvAJjntSbkes6zqD1Qrf2s5AT2IwTjYqjrN+8TjOvmuDz1DDwZlz+//eh5Cr24ceCNh+bDB+P6mWH8CMG4brb3rwzG9Usj/9AH+LbBuG42+OkE6IPJ/ocIxnWzwR89GNdLNvC+++V2vhvBuG5nfKtgXDdr/+jBuH6PBjduwbqd8a2OFt+s/aMH4/o9GvA/SoO3CsZ1s/aPHozr92hwYwfTC0rrvWjw9wXjeokq3x/sZwz2vxeM60Wm/QzG9eug7x2Dcd1u7bsK9BsG43ojdf1WwbheRLifwbj+OU/v5zHdX9058Qdm7u+vxT+Dcf0XhPjzKqSPIM1veer+xQn6pzR/SvOnNP+mNL9lMK4XvGifu5T/dnH+3KX8IeT5LYNxvTDF/wzG9ZeK8T+2JepPBuP6E9b4vSfKn8G4Pswa2K2ISD+1BvYZjOuu/48a9UcOkd8mxAcKxvU2rPdmwbhu1/4Dy6+/v73zF9T6+wbjeoFS//FgXC9wyYtbTN+aFswTWnwG4/rbgPI/ON/9qWBcPyWL7x6M63Zr3+TK0l+wL+8bjOtFyPBfDMb1AnN8KLNy42jrZzCuv9S8fG5Y+KNumA8bjOt2a39jk/tfGIzr9+aqF0Px3bnqu+6YZ5461j7XrP8+rfy5Zv0eXvL3DMZ1W5p/2tH3lwbj+gOa+Y8fF7gRjOt/1L1/1xte3mjX/xXj/3OxuX6LN66RUz74AZ/nIjz9R2JzvcDQLy0nfbDYXC8y2Gdsrn8XrX3Ood8QpL1jbK7bCuCjOm/fNzbX7bH6gXBUrzRW7xp+6naLPs4FGc/FZ3jHwXm6W/czNtensf5AGw//5dhcv6pE3tYwP90g/HhO9Rmb6zM212dsrp8Q8ZdXt94vNtft1r58fdCr7CB+7gai74Xqeh/H6SXj95e0fnf75W0P66/H7/rZnb2Pa2JE+X5xT32/Mv1S/oc7gf+YK+lpbKg/GUxMIDHEMHd+EL7g/0zosBdk/1lN9edDh129lsLNDfCv74e8Fe7p34gW8AuukfeMFnCbek/9/5/RAt41WsCvcNX7RQu4zVQ3liY+owX8YaK/Z7SA21S/dfmUSJYPCSyQSFhI6baRf35bwWsD0asIfBeIcu96rxf31Aj+9QHtfo9ur7PzgnkMouRHwvLKWy2uOztel84f6EbO7xNa/FEB/V2O+D0Bfbpq8AmjXgtGBcSdDEP2kAdfAVY9nKgxwltGQ31RGu5fwJrnyTpq5fsbbPa3+4/JD9KRgoj/VqWigSM88M+iwoH/62u1hrJtPfth3zJ2mNF4iCedBIshPUxbLrsJ1IZtmPXEO81NJO1OuZGtxIDeV1ViJybdWvRoR9SqEYPUeh7KHsGZ0+0e8962Gm2PwizbRra46jt20l24gm6Nmt5G65bKIkw6xkigVMagDdxrbcar/W4y8ICVsCifmOHIO/W2p7I5nV3SOI4AR+FAHUjLljmORu2Lp1q6+038ueJqiH7tX60v95hSt8Ap2JqEQuMJkWCe4kOsMIaubqai5I7xkbh/xTomrYEn4GayGtaKYPhPRxstimWaDORQZhfsvlMkrWXeyUtTtjjC1UYVLwhtO4sO8UyTx0iGLrUqWkReo6O+ExS2WbE+pB0k/hBND0ttNRYpvBETg6XGyJy6egLTAJzUON85/XrJsFy9l1slWxlrRwyqsFByjHCBdOiN3o7XqW/WvURDnboXzlr7sOVzJ6kuK/60p0JqCVU5I18zkMwQf7kDcoT7lsx4FbxhSkS8Ni1pspRE6TQ+1RaXIZynXmBV8UZ1dvFpdoAv3H21XagOt0YoH6JOVOMWnjPhJk7dccVEqEPopG1HtcIWTY06J00dt7Q5GZQwFXt0tIioQa7I0wy1ZvN0hqxUR7316jxcUEGLxRNEhuo4QMpCQ7qNdkY/USWGT2tSpbpVZgiTlYiFSjzy3JG0xG5vl/VYKQVOkFsJqc7Ty8sCR0YIeDBFZGdzNEyTbQ2f3TFayOYNNvDAnEXzRJlm9irp2lqtm+OFxbM5ZFJGaLij0nMd6m6KkLcqCdfCq3GCy/kyQxOjRqqhpjTpu7aykVdK+TxpTzK0WqhIHRbCGNRaazO0Fpov1Vq7MBJN11a6vk70OT07nOaFWcSLnF0jzYtQe2kX9qBiQ6iIrEx0dFRTiWYMdM3WrfagOLphLIfIrvdaoOpaggbCjpWtxkUF9GapjHyED8fSWleiBgpAnnacmRI3BXQ/5+aJ7lNkAX0cKW1lnKF1skUpWmyBQ/CC11ZokczRDi0UmWkqu23oiw4agfZQnSXDWORL0R0JnapWc6rQDMuGOqxpGPqVkqnj1FbaWYJzux877HCCcNpD+WiM2iUiAmRDJfMUKe0V5Gl2HDIWGlYxhdEAxXquLIDrCduvAs1QbQ1phFKkzATSBVUa2mbRBZbPUoQ9Bzo30AKNauUK6uG5nuMpjAY6DQ1c6hxoucTQxspeAf1ZDzVIm5G0QhujS5qBJ0qAkJnbaGIDT6s7zW6N9RrqWujwTiPvHGQfIRPOcdNBNlqhxJrZkEaWx/LWOS0haUPkDE3QstSqTpOxraO8TwMHU6Cp1LKbdPMFqB+P9GEgQR/UBSiCvAea21AbEFtqCd+2U/i2X9uottAQeUeyWjhzYAwHQ2iLMejjPt6hBCcHfYUmK7JKWBO2C5CsQatzSoPxkEiRORQZDGAIjdYWHsMWlG6ARkIs6C/gM0cx0UmxFUSNxsq0C6UDgoERtLrweriVdA4bMP7IBKpADYQu0gLhmFRj2imwzQr6ddRsTUe6soLhPZJh6DdtO4NWHTVK6yrE0iCs2JAVJGhFBratzBVtQaqrTchLxoTTONACGkaGsoHylg7UVWB9aBg65FPioT0yetCUeiClaOVB+9TEz1OkjYAE/YWvZ4qmQDliPtbbGB51LHbtbpsYz5k60eouAiupFOskmc0SHRo5ttF40IO0epJ2EwVoiqwtxqieIxujikuRPtKhn8lGc9AKmB94sjWaKQtinnuoq+/UNUl1oNv9EXBVOx0Bj27J1xQQXKlJj+cYXhIuScbaEKU1ac+8C2mk9wmlkXyQ1gbum4+7kAbcp00U6J8H3Le2z+8cNBxDU1Totm2Qkc0VmtCgS9KGkJaStPY57ZxvhGyKgAKqNUtzrbNHjKLFbVFuCbq/7OKLVnTRkSgx8XQaWJtkKYygMp1xHV4KXJc7jsSlpMXToEsWMYlMrciTrmik113SRAUtEPlFaKsBKwOh7YT80keXVJTowH43Pj0XqGGFoJvzp08KvP/puUAEbKb819pSqnyitE+zUuWI/Tv0p9ngeOqOZlEFIocZfyDN2qegjN2miqvlJusQWz0vmgDebmjD5dN+KMkRse2bKKoVAIOqDcCY8WdLecj67aoIMmsh1iLtROwp5U3Xp9yDy1phSwTkNXb6J/64cQx3XlhI5btogHv8bE+QQNd0JM+NuL7kdW2nRxAQ40wIaKCOstkvaptezzEvmJ0mGRyIrZI3k6QFagdgIi4OZ1iB4yUBb367XU7miczI/Mx0N8vMHbhgh2JmaQQO2TIgmYcuBZzt+aidTYycjfcS4dKps56akWfiU9BBWkHKHu6roxVvysol6Kw1j6azIcEyp/BODwIhp7segQZxLB4r2TyeAHMyVIbB1ICV6c2DQbzizoZs0zMTkT6hqb1BvfTgGIcq3JNSve1okMxnSmKCeh6Mw90GtyrSo6a2xx0bgw3q5TuBgBaxs2V0OyzZwOhGQH0ajfHk0JCmtZfTce1LUqdGSa9uoUnkEmhSuaYYrCW7xSlJb8tYHRzTqt4IQQvdpe3b5bk3giz3yuy4DxkBMQmYqNAN0nbg6F1lfmU0SJOyUvTcRjPGXQPlvf0cxrnddy1X2FJ+rs6w3Zq4fZnRp5mXCfw1zXVEiW/3Q+NevrEbs5nMrPdS41ugItCe9GYeE/U3AHZJ2PlB2G0HiQZWKFd38aZzrIYpRQmyAUY8I2lzsnMjXPZDy83XuBdFXmgHICcA3/DErE61vBpUx+NybRJcnLBoroSgUyfHzV5SziB0V2p6zwgOfA1SNUDpfEJ2YDTkHrUWC0ho3TYJHlcPzM6FB9S14fP9Sog9qzLqTh+4tHWWKGTVLZEzl+FsSzh306lYjVGXHN+OD5aoH+keBqLiYyUozD6Wzuw9S+koPjQJ8kFNe73xcdaNB9FhPK2HOpHKRgyDEASwZcpEfzJulM0XhKxlSjtEil3LcEoAfDCcuQpU3/FTLMnukgBOa5wSXjXZmVwsg5XMnTpNbc5mrMg1g6zx4F2VTpEvS7tQNbbDQ3/ZeGfcPcRJWwP7m/RGlGRU/T2HMrX0EcC/D6jZXmzLOo8NZDN+K49VVm486nRghrqjypw7FefVUmf89ZLomwGZD0qBdJTj0yDrVwBb8Zbs+jEavwRuCSXxRKEJzCK8/rhkTr5GwKIhzmR5qTWSRbBzsBPJTC9gcS1GhDOP570+eeWGBy/1o7g9FvidOMC1EncBz23dfotogGX3GC9GZIIGOgwoz8cucxyVZMvTwd02k0zJVnvdspw6MtTyVLGFt0Il8P+0N7PWZDkK16A6WX8GXLY3Oge27OOjJAWCuPMNrEaSv0y9kMVyY6KW4gBCp0bbDdjpOKmEOtg1zanhTB9xaH0ulbQKnzWdXJLBUQn3dY+nnhUzfsmpCQAkhR7S1UmMIyIBW8g0nSfdMQMAaKUD3l74/cVe2oC6WfGN1IkOtnLUajyeCP56IlZ+fHJ7zQr3QeYZIsnqesrp5XXSjU1FQYIyAEA3WccuF89ks+mppXlaLPq1YmeJkuxKxvJ1yjsecLxJJGCEMdFIR9+SBZ6oxJWVooroFLMRuD6zm2fWMAGeO6dx8sEnSnZSdScHhnYK1m5TREfu/GJ0Uoaq7lx4lKTFK7oSPUnHu0UXtdSJaBVsznu8R9CNqU4UH/WU3Y4PD0OFOlEPUgU33OS8v1UepJ5lU3Izqjxb47lQt+21Db1a9rkND+wy1sQB6066MAUY2C0U+iT2LbTbYA5rd5sYiVGTVMv1C9/vEvOg5d3h6QTaFSTGJNZnSxOXQKdYlFE0DrasSGU19CtRbSUbE42STzZxOclxd7hcswZAOpyvnAsVsFVtGGmFirwhii8PGRkmVK0a69julcHGF4cynyF+RLITBuEw0Y9lvKD8/bpXUZwsce06ijf66YwPplt7EDOEW+yIJsy+m1UnY7o/0ZSR9GGW0x+O2r7an+/pY8qgbuIylrhf7jc8SwgV1eL4ZPYt+TDtEaE58nwaFlWNSj1A+cDt1dZmJ83KRigmMREdvlMW/mpTtWdRG5/O2CBu44Lpu7R05HBcrYc5X/YGzTjo0+xR0A2lJvOehTfe4DUfxqhdh8sZBkz+8fTai23JSrUhYM8rl3dgTx2DoT12Uzuu0iMLUM9vO+MiZvnirH42GbuX5gZPgFVHBqh36IeFHC6rKgCUAUCv73jrjPJnxIANN36nKmaZRWbLtbiOA9bJAJn5NGETl7bkAScS++2L3LEDyIBPTOQ0OipbPZ4j8BGQwFDaOTG3p2bdJCa8S1hnNDEtFuCeXthj6gz3IvWUhGe4Fy6nSStpIYF3sHQoLnAPtDBwlXsGe9uZAWBv7YaOn+QOu5QA7B3qwaFbK/lxNAecNjE4AhYA7MnTJUC9Q2Thkw1QD8r1eJsAUcGKWQL2XHluRv5ZATahhQIyEZr22tWdM8cnUM86OgD1snp1hnql1p9LBOr1ycbNjTkAqFcjBoiySytH2lWh6EvefGomW5jAm0RyZ8HO7XaqmG3q2u7Yw6SHdsWWyHGf73SZvi0vmKDAAdCdggmkexiK87Y6dUHTSx3g0h5q4Unk34G8gWgsCApY16BBFpTF4oh2tEYwYD58Tqs6F5hX8npJjNQubAPQI5pw4MQpY9v6XJndsRNJA/nEGZHsuYuRBRNGJfcqX87oTp/tTwS+9nLHxjYdTvoDoTfNovW3tHgkFpBP1u/lCyaxuyOwvkXmhI1ojWHWnqNxa3z218mDUzBKNgD4vIWRrLo5yvtbAHx1hQ8s1QPAtwJFmPf3c+8UL8X5wJ2t1d5h6ofESdKtwd6ERuUQ98Sq3DcA+Iw7JbaiAPIFyNOnx/OERjFEeTdA/V4RFDwIH1Auna/PaFuWJFZLOuuMKGp6mbG7DqMA9sYA98RYsEqjtnqT6jwTQNaxtacA7AVnsFe1qo3OqA4lti+o4Mj2Fvs7uKeCxiEOUdIcHwBfcMAr7CsIezv7NJv7d4BvYRDAN2TjQL4DfKzIbLxs3szl8sgP73pD4B5ZtsBosFVh/rSbRwD34mXLtNx0z1isTpfLoJA5Rz3WZgCIgKsHGT8jTcxc1Kel3aBjbZtDb7mYFheoNwWFsij6I602z1Bvqh580DAfUK99tuVva0uONuEpCWB6IlM5W6wFtcCUjs3GBKymSEJ/l3u6jTzKOtRuwhFv7E73BZbzZZije4ZSFjP3BBopDnFQIGe00E5OmnGheWLHAhsXajJw900SW5W7zkcyZucJlJZvDpmNE8ffhSJhc3WYpKipljbaNSEvThPN7kF7D/B2hWYaIxQRTneppuSKtmGr5W4OlaFe5aaBMJc24ZZCCaZ6+DCguBbqHY7agInx2dtKk3mUaW1iuU8jdEC0vww2BD6rqZC0QaW2N87ytCa+6JS4UioemSgikzocmJZBt2g0OHvVJb4y29oUmUrkmjxZEmKlyuvC4HQXc+JlpDiW2wXEsX0aGlAxzIADHbRzJwCRJ+DGtOyC6iwxvFy3MuSkdaDaZEgprgfve3XYRuikS05EYOHO98VJDlZkgYmyJ+YvEpktKd7CtpUcc9QR6YPGsodtm6cQkvotcyBxMdF/lm3VM3VWz5O6zbdKqqlcGyYPa2JZ6MmGmy6xDZTYASzg5Am9NOL5VOQjEaUKexRJeD+yzNWayY7MoR2wF1BL6smArYGXclVTLWHC2AN7DMierGdI+FBSXKfG3bA3EBuC+JsMraslsEIUi4EzC+tBsuiMRTYKL/B1v5hRMLo7DMrZ5WTbCtudYttHUIEQGxHm6Q0ri4qOpsTWJQzXW9moUGqBJfpS6MwP1hl6bMjMcEGWkllvaCvUZLBKGOtgjal+cjxqWtJuLbm2JKMQH5takEJiTWaLhO6YZYs/Lgwt8Wd7YhhO7UNmbAj0BaPPS4RzBuUAoeNwGxZtjsJU0wiWlpihWFF43LKqESELuzyZcsrbp/K0Y51yM7dONUylkCiXy23lUzrTnx4oOeOpfkZvY2I+OINDXkthCV8oIsgQTF/YiTgRukSe8Ka+uVz53X89hVCtjueUuPQnsTzandd8Yf4w5rX9qpskCVkLP2+GeKttA48ifH6N/npv04DAPt00cE3785sGnt7M9Rl/5i88P/oP3sf7M/Fn3mxXkPRoj6DA3dgW9KYHTrmn53euRzA4Iurq3QGeD3zoggw4DtN9FFyYtI6KtyTqdcfW9f6164h+72DGdQ/2HyfplVc+zE7pVzqJJz7aZEdfL3p+ox2316tKHhnHi8R8msZP0/hBTePlUOazW4I/jL0UhYe69dbppDe1ltf4cfdEvu9lfuh9ivtfIu7/WAi39R13vuJRMv7hlPUVr5+FP/c5OdL3zYiTjhqg0UiO/x8=&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <g/>
        <g>
            <rect x="0" y="0" width="860" height="735" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="261" y="12" width="569" height="311" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 567px; height: 1px; padding-top: 19px; margin-left: 263px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Gevanni
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="263" y="31" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Gevanni
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="262" y="355" width="568" height="369" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 566px; height: 1px; padding-top: 362px; margin-left: 264px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    アプリ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="264" y="374" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        アプリ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 323 191 L 323 236.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 323 241.88 L 319.5 234.88 L 323 236.63 L 326.5 234.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 298 141 L 348 141 L 348 191 L 298 191 Z" fill="#7aa116" stroke="none" pointer-events="all" style="fill: light-dark(rgb(122, 161, 22), rgb(97, 130, 11));"/>
            <path d="M 337.92 168.07 L 338.2 166.13 C 340.73 167.65 340.76 168.28 340.76 168.29 C 340.76 168.3 340.32 168.66 337.92 168.07 Z M 336.54 167.68 C 332.17 166.36 326.08 163.56 323.61 162.4 C 323.61 162.39 323.62 162.38 323.62 162.37 C 323.62 161.43 322.85 160.66 321.9 160.66 C 320.95 160.66 320.18 161.43 320.18 162.37 C 320.18 163.32 320.95 164.09 321.9 164.09 C 322.31 164.09 322.69 163.93 322.99 163.69 C 325.89 165.06 331.93 167.81 336.33 169.11 L 334.59 181.4 C 334.59 181.43 334.58 181.47 334.58 181.5 C 334.58 182.58 329.79 184.57 321.97 184.57 C 314.06 184.57 309.21 182.58 309.21 181.5 C 309.21 181.47 309.21 181.44 309.21 181.4 L 305.57 154.83 C 308.72 157 315.49 158.14 321.97 158.14 C 328.44 158.14 335.2 157 338.36 154.84 Z M 305.18 152.06 C 305.24 151.12 310.64 147.43 321.97 147.43 C 333.3 147.43 338.7 151.12 338.76 152.06 L 338.76 152.38 C 338.13 154.48 331.13 156.71 321.97 156.71 C 312.79 156.71 305.79 154.48 305.18 152.37 Z M 340.18 152.07 C 340.18 149.6 333.09 146 321.97 146 C 310.85 146 303.76 149.6 303.76 152.07 L 303.82 152.61 L 307.79 181.56 C 307.88 184.79 316.51 186 321.97 186 C 328.73 186 335.92 184.44 336.01 181.56 L 337.72 169.49 C 338.68 169.72 339.46 169.83 340.09 169.83 C 340.93 169.83 341.5 169.63 341.85 169.21 C 342.14 168.88 342.24 168.47 342.16 168.03 C 341.98 167.04 340.8 165.97 338.41 164.61 L 340.11 152.64 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 198px; margin-left: 323px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ソースバケット
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="323" y="210" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ソースバケット
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 298 243 L 348 243 L 348 293 L 298 293 Z" fill="#e7157b" stroke="none" pointer-events="all" style="fill: light-dark(rgb(231, 21, 123), rgb(255, 129, 217));"/>
            <path d="M 332.75 286.57 C 331.22 286.57 329.98 285.32 329.98 283.78 C 329.98 282.24 331.22 280.99 332.75 280.99 C 334.29 280.99 335.53 282.24 335.53 283.78 C 335.53 285.32 334.29 286.57 332.75 286.57 Z M 326.67 274.43 L 319.28 274.43 L 315.59 268 L 319.28 261.57 L 326.67 261.57 L 330.36 268 Z M 314.16 255.01 C 312.63 255.01 311.38 253.76 311.38 252.22 C 311.38 250.68 312.63 249.43 314.16 249.43 C 315.69 249.43 316.94 250.68 316.94 252.22 C 316.94 253.76 315.69 255.01 314.16 255.01 Z M 332.75 279.56 C 332.23 279.56 331.72 279.66 331.26 279.84 L 328.24 274.72 L 328.08 274.82 L 331.79 268.36 C 331.92 268.14 331.92 267.86 331.79 267.64 L 327.69 260.5 C 327.56 260.28 327.33 260.14 327.08 260.14 L 319.61 260.14 L 319.64 260.13 L 316.88 255.43 C 317.78 254.66 318.36 253.51 318.36 252.22 C 318.36 249.89 316.47 248 314.16 248 C 311.84 248 309.96 249.89 309.96 252.22 C 309.96 254.55 311.84 256.44 314.16 256.44 C 314.69 256.44 315.19 256.34 315.65 256.16 L 318.24 260.54 L 314.16 267.64 C 314.03 267.86 314.03 268.14 314.16 268.36 L 318.26 275.5 C 318.39 275.72 318.62 275.86 318.87 275.86 L 327.08 275.86 C 327.13 275.86 327.19 275.85 327.25 275.83 L 330.04 280.57 C 329.13 281.34 328.56 282.49 328.56 283.78 C 328.56 286.11 330.44 288 332.75 288 C 335.07 288 336.95 286.11 336.95 283.78 C 336.95 281.45 335.07 279.56 332.75 279.56 Z M 336.99 262.36 C 335.46 262.36 334.21 261.11 334.21 259.57 C 334.21 258.03 335.46 256.78 336.99 256.78 C 338.52 256.78 339.77 258.03 339.77 259.57 C 339.77 261.11 338.52 262.36 336.99 262.36 Z M 342.19 267.64 L 339.49 262.95 C 340.52 262.18 341.19 260.95 341.19 259.57 C 341.19 257.24 339.3 255.35 336.99 255.35 C 336.4 255.35 335.84 255.47 335.33 255.69 L 333.13 251.86 C 333 251.64 332.77 251.51 332.52 251.51 L 323.71 251.51 L 323.71 252.94 L 332.11 252.94 L 334.14 256.48 C 333.31 257.25 332.79 258.35 332.79 259.57 C 332.79 261.9 334.67 263.79 336.99 263.79 C 337.42 263.79 337.84 263.72 338.23 263.6 L 340.76 268 L 337.21 274.18 L 338.44 274.89 L 342.19 268.36 C 342.32 268.14 342.32 267.86 342.19 267.64 Z M 309.59 278.89 C 308.06 278.89 306.81 277.64 306.81 276.1 C 306.81 274.56 308.06 273.31 309.59 273.31 C 311.12 273.31 312.37 274.56 312.37 276.1 C 312.37 277.64 311.12 278.89 309.59 278.89 Z M 311.91 279.61 C 313.04 278.86 313.79 277.56 313.79 276.1 C 313.79 273.77 311.91 271.88 309.59 271.88 C 308.92 271.88 308.29 272.04 307.73 272.32 L 305.25 268 L 309.36 260.84 L 308.13 260.12 L 303.81 267.64 C 303.68 267.86 303.68 268.14 303.81 268.36 L 306.57 273.17 C 305.85 273.93 305.39 274.96 305.39 276.1 C 305.39 278.43 307.28 280.32 309.59 280.32 C 309.94 280.32 310.28 280.27 310.6 280.19 L 312.87 284.14 C 313 284.36 313.23 284.49 313.48 284.49 L 322.29 284.49 L 322.29 283.07 L 313.89 283.07 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 300px; margin-left: 323px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    EventBridge
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="323" y="312" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        EventBri...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 298 41 L 348 41 L 348 91 L 298 91 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
            <path d="M 321.8 63.98 C 321.45 64.18 321.24 64.55 321.24 64.96 L 321.24 84.4 L 306.53 76.18 L 306.53 57.06 L 323.04 47.49 L 337.71 54.76 Z M 339.41 54.74 C 339.41 54.34 339.2 53.96 338.81 53.74 L 323.59 46.2 C 323.25 46 322.81 46 322.47 46.2 L 305.69 55.92 C 305.34 56.12 305.13 56.5 305.13 56.9 L 305.13 76.34 C 305.13 76.74 305.34 77.12 305.7 77.32 L 320.95 85.85 C 321.12 85.95 321.32 86 321.51 86 C 321.71 86 321.9 85.95 322.08 85.85 C 322.42 85.65 322.64 85.27 322.64 84.87 L 322.64 65.12 L 338.85 55.72 C 339.2 55.52 339.41 55.15 339.41 54.74 Z M 339.45 76.22 L 326.14 84.36 L 326.14 77.07 L 333.08 72.59 L 333.13 72.28 C 333.14 72.2 333.15 72.2 333.15 71.82 L 333.17 63.12 L 339.47 59.47 Z M 340.31 58.02 C 339.96 57.81 339.53 57.81 339.18 58.02 L 332.47 61.9 C 332.26 62.02 331.77 62.29 331.77 62.84 L 331.74 71.78 L 325.35 75.91 C 324.97 76.14 324.74 76.5 324.74 76.88 L 324.74 84.81 C 324.74 85.21 324.95 85.57 325.31 85.78 C 325.5 85.89 325.71 85.94 325.91 85.94 C 326.12 85.94 326.32 85.89 326.51 85.78 L 340.29 77.35 C 340.64 77.15 340.85 76.78 340.85 76.38 L 340.87 58.99 C 340.87 58.59 340.66 58.22 340.31 58.02 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 98px; margin-left: 323px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ECR
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="323" y="110" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ECR
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 413 43 L 463 43 L 463 93 L 413 93 Z" fill="#7aa116" stroke="none" pointer-events="all" style="fill: light-dark(rgb(122, 161, 22), rgb(97, 130, 11));"/>
            <path d="M 452.92 70.07 L 453.2 68.14 C 455.73 69.65 455.76 70.28 455.76 70.29 C 455.76 70.3 455.32 70.66 452.92 70.07 Z M 451.54 69.68 C 447.17 68.36 441.08 65.56 438.61 64.4 C 438.61 64.39 438.62 64.38 438.62 64.37 C 438.62 63.42 437.85 62.66 436.9 62.66 C 435.95 62.66 435.18 63.42 435.18 64.37 C 435.18 65.32 435.95 66.09 436.9 66.09 C 437.31 66.09 437.69 65.93 437.99 65.69 C 440.89 67.06 446.93 69.81 451.33 71.11 L 449.59 83.4 C 449.59 83.43 449.58 83.47 449.58 83.5 C 449.58 84.58 444.79 86.57 436.97 86.57 C 429.06 86.57 424.21 84.58 424.21 83.5 C 424.21 83.47 424.21 83.44 424.21 83.4 L 420.57 56.83 C 423.72 59 430.49 60.14 436.97 60.14 C 443.44 60.14 450.2 59 453.36 56.84 Z M 420.18 54.06 C 420.24 53.12 425.64 49.43 436.97 49.43 C 448.3 49.43 453.7 53.12 453.76 54.06 L 453.76 54.38 C 453.13 56.48 446.13 58.71 436.97 58.71 C 427.79 58.71 420.79 56.48 420.18 54.37 Z M 455.18 54.07 C 455.18 51.6 448.09 48 436.97 48 C 425.85 48 418.76 51.6 418.76 54.07 L 418.82 54.61 L 422.79 83.56 C 422.88 86.79 431.51 88 436.97 88 C 443.73 88 450.92 86.44 451.01 83.56 L 452.72 71.49 C 453.68 71.72 454.46 71.83 455.09 71.83 C 455.93 71.83 456.5 71.63 456.85 71.21 C 457.14 70.88 457.24 70.47 457.16 70.03 C 456.98 69.04 455.8 67.97 453.41 66.61 L 455.11 54.64 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 100px; margin-left: 438px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    マスターバケット
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="438" y="112" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        マスターバケット
                    </text>
                </switch>
            </g>
        </g>
        <g/>
        <g/>
        <g>
            <rect x="551" y="105" width="156" height="122" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 551 105 L 601 105 L 601 155 L 551 155 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
            <path d="M 592.48 135.44 L 586.87 132.07 L 586.87 124.06 C 586.87 123.81 586.74 123.58 586.52 123.45 L 578.45 118.75 L 578.45 111.96 L 592.48 120.25 Z M 593.54 119.25 L 578.11 110.13 C 577.89 110 577.63 110 577.41 110.12 C 577.19 110.25 577.05 110.48 577.05 110.73 L 577.05 119.15 C 577.05 119.4 577.18 119.63 577.4 119.75 L 585.47 124.46 L 585.47 132.47 C 585.47 132.72 585.6 132.94 585.81 133.07 L 592.82 137.28 C 592.93 137.34 593.05 137.38 593.18 137.38 C 593.3 137.38 593.42 137.35 593.52 137.29 C 593.74 137.16 593.88 136.93 593.88 136.68 L 593.88 119.85 C 593.88 119.6 593.75 119.37 593.54 119.25 Z M 575.96 148.5 L 559.52 139.76 L 559.52 120.25 L 573.55 111.96 L 573.55 118.76 L 566.16 123.46 C 565.96 123.59 565.83 123.82 565.83 124.06 L 565.83 135.98 C 565.83 136.24 565.98 136.48 566.21 136.6 L 575.64 141.51 C 575.85 141.61 576.09 141.61 576.29 141.51 L 585.44 136.78 L 591.07 140.16 Z M 592.84 139.58 L 585.83 135.37 C 585.62 135.25 585.36 135.24 585.14 135.35 L 575.97 140.09 L 567.24 135.55 L 567.24 124.44 L 574.62 119.74 C 574.83 119.61 574.95 119.39 574.95 119.15 L 574.95 110.73 C 574.95 110.48 574.81 110.25 574.59 110.12 C 574.38 110 574.11 110 573.89 110.13 L 558.46 119.25 C 558.25 119.37 558.12 119.6 558.12 119.85 L 558.12 140.18 C 558.12 140.44 558.26 140.68 558.49 140.8 L 575.64 149.92 C 575.74 149.97 575.85 150 575.97 150 C 576.08 150 576.2 149.97 576.31 149.91 L 592.82 140.8 C 593.04 140.68 593.17 140.45 593.18 140.2 C 593.18 139.94 593.05 139.71 592.84 139.58 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 130px; margin-left: 603px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    ECS
                                    <span style="background-color: transparent;">
                                        クラスター
                                    </span>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="603" y="134" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">
                        ECSクラスター
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 92 420 L 241 420 L 241 514 L 186.5 514 L 230.57 544 L 166.5 514 L 92 514 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 147px; height: 1px; padding-top: 466px; margin-left: 93px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    ①アカウントを用意する
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="167" y="470" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ①アカウントを用意する
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 52.91 167.5 L 77.63 167.5" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 82.88 167.5 L 75.88 171 L 77.63 167.5 L 75.88 164 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 168px; margin-left: 68px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    申請
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="68" y="171" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        申請
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="9" y="144.5" width="46" height="46" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 13.21 188.41 C 13.73 178.04 21.96 169.78 32 169.78 C 35.4 169.78 38.72 170.73 41.62 172.54 C 47.01 175.9 50.46 181.91 50.79 188.41 Z M 21.75 156.92 C 21.75 151.22 26.35 146.59 32 146.59 C 37.65 146.59 42.25 151.22 42.25 156.92 C 42.25 162.61 37.65 167.25 32 167.25 C 26.35 167.25 21.75 162.61 21.75 156.92 Z M 42.73 170.77 C 40.92 169.64 38.95 168.82 36.91 168.31 C 41.28 166.39 44.34 162.01 44.34 156.92 C 44.34 150.07 38.81 144.5 32 144.5 C 25.19 144.5 19.66 150.07 19.66 156.92 C 19.66 162.02 22.73 166.4 27.1 168.31 C 17.93 170.62 11.09 179.21 11.09 189.45 C 11.09 190.03 11.56 190.5 12.14 190.5 L 51.86 190.5 C 52.44 190.5 52.91 190.03 52.91 189.45 C 52.91 181.84 49.01 174.68 42.73 170.77 Z" fill="#232f3d" stroke="none" pointer-events="all" style="fill: light-dark(rgb(35, 47, 61), rgb(190, 200, 212));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 198px; margin-left: 32px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    app team
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="32" y="210" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        app team
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <image x="83.5" y="135.5" width="63" height="63" xlink:href="data:image/png;base64,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" preserveAspectRatio="none"/>
        </g>
        <g>
            <g fill="#000000" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));">
                <rect fill="#ffffff" stroke="none" x="82" y="207" width="69" height="15" stroke-width="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/>
                <text x="115" y="216.5">
                    GoogleForm
                </text>
            </g>
        </g>
        <g>
            <path d="M 231 167.5 L 254.63 167.5" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 259.88 167.5 L 252.88 171 L 254.63 167.5 L 252.88 164 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 181 142.5 L 231 142.5 L 231 192.5 L 181 192.5 Z" fill="#c925d1" stroke="none" pointer-events="all" style="fill: light-dark(rgb(201, 37, 209), rgb(255, 124, 255));"/>
            <path d="M 222.03 147.5 L 189.97 147.5 C 187.78 147.5 186 149.28 186 151.47 L 186 160.98 C 186 163.17 187.78 164.95 189.97 164.95 L 191.09 164.95 L 191.09 186.77 C 191.09 187.17 191.42 187.5 191.82 187.5 L 219.45 187.5 C 219.86 187.5 220.18 187.17 220.18 186.77 L 220.18 164.95 L 222.03 164.95 C 224.22 164.95 226 163.17 226 160.98 L 226 151.47 C 226 149.28 224.22 147.5 222.03 147.5 Z M 192.55 186.05 L 192.55 164.95 L 218.73 164.95 L 218.73 186.05 Z M 222.03 163.5 L 189.97 163.5 C 188.59 163.5 187.45 162.37 187.45 160.98 L 187.45 160.59 L 196.18 160.59 L 196.18 159.14 L 187.45 159.14 L 187.45 151.47 C 187.45 150.08 188.59 148.95 189.97 148.95 L 222.03 148.95 C 223.41 148.95 224.55 150.08 224.55 151.47 L 224.55 159.14 L 204.18 159.14 L 204.18 160.59 L 224.55 160.59 L 224.55 160.98 C 224.55 162.37 223.41 163.5 222.03 163.5 Z M 195.1 175.17 C 195.1 174.96 195.19 174.76 195.34 174.62 L 199.95 170.55 L 200.91 171.64 L 196.93 175.16 L 200.89 178.58 L 199.94 179.68 L 195.35 175.72 C 195.19 175.58 195.1 175.38 195.1 175.17 Z M 209.69 178.61 L 213.69 175.11 L 209.69 171.64 L 210.64 170.54 L 215.27 174.55 C 215.43 174.69 215.52 174.89 215.52 175.1 C 215.52 175.31 215.43 175.51 215.27 175.65 L 210.65 179.71 Z M 203.21 182.53 L 201.87 181.98 L 207.39 168.51 L 208.73 169.06 Z M 198.36 160.59 L 202 160.59 L 202 159.14 L 198.36 159.14 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 200px; margin-left: 206px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    CodePipeline
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="206" y="212" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        CodePipe...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 147 167.5 L 174.63 167.5" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 179.88 167.5 L 172.88 171 L 174.63 167.5 L 172.88 164 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 50 10 L 199 10 L 199 104 L 144.5 104 L 163.24 134 L 124.5 104 L 50 104 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 147px; height: 1px; padding-top: 56px; margin-left: 51px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    ②Gevanniアカウントに
                                    <div>
                                        リソースをデプロイ
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="125" y="60" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ②Gevanniアカウントに...
                    </text>
                </switch>
            </g>
        </g>
        <g/>
        <g>
            <rect x="858" y="0" width="860" height="735" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="1119" y="12" width="569" height="311" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 567px; height: 1px; padding-top: 19px; margin-left: 1121px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Gevanni
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1121" y="31" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Gevanni
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1120" y="355" width="568" height="369" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 566px; height: 1px; padding-top: 362px; margin-left: 1122px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    アプリ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1122" y="374" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        アプリ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1156" y="552" width="482" height="152" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1181 191 L 1181 236.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1181 241.88 L 1177.5 234.88 L 1181 236.63 L 1184.5 234.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1181 293 L 1181 395.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1181 400.88 L 1177.5 393.88 L 1181 395.63 L 1184.5 393.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1181 452 L 1181 545.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1181 550.88 L 1177.5 543.88 L 1181 545.63 L 1184.5 543.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 995 166 L 1149.63 166" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1154.88 166 L 1147.88 169.5 L 1149.63 166 L 1147.88 162.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 916 166 L 938.63 166" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 943.88 166 L 936.88 169.5 L 938.63 166 L 936.88 162.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="945" y="141" width="50" height="50" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 945 166 C 945 152.19 956.19 141 970 141 C 983.81 141 995 152.19 995 166 C 995 179.81 983.81 191 970 191 C 956.19 191 945 179.81 945 166 Z M 947.4 166 C 947.34 176.01 953.9 184.85 963.5 187.7 L 963.5 183.35 C 963.59 181.79 964.41 180.37 965.7 179.5 C 961.9 179.08 958.47 177.4 956.22 174.85 C 953.98 172.3 953.1 169.1 953.8 166 C 954.16 163.85 955.07 161.84 956.45 160.15 C 955.6 157.98 955.67 155.56 956.65 153.45 C 959.18 153.55 961.6 154.52 963.5 156.2 C 967.71 154.74 972.28 154.72 976.5 156.15 C 978.43 154.47 980.89 153.51 983.45 153.45 C 984.44 155.65 984.52 158.15 983.65 160.4 C 984.95 162.03 985.83 163.95 986.2 166 C 986.9 169.09 986.03 172.28 983.79 174.83 C 981.55 177.38 978.14 179.07 974.35 179.5 C 975.57 180.28 976.39 181.56 976.6 183 L 976.6 187.8 C 986.21 184.92 992.76 176.03 992.65 166 C 992.65 160.01 990.26 154.26 986.01 150.04 C 981.75 145.81 975.99 143.46 970 143.5 C 964.02 143.47 958.27 145.83 954.03 150.05 C 949.78 154.28 947.4 160.02 947.4 166 Z" fill="#00bef2" stroke="none" pointer-events="all" style="fill: light-dark(rgb(0, 190, 242), rgb(0, 137, 182));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 198px; margin-left: 970px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    GitHub Actions
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="970" y="210" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        GitHub A...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 995 166 L 1150.59 69.36" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1155.05 66.59 L 1150.95 73.26 L 1150.59 69.36 L 1147.26 67.31 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1156 141 L 1206 141 L 1206 191 L 1156 191 Z" fill="#7aa116" stroke="none" pointer-events="all" style="fill: light-dark(rgb(122, 161, 22), rgb(97, 130, 11));"/>
            <path d="M 1195.92 168.07 L 1196.2 166.13 C 1198.73 167.65 1198.76 168.28 1198.76 168.29 C 1198.76 168.3 1198.32 168.66 1195.92 168.07 Z M 1194.54 167.68 C 1190.16 166.36 1184.08 163.56 1181.61 162.4 C 1181.61 162.39 1181.62 162.38 1181.62 162.37 C 1181.62 161.43 1180.85 160.66 1179.9 160.66 C 1178.95 160.66 1178.18 161.43 1178.18 162.37 C 1178.18 163.32 1178.95 164.09 1179.9 164.09 C 1180.32 164.09 1180.69 163.93 1180.99 163.69 C 1183.89 165.06 1189.93 167.81 1194.33 169.11 L 1192.59 181.4 C 1192.59 181.43 1192.58 181.47 1192.58 181.5 C 1192.58 182.58 1187.79 184.57 1179.97 184.57 C 1172.06 184.57 1167.21 182.58 1167.21 181.5 C 1167.21 181.47 1167.21 181.44 1167.21 181.4 L 1163.57 154.83 C 1166.72 157 1173.49 158.14 1179.97 158.14 C 1186.44 158.14 1193.2 157 1196.36 154.84 Z M 1163.18 152.06 C 1163.24 151.12 1168.64 147.43 1179.97 147.43 C 1191.3 147.43 1196.7 151.12 1196.76 152.06 L 1196.76 152.38 C 1196.13 154.48 1189.13 156.71 1179.97 156.71 C 1170.79 156.71 1163.79 154.48 1163.18 152.37 Z M 1198.18 152.07 C 1198.18 149.6 1191.09 146 1179.97 146 C 1168.85 146 1161.76 149.6 1161.76 152.07 L 1161.82 152.61 L 1165.79 181.56 C 1165.88 184.79 1174.51 186 1179.97 186 C 1186.73 186 1193.92 184.44 1194.01 181.56 L 1195.72 169.49 C 1196.68 169.72 1197.46 169.83 1198.09 169.83 C 1198.93 169.83 1199.51 169.63 1199.85 169.21 C 1200.14 168.88 1200.24 168.47 1200.16 168.03 C 1199.98 167.04 1198.8 165.97 1196.41 164.61 L 1198.11 152.64 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 198px; margin-left: 1181px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ソースバケット
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1181" y="210" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ソースバケット
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1156 243 L 1206 243 L 1206 293 L 1156 293 Z" fill="#e7157b" stroke="none" pointer-events="all" style="fill: light-dark(rgb(231, 21, 123), rgb(255, 129, 217));"/>
            <path d="M 1190.75 286.57 C 1189.22 286.57 1187.98 285.32 1187.98 283.78 C 1187.98 282.24 1189.22 280.99 1190.75 280.99 C 1192.29 280.99 1193.53 282.24 1193.53 283.78 C 1193.53 285.32 1192.29 286.57 1190.75 286.57 Z M 1184.67 274.43 L 1177.28 274.43 L 1173.59 268 L 1177.28 261.57 L 1184.67 261.57 L 1188.36 268 Z M 1172.16 255.01 C 1170.63 255.01 1169.38 253.76 1169.38 252.22 C 1169.38 250.68 1170.63 249.43 1172.16 249.43 C 1173.69 249.43 1174.94 250.68 1174.94 252.22 C 1174.94 253.76 1173.69 255.01 1172.16 255.01 Z M 1190.75 279.56 C 1190.23 279.56 1189.72 279.66 1189.26 279.84 L 1186.24 274.72 L 1186.08 274.82 L 1189.79 268.36 C 1189.92 268.14 1189.92 267.86 1189.79 267.64 L 1185.69 260.5 C 1185.56 260.28 1185.33 260.14 1185.08 260.14 L 1177.61 260.14 L 1177.64 260.13 L 1174.88 255.43 C 1175.78 254.66 1176.36 253.51 1176.36 252.22 C 1176.36 249.89 1174.47 248 1172.16 248 C 1169.84 248 1167.96 249.89 1167.96 252.22 C 1167.96 254.55 1169.84 256.44 1172.16 256.44 C 1172.69 256.44 1173.19 256.34 1173.65 256.16 L 1176.24 260.54 L 1172.16 267.64 C 1172.03 267.86 1172.03 268.14 1172.16 268.36 L 1176.26 275.5 C 1176.39 275.72 1176.62 275.86 1176.87 275.86 L 1185.08 275.86 C 1185.13 275.86 1185.19 275.85 1185.25 275.83 L 1188.04 280.57 C 1187.13 281.34 1186.56 282.49 1186.56 283.78 C 1186.56 286.11 1188.44 288 1190.75 288 C 1193.07 288 1194.95 286.11 1194.95 283.78 C 1194.95 281.45 1193.07 279.56 1190.75 279.56 Z M 1194.99 262.36 C 1193.46 262.36 1192.21 261.11 1192.21 259.57 C 1192.21 258.03 1193.46 256.78 1194.99 256.78 C 1196.52 256.78 1197.77 258.03 1197.77 259.57 C 1197.77 261.11 1196.52 262.36 1194.99 262.36 Z M 1200.19 267.64 L 1197.49 262.95 C 1198.52 262.18 1199.19 260.95 1199.19 259.57 C 1199.19 257.24 1197.3 255.35 1194.99 255.35 C 1194.4 255.35 1193.84 255.47 1193.33 255.69 L 1191.13 251.86 C 1191 251.64 1190.77 251.51 1190.52 251.51 L 1181.71 251.51 L 1181.71 252.94 L 1190.11 252.94 L 1192.14 256.48 C 1191.31 257.25 1190.79 258.35 1190.79 259.57 C 1190.79 261.9 1192.67 263.79 1194.99 263.79 C 1195.42 263.79 1195.84 263.72 1196.23 263.6 L 1198.76 268 L 1195.21 274.18 L 1196.44 274.89 L 1200.19 268.36 C 1200.32 268.14 1200.32 267.86 1200.19 267.64 Z M 1167.59 278.89 C 1166.06 278.89 1164.81 277.64 1164.81 276.1 C 1164.81 274.56 1166.06 273.31 1167.59 273.31 C 1169.12 273.31 1170.37 274.56 1170.37 276.1 C 1170.37 277.64 1169.12 278.89 1167.59 278.89 Z M 1169.91 279.61 C 1171.04 278.86 1171.79 277.56 1171.79 276.1 C 1171.79 273.77 1169.91 271.88 1167.59 271.88 C 1166.92 271.88 1166.29 272.04 1165.73 272.32 L 1163.25 268 L 1167.36 260.84 L 1166.13 260.12 L 1161.81 267.64 C 1161.68 267.86 1161.68 268.14 1161.81 268.36 L 1164.57 273.17 C 1163.85 273.93 1163.39 274.96 1163.39 276.1 C 1163.39 278.43 1165.28 280.32 1167.59 280.32 C 1167.94 280.32 1168.28 280.27 1168.6 280.19 L 1170.87 284.14 C 1171 284.36 1171.23 284.49 1171.48 284.49 L 1180.29 284.49 L 1180.29 283.07 L 1171.89 283.07 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 300px; margin-left: 1181px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    EventBridge
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1181" y="312" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        EventBri...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1156 402 L 1206 402 L 1206 452 L 1156 452 Z" fill="#e7157b" stroke="none" pointer-events="all" style="fill: light-dark(rgb(231, 21, 123), rgb(255, 129, 217));"/>
            <path d="M 1190.75 445.57 C 1189.22 445.57 1187.98 444.32 1187.98 442.78 C 1187.98 441.24 1189.22 439.99 1190.75 439.99 C 1192.29 439.99 1193.53 441.24 1193.53 442.78 C 1193.53 444.32 1192.29 445.57 1190.75 445.57 Z M 1184.67 433.43 L 1177.28 433.43 L 1173.59 427 L 1177.28 420.57 L 1184.67 420.57 L 1188.36 427 Z M 1172.16 414.01 C 1170.63 414.01 1169.38 412.76 1169.38 411.22 C 1169.38 409.68 1170.63 408.43 1172.16 408.43 C 1173.69 408.43 1174.94 409.68 1174.94 411.22 C 1174.94 412.76 1173.69 414.01 1172.16 414.01 Z M 1190.75 438.56 C 1190.23 438.56 1189.72 438.66 1189.26 438.84 L 1186.24 433.72 L 1186.08 433.82 L 1189.79 427.36 C 1189.92 427.14 1189.92 426.86 1189.79 426.64 L 1185.69 419.5 C 1185.56 419.28 1185.33 419.14 1185.08 419.14 L 1177.61 419.14 L 1177.64 419.13 L 1174.88 414.43 C 1175.78 413.66 1176.36 412.51 1176.36 411.22 C 1176.36 408.89 1174.47 407 1172.16 407 C 1169.84 407 1167.96 408.89 1167.96 411.22 C 1167.96 413.55 1169.84 415.44 1172.16 415.44 C 1172.69 415.44 1173.19 415.34 1173.65 415.16 L 1176.24 419.54 L 1172.16 426.64 C 1172.03 426.86 1172.03 427.14 1172.16 427.36 L 1176.26 434.5 C 1176.39 434.72 1176.62 434.86 1176.87 434.86 L 1185.08 434.86 C 1185.13 434.86 1185.19 434.85 1185.25 434.83 L 1188.04 439.57 C 1187.13 440.34 1186.56 441.49 1186.56 442.78 C 1186.56 445.11 1188.44 447 1190.75 447 C 1193.07 447 1194.95 445.11 1194.95 442.78 C 1194.95 440.45 1193.07 438.56 1190.75 438.56 Z M 1194.99 421.36 C 1193.46 421.36 1192.21 420.11 1192.21 418.57 C 1192.21 417.03 1193.46 415.78 1194.99 415.78 C 1196.52 415.78 1197.77 417.03 1197.77 418.57 C 1197.77 420.11 1196.52 421.36 1194.99 421.36 Z M 1200.19 426.64 L 1197.49 421.95 C 1198.52 421.18 1199.19 419.95 1199.19 418.57 C 1199.19 416.24 1197.3 414.35 1194.99 414.35 C 1194.4 414.35 1193.84 414.47 1193.33 414.69 L 1191.13 410.86 C 1191 410.64 1190.77 410.51 1190.52 410.51 L 1181.71 410.51 L 1181.71 411.94 L 1190.11 411.94 L 1192.14 415.48 C 1191.31 416.25 1190.79 417.35 1190.79 418.57 C 1190.79 420.89 1192.67 422.79 1194.99 422.79 C 1195.42 422.79 1195.84 422.72 1196.23 422.6 L 1198.76 427 L 1195.21 433.18 L 1196.44 433.89 L 1200.19 427.36 C 1200.32 427.14 1200.32 426.86 1200.19 426.64 Z M 1167.59 437.89 C 1166.06 437.89 1164.81 436.64 1164.81 435.1 C 1164.81 433.56 1166.06 432.31 1167.59 432.31 C 1169.12 432.31 1170.37 433.56 1170.37 435.1 C 1170.37 436.64 1169.12 437.89 1167.59 437.89 Z M 1169.91 438.61 C 1171.04 437.86 1171.79 436.56 1171.79 435.1 C 1171.79 432.77 1169.91 430.88 1167.59 430.88 C 1166.92 430.88 1166.29 431.04 1165.73 431.32 L 1163.25 427 L 1167.36 419.84 L 1166.13 419.12 L 1161.81 426.64 C 1161.68 426.86 1161.68 427.14 1161.81 427.36 L 1164.57 432.17 C 1163.85 432.93 1163.39 433.96 1163.39 435.1 C 1163.39 437.43 1165.28 439.32 1167.59 439.32 C 1167.94 439.32 1168.28 439.27 1168.6 439.19 L 1170.87 443.14 C 1171 443.36 1171.23 443.49 1171.48 443.49 L 1180.29 443.49 L 1180.29 442.06 L 1171.89 442.06 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 459px; margin-left: 1181px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    EventBridge
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1181" y="471" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        EventBri...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1156 552 L 1206 552 L 1206 602 L 1156 602 Z" fill="#c925d1" stroke="none" pointer-events="all" style="fill: light-dark(rgb(201, 37, 209), rgb(255, 124, 255));"/>
            <path d="M 1197.03 557 L 1164.97 557 C 1162.78 557 1161 558.78 1161 560.97 L 1161 570.48 C 1161 572.67 1162.78 574.45 1164.97 574.45 L 1166.09 574.45 L 1166.09 596.27 C 1166.09 596.67 1166.42 597 1166.82 597 L 1194.45 597 C 1194.86 597 1195.18 596.67 1195.18 596.27 L 1195.18 574.45 L 1197.03 574.45 C 1199.22 574.45 1201 572.67 1201 570.48 L 1201 560.97 C 1201 558.78 1199.22 557 1197.03 557 Z M 1167.55 595.55 L 1167.55 574.45 L 1193.73 574.45 L 1193.73 595.55 Z M 1197.03 573 L 1164.97 573 C 1163.59 573 1162.45 571.87 1162.45 570.48 L 1162.45 570.09 L 1171.18 570.09 L 1171.18 568.64 L 1162.45 568.64 L 1162.45 560.97 C 1162.45 559.58 1163.59 558.45 1164.97 558.45 L 1197.03 558.45 C 1198.41 558.45 1199.55 559.58 1199.55 560.97 L 1199.55 568.64 L 1179.18 568.64 L 1179.18 570.09 L 1199.55 570.09 L 1199.55 570.48 C 1199.55 571.87 1198.41 573 1197.03 573 Z M 1170.1 584.67 C 1170.1 584.46 1170.19 584.26 1170.34 584.12 L 1174.95 580.05 L 1175.91 581.14 L 1171.93 584.66 L 1175.89 588.08 L 1174.94 589.18 L 1170.35 585.22 C 1170.19 585.08 1170.1 584.88 1170.1 584.67 Z M 1184.69 588.11 L 1188.69 584.61 L 1184.69 581.14 L 1185.64 580.04 L 1190.27 584.05 C 1190.43 584.19 1190.52 584.39 1190.52 584.6 C 1190.52 584.81 1190.43 585.01 1190.27 585.15 L 1185.65 589.21 Z M 1178.21 592.03 L 1176.87 591.48 L 1182.39 578.01 L 1183.73 578.56 Z M 1173.36 570.09 L 1177 570.09 L 1177 568.64 L 1173.36 568.64 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 609px; margin-left: 1181px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    CodePipeline
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1181" y="621" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        CodePipe...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1156 41 L 1206 41 L 1206 91 L 1156 91 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
            <path d="M 1179.8 63.98 C 1179.45 64.18 1179.24 64.55 1179.24 64.96 L 1179.24 84.4 L 1164.53 76.18 L 1164.53 57.06 L 1181.04 47.49 L 1195.71 54.76 Z M 1197.41 54.74 C 1197.41 54.34 1197.2 53.96 1196.81 53.74 L 1181.59 46.2 C 1181.25 46 1180.81 46 1180.47 46.2 L 1163.69 55.92 C 1163.34 56.12 1163.13 56.5 1163.13 56.9 L 1163.13 76.34 C 1163.13 76.74 1163.34 77.12 1163.7 77.32 L 1178.95 85.85 C 1179.12 85.95 1179.32 86 1179.51 86 C 1179.71 86 1179.9 85.95 1180.08 85.85 C 1180.42 85.65 1180.64 85.27 1180.64 84.87 L 1180.64 65.12 L 1196.85 55.72 C 1197.2 55.52 1197.41 55.15 1197.41 54.74 Z M 1197.45 76.22 L 1184.14 84.36 L 1184.14 77.07 L 1191.08 72.59 L 1191.13 72.28 C 1191.14 72.2 1191.15 72.2 1191.15 71.82 L 1191.17 63.12 L 1197.47 59.47 Z M 1198.31 58.02 C 1197.96 57.81 1197.53 57.81 1197.18 58.02 L 1190.47 61.9 C 1190.26 62.02 1189.77 62.29 1189.77 62.84 L 1189.74 71.78 L 1183.35 75.91 C 1182.97 76.14 1182.74 76.5 1182.74 76.88 L 1182.74 84.81 C 1182.74 85.21 1182.95 85.57 1183.31 85.78 C 1183.5 85.89 1183.71 85.94 1183.91 85.94 C 1184.12 85.94 1184.32 85.89 1184.51 85.78 L 1198.29 77.35 C 1198.64 77.15 1198.85 76.78 1198.85 76.38 L 1198.87 58.99 C 1198.87 58.59 1198.66 58.22 1198.31 58.02 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 98px; margin-left: 1181px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ECR
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1181" y="110" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ECR
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1296 561.63 L 1296 188.44 Q 1296 178.44 1286 178.45 L 1206 178.5" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1296 566.88 L 1292.5 559.88 L 1296 561.63 L 1299.5 559.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 505px; margin-left: 1294px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    複製
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1294" y="508" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        複製
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1361 628 L 1415.63 628" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1420.88 628 L 1413.88 631.5 L 1415.63 628 L 1413.88 624.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g/>
        <g>
            <rect x="1422" y="568" width="130" height="120" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 128px; height: 1px; padding-top: 575px; margin-left: 1423px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    デプロイステージ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1487" y="587" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        デプロイステージ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1465 603 L 1515 603 L 1515 653 L 1465 653 Z" fill="#c925d1" stroke="none" pointer-events="all" style="fill: light-dark(rgb(201, 37, 209), rgb(255, 124, 255));"/>
            <path d="M 1507.49 615.6 L 1503.39 611.85 L 1499.29 615.6 Z M 1501.82 611.43 L 1493.5 611.44 L 1497.94 614.97 Z M 1498.07 623.52 L 1491.52 625.93 L 1504.11 625.93 Z M 1488.46 639.71 L 1506.91 639.71 L 1506.91 627.31 L 1488.46 627.31 Z M 1496.53 615.6 L 1491.99 612 L 1487.49 615.6 Z M 1486.05 614.99 L 1490.49 611.45 L 1481.65 611.46 Z M 1484.62 615.6 L 1480.09 611.97 L 1475.56 615.6 L 1482.3 615.6 Z M 1481.62 616.98 L 1476.09 616.98 L 1481.62 622.47 Z M 1481.62 624.61 L 1475.13 630.52 L 1481.62 636.88 Z M 1481.62 639.11 L 1474.79 645.8 L 1474.79 645.91 L 1481.62 645.91 Z M 1474.79 632.11 L 1474.79 643.88 L 1480.8 638 Z M 1474.79 628.98 L 1480.76 623.54 L 1474.79 617.62 Z M 1474.1 615.02 L 1478.53 611.46 L 1474.1 611.47 Z M 1472.73 610.09 L 1471.37 610.09 L 1471.37 616.98 L 1472.73 616.98 L 1472.73 616.29 L 1472.73 610.78 Z M 1509.9 616.54 C 1509.8 616.81 1509.54 616.98 1509.26 616.98 L 1498.71 616.98 L 1498.71 622.49 L 1507.85 625.98 L 1507.85 625.99 C 1508.1 626.09 1508.28 626.33 1508.28 626.62 L 1508.28 640.4 C 1508.28 640.78 1507.97 641.09 1507.6 641.09 L 1487.77 641.09 C 1487.4 641.09 1487.09 640.78 1487.09 640.4 L 1487.09 626.62 C 1487.09 626.33 1487.27 626.08 1487.53 625.98 L 1487.53 625.98 L 1497.34 622.49 L 1497.34 616.98 L 1482.99 616.98 L 1482.99 646.59 C 1482.99 646.97 1482.68 647.28 1482.3 647.28 L 1474.1 647.28 C 1473.72 647.28 1473.42 646.97 1473.42 646.59 L 1473.42 618.36 L 1470.68 618.36 C 1470.31 618.36 1470 618.05 1470 617.67 L 1470 609.41 C 1470 609.03 1470.31 608.72 1470.68 608.72 L 1473.42 608.72 C 1473.8 608.72 1474.1 609.03 1474.1 609.41 L 1474.1 610.09 L 1503.49 610.09 L 1509.72 615.78 C 1509.93 615.97 1510 616.27 1509.9 616.54 Z M 1495.72 638.54 L 1499.69 629.43 L 1498.44 628.88 L 1494.46 637.99 Z M 1499.45 635.5 L 1500.34 636.55 L 1503.29 634.03 C 1503.43 633.91 1503.52 633.73 1503.53 633.54 C 1503.54 633.34 1503.47 633.16 1503.33 633.02 L 1500.87 630.5 L 1499.9 631.47 L 1501.84 633.46 Z M 1490.57 633.5 C 1490.42 633.36 1490.34 633.16 1490.35 632.96 C 1490.36 632.75 1490.46 632.56 1490.63 632.44 L 1493.98 629.94 L 1494.8 631.04 L 1492.11 633.05 L 1494.36 635.13 L 1493.44 636.15 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 660px; margin-left: 1490px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    codebuild
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1490" y="672" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        codebuild
                    </text>
                </switch>
            </g>
        </g>
        <g/>
        <g>
            <rect x="1231" y="568" width="130" height="120" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 128px; height: 1px; padding-top: 575px; margin-left: 1232px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    ソースステージ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1296" y="587" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ソースステージ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1271 603 L 1321 603 L 1321 653 L 1271 653 Z" fill="#7aa116" stroke="none" pointer-events="all" style="fill: light-dark(rgb(122, 161, 22), rgb(97, 130, 11));"/>
            <path d="M 1310.92 630.07 L 1311.2 628.14 C 1313.73 629.65 1313.76 630.28 1313.76 630.29 C 1313.76 630.3 1313.32 630.66 1310.92 630.07 Z M 1309.54 629.68 C 1305.16 628.36 1299.08 625.57 1296.61 624.4 C 1296.61 624.39 1296.62 624.38 1296.62 624.37 C 1296.62 623.43 1295.85 622.66 1294.9 622.66 C 1293.95 622.66 1293.18 623.43 1293.18 624.37 C 1293.18 625.32 1293.95 626.09 1294.9 626.09 C 1295.32 626.09 1295.69 625.93 1295.99 625.69 C 1298.89 627.06 1304.93 629.81 1309.33 631.11 L 1307.59 643.4 C 1307.59 643.43 1307.58 643.47 1307.58 643.5 C 1307.58 644.58 1302.79 646.57 1294.97 646.57 C 1287.06 646.57 1282.21 644.58 1282.21 643.5 C 1282.21 643.47 1282.21 643.44 1282.21 643.4 L 1278.57 616.83 C 1281.72 619 1288.49 620.14 1294.97 620.14 C 1301.44 620.14 1308.2 619 1311.36 616.84 Z M 1278.18 614.06 C 1278.24 613.12 1283.64 609.43 1294.97 609.43 C 1306.3 609.43 1311.7 613.12 1311.76 614.06 L 1311.76 614.38 C 1311.13 616.48 1304.13 618.71 1294.97 618.71 C 1285.79 618.71 1278.79 616.48 1278.18 614.37 Z M 1313.18 614.07 C 1313.18 611.6 1306.09 608 1294.97 608 C 1283.85 608 1276.76 611.6 1276.76 614.07 L 1276.82 614.61 L 1280.79 643.56 C 1280.88 646.79 1289.51 648 1294.97 648 C 1301.73 648 1308.92 646.44 1309.01 643.56 L 1310.72 631.49 C 1311.68 631.72 1312.46 631.83 1313.09 631.83 C 1313.93 631.83 1314.51 631.63 1314.85 631.21 C 1315.14 630.88 1315.24 630.47 1315.16 630.03 C 1314.98 629.04 1313.8 627.97 1311.41 626.61 L 1313.11 614.64 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 660px; margin-left: 1296px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    artifact bucket
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1296" y="672" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        artifact...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1624.78 439.37 L 1624.02 549.72" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1624.82 434.12 L 1628.27 441.14 L 1624.78 439.37 L 1621.27 441.09 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1600 383 L 1650 383 L 1650 433 L 1600 433 Z" fill="#e7157b" stroke="none" pointer-events="all" style="fill: light-dark(rgb(231, 21, 123), rgb(255, 129, 217));"/>
            <path d="M 1634.75 426.57 C 1633.22 426.57 1631.98 425.32 1631.98 423.78 C 1631.98 422.24 1633.22 420.99 1634.75 420.99 C 1636.29 420.99 1637.53 422.24 1637.53 423.78 C 1637.53 425.32 1636.29 426.57 1634.75 426.57 Z M 1628.67 414.43 L 1621.28 414.43 L 1617.59 408 L 1621.28 401.57 L 1628.67 401.57 L 1632.36 408 Z M 1616.16 395.01 C 1614.63 395.01 1613.38 393.76 1613.38 392.22 C 1613.38 390.68 1614.63 389.43 1616.16 389.43 C 1617.69 389.43 1618.94 390.68 1618.94 392.22 C 1618.94 393.76 1617.69 395.01 1616.16 395.01 Z M 1634.75 419.56 C 1634.23 419.56 1633.72 419.66 1633.26 419.84 L 1630.24 414.72 L 1630.08 414.82 L 1633.79 408.36 C 1633.92 408.14 1633.92 407.86 1633.79 407.64 L 1629.69 400.5 C 1629.56 400.28 1629.33 400.14 1629.08 400.14 L 1621.61 400.14 L 1621.64 400.13 L 1618.88 395.43 C 1619.78 394.66 1620.36 393.51 1620.36 392.22 C 1620.36 389.89 1618.47 388 1616.16 388 C 1613.84 388 1611.96 389.89 1611.96 392.22 C 1611.96 394.55 1613.84 396.44 1616.16 396.44 C 1616.69 396.44 1617.19 396.34 1617.65 396.16 L 1620.24 400.54 L 1616.16 407.64 C 1616.03 407.86 1616.03 408.14 1616.16 408.36 L 1620.26 415.5 C 1620.39 415.72 1620.62 415.86 1620.87 415.86 L 1629.08 415.86 C 1629.13 415.86 1629.19 415.85 1629.25 415.83 L 1632.04 420.57 C 1631.13 421.34 1630.56 422.49 1630.56 423.78 C 1630.56 426.11 1632.44 428 1634.75 428 C 1637.07 428 1638.95 426.11 1638.95 423.78 C 1638.95 421.45 1637.07 419.56 1634.75 419.56 Z M 1638.99 402.36 C 1637.46 402.36 1636.21 401.11 1636.21 399.57 C 1636.21 398.03 1637.46 396.78 1638.99 396.78 C 1640.52 396.78 1641.77 398.03 1641.77 399.57 C 1641.77 401.11 1640.52 402.36 1638.99 402.36 Z M 1644.19 407.64 L 1641.49 402.95 C 1642.52 402.18 1643.19 400.95 1643.19 399.57 C 1643.19 397.24 1641.3 395.35 1638.99 395.35 C 1638.4 395.35 1637.84 395.47 1637.33 395.69 L 1635.13 391.86 C 1635 391.64 1634.77 391.51 1634.52 391.51 L 1625.71 391.51 L 1625.71 392.94 L 1634.11 392.94 L 1636.14 396.48 C 1635.31 397.25 1634.79 398.35 1634.79 399.57 C 1634.79 401.9 1636.67 403.79 1638.99 403.79 C 1639.42 403.79 1639.84 403.72 1640.23 403.6 L 1642.76 408 L 1639.21 414.18 L 1640.44 414.89 L 1644.19 408.36 C 1644.32 408.14 1644.32 407.86 1644.19 407.64 Z M 1611.59 418.89 C 1610.06 418.89 1608.81 417.64 1608.81 416.1 C 1608.81 414.56 1610.06 413.31 1611.59 413.31 C 1613.12 413.31 1614.37 414.56 1614.37 416.1 C 1614.37 417.64 1613.12 418.89 1611.59 418.89 Z M 1613.91 419.61 C 1615.04 418.86 1615.79 417.56 1615.79 416.1 C 1615.79 413.77 1613.91 411.88 1611.59 411.88 C 1610.92 411.88 1610.29 412.04 1609.73 412.32 L 1607.25 408 L 1611.36 400.84 L 1610.13 400.12 L 1605.81 407.64 C 1605.68 407.86 1605.68 408.14 1605.81 408.36 L 1608.57 413.17 C 1607.85 413.93 1607.39 414.96 1607.39 416.1 C 1607.39 418.43 1609.28 420.32 1611.59 420.32 C 1611.94 420.32 1612.28 420.27 1612.6 420.19 L 1614.87 424.14 C 1615 424.36 1615.23 424.49 1615.48 424.49 L 1624.29 424.49 L 1624.29 423.07 L 1615.89 423.07 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 440px; margin-left: 1625px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    EventBridge
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1625" y="452" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        EventBri...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1600 408 L 1573.37 408" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1568.12 408 L 1575.12 404.5 L 1573.37 408 L 1575.12 411.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1296 93 L 1296 143.56 Q 1296 153.56 1286 153.55 L 1212.37 153.5" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1207.12 153.5 L 1214.12 150.01 L 1212.37 153.5 L 1214.12 157.01 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 136px; margin-left: 1297px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    コピー
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1297" y="140" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        コピー
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1271 43 L 1321 43 L 1321 93 L 1271 93 Z" fill="#7aa116" stroke="none" pointer-events="all" style="fill: light-dark(rgb(122, 161, 22), rgb(97, 130, 11));"/>
            <path d="M 1310.92 70.07 L 1311.2 68.14 C 1313.73 69.65 1313.76 70.28 1313.76 70.29 C 1313.76 70.3 1313.32 70.66 1310.92 70.07 Z M 1309.54 69.68 C 1305.16 68.36 1299.08 65.56 1296.61 64.4 C 1296.61 64.39 1296.62 64.38 1296.62 64.37 C 1296.62 63.42 1295.85 62.66 1294.9 62.66 C 1293.95 62.66 1293.18 63.42 1293.18 64.37 C 1293.18 65.32 1293.95 66.09 1294.9 66.09 C 1295.32 66.09 1295.69 65.93 1295.99 65.69 C 1298.89 67.06 1304.93 69.81 1309.33 71.11 L 1307.59 83.4 C 1307.59 83.43 1307.58 83.47 1307.58 83.5 C 1307.58 84.58 1302.79 86.57 1294.97 86.57 C 1287.06 86.57 1282.21 84.58 1282.21 83.5 C 1282.21 83.47 1282.21 83.44 1282.21 83.4 L 1278.57 56.83 C 1281.72 59 1288.49 60.14 1294.97 60.14 C 1301.44 60.14 1308.2 59 1311.36 56.84 Z M 1278.18 54.06 C 1278.24 53.12 1283.64 49.43 1294.97 49.43 C 1306.3 49.43 1311.7 53.12 1311.76 54.06 L 1311.76 54.38 C 1311.13 56.48 1304.13 58.71 1294.97 58.71 C 1285.79 58.71 1278.79 56.48 1278.18 54.37 Z M 1313.18 54.07 C 1313.18 51.6 1306.09 48 1294.97 48 C 1283.85 48 1276.76 51.6 1276.76 54.07 L 1276.82 54.61 L 1280.79 83.56 C 1280.88 86.79 1289.51 88 1294.97 88 C 1301.73 88 1308.92 86.44 1309.01 83.56 L 1310.72 71.49 C 1311.68 71.72 1312.46 71.83 1313.09 71.83 C 1313.93 71.83 1314.51 71.63 1314.85 71.21 C 1315.14 70.88 1315.24 70.47 1315.16 70.03 C 1314.98 69.04 1313.8 67.97 1311.41 66.61 L 1313.11 54.64 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 100px; margin-left: 1296px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    マスターバケット
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1296" y="112" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        マスターバケット
                    </text>
                </switch>
            </g>
        </g>
        <g/>
        <g/>
        <g>
            <rect x="1409" y="105" width="156" height="122" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1409 105 L 1459 105 L 1459 155 L 1409 155 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
            <path d="M 1450.48 135.44 L 1444.87 132.07 L 1444.87 124.06 C 1444.87 123.81 1444.74 123.58 1444.52 123.45 L 1436.45 118.75 L 1436.45 111.96 L 1450.48 120.25 Z M 1451.54 119.25 L 1436.11 110.13 C 1435.89 110 1435.63 110 1435.41 110.12 C 1435.19 110.25 1435.05 110.48 1435.05 110.73 L 1435.05 119.15 C 1435.05 119.4 1435.18 119.63 1435.4 119.75 L 1443.47 124.46 L 1443.47 132.47 C 1443.47 132.72 1443.6 132.94 1443.81 133.07 L 1450.82 137.28 C 1450.93 137.34 1451.05 137.38 1451.18 137.38 C 1451.3 137.38 1451.42 137.35 1451.52 137.29 C 1451.74 137.16 1451.88 136.93 1451.88 136.68 L 1451.88 119.85 C 1451.88 119.6 1451.75 119.37 1451.54 119.25 Z M 1433.96 148.5 L 1417.52 139.76 L 1417.52 120.25 L 1431.55 111.96 L 1431.55 118.76 L 1424.16 123.46 C 1423.96 123.59 1423.83 123.82 1423.83 124.06 L 1423.83 135.98 C 1423.83 136.24 1423.98 136.48 1424.21 136.6 L 1433.64 141.51 C 1433.85 141.61 1434.09 141.61 1434.29 141.51 L 1443.44 136.78 L 1449.07 140.16 Z M 1450.84 139.58 L 1443.83 135.37 C 1443.62 135.25 1443.36 135.24 1443.14 135.35 L 1433.97 140.09 L 1425.24 135.55 L 1425.24 124.44 L 1432.62 119.74 C 1432.83 119.61 1432.95 119.39 1432.95 119.15 L 1432.95 110.73 C 1432.95 110.48 1432.81 110.25 1432.59 110.12 C 1432.38 110 1432.11 110 1431.89 110.13 L 1416.46 119.25 C 1416.25 119.37 1416.12 119.6 1416.12 119.85 L 1416.12 140.18 C 1416.12 140.44 1416.26 140.68 1416.49 140.8 L 1433.64 149.92 C 1433.74 149.97 1433.85 150 1433.97 150 C 1434.08 150 1434.2 149.97 1434.31 149.91 L 1450.82 140.8 C 1451.04 140.68 1451.17 140.45 1451.18 140.2 C 1451.18 139.94 1451.05 139.71 1450.84 139.58 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 130px; margin-left: 1461px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    ECS
                                    <span style="background-color: transparent;">
                                        クラスター
                                    </span>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1461" y="134" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">
                        ECSクラスター
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <image x="1463.5" y="154.5" width="46" height="46" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLBAMAAADKYGfZAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAwUExURQAAAO9wAOxwAOxwAOtwAO1yAO1xAO1xAO5xAO5xAO9wAOpwAOtwAO1wAO5yAO1yACH696YAAAAQdFJOUwAQUGBAn//fz78gMIBwr48dwG1XAAAACXBIWXMAABcRAAAXEQHKJvM/AAAAwklEQVRIx+3VPQ4BQRiA4XeXXT+bvQOjQiNxAVG4g1LCrIaoHEGipJtQOII4gUriAmpRuIALMKvfkZBovjeZqZ5JJlPMhyT9Jq/WdtQhB4F2NSKEgpMlb7Y0mS0SSpZ1si/fS+z+e5ZXOyK1p6xeR1TltarQbcChmTL/zQJ9JNZX8nqGpwcwH8J9ApdpykJhwoQJ+57FpoVvjkTmimdW0N/AeQ31bcr+91s+sqfHKbHj48O5UHSysWWRcXWzDyJJXwdPyDmISQ6XpuMAAAAASUVORK5CYII=" preserveAspectRatio="none"/>
        </g>
        <g>
            <g fill="#000000" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));">
                <rect fill="#ffffff" stroke="none" x="1463" y="209" width="50" height="15" stroke-width="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/>
                <text x="1486.5" y="218.5">
                    コンテナ
                </text>
            </g>
        </g>
        <g>
            <path d="M 1487 568 L 1487 233.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1487 228.12 L 1490.5 235.12 L 1487 233.37 L 1483.5 235.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 294px; margin-left: 1490px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ecspresso deploy
                                    <div>
                                        (コンテナ初回作成)
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1490" y="297" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        ecspresso deploy...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 884 590 L 1050 590 L 1050 665 L 987 665 L 1015.14 695 L 967 665 L 884 665 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" transform="rotate(180,967,642.5)" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 164px; height: 1px; padding-top: 658px; margin-left: 885px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    ➂アプリアカウントに
                                    <div>
                                        パイプラインをデプロイ
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="967" y="662" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ➂アプリアカウントに
パイプラインをデプロイ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="874" y="516.5" width="46" height="46" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 878.21 560.41 C 878.73 550.04 886.96 541.78 897 541.78 C 900.4 541.78 903.72 542.73 906.62 544.54 C 912.01 547.9 915.46 553.91 915.79 560.41 Z M 886.75 528.92 C 886.75 523.22 891.35 518.59 897 518.59 C 902.65 518.59 907.25 523.22 907.25 528.92 C 907.25 534.61 902.65 539.25 897 539.25 C 891.35 539.25 886.75 534.61 886.75 528.92 Z M 907.73 542.77 C 905.92 541.64 903.95 540.82 901.91 540.31 C 906.28 538.39 909.34 534.01 909.34 528.92 C 909.34 522.07 903.81 516.5 897 516.5 C 890.19 516.5 884.66 522.07 884.66 528.92 C 884.66 534.02 887.73 538.4 892.1 540.31 C 882.93 542.62 876.09 551.21 876.09 561.45 C 876.09 562.03 876.56 562.5 877.14 562.5 L 916.86 562.5 C 917.44 562.5 917.91 562.03 917.91 561.45 C 917.91 553.84 914.01 546.68 907.73 542.77 Z" fill="#232f3d" stroke="none" pointer-events="all" style="fill: light-dark(rgb(35, 47, 61), rgb(190, 200, 212));"/>
        </g>
        <g>
            <path d="M 1027 539.5 L 1113.63 539.97" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1118.88 539.99 L 1111.86 543.46 L 1113.63 539.97 L 1111.9 536.46 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 540px; margin-left: 1074px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    デプロイ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1074" y="543" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        デプロイ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 977 514.5 L 1027 514.5 L 1027 564.5 L 977 564.5 Z" fill="#c925d1" stroke="none" pointer-events="all" style="fill: light-dark(rgb(201, 37, 209), rgb(255, 124, 255));"/>
            <path d="M 1018.03 519.5 L 985.97 519.5 C 983.78 519.5 982 521.28 982 523.47 L 982 532.98 C 982 535.17 983.78 536.95 985.97 536.95 L 987.09 536.95 L 987.09 558.77 C 987.09 559.17 987.42 559.5 987.82 559.5 L 1015.45 559.5 C 1015.86 559.5 1016.18 559.17 1016.18 558.77 L 1016.18 536.95 L 1018.03 536.95 C 1020.22 536.95 1022 535.17 1022 532.98 L 1022 523.47 C 1022 521.28 1020.22 519.5 1018.03 519.5 Z M 988.55 558.05 L 988.55 536.95 L 1014.73 536.95 L 1014.73 558.05 Z M 1018.03 535.5 L 985.97 535.5 C 984.59 535.5 983.45 534.37 983.45 532.98 L 983.45 532.59 L 992.18 532.59 L 992.18 531.14 L 983.45 531.14 L 983.45 523.47 C 983.45 522.08 984.59 520.95 985.97 520.95 L 1018.03 520.95 C 1019.41 520.95 1020.55 522.08 1020.55 523.47 L 1020.55 531.14 L 1000.18 531.14 L 1000.18 532.59 L 1020.55 532.59 L 1020.55 532.98 C 1020.55 534.37 1019.41 535.5 1018.03 535.5 Z M 991.1 547.17 C 991.1 546.96 991.19 546.76 991.34 546.62 L 995.95 542.55 L 996.91 543.64 L 992.93 547.16 L 996.89 550.58 L 995.94 551.68 L 991.35 547.72 C 991.19 547.58 991.1 547.38 991.1 547.17 Z M 1005.69 550.61 L 1009.69 547.11 L 1005.69 543.64 L 1006.64 542.54 L 1011.27 546.55 C 1011.43 546.69 1011.52 546.89 1011.52 547.1 C 1011.52 547.31 1011.43 547.51 1011.27 547.65 L 1006.65 551.71 Z M 999.21 554.53 L 997.87 553.98 L 1003.39 540.51 L 1004.73 541.06 Z M 994.36 532.59 L 998 532.59 L 998 531.14 L 994.36 531.14 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 572px; margin-left: 1002px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    CodePipeline
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1002" y="584" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        CodePipe...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 917.91 539.5 L 970.63 539.5" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 975.88 539.5 L 968.88 543 L 970.63 539.5 L 968.88 536 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 874 28 L 1040 28 L 1040 103 L 977 103 L 937.08 133 L 957 103 L 874 103 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 164px; height: 1px; padding-top: 65px; margin-left: 875px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    ➃GHAからECSをデプロイ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="957" y="69" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ➃GHAからECSをデプロイ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="871" y="143" width="46" height="46" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 875.21 186.91 C 875.73 176.54 883.96 168.28 894 168.28 C 897.4 168.28 900.72 169.23 903.62 171.04 C 909.01 174.4 912.46 180.41 912.79 186.91 Z M 883.75 155.42 C 883.75 149.72 888.35 145.09 894 145.09 C 899.65 145.09 904.25 149.72 904.25 155.42 C 904.25 161.11 899.65 165.75 894 165.75 C 888.35 165.75 883.75 161.11 883.75 155.42 Z M 904.73 169.27 C 902.92 168.14 900.95 167.32 898.91 166.81 C 903.28 164.89 906.34 160.51 906.34 155.42 C 906.34 148.57 900.81 143 894 143 C 887.19 143 881.66 148.57 881.66 155.42 C 881.66 160.52 884.73 164.9 889.1 166.81 C 879.93 169.12 873.09 177.71 873.09 187.95 C 873.09 188.53 873.56 189 874.14 189 L 913.86 189 C 914.44 189 914.91 188.53 914.91 187.95 C 914.91 180.34 911.01 173.18 904.73 169.27 Z" fill="#232f3d" stroke="none" pointer-events="all" style="fill: light-dark(rgb(35, 47, 61), rgb(190, 200, 212));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 196px; margin-left: 894px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    app team
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="894" y="208" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        app team
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1552" y="603" width="80" height="50" fill="none" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 628px; margin-left: 1553px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; max-height: 46px; overflow: hidden; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    <h1 style="margin-top: 0px;">
                                        ・・・
                                    </h1>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1592" y="632" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ・・・
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1516 384 L 1566 384 L 1566 434 L 1516 434 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
            <path d="M 1532.45 427.57 L 1523.58 427.57 L 1533.39 407.07 L 1537.84 416.22 Z M 1534.03 405.11 C 1533.91 404.86 1533.66 404.71 1533.39 404.71 L 1533.39 404.71 C 1533.11 404.71 1532.86 404.87 1532.74 405.11 L 1521.8 427.98 C 1521.7 428.2 1521.71 428.46 1521.84 428.67 C 1521.97 428.87 1522.2 429 1522.45 429 L 1532.9 429 C 1533.18 429 1533.43 428.84 1533.55 428.59 L 1539.28 416.52 C 1539.37 416.32 1539.37 416.1 1539.27 415.9 Z M 1558.87 427.57 L 1550.06 427.57 L 1535.92 397.98 C 1535.81 397.73 1535.55 397.57 1535.28 397.57 L 1529.51 397.57 L 1529.52 390.43 L 1540.82 390.43 L 1554.89 420.02 C 1555.01 420.27 1555.26 420.43 1555.54 420.43 L 1558.87 420.43 Z M 1559.59 419 L 1555.99 419 L 1541.92 389.41 C 1541.8 389.16 1541.55 389 1541.27 389 L 1528.8 389 C 1528.41 389 1528.09 389.32 1528.09 389.71 L 1528.08 398.29 C 1528.08 398.48 1528.15 398.66 1528.29 398.79 C 1528.42 398.93 1528.6 399 1528.79 399 L 1534.83 399 L 1548.96 428.59 C 1549.08 428.84 1549.33 429 1549.61 429 L 1559.59 429 C 1559.98 429 1560.3 428.68 1560.3 428.29 L 1560.3 419.71 C 1560.3 419.32 1559.98 419 1559.59 419 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 441px; margin-left: 1541px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    Lambda
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1541" y="453" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Lambda
                    </text>
                </switch>
            </g>
        </g>
        <g/>
        <g>
            <rect x="1718" y="0" width="860" height="735" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="1979" y="12" width="569" height="311" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 567px; height: 1px; padding-top: 19px; margin-left: 1981px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Gevanni
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1981" y="31" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Gevanni
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1980" y="355" width="568" height="369" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 566px; height: 1px; padding-top: 362px; margin-left: 1982px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    アプリ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1982" y="374" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        アプリ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="2016" y="552" width="482" height="152" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 2041 191 L 2041 236.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 2041 241.88 L 2037.5 234.88 L 2041 236.63 L 2044.5 234.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 2041 293 L 2041 395.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 2041 400.88 L 2037.5 393.88 L 2041 395.63 L 2044.5 393.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 2041 452 L 2041 545.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 2041 550.88 L 2037.5 543.88 L 2041 545.63 L 2044.5 543.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1855 166 L 2009.63 166" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 2014.88 166 L 2007.88 169.5 L 2009.63 166 L 2007.88 162.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1776 166 L 1798.63 166" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1803.88 166 L 1796.88 169.5 L 1798.63 166 L 1796.88 162.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="1805" y="141" width="50" height="50" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 1805 166 C 1805 152.19 1816.19 141 1830 141 C 1843.81 141 1855 152.19 1855 166 C 1855 179.81 1843.81 191 1830 191 C 1816.19 191 1805 179.81 1805 166 Z M 1807.4 166 C 1807.34 176.01 1813.9 184.85 1823.5 187.7 L 1823.5 183.35 C 1823.59 181.79 1824.41 180.37 1825.7 179.5 C 1821.9 179.08 1818.47 177.4 1816.22 174.85 C 1813.98 172.3 1813.1 169.1 1813.8 166 C 1814.16 163.85 1815.07 161.84 1816.45 160.15 C 1815.6 157.98 1815.67 155.56 1816.65 153.45 C 1819.18 153.55 1821.6 154.52 1823.5 156.2 C 1827.71 154.74 1832.28 154.72 1836.5 156.15 C 1838.43 154.47 1840.89 153.51 1843.45 153.45 C 1844.44 155.65 1844.52 158.15 1843.65 160.4 C 1844.95 162.03 1845.83 163.95 1846.2 166 C 1846.9 169.09 1846.03 172.28 1843.79 174.83 C 1841.55 177.38 1838.14 179.07 1834.35 179.5 C 1835.57 180.28 1836.39 181.56 1836.6 183 L 1836.6 187.8 C 1846.21 184.92 1852.76 176.03 1852.65 166 C 1852.65 160.01 1850.26 154.26 1846.01 150.04 C 1841.75 145.81 1835.99 143.46 1830 143.5 C 1824.02 143.47 1818.27 145.83 1814.03 150.05 C 1809.78 154.28 1807.4 160.02 1807.4 166 Z" fill="#00bef2" stroke="none" pointer-events="all" style="fill: light-dark(rgb(0, 190, 242), rgb(0, 137, 182));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 198px; margin-left: 1830px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    GitHub Actions
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1830" y="210" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        GitHub A...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1855 166 L 2010.59 69.36" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 2015.05 66.59 L 2010.95 73.26 L 2010.59 69.36 L 2007.26 67.31 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 2016 141 L 2066 141 L 2066 191 L 2016 191 Z" fill="#7aa116" stroke="none" pointer-events="all" style="fill: light-dark(rgb(122, 161, 22), rgb(97, 130, 11));"/>
            <path d="M 2055.92 168.07 L 2056.2 166.13 C 2058.73 167.65 2058.76 168.28 2058.76 168.29 C 2058.76 168.3 2058.32 168.66 2055.92 168.07 Z M 2054.54 167.68 C 2050.16 166.36 2044.08 163.56 2041.61 162.4 C 2041.61 162.39 2041.62 162.38 2041.62 162.37 C 2041.62 161.43 2040.85 160.66 2039.9 160.66 C 2038.95 160.66 2038.18 161.43 2038.18 162.37 C 2038.18 163.32 2038.95 164.09 2039.9 164.09 C 2040.32 164.09 2040.69 163.93 2040.99 163.69 C 2043.89 165.06 2049.93 167.81 2054.33 169.11 L 2052.59 181.4 C 2052.59 181.43 2052.58 181.47 2052.58 181.5 C 2052.58 182.58 2047.79 184.57 2039.97 184.57 C 2032.06 184.57 2027.21 182.58 2027.21 181.5 C 2027.21 181.47 2027.21 181.44 2027.21 181.4 L 2023.57 154.83 C 2026.72 157 2033.49 158.14 2039.97 158.14 C 2046.44 158.14 2053.2 157 2056.36 154.84 Z M 2023.18 152.06 C 2023.24 151.12 2028.64 147.43 2039.97 147.43 C 2051.3 147.43 2056.7 151.12 2056.76 152.06 L 2056.76 152.38 C 2056.13 154.48 2049.13 156.71 2039.97 156.71 C 2030.79 156.71 2023.79 154.48 2023.18 152.37 Z M 2058.18 152.07 C 2058.18 149.6 2051.09 146 2039.97 146 C 2028.85 146 2021.76 149.6 2021.76 152.07 L 2021.82 152.61 L 2025.79 181.56 C 2025.88 184.79 2034.51 186 2039.97 186 C 2046.73 186 2053.92 184.44 2054.01 181.56 L 2055.72 169.49 C 2056.68 169.72 2057.46 169.83 2058.09 169.83 C 2058.93 169.83 2059.51 169.63 2059.85 169.21 C 2060.14 168.88 2060.24 168.47 2060.16 168.03 C 2059.98 167.04 2058.8 165.97 2056.41 164.61 L 2058.11 152.64 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 198px; margin-left: 2041px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ソースバケット
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="2041" y="210" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ソースバケット
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 2016 243 L 2066 243 L 2066 293 L 2016 293 Z" fill="#e7157b" stroke="none" pointer-events="all" style="fill: light-dark(rgb(231, 21, 123), rgb(255, 129, 217));"/>
            <path d="M 2050.75 286.57 C 2049.22 286.57 2047.98 285.32 2047.98 283.78 C 2047.98 282.24 2049.22 280.99 2050.75 280.99 C 2052.29 280.99 2053.53 282.24 2053.53 283.78 C 2053.53 285.32 2052.29 286.57 2050.75 286.57 Z M 2044.67 274.43 L 2037.28 274.43 L 2033.59 268 L 2037.28 261.57 L 2044.67 261.57 L 2048.36 268 Z M 2032.16 255.01 C 2030.63 255.01 2029.38 253.76 2029.38 252.22 C 2029.38 250.68 2030.63 249.43 2032.16 249.43 C 2033.69 249.43 2034.94 250.68 2034.94 252.22 C 2034.94 253.76 2033.69 255.01 2032.16 255.01 Z M 2050.75 279.56 C 2050.23 279.56 2049.72 279.66 2049.26 279.84 L 2046.24 274.72 L 2046.08 274.82 L 2049.79 268.36 C 2049.92 268.14 2049.92 267.86 2049.79 267.64 L 2045.69 260.5 C 2045.56 260.28 2045.33 260.14 2045.08 260.14 L 2037.61 260.14 L 2037.64 260.13 L 2034.88 255.43 C 2035.78 254.66 2036.36 253.51 2036.36 252.22 C 2036.36 249.89 2034.47 248 2032.16 248 C 2029.84 248 2027.96 249.89 2027.96 252.22 C 2027.96 254.55 2029.84 256.44 2032.16 256.44 C 2032.69 256.44 2033.19 256.34 2033.65 256.16 L 2036.24 260.54 L 2032.16 267.64 C 2032.03 267.86 2032.03 268.14 2032.16 268.36 L 2036.26 275.5 C 2036.39 275.72 2036.62 275.86 2036.87 275.86 L 2045.08 275.86 C 2045.13 275.86 2045.19 275.85 2045.25 275.83 L 2048.04 280.57 C 2047.13 281.34 2046.56 282.49 2046.56 283.78 C 2046.56 286.11 2048.44 288 2050.75 288 C 2053.07 288 2054.95 286.11 2054.95 283.78 C 2054.95 281.45 2053.07 279.56 2050.75 279.56 Z M 2054.99 262.36 C 2053.46 262.36 2052.21 261.11 2052.21 259.57 C 2052.21 258.03 2053.46 256.78 2054.99 256.78 C 2056.52 256.78 2057.77 258.03 2057.77 259.57 C 2057.77 261.11 2056.52 262.36 2054.99 262.36 Z M 2060.19 267.64 L 2057.49 262.95 C 2058.52 262.18 2059.19 260.95 2059.19 259.57 C 2059.19 257.24 2057.3 255.35 2054.99 255.35 C 2054.4 255.35 2053.84 255.47 2053.33 255.69 L 2051.13 251.86 C 2051 251.64 2050.77 251.51 2050.52 251.51 L 2041.71 251.51 L 2041.71 252.94 L 2050.11 252.94 L 2052.14 256.48 C 2051.31 257.25 2050.79 258.35 2050.79 259.57 C 2050.79 261.9 2052.67 263.79 2054.99 263.79 C 2055.42 263.79 2055.84 263.72 2056.23 263.6 L 2058.76 268 L 2055.21 274.18 L 2056.44 274.89 L 2060.19 268.36 C 2060.32 268.14 2060.32 267.86 2060.19 267.64 Z M 2027.59 278.89 C 2026.06 278.89 2024.81 277.64 2024.81 276.1 C 2024.81 274.56 2026.06 273.31 2027.59 273.31 C 2029.12 273.31 2030.37 274.56 2030.37 276.1 C 2030.37 277.64 2029.12 278.89 2027.59 278.89 Z M 2029.91 279.61 C 2031.04 278.86 2031.79 277.56 2031.79 276.1 C 2031.79 273.77 2029.91 271.88 2027.59 271.88 C 2026.92 271.88 2026.29 272.04 2025.73 272.32 L 2023.25 268 L 2027.36 260.84 L 2026.13 260.12 L 2021.81 267.64 C 2021.68 267.86 2021.68 268.14 2021.81 268.36 L 2024.57 273.17 C 2023.85 273.93 2023.39 274.96 2023.39 276.1 C 2023.39 278.43 2025.28 280.32 2027.59 280.32 C 2027.94 280.32 2028.28 280.27 2028.6 280.19 L 2030.87 284.14 C 2031 284.36 2031.23 284.49 2031.48 284.49 L 2040.29 284.49 L 2040.29 283.07 L 2031.89 283.07 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 300px; margin-left: 2041px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    EventBridge
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="2041" y="312" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        EventBri...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 2016 402 L 2066 402 L 2066 452 L 2016 452 Z" fill="#e7157b" stroke="none" pointer-events="all" style="fill: light-dark(rgb(231, 21, 123), rgb(255, 129, 217));"/>
            <path d="M 2050.75 445.57 C 2049.22 445.57 2047.98 444.32 2047.98 442.78 C 2047.98 441.24 2049.22 439.99 2050.75 439.99 C 2052.29 439.99 2053.53 441.24 2053.53 442.78 C 2053.53 444.32 2052.29 445.57 2050.75 445.57 Z M 2044.67 433.43 L 2037.28 433.43 L 2033.59 427 L 2037.28 420.57 L 2044.67 420.57 L 2048.36 427 Z M 2032.16 414.01 C 2030.63 414.01 2029.38 412.76 2029.38 411.22 C 2029.38 409.68 2030.63 408.43 2032.16 408.43 C 2033.69 408.43 2034.94 409.68 2034.94 411.22 C 2034.94 412.76 2033.69 414.01 2032.16 414.01 Z M 2050.75 438.56 C 2050.23 438.56 2049.72 438.66 2049.26 438.84 L 2046.24 433.72 L 2046.08 433.82 L 2049.79 427.36 C 2049.92 427.14 2049.92 426.86 2049.79 426.64 L 2045.69 419.5 C 2045.56 419.28 2045.33 419.14 2045.08 419.14 L 2037.61 419.14 L 2037.64 419.13 L 2034.88 414.43 C 2035.78 413.66 2036.36 412.51 2036.36 411.22 C 2036.36 408.89 2034.47 407 2032.16 407 C 2029.84 407 2027.96 408.89 2027.96 411.22 C 2027.96 413.55 2029.84 415.44 2032.16 415.44 C 2032.69 415.44 2033.19 415.34 2033.65 415.16 L 2036.24 419.54 L 2032.16 426.64 C 2032.03 426.86 2032.03 427.14 2032.16 427.36 L 2036.26 434.5 C 2036.39 434.72 2036.62 434.86 2036.87 434.86 L 2045.08 434.86 C 2045.13 434.86 2045.19 434.85 2045.25 434.83 L 2048.04 439.57 C 2047.13 440.34 2046.56 441.49 2046.56 442.78 C 2046.56 445.11 2048.44 447 2050.75 447 C 2053.07 447 2054.95 445.11 2054.95 442.78 C 2054.95 440.45 2053.07 438.56 2050.75 438.56 Z M 2054.99 421.36 C 2053.46 421.36 2052.21 420.11 2052.21 418.57 C 2052.21 417.03 2053.46 415.78 2054.99 415.78 C 2056.52 415.78 2057.77 417.03 2057.77 418.57 C 2057.77 420.11 2056.52 421.36 2054.99 421.36 Z M 2060.19 426.64 L 2057.49 421.95 C 2058.52 421.18 2059.19 419.95 2059.19 418.57 C 2059.19 416.24 2057.3 414.35 2054.99 414.35 C 2054.4 414.35 2053.84 414.47 2053.33 414.69 L 2051.13 410.86 C 2051 410.64 2050.77 410.51 2050.52 410.51 L 2041.71 410.51 L 2041.71 411.94 L 2050.11 411.94 L 2052.14 415.48 C 2051.31 416.25 2050.79 417.35 2050.79 418.57 C 2050.79 420.89 2052.67 422.79 2054.99 422.79 C 2055.42 422.79 2055.84 422.72 2056.23 422.6 L 2058.76 427 L 2055.21 433.18 L 2056.44 433.89 L 2060.19 427.36 C 2060.32 427.14 2060.32 426.86 2060.19 426.64 Z M 2027.59 437.89 C 2026.06 437.89 2024.81 436.64 2024.81 435.1 C 2024.81 433.56 2026.06 432.31 2027.59 432.31 C 2029.12 432.31 2030.37 433.56 2030.37 435.1 C 2030.37 436.64 2029.12 437.89 2027.59 437.89 Z M 2029.91 438.61 C 2031.04 437.86 2031.79 436.56 2031.79 435.1 C 2031.79 432.77 2029.91 430.88 2027.59 430.88 C 2026.92 430.88 2026.29 431.04 2025.73 431.32 L 2023.25 427 L 2027.36 419.84 L 2026.13 419.12 L 2021.81 426.64 C 2021.68 426.86 2021.68 427.14 2021.81 427.36 L 2024.57 432.17 C 2023.85 432.93 2023.39 433.96 2023.39 435.1 C 2023.39 437.43 2025.28 439.32 2027.59 439.32 C 2027.94 439.32 2028.28 439.27 2028.6 439.19 L 2030.87 443.14 C 2031 443.36 2031.23 443.49 2031.48 443.49 L 2040.29 443.49 L 2040.29 442.06 L 2031.89 442.06 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 459px; margin-left: 2041px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    EventBridge
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="2041" y="471" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        EventBri...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 2016 552 L 2066 552 L 2066 602 L 2016 602 Z" fill="#c925d1" stroke="none" pointer-events="all" style="fill: light-dark(rgb(201, 37, 209), rgb(255, 124, 255));"/>
            <path d="M 2057.03 557 L 2024.97 557 C 2022.78 557 2021 558.78 2021 560.97 L 2021 570.48 C 2021 572.67 2022.78 574.45 2024.97 574.45 L 2026.09 574.45 L 2026.09 596.27 C 2026.09 596.67 2026.42 597 2026.82 597 L 2054.45 597 C 2054.86 597 2055.18 596.67 2055.18 596.27 L 2055.18 574.45 L 2057.03 574.45 C 2059.22 574.45 2061 572.67 2061 570.48 L 2061 560.97 C 2061 558.78 2059.22 557 2057.03 557 Z M 2027.55 595.55 L 2027.55 574.45 L 2053.73 574.45 L 2053.73 595.55 Z M 2057.03 573 L 2024.97 573 C 2023.59 573 2022.45 571.87 2022.45 570.48 L 2022.45 570.09 L 2031.18 570.09 L 2031.18 568.64 L 2022.45 568.64 L 2022.45 560.97 C 2022.45 559.58 2023.59 558.45 2024.97 558.45 L 2057.03 558.45 C 2058.41 558.45 2059.55 559.58 2059.55 560.97 L 2059.55 568.64 L 2039.18 568.64 L 2039.18 570.09 L 2059.55 570.09 L 2059.55 570.48 C 2059.55 571.87 2058.41 573 2057.03 573 Z M 2030.1 584.67 C 2030.1 584.46 2030.19 584.26 2030.34 584.12 L 2034.95 580.05 L 2035.91 581.14 L 2031.93 584.66 L 2035.89 588.08 L 2034.94 589.18 L 2030.35 585.22 C 2030.19 585.08 2030.1 584.88 2030.1 584.67 Z M 2044.69 588.11 L 2048.69 584.61 L 2044.69 581.14 L 2045.64 580.04 L 2050.27 584.05 C 2050.43 584.19 2050.52 584.39 2050.52 584.6 C 2050.52 584.81 2050.43 585.01 2050.27 585.15 L 2045.65 589.21 Z M 2038.21 592.03 L 2036.87 591.48 L 2042.39 578.01 L 2043.73 578.56 Z M 2033.36 570.09 L 2037 570.09 L 2037 568.64 L 2033.36 568.64 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 609px; margin-left: 2041px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    CodePipeline
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="2041" y="621" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        CodePipe...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 2016 41 L 2066 41 L 2066 91 L 2016 91 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
            <path d="M 2039.8 63.98 C 2039.45 64.18 2039.24 64.55 2039.24 64.96 L 2039.24 84.4 L 2024.53 76.18 L 2024.53 57.06 L 2041.04 47.49 L 2055.71 54.76 Z M 2057.41 54.74 C 2057.41 54.34 2057.2 53.96 2056.81 53.74 L 2041.59 46.2 C 2041.25 46 2040.81 46 2040.47 46.2 L 2023.69 55.92 C 2023.34 56.12 2023.13 56.5 2023.13 56.9 L 2023.13 76.34 C 2023.13 76.74 2023.34 77.12 2023.7 77.32 L 2038.95 85.85 C 2039.12 85.95 2039.32 86 2039.51 86 C 2039.71 86 2039.9 85.95 2040.08 85.85 C 2040.42 85.65 2040.64 85.27 2040.64 84.87 L 2040.64 65.12 L 2056.85 55.72 C 2057.2 55.52 2057.41 55.15 2057.41 54.74 Z M 2057.45 76.22 L 2044.14 84.36 L 2044.14 77.07 L 2051.08 72.59 L 2051.13 72.28 C 2051.14 72.2 2051.15 72.2 2051.15 71.82 L 2051.17 63.12 L 2057.47 59.47 Z M 2058.31 58.02 C 2057.96 57.81 2057.53 57.81 2057.18 58.02 L 2050.47 61.9 C 2050.26 62.02 2049.77 62.29 2049.77 62.84 L 2049.74 71.78 L 2043.35 75.91 C 2042.97 76.14 2042.74 76.5 2042.74 76.88 L 2042.74 84.81 C 2042.74 85.21 2042.95 85.57 2043.31 85.78 C 2043.5 85.89 2043.71 85.94 2043.91 85.94 C 2044.12 85.94 2044.32 85.89 2044.51 85.78 L 2058.29 77.35 C 2058.64 77.15 2058.85 76.78 2058.85 76.38 L 2058.87 58.99 C 2058.87 58.59 2058.66 58.22 2058.31 58.02 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 98px; margin-left: 2041px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ECR
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="2041" y="110" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ECR
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 2156 561.63 L 2156 188.44 Q 2156 178.44 2146 178.45 L 2066 178.5" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 2156 566.88 L 2152.5 559.88 L 2156 561.63 L 2159.5 559.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 505px; margin-left: 2154px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    複製
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="2154" y="508" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        複製
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 2221 628 L 2275.63 628" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 2280.88 628 L 2273.88 631.5 L 2275.63 628 L 2273.88 624.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g/>
        <g>
            <rect x="2282" y="568" width="130" height="120" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 128px; height: 1px; padding-top: 575px; margin-left: 2283px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    デプロイステージ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="2347" y="587" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        デプロイステージ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 2325 603 L 2375 603 L 2375 653 L 2325 653 Z" fill="#c925d1" stroke="none" pointer-events="all" style="fill: light-dark(rgb(201, 37, 209), rgb(255, 124, 255));"/>
            <path d="M 2367.49 615.6 L 2363.39 611.85 L 2359.29 615.6 Z M 2361.82 611.43 L 2353.5 611.44 L 2357.94 614.97 Z M 2358.07 623.52 L 2351.52 625.93 L 2364.11 625.93 Z M 2348.46 639.71 L 2366.91 639.71 L 2366.91 627.31 L 2348.46 627.31 Z M 2356.53 615.6 L 2351.99 612 L 2347.49 615.6 Z M 2346.05 614.99 L 2350.49 611.45 L 2341.65 611.46 Z M 2344.62 615.6 L 2340.09 611.97 L 2335.56 615.6 L 2342.3 615.6 Z M 2341.62 616.98 L 2336.09 616.98 L 2341.62 622.47 Z M 2341.62 624.61 L 2335.13 630.52 L 2341.62 636.88 Z M 2341.62 639.11 L 2334.79 645.8 L 2334.79 645.91 L 2341.62 645.91 Z M 2334.79 632.11 L 2334.79 643.88 L 2340.8 638 Z M 2334.79 628.98 L 2340.76 623.54 L 2334.79 617.62 Z M 2334.1 615.02 L 2338.53 611.46 L 2334.1 611.47 Z M 2332.73 610.09 L 2331.37 610.09 L 2331.37 616.98 L 2332.73 616.98 L 2332.73 616.29 L 2332.73 610.78 Z M 2369.9 616.54 C 2369.8 616.81 2369.54 616.98 2369.26 616.98 L 2358.71 616.98 L 2358.71 622.49 L 2367.85 625.98 L 2367.85 625.99 C 2368.1 626.09 2368.28 626.33 2368.28 626.62 L 2368.28 640.4 C 2368.28 640.78 2367.97 641.09 2367.6 641.09 L 2347.77 641.09 C 2347.4 641.09 2347.09 640.78 2347.09 640.4 L 2347.09 626.62 C 2347.09 626.33 2347.27 626.08 2347.53 625.98 L 2347.53 625.98 L 2357.34 622.49 L 2357.34 616.98 L 2342.99 616.98 L 2342.99 646.59 C 2342.99 646.97 2342.68 647.28 2342.3 647.28 L 2334.1 647.28 C 2333.72 647.28 2333.42 646.97 2333.42 646.59 L 2333.42 618.36 L 2330.68 618.36 C 2330.31 618.36 2330 618.05 2330 617.67 L 2330 609.41 C 2330 609.03 2330.31 608.72 2330.68 608.72 L 2333.42 608.72 C 2333.8 608.72 2334.1 609.03 2334.1 609.41 L 2334.1 610.09 L 2363.49 610.09 L 2369.72 615.78 C 2369.93 615.97 2370 616.27 2369.9 616.54 Z M 2355.72 638.54 L 2359.69 629.43 L 2358.44 628.88 L 2354.46 637.99 Z M 2359.45 635.5 L 2360.34 636.55 L 2363.29 634.03 C 2363.43 633.91 2363.52 633.73 2363.53 633.54 C 2363.54 633.34 2363.47 633.16 2363.33 633.02 L 2360.87 630.5 L 2359.9 631.47 L 2361.84 633.46 Z M 2350.57 633.5 C 2350.42 633.36 2350.34 633.16 2350.35 632.96 C 2350.36 632.75 2350.46 632.56 2350.63 632.44 L 2353.98 629.94 L 2354.8 631.04 L 2352.11 633.05 L 2354.36 635.13 L 2353.44 636.15 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 660px; margin-left: 2350px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    codebuild
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="2350" y="672" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        codebuild
                    </text>
                </switch>
            </g>
        </g>
        <g/>
        <g>
            <rect x="2091" y="568" width="130" height="120" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 128px; height: 1px; padding-top: 575px; margin-left: 2092px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    ソースステージ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="2156" y="587" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ソースステージ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 2131 603 L 2181 603 L 2181 653 L 2131 653 Z" fill="#7aa116" stroke="none" pointer-events="all" style="fill: light-dark(rgb(122, 161, 22), rgb(97, 130, 11));"/>
            <path d="M 2170.92 630.07 L 2171.2 628.14 C 2173.73 629.65 2173.76 630.28 2173.76 630.29 C 2173.76 630.3 2173.32 630.66 2170.92 630.07 Z M 2169.54 629.68 C 2165.16 628.36 2159.08 625.57 2156.61 624.4 C 2156.61 624.39 2156.62 624.38 2156.62 624.37 C 2156.62 623.43 2155.85 622.66 2154.9 622.66 C 2153.95 622.66 2153.18 623.43 2153.18 624.37 C 2153.18 625.32 2153.95 626.09 2154.9 626.09 C 2155.32 626.09 2155.69 625.93 2155.99 625.69 C 2158.89 627.06 2164.93 629.81 2169.33 631.11 L 2167.59 643.4 C 2167.59 643.43 2167.58 643.47 2167.58 643.5 C 2167.58 644.58 2162.79 646.57 2154.97 646.57 C 2147.05 646.57 2142.21 644.58 2142.21 643.5 C 2142.21 643.47 2142.21 643.44 2142.21 643.4 L 2138.57 616.83 C 2141.72 619 2148.49 620.14 2154.97 620.14 C 2161.44 620.14 2168.2 619 2171.36 616.84 Z M 2138.18 614.06 C 2138.24 613.12 2143.64 609.43 2154.97 609.43 C 2166.3 609.43 2171.7 613.12 2171.76 614.06 L 2171.76 614.38 C 2171.13 616.48 2164.13 618.71 2154.97 618.71 C 2145.79 618.71 2138.79 616.48 2138.18 614.37 Z M 2173.18 614.07 C 2173.18 611.6 2166.09 608 2154.97 608 C 2143.85 608 2136.76 611.6 2136.76 614.07 L 2136.82 614.61 L 2140.79 643.56 C 2140.88 646.79 2149.51 648 2154.97 648 C 2161.73 648 2168.92 646.44 2169.01 643.56 L 2170.72 631.49 C 2171.68 631.72 2172.46 631.83 2173.09 631.83 C 2173.93 631.83 2174.51 631.63 2174.85 631.21 C 2175.14 630.88 2175.24 630.47 2175.16 630.03 C 2174.98 629.04 2173.8 627.97 2171.41 626.61 L 2173.11 614.64 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 660px; margin-left: 2156px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    artifact bucket
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="2156" y="672" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        artifact...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 2484.78 439.37 L 2484.02 549.72" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 2484.82 434.12 L 2488.27 441.14 L 2484.78 439.37 L 2481.27 441.09 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 2460 383 L 2510 383 L 2510 433 L 2460 433 Z" fill="#e7157b" stroke="none" pointer-events="all" style="fill: light-dark(rgb(231, 21, 123), rgb(255, 129, 217));"/>
            <path d="M 2494.75 426.57 C 2493.22 426.57 2491.98 425.32 2491.98 423.78 C 2491.98 422.24 2493.22 420.99 2494.75 420.99 C 2496.29 420.99 2497.53 422.24 2497.53 423.78 C 2497.53 425.32 2496.29 426.57 2494.75 426.57 Z M 2488.67 414.43 L 2481.28 414.43 L 2477.59 408 L 2481.28 401.57 L 2488.67 401.57 L 2492.36 408 Z M 2476.16 395.01 C 2474.63 395.01 2473.38 393.76 2473.38 392.22 C 2473.38 390.68 2474.63 389.43 2476.16 389.43 C 2477.69 389.43 2478.94 390.68 2478.94 392.22 C 2478.94 393.76 2477.69 395.01 2476.16 395.01 Z M 2494.75 419.56 C 2494.23 419.56 2493.72 419.66 2493.26 419.84 L 2490.24 414.72 L 2490.08 414.82 L 2493.79 408.36 C 2493.92 408.14 2493.92 407.86 2493.79 407.64 L 2489.69 400.5 C 2489.56 400.28 2489.33 400.14 2489.08 400.14 L 2481.61 400.14 L 2481.64 400.13 L 2478.88 395.43 C 2479.78 394.66 2480.36 393.51 2480.36 392.22 C 2480.36 389.89 2478.47 388 2476.16 388 C 2473.84 388 2471.96 389.89 2471.96 392.22 C 2471.96 394.55 2473.84 396.44 2476.16 396.44 C 2476.69 396.44 2477.19 396.34 2477.65 396.16 L 2480.24 400.54 L 2476.16 407.64 C 2476.03 407.86 2476.03 408.14 2476.16 408.36 L 2480.26 415.5 C 2480.39 415.72 2480.62 415.86 2480.87 415.86 L 2489.08 415.86 C 2489.13 415.86 2489.19 415.85 2489.25 415.83 L 2492.04 420.57 C 2491.13 421.34 2490.56 422.49 2490.56 423.78 C 2490.56 426.11 2492.44 428 2494.75 428 C 2497.07 428 2498.95 426.11 2498.95 423.78 C 2498.95 421.45 2497.07 419.56 2494.75 419.56 Z M 2498.99 402.36 C 2497.46 402.36 2496.21 401.11 2496.21 399.57 C 2496.21 398.03 2497.46 396.78 2498.99 396.78 C 2500.52 396.78 2501.77 398.03 2501.77 399.57 C 2501.77 401.11 2500.52 402.36 2498.99 402.36 Z M 2504.19 407.64 L 2501.49 402.95 C 2502.52 402.18 2503.19 400.95 2503.19 399.57 C 2503.19 397.24 2501.3 395.35 2498.99 395.35 C 2498.4 395.35 2497.84 395.47 2497.33 395.69 L 2495.13 391.86 C 2495 391.64 2494.77 391.51 2494.52 391.51 L 2485.71 391.51 L 2485.71 392.94 L 2494.11 392.94 L 2496.14 396.48 C 2495.31 397.25 2494.79 398.35 2494.79 399.57 C 2494.79 401.9 2496.67 403.79 2498.99 403.79 C 2499.42 403.79 2499.84 403.72 2500.23 403.6 L 2502.76 408 L 2499.21 414.18 L 2500.44 414.89 L 2504.19 408.36 C 2504.32 408.14 2504.32 407.86 2504.19 407.64 Z M 2471.59 418.89 C 2470.06 418.89 2468.81 417.64 2468.81 416.1 C 2468.81 414.56 2470.06 413.31 2471.59 413.31 C 2473.12 413.31 2474.37 414.56 2474.37 416.1 C 2474.37 417.64 2473.12 418.89 2471.59 418.89 Z M 2473.91 419.61 C 2475.04 418.86 2475.79 417.56 2475.79 416.1 C 2475.79 413.77 2473.91 411.88 2471.59 411.88 C 2470.92 411.88 2470.29 412.04 2469.73 412.32 L 2467.25 408 L 2471.36 400.84 L 2470.13 400.12 L 2465.81 407.64 C 2465.68 407.86 2465.68 408.14 2465.81 408.36 L 2468.57 413.17 C 2467.85 413.93 2467.39 414.96 2467.39 416.1 C 2467.39 418.43 2469.28 420.32 2471.59 420.32 C 2471.94 420.32 2472.28 420.27 2472.6 420.19 L 2474.87 424.14 C 2475 424.36 2475.23 424.49 2475.48 424.49 L 2484.29 424.49 L 2484.29 423.07 L 2475.89 423.07 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 440px; margin-left: 2485px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    EventBridge
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="2485" y="452" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        EventBri...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 2460 408 L 2433.37 408" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 2428.12 408 L 2435.12 404.5 L 2433.37 408 L 2435.12 411.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 2156 93 L 2156 143.56 Q 2156 153.56 2146 153.55 L 2072.37 153.5" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 2067.12 153.5 L 2074.12 150.01 L 2072.37 153.5 L 2074.12 157.01 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 136px; margin-left: 2157px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    コピー
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="2157" y="140" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        コピー
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 2131 43 L 2181 43 L 2181 93 L 2131 93 Z" fill="#7aa116" stroke="none" pointer-events="all" style="fill: light-dark(rgb(122, 161, 22), rgb(97, 130, 11));"/>
            <path d="M 2170.92 70.07 L 2171.2 68.14 C 2173.73 69.65 2173.76 70.28 2173.76 70.29 C 2173.76 70.3 2173.32 70.66 2170.92 70.07 Z M 2169.54 69.68 C 2165.16 68.36 2159.08 65.56 2156.61 64.4 C 2156.61 64.39 2156.62 64.38 2156.62 64.37 C 2156.62 63.42 2155.85 62.66 2154.9 62.66 C 2153.95 62.66 2153.18 63.42 2153.18 64.37 C 2153.18 65.32 2153.95 66.09 2154.9 66.09 C 2155.32 66.09 2155.69 65.93 2155.99 65.69 C 2158.89 67.06 2164.93 69.81 2169.33 71.11 L 2167.59 83.4 C 2167.59 83.43 2167.58 83.47 2167.58 83.5 C 2167.58 84.58 2162.79 86.57 2154.97 86.57 C 2147.05 86.57 2142.21 84.58 2142.21 83.5 C 2142.21 83.47 2142.21 83.44 2142.21 83.4 L 2138.57 56.83 C 2141.72 59 2148.49 60.14 2154.97 60.14 C 2161.44 60.14 2168.2 59 2171.36 56.84 Z M 2138.18 54.06 C 2138.24 53.12 2143.64 49.43 2154.97 49.43 C 2166.3 49.43 2171.7 53.12 2171.76 54.06 L 2171.76 54.38 C 2171.13 56.48 2164.13 58.71 2154.97 58.71 C 2145.79 58.71 2138.79 56.48 2138.18 54.37 Z M 2173.18 54.07 C 2173.18 51.6 2166.09 48 2154.97 48 C 2143.85 48 2136.76 51.6 2136.76 54.07 L 2136.82 54.61 L 2140.79 83.56 C 2140.88 86.79 2149.51 88 2154.97 88 C 2161.73 88 2168.92 86.44 2169.01 83.56 L 2170.72 71.49 C 2171.68 71.72 2172.46 71.83 2173.09 71.83 C 2173.93 71.83 2174.51 71.63 2174.85 71.21 C 2175.14 70.88 2175.24 70.47 2175.16 70.03 C 2174.98 69.04 2173.8 67.97 2171.41 66.61 L 2173.11 54.64 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 100px; margin-left: 2156px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    マスターバケット
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="2156" y="112" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        マスターバケット
                    </text>
                </switch>
            </g>
        </g>
        <g/>
        <g/>
        <g>
            <rect x="2269" y="105" width="156" height="122" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 2269 105 L 2319 105 L 2319 155 L 2269 155 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
            <path d="M 2310.48 135.44 L 2304.87 132.07 L 2304.87 124.06 C 2304.87 123.81 2304.74 123.58 2304.52 123.45 L 2296.45 118.75 L 2296.45 111.96 L 2310.48 120.25 Z M 2311.54 119.25 L 2296.11 110.13 C 2295.89 110 2295.63 110 2295.41 110.12 C 2295.19 110.25 2295.05 110.48 2295.05 110.73 L 2295.05 119.15 C 2295.05 119.4 2295.18 119.63 2295.4 119.75 L 2303.47 124.46 L 2303.47 132.47 C 2303.47 132.72 2303.6 132.94 2303.81 133.07 L 2310.82 137.28 C 2310.93 137.34 2311.05 137.38 2311.18 137.38 C 2311.3 137.38 2311.42 137.35 2311.52 137.29 C 2311.74 137.16 2311.88 136.93 2311.88 136.68 L 2311.88 119.85 C 2311.88 119.6 2311.75 119.37 2311.54 119.25 Z M 2293.96 148.5 L 2277.52 139.76 L 2277.52 120.25 L 2291.55 111.96 L 2291.55 118.76 L 2284.16 123.46 C 2283.96 123.59 2283.83 123.82 2283.83 124.06 L 2283.83 135.98 C 2283.83 136.24 2283.98 136.48 2284.21 136.6 L 2293.64 141.51 C 2293.85 141.61 2294.09 141.61 2294.29 141.51 L 2303.44 136.78 L 2309.07 140.16 Z M 2310.84 139.58 L 2303.83 135.37 C 2303.62 135.25 2303.36 135.24 2303.14 135.35 L 2293.97 140.09 L 2285.24 135.55 L 2285.24 124.44 L 2292.62 119.74 C 2292.83 119.61 2292.95 119.39 2292.95 119.15 L 2292.95 110.73 C 2292.95 110.48 2292.81 110.25 2292.59 110.12 C 2292.38 110 2292.11 110 2291.89 110.13 L 2276.46 119.25 C 2276.25 119.37 2276.12 119.6 2276.12 119.85 L 2276.12 140.18 C 2276.12 140.44 2276.26 140.68 2276.49 140.8 L 2293.64 149.92 C 2293.74 149.97 2293.85 150 2293.97 150 C 2294.08 150 2294.2 149.97 2294.31 149.91 L 2310.82 140.8 C 2311.04 140.68 2311.17 140.45 2311.18 140.2 C 2311.18 139.94 2311.05 139.71 2310.84 139.58 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 130px; margin-left: 2321px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    ECS
                                    <span style="background-color: transparent;">
                                        クラスター
                                    </span>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="2321" y="134" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">
                        ECSクラスター
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <image x="2323.5" y="154.5" width="46" height="46" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLBAMAAADKYGfZAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAwUExURQAAAO9wAOxwAOxwAOtwAO1yAO1xAO1xAO5xAO5xAO9wAOpwAOtwAO1wAO5yAO1yACH696YAAAAQdFJOUwAQUGBAn//fz78gMIBwr48dwG1XAAAACXBIWXMAABcRAAAXEQHKJvM/AAAAwklEQVRIx+3VPQ4BQRiA4XeXXT+bvQOjQiNxAVG4g1LCrIaoHEGipJtQOII4gUriAmpRuIALMKvfkZBovjeZqZ5JJlPMhyT9Jq/WdtQhB4F2NSKEgpMlb7Y0mS0SSpZ1si/fS+z+e5ZXOyK1p6xeR1TltarQbcChmTL/zQJ9JNZX8nqGpwcwH8J9ApdpykJhwoQJ+57FpoVvjkTmimdW0N/AeQ31bcr+91s+sqfHKbHj48O5UHSysWWRcXWzDyJJXwdPyDmISQ6XpuMAAAAASUVORK5CYII=" preserveAspectRatio="none"/>
        </g>
        <g>
            <g fill="#000000" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));">
                <rect fill="#ffffff" stroke="none" x="2323" y="209" width="50" height="15" stroke-width="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/>
                <text x="2346.5" y="218.5">
                    コンテナ
                </text>
            </g>
        </g>
        <g>
            <path d="M 2347 568 L 2347.11 289.11 Q 2347.11 279.11 2357.11 279.11 L 2499.11 279.11 Q 2509.11 279.11 2509.1 269.11 L 2509.01 199.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 2509 194.12 L 2512.51 201.11 L 2509.01 199.37 L 2505.51 201.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 284px; margin-left: 2431px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ecspresso deploy
                                    <div>
                                        (コンテナ更新)
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="2431" y="288" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        ecspresso deploy...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1734 28 L 1900 28 L 1900 103 L 1837 103 L 1797.08 133 L 1817 103 L 1734 103 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 164px; height: 1px; padding-top: 65px; margin-left: 1735px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    ➃GHAからECSをデプロイ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1817" y="69" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ➃GHAからECSをデプロイ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1731" y="143" width="46" height="46" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 1735.21 186.91 C 1735.73 176.54 1743.96 168.28 1754 168.28 C 1757.4 168.28 1760.72 169.23 1763.62 171.04 C 1769.01 174.4 1772.46 180.41 1772.79 186.91 Z M 1743.75 155.42 C 1743.75 149.72 1748.35 145.09 1754 145.09 C 1759.65 145.09 1764.25 149.72 1764.25 155.42 C 1764.25 161.11 1759.65 165.75 1754 165.75 C 1748.35 165.75 1743.75 161.11 1743.75 155.42 Z M 1764.73 169.27 C 1762.92 168.14 1760.95 167.32 1758.91 166.81 C 1763.28 164.89 1766.34 160.51 1766.34 155.42 C 1766.34 148.57 1760.81 143 1754 143 C 1747.19 143 1741.66 148.57 1741.66 155.42 C 1741.66 160.52 1744.73 164.9 1749.1 166.81 C 1739.93 169.12 1733.09 177.71 1733.09 187.95 C 1733.09 188.53 1733.56 189 1734.14 189 L 1773.86 189 C 1774.44 189 1774.91 188.53 1774.91 187.95 C 1774.91 180.34 1771.01 173.18 1764.73 169.27 Z" fill="#232f3d" stroke="none" pointer-events="all" style="fill: light-dark(rgb(35, 47, 61), rgb(190, 200, 212));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 196px; margin-left: 1754px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    app team
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1754" y="208" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        app team
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="2412" y="603" width="80" height="50" fill="none" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 628px; margin-left: 2413px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; max-height: 46px; overflow: hidden; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    <h1 style="margin-top: 0px;">
                                        ・・・
                                    </h1>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="2452" y="632" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ・・・
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1773.91 289.5 L 1798.63 289.5" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1803.88 289.5 L 1796.88 293 L 1798.63 289.5 L 1796.88 286 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 290px; margin-left: 1789px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    申請
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1789" y="293" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        申請
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1952 289.5 L 1975.63 289.89" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1980.88 289.98 L 1973.82 293.36 L 1975.63 289.89 L 1973.94 286.37 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1868 289.5 L 1895.63 289.5" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1900.88 289.5 L 1893.88 293 L 1895.63 289.5 L 1893.88 286 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="1730" y="266.5" width="46" height="46" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 1734.21 310.41 C 1734.73 300.04 1742.96 291.78 1753 291.78 C 1756.4 291.78 1759.72 292.73 1762.62 294.54 C 1768.01 297.9 1771.46 303.91 1771.79 310.41 Z M 1742.75 278.92 C 1742.75 273.22 1747.35 268.59 1753 268.59 C 1758.65 268.59 1763.25 273.22 1763.25 278.92 C 1763.25 284.61 1758.65 289.25 1753 289.25 C 1747.35 289.25 1742.75 284.61 1742.75 278.92 Z M 1763.73 292.77 C 1761.92 291.64 1759.95 290.82 1757.91 290.31 C 1762.28 288.39 1765.34 284.01 1765.34 278.92 C 1765.34 272.07 1759.81 266.5 1753 266.5 C 1746.19 266.5 1740.66 272.07 1740.66 278.92 C 1740.66 284.02 1743.73 288.4 1748.1 290.31 C 1738.93 292.62 1732.09 301.21 1732.09 311.45 C 1732.09 312.03 1732.56 312.5 1733.14 312.5 L 1772.86 312.5 C 1773.44 312.5 1773.91 312.03 1773.91 311.45 C 1773.91 303.84 1770.01 296.68 1763.73 292.77 Z" fill="#232f3d" stroke="none" pointer-events="all" style="fill: light-dark(rgb(35, 47, 61), rgb(190, 200, 212));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 320px; margin-left: 1753px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    app team
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1753" y="332" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        app team
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <image x="1804.5" y="257.5" width="63" height="63" xlink:href="data:image/png;base64,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" preserveAspectRatio="none"/>
        </g>
        <g>
            <g fill="#000000" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));">
                <rect fill="#ffffff" stroke="none" x="1803" y="329" width="69" height="15" stroke-width="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/>
                <text x="1836" y="338.5">
                    GoogleForm
                </text>
            </g>
        </g>
        <g>
            <path d="M 1902 264.5 L 1952 264.5 L 1952 314.5 L 1902 314.5 Z" fill="#c925d1" stroke="none" pointer-events="all" style="fill: light-dark(rgb(201, 37, 209), rgb(255, 124, 255));"/>
            <path d="M 1943.03 269.5 L 1910.97 269.5 C 1908.78 269.5 1907 271.28 1907 273.47 L 1907 282.98 C 1907 285.17 1908.78 286.95 1910.97 286.95 L 1912.09 286.95 L 1912.09 308.77 C 1912.09 309.17 1912.42 309.5 1912.82 309.5 L 1940.45 309.5 C 1940.86 309.5 1941.18 309.17 1941.18 308.77 L 1941.18 286.95 L 1943.03 286.95 C 1945.22 286.95 1947 285.17 1947 282.98 L 1947 273.47 C 1947 271.28 1945.22 269.5 1943.03 269.5 Z M 1913.55 308.05 L 1913.55 286.95 L 1939.73 286.95 L 1939.73 308.05 Z M 1943.03 285.5 L 1910.97 285.5 C 1909.59 285.5 1908.45 284.37 1908.45 282.98 L 1908.45 282.59 L 1917.18 282.59 L 1917.18 281.14 L 1908.45 281.14 L 1908.45 273.47 C 1908.45 272.08 1909.59 270.95 1910.97 270.95 L 1943.03 270.95 C 1944.41 270.95 1945.55 272.08 1945.55 273.47 L 1945.55 281.14 L 1925.18 281.14 L 1925.18 282.59 L 1945.55 282.59 L 1945.55 282.98 C 1945.55 284.37 1944.41 285.5 1943.03 285.5 Z M 1916.1 297.17 C 1916.1 296.96 1916.19 296.76 1916.34 296.62 L 1920.95 292.55 L 1921.91 293.64 L 1917.93 297.16 L 1921.89 300.58 L 1920.94 301.68 L 1916.35 297.72 C 1916.19 297.58 1916.1 297.38 1916.1 297.17 Z M 1930.69 300.61 L 1934.69 297.11 L 1930.69 293.64 L 1931.64 292.54 L 1936.27 296.55 C 1936.43 296.69 1936.52 296.89 1936.52 297.1 C 1936.52 297.31 1936.43 297.51 1936.27 297.65 L 1931.65 301.71 Z M 1924.21 304.53 L 1922.87 303.98 L 1928.39 290.51 L 1929.73 291.06 Z M 1919.36 282.59 L 1923 282.59 L 1923 281.14 L 1919.36 281.14 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 322px; margin-left: 1927px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    CodePipeline
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1927" y="334" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        CodePipe...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1764 349 L 1930 349 L 1930 413 L 1867 413 L 1800.52 443 L 1847 413 L 1764 413 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" transform="rotate(180,1847,396)" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 164px; height: 1px; padding-top: 412px; margin-left: 1765px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    ➄CodeDeployをデプロイ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1847" y="415" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ➄CodeDeployをデプロイ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 2484 168 L 2436.37 168" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 2431.12 168 L 2438.12 164.5 L 2436.37 168 L 2438.12 171.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 2484 143 L 2534 143 L 2534 193 L 2484 193 Z" fill="#c925d1" stroke="none" pointer-events="all" style="fill: light-dark(rgb(201, 37, 209), rgb(255, 124, 255));"/>
            <path d="M 2509.02 148 C 2498.03 148 2489.43 156.38 2489.01 167.5 C 2489 167.76 2489.15 168 2489.37 168.13 L 2496.55 172.86 L 2497.32 171.67 L 2490.52 167.21 C 2490.88 165.69 2491.98 164.55 2493.57 163.98 C 2494.33 163.7 2495.19 163.55 2496.14 163.55 C 2497.68 163.55 2499.13 164.14 2500.21 165.19 L 2500.34 165.32 C 2501.31 166.26 2501.88 167.98 2501.88 167.99 C 2501.89 168.03 2502.84 171.61 2502.84 171.61 L 2504.2 171.24 L 2503.29 167.84 L 2503.29 167.76 C 2503.63 164.66 2506.47 163.55 2509.01 163.55 C 2510.55 163.55 2512 164.14 2513.09 165.19 C 2513.09 165.19 2514.61 166.37 2514.73 167.75 L 2514.73 167.85 L 2513.81 171.23 L 2515.17 171.61 L 2516.13 168.08 C 2516.13 168.05 2516.13 168.02 2516.14 167.99 C 2516.33 165.29 2518.58 163.55 2521.88 163.55 C 2523.42 163.55 2524.87 164.14 2525.95 165.19 C 2526.79 166.02 2527.38 166.64 2527.55 167.25 L 2521.49 171.51 L 2522.29 172.67 L 2528.7 168.16 C 2528.87 168.03 2529 167.53 2529 167.5 C 2528.8 156.39 2520.23 148.01 2509.02 148 Z M 2501.91 164.89 C 2501.72 164.7 2501.53 164.5 2501.32 164.3 L 2501.19 164.18 C 2499.84 162.86 2498.05 162.14 2496.14 162.14 C 2493.97 162.14 2492.14 162.8 2490.87 163.94 C 2492.54 156.43 2498.43 150.84 2506 149.66 C 2503.74 152.31 2502.18 157.93 2501.91 164.89 Z M 2514.31 164.42 L 2514.06 164.18 C 2512.71 162.86 2510.92 162.14 2509.01 162.14 C 2506.58 162.14 2504.6 162.96 2503.34 164.35 C 2503.8 155.43 2506.54 149.43 2509.02 149.42 C 2509.02 149.42 2509.02 149.42 2509.03 149.42 C 2511.55 149.44 2514.33 155.65 2514.72 164.82 C 2514.58 164.69 2514.45 164.56 2514.31 164.42 Z M 2526.93 164.18 C 2525.58 162.86 2523.79 162.14 2521.88 162.14 C 2519.4 162.14 2517.37 163.02 2516.11 164.5 C 2515.8 157.71 2514.25 152.26 2512.03 149.66 C 2519.97 150.86 2525.99 156.72 2527.32 164.57 C 2527.19 164.44 2527.06 164.31 2526.93 164.18 Z M 2502.61 180.39 C 2502.47 180.24 2502.39 180.04 2502.4 179.84 C 2502.42 179.63 2502.52 179.44 2502.68 179.32 L 2506.01 176.8 L 2506.85 177.94 L 2504.18 179.95 L 2506.57 182.3 L 2505.59 183.31 Z M 2514.64 180.64 L 2512.25 178.29 L 2513.23 177.27 L 2516.21 180.2 C 2516.36 180.34 2516.43 180.55 2516.42 180.75 C 2516.41 180.96 2516.31 181.15 2516.14 181.27 L 2512.81 183.79 L 2511.97 182.65 Z M 2506.62 185.33 L 2510.86 175.6 L 2512.15 176.17 L 2507.9 185.9 Z M 2519.18 173.13 L 2499.53 173.13 C 2499.15 173.13 2498.83 173.45 2498.83 173.84 L 2498.83 187.29 C 2498.83 187.68 2499.15 188 2499.53 188 L 2519.18 188 C 2519.57 188 2519.88 187.68 2519.88 187.29 L 2519.88 173.84 C 2519.88 173.45 2519.57 173.13 2519.18 173.13 Z M 2500.24 186.58 L 2500.24 174.55 L 2518.48 174.55 L 2518.48 186.58 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 200px; margin-left: 2509px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    CodeDeploy
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="2509" y="212" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        CodeDepl...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 2375 383 L 2425 383 L 2425 433 L 2375 433 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
            <path d="M 2391.45 426.57 L 2382.58 426.57 L 2392.39 406.07 L 2396.84 415.22 Z M 2393.03 404.11 C 2392.91 403.86 2392.66 403.71 2392.39 403.71 L 2392.39 403.71 C 2392.11 403.71 2391.86 403.87 2391.74 404.11 L 2380.8 426.98 C 2380.7 427.2 2380.71 427.46 2380.84 427.67 C 2380.97 427.87 2381.2 428 2381.45 428 L 2391.91 428 C 2392.18 428 2392.43 427.84 2392.55 427.59 L 2398.28 415.52 C 2398.37 415.32 2398.37 415.1 2398.27 414.9 Z M 2417.87 426.57 L 2409.06 426.57 L 2394.92 396.98 C 2394.8 396.73 2394.55 396.57 2394.28 396.57 L 2388.51 396.57 L 2388.52 389.43 L 2399.82 389.43 L 2413.89 419.02 C 2414.01 419.27 2414.26 419.43 2414.54 419.43 L 2417.87 419.43 Z M 2418.59 418 L 2414.99 418 L 2400.92 388.41 C 2400.8 388.16 2400.55 388 2400.27 388 L 2387.8 388 C 2387.41 388 2387.09 388.32 2387.09 388.71 L 2387.08 397.29 C 2387.08 397.48 2387.15 397.66 2387.29 397.79 C 2387.42 397.93 2387.6 398 2387.79 398 L 2393.82 398 L 2407.96 427.59 C 2408.08 427.84 2408.33 428 2408.61 428 L 2418.59 428 C 2418.98 428 2419.3 427.68 2419.3 427.29 L 2419.3 418.71 C 2419.3 418.32 2418.98 418 2418.59 418 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 440px; margin-left: 2400px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    Lambda
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="2400" y="452" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Lambda
                    </text>
                </switch>
            </g>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>