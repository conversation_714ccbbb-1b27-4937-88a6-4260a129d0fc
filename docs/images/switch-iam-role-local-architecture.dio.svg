<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="975px" height="322px" viewBox="-0.5 -0.5 975 322" content="&lt;mxfile&gt;&lt;diagram id=&quot;LoVL-qIVPpizgKBHFZMk&quot; name=&quot;250122&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-4d72f3-1-3334b9-1-s-0">
            <stop offset="0%" style="stop-color: rgb(51, 52, 185); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(77, 114, 243); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f78e04-1-d05c17-1-s-0">
            <stop offset="0%" style="stop-color: rgb(208, 92, 23); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(247, 142, 4); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-60a337-1-277116-1-s-0">
            <stop offset="0%" style="stop-color: rgb(39, 113, 22); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(96, 163, 55); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <path d="M 574 90 L 603.94 90 Q 613.94 90 613.94 100 L 613.94 225.06 Q 613.94 235.06 623.94 235.05 L 673.9 235.01" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 680.65 235 L 671.65 239.51 L 673.9 235.01 L 671.64 230.51 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 161px; margin-left: 664px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                操作権限無し
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="664" y="164" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    操作権限無し
                </text>
            </switch>
        </g>
        <path d="M 254 60 L 294 60 L 294 100 L 254 100 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 277.36 91.46 L 285.87 91.46 L 285.87 90.32 L 277.36 90.32 Z M 270.9 91.33 L 276.58 86.79 C 276.71 86.69 276.79 86.52 276.79 86.35 C 276.79 86.18 276.71 86.02 276.58 85.91 L 270.9 81.37 L 270.19 82.25 L 275.32 86.35 L 270.19 90.45 Z M 266.58 94.86 L 288.71 94.86 L 288.71 77.84 L 266.58 77.84 Z M 289.85 77.27 L 289.85 95.43 C 289.85 95.75 289.59 96 289.28 96 L 266.01 96 C 265.69 96 265.44 95.75 265.44 95.43 L 265.44 77.27 C 265.44 76.96 265.69 76.7 266.01 76.7 L 289.28 76.7 C 289.59 76.7 289.85 76.96 289.85 77.27 Z M 264.83 66.02 C 263.29 66.68 262.08 68.57 262.08 70.32 L 262.12 70.94 C 262.13 71.21 261.96 71.46 261.69 71.52 C 260.79 71.75 259.29 72.47 259.29 74.63 C 259.29 76.26 260.17 77.17 260.91 77.63 C 260.99 77.68 261.27 77.77 261.53 77.84 L 263.17 77.84 L 263.17 78.97 L 261.47 78.97 C 261.43 78.97 261.38 78.97 261.34 78.96 C 261.18 78.92 260.61 78.79 260.3 78.59 C 259.5 78.09 258.15 76.9 258.15 74.63 C 258.15 72.68 259.19 71.19 260.96 70.56 L 260.95 70.35 C 260.95 68.11 262.43 65.81 264.39 64.98 C 266.68 64 269.12 64.49 270.89 66.27 C 271.39 66.78 271.81 67.38 272.14 68.06 C 272.84 67.57 273.74 67.42 274.58 67.7 C 275.7 68.07 276.42 69.08 276.54 70.45 C 277.31 70.65 278.51 71.13 279.26 72.26 C 279.72 72.93 279.95 73.74 279.95 74.66 L 278.82 74.66 C 278.82 73.97 278.65 73.38 278.32 72.89 C 277.63 71.87 276.39 71.55 275.89 71.47 C 275.58 71.41 275.38 71.13 275.42 70.83 C 275.42 69.79 274.98 69.03 274.22 68.78 C 273.56 68.56 272.82 68.8 272.39 69.38 C 272.27 69.55 272.06 69.64 271.85 69.61 C 271.64 69.58 271.47 69.43 271.4 69.24 C 271.09 68.37 270.65 67.64 270.09 67.07 C 269.4 66.38 267.5 64.89 264.83 66.02 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 107px; margin-left: 274px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                CloudShell
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="274" y="119" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    CloudS...
                </text>
            </switch>
        </g>
        <path d="M 194 0 L 354 0 L 354 320 L 194 320 Z" fill="none" stroke="#232f3e" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 200.09 7.18 C 200.01 7.18 199.93 7.19 199.85 7.19 C 199.5 7.19 199.15 7.23 198.81 7.32 C 198.53 7.39 198.25 7.49 197.98 7.62 C 197.9 7.65 197.84 7.7 197.79 7.76 C 197.75 7.83 197.74 7.91 197.74 7.99 L 197.74 8.32 C 197.74 8.46 197.78 8.53 197.88 8.53 L 197.99 8.53 L 198.22 8.44 C 198.45 8.35 198.69 8.27 198.94 8.21 C 199.17 8.16 199.41 8.13 199.65 8.13 C 200.04 8.09 200.43 8.2 200.74 8.44 C 200.97 8.74 201.09 9.12 201.05 9.5 L 201.05 9.99 C 200.78 9.93 200.54 9.88 200.29 9.84 C 200.05 9.81 199.81 9.79 199.57 9.79 C 198.98 9.76 198.4 9.94 197.94 10.31 C 197.54 10.65 197.32 11.15 197.34 11.68 C 197.31 12.15 197.49 12.62 197.82 12.96 C 198.18 13.29 198.66 13.46 199.15 13.44 C 199.91 13.45 200.63 13.11 201.11 12.51 C 201.18 12.66 201.24 12.79 201.31 12.91 C 201.38 13.02 201.46 13.12 201.55 13.21 C 201.6 13.27 201.67 13.31 201.75 13.31 C 201.81 13.31 201.87 13.29 201.92 13.25 L 202.34 12.97 C 202.41 12.93 202.46 12.86 202.47 12.77 C 202.47 12.72 202.45 12.67 202.42 12.62 C 202.34 12.47 202.26 12.31 202.21 12.14 C 202.15 11.95 202.12 11.75 202.13 11.55 L 202.14 9.37 C 202.2 8.77 202 8.18 201.59 7.74 C 201.17 7.39 200.64 7.19 200.09 7.18 Z M 213.89 7.19 C 213.78 7.19 213.68 7.19 213.57 7.2 C 213.29 7.2 213 7.24 212.73 7.31 C 212.47 7.38 212.23 7.5 212.01 7.66 C 211.82 7.81 211.66 7.99 211.54 8.21 C 211.42 8.43 211.35 8.67 211.36 8.92 C 211.36 9.27 211.48 9.61 211.69 9.89 C 211.97 10.22 212.34 10.46 212.76 10.56 L 213.72 10.87 C 213.97 10.93 214.2 11.05 214.39 11.22 C 214.51 11.35 214.58 11.51 214.57 11.69 C 214.58 11.94 214.45 12.18 214.23 12.31 C 213.93 12.48 213.6 12.56 213.26 12.54 C 212.99 12.54 212.72 12.51 212.46 12.45 C 212.22 12.4 211.98 12.32 211.75 12.22 L 211.59 12.15 C 211.54 12.14 211.5 12.14 211.46 12.15 C 211.36 12.15 211.31 12.22 211.31 12.36 L 211.31 12.69 C 211.31 12.76 211.32 12.82 211.35 12.89 C 211.4 12.97 211.47 13.03 211.56 13.07 C 211.8 13.19 212.06 13.28 212.32 13.34 C 212.66 13.41 213 13.45 213.35 13.45 L 213.33 13.46 C 213.66 13.45 213.98 13.4 214.29 13.3 C 214.55 13.22 214.8 13.09 215.01 12.92 C 215.21 12.77 215.38 12.57 215.49 12.34 C 215.61 12.1 215.67 11.83 215.66 11.56 C 215.67 11.23 215.56 10.9 215.36 10.63 C 215.09 10.32 214.73 10.09 214.33 9.99 L 213.39 9.69 C 213.13 9.61 212.88 9.49 212.67 9.32 C 212.54 9.2 212.47 9.03 212.47 8.85 C 212.46 8.61 212.58 8.38 212.79 8.25 C 213.06 8.11 213.36 8.05 213.67 8.06 C 214.11 8.06 214.55 8.14 214.96 8.32 C 215.04 8.37 215.12 8.4 215.21 8.41 C 215.31 8.41 215.36 8.34 215.36 8.19 L 215.36 7.88 C 215.37 7.8 215.35 7.72 215.31 7.66 C 215.25 7.59 215.18 7.54 215.11 7.49 L 214.83 7.38 L 214.45 7.27 L 214.01 7.2 C 213.97 7.2 213.93 7.19 213.89 7.19 Z M 210.02 7.36 C 209.94 7.35 209.86 7.38 209.79 7.42 C 209.72 7.5 209.68 7.59 209.66 7.69 L 208.51 12.14 L 207.47 7.71 C 207.45 7.61 207.41 7.52 207.34 7.44 C 207.26 7.39 207.17 7.37 207.07 7.38 L 206.54 7.38 C 206.44 7.37 206.35 7.39 206.27 7.44 C 206.2 7.51 206.15 7.61 206.14 7.71 L 205.09 12.14 L 203.97 7.7 C 203.95 7.6 203.91 7.51 203.84 7.44 C 203.76 7.39 203.67 7.36 203.58 7.37 L 202.92 7.37 C 202.81 7.37 202.76 7.43 202.76 7.54 C 202.77 7.63 202.79 7.72 202.82 7.81 L 204.38 12.95 C 204.4 13.05 204.45 13.14 204.52 13.21 C 204.6 13.26 204.69 13.29 204.78 13.28 L 205.36 13.26 C 205.46 13.27 205.55 13.25 205.63 13.19 C 205.7 13.12 205.74 13.03 205.76 12.93 L 206.79 8.64 L 207.82 12.93 C 207.83 13.03 207.88 13.12 207.95 13.19 C 208.03 13.25 208.12 13.27 208.21 13.26 L 208.78 13.26 C 208.88 13.27 208.97 13.25 209.04 13.2 C 209.11 13.13 209.16 13.03 209.18 12.94 L 210.79 7.79 C 210.84 7.72 210.84 7.63 210.84 7.63 C 210.84 7.59 210.84 7.56 210.84 7.52 C 210.84 7.48 210.82 7.43 210.79 7.4 C 210.76 7.37 210.72 7.35 210.67 7.36 L 210.05 7.36 C 210.04 7.36 210.03 7.36 210.02 7.36 Z M 199.65 10.62 C 199.7 10.62 199.75 10.62 199.8 10.62 L 200.43 10.62 C 200.64 10.64 200.85 10.67 201.06 10.71 L 201.06 11.01 C 201.07 11.21 201.05 11.4 201 11.59 C 200.96 11.75 200.88 11.9 200.77 12.01 C 200.61 12.21 200.39 12.36 200.14 12.44 C 199.91 12.52 199.67 12.56 199.43 12.56 C 199.18 12.6 198.93 12.53 198.73 12.37 C 198.55 12.18 198.46 11.92 198.49 11.66 C 198.47 11.36 198.59 11.08 198.81 10.89 C 199.06 10.72 199.35 10.62 199.65 10.62 Z M 215.04 14.72 C 214.34 14.73 213.51 14.89 212.88 15.33 C 212.69 15.46 212.72 15.63 212.94 15.63 C 213.64 15.54 215.21 15.35 215.5 15.71 C 215.78 16.06 215.19 17.54 214.94 18.21 C 214.86 18.41 215.03 18.49 215.21 18.34 C 216.39 17.36 216.72 15.3 216.46 15 C 216.32 14.85 215.74 14.71 215.04 14.72 Z M 196.65 15.1 C 196.5 15.12 196.42 15.3 196.58 15.44 C 199.29 17.89 202.82 19.23 206.48 19.21 C 209.37 19.22 212.2 18.36 214.59 16.74 C 214.95 16.47 214.63 16.07 214.26 16.23 C 211.87 17.24 209.3 17.76 206.71 17.77 C 203.23 17.78 199.82 16.87 196.81 15.14 C 196.75 15.11 196.69 15.1 196.65 15.1 Z M 194 0 L 219 0 L 219 25 L 194 25 Z" fill="#232f3e" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 128px; height: 1px; padding-top: 7px; margin-left: 226px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                踏み台アカウント
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="226" y="19" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    踏み台アカウント
                </text>
            </switch>
        </g>
        <path d="M 434 0 L 974 0 L 974 320 L 434 320 Z" fill="none" stroke="#232f3e" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 440.09 7.18 C 440.01 7.18 439.93 7.19 439.85 7.19 C 439.5 7.19 439.15 7.23 438.81 7.32 C 438.53 7.39 438.25 7.49 437.98 7.62 C 437.9 7.65 437.84 7.7 437.79 7.76 C 437.75 7.83 437.74 7.91 437.74 7.99 L 437.74 8.32 C 437.74 8.46 437.78 8.53 437.88 8.53 L 437.99 8.53 L 438.22 8.44 C 438.45 8.35 438.69 8.27 438.94 8.21 C 439.17 8.16 439.41 8.13 439.65 8.13 C 440.04 8.09 440.43 8.2 440.73 8.44 C 440.97 8.74 441.09 9.12 441.05 9.5 L 441.05 9.99 C 440.78 9.93 440.54 9.88 440.29 9.84 C 440.05 9.81 439.81 9.79 439.57 9.79 C 438.98 9.76 438.4 9.94 437.94 10.31 C 437.54 10.65 437.32 11.15 437.34 11.68 C 437.31 12.15 437.49 12.62 437.82 12.96 C 438.18 13.29 438.66 13.46 439.15 13.44 C 439.91 13.45 440.63 13.11 441.11 12.51 C 441.18 12.66 441.24 12.79 441.31 12.91 C 441.38 13.02 441.46 13.12 441.55 13.21 C 441.6 13.27 441.67 13.31 441.75 13.31 C 441.81 13.31 441.87 13.29 441.92 13.25 L 442.34 12.97 C 442.41 12.93 442.46 12.86 442.47 12.77 C 442.47 12.72 442.45 12.67 442.42 12.62 C 442.34 12.47 442.26 12.31 442.21 12.14 C 442.15 11.95 442.12 11.75 442.13 11.55 L 442.14 9.37 C 442.2 8.77 442 8.18 441.59 7.74 C 441.17 7.39 440.64 7.19 440.09 7.18 Z M 453.89 7.19 C 453.78 7.19 453.68 7.19 453.57 7.2 C 453.29 7.2 453 7.24 452.73 7.31 C 452.47 7.38 452.23 7.5 452.01 7.66 C 451.82 7.81 451.66 7.99 451.54 8.21 C 451.42 8.43 451.35 8.67 451.36 8.92 C 451.36 9.27 451.48 9.61 451.69 9.89 C 451.97 10.22 452.34 10.46 452.76 10.56 L 453.72 10.87 C 453.97 10.93 454.2 11.05 454.39 11.22 C 454.51 11.35 454.58 11.51 454.57 11.69 C 454.58 11.94 454.45 12.18 454.23 12.31 C 453.93 12.48 453.6 12.56 453.26 12.54 C 452.99 12.54 452.72 12.51 452.46 12.45 C 452.22 12.4 451.98 12.32 451.75 12.22 L 451.58 12.15 C 451.54 12.14 451.5 12.14 451.46 12.15 C 451.36 12.15 451.31 12.22 451.31 12.36 L 451.31 12.69 C 451.31 12.76 451.32 12.82 451.35 12.89 C 451.4 12.97 451.47 13.03 451.56 13.07 C 451.8 13.19 452.06 13.28 452.32 13.34 C 452.66 13.41 453 13.45 453.35 13.45 L 453.33 13.46 C 453.66 13.45 453.98 13.4 454.29 13.3 C 454.55 13.22 454.8 13.09 455.01 12.92 C 455.21 12.77 455.38 12.57 455.49 12.34 C 455.61 12.1 455.67 11.83 455.66 11.56 C 455.67 11.23 455.56 10.9 455.36 10.63 C 455.09 10.32 454.73 10.09 454.33 9.99 L 453.39 9.69 C 453.13 9.61 452.88 9.49 452.67 9.32 C 452.54 9.2 452.47 9.03 452.47 8.85 C 452.46 8.61 452.58 8.38 452.79 8.25 C 453.06 8.11 453.36 8.05 453.67 8.06 C 454.11 8.06 454.55 8.14 454.96 8.32 C 455.04 8.37 455.12 8.4 455.21 8.41 C 455.31 8.41 455.36 8.34 455.36 8.19 L 455.36 7.88 C 455.37 7.8 455.35 7.72 455.31 7.66 C 455.25 7.59 455.18 7.54 455.11 7.49 L 454.83 7.38 L 454.45 7.27 L 454.01 7.2 C 453.97 7.2 453.93 7.19 453.89 7.19 Z M 450.02 7.36 C 449.94 7.35 449.86 7.38 449.79 7.42 C 449.72 7.5 449.68 7.59 449.66 7.69 L 448.51 12.14 L 447.47 7.71 C 447.45 7.61 447.41 7.52 447.34 7.44 C 447.26 7.39 447.17 7.37 447.07 7.38 L 446.54 7.38 C 446.44 7.37 446.35 7.39 446.27 7.44 C 446.2 7.51 446.15 7.61 446.14 7.71 L 445.09 12.14 L 443.97 7.7 C 443.95 7.6 443.91 7.51 443.84 7.44 C 443.76 7.39 443.67 7.36 443.58 7.37 L 442.92 7.37 C 442.81 7.37 442.76 7.43 442.76 7.54 C 442.77 7.63 442.79 7.72 442.82 7.81 L 444.38 12.95 C 444.4 13.05 444.45 13.14 444.52 13.21 C 444.6 13.26 444.69 13.29 444.78 13.28 L 445.36 13.26 C 445.46 13.27 445.55 13.25 445.63 13.19 C 445.7 13.12 445.74 13.03 445.76 12.93 L 446.79 8.64 L 447.82 12.93 C 447.83 13.03 447.88 13.12 447.95 13.19 C 448.03 13.25 448.12 13.27 448.21 13.26 L 448.78 13.26 C 448.88 13.27 448.97 13.25 449.04 13.2 C 449.11 13.13 449.16 13.03 449.18 12.94 L 450.79 7.79 C 450.84 7.72 450.84 7.63 450.84 7.63 C 450.84 7.59 450.84 7.56 450.84 7.52 C 450.84 7.48 450.82 7.43 450.79 7.4 C 450.76 7.37 450.72 7.35 450.67 7.36 L 450.05 7.36 C 450.04 7.36 450.03 7.36 450.02 7.36 Z M 439.65 10.62 C 439.7 10.62 439.75 10.62 439.8 10.62 L 440.43 10.62 C 440.64 10.64 440.85 10.67 441.06 10.71 L 441.06 11.01 C 441.07 11.21 441.05 11.4 441 11.59 C 440.96 11.75 440.88 11.9 440.77 12.01 C 440.61 12.21 440.39 12.36 440.14 12.44 C 439.91 12.52 439.67 12.56 439.43 12.56 C 439.18 12.6 438.93 12.53 438.73 12.37 C 438.55 12.18 438.46 11.92 438.49 11.66 C 438.47 11.36 438.59 11.08 438.81 10.89 C 439.06 10.72 439.35 10.62 439.65 10.62 Z M 455.04 14.72 C 454.34 14.73 453.51 14.89 452.88 15.33 C 452.69 15.46 452.72 15.63 452.94 15.63 C 453.64 15.54 455.21 15.35 455.5 15.71 C 455.78 16.06 455.19 17.54 454.94 18.21 C 454.86 18.41 455.03 18.49 455.21 18.34 C 456.39 17.36 456.72 15.3 456.46 15 C 456.32 14.85 455.74 14.71 455.04 14.72 Z M 436.65 15.1 C 436.5 15.12 436.42 15.3 436.58 15.44 C 439.29 17.89 442.82 19.23 446.48 19.21 C 449.37 19.22 452.2 18.36 454.59 16.74 C 454.95 16.47 454.63 16.07 454.26 16.23 C 451.87 17.24 449.3 17.76 446.71 17.77 C 443.23 17.78 439.82 16.87 436.81 15.14 C 436.75 15.11 436.69 15.1 436.65 15.1 Z M 434 0 L 459 0 L 459 25 L 434 25 Z" fill="#232f3e" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 508px; height: 1px; padding-top: 7px; margin-left: 466px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Gevanni アカウント
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="466" y="19" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    Gevanni アカウント
                </text>
            </switch>
        </g>
        <path d="M 574 70 L 673.9 70" fill="none" stroke="#b85450" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 680.65 70 L 671.65 74.5 L 673.9 70 L 671.65 65.5 Z" fill="#b85450" stroke="#b85450" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 50px; margin-left: 625px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                操作権限有り
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="625" y="53" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    操作権限有り
                </text>
            </switch>
        </g>
        <rect x="494" y="60" width="70" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 562.86 86.44 C 562.64 86.24 562.37 86.09 562.08 85.97 C 560.43 71.31 547.94 60 533.04 60 C 526.32 60 519.76 62.34 514.57 66.57 C 507.71 72.15 503.78 80.42 503.78 89.26 C 503.78 89.32 503.79 89.39 503.79 89.45 C 502.74 89.98 501.63 90.48 500.52 90.97 C 497.16 92.44 494.73 93.5 494.37 95.42 C 494.28 95.9 494.26 96.86 495.19 97.78 C 496.82 99.41 499.95 100 503.14 100 C 504.46 100 505.79 99.9 507.03 99.73 C 509.14 99.44 513.08 98.67 515.75 96.71 C 519.97 93.66 529.5 92.51 534.46 92.9 C 537.42 93.13 541.01 94.68 543.89 95.93 C 546.09 96.89 547.83 97.64 549.19 97.83 C 551.06 98.09 554.13 97.78 557.37 97.44 L 557.64 97.42 C 558.49 97.32 559.32 97.25 560.15 97.18 C 560.49 97.15 560.82 97.12 561.27 97.09 C 562.66 96.99 563.74 95.83 563.74 94.44 L 563.74 88.42 C 563.74 87.66 563.42 86.94 562.86 86.44 Z M 533.04 63.15 C 546.22 63.15 557.28 73.06 558.91 85.97 C 558.46 86.01 557.98 86.06 557.47 86.1 L 557.18 86.13 C 557.15 84.68 555.96 83.52 554.51 83.52 L 553.39 83.52 C 549.92 73.32 539.88 66.29 528.52 66.29 C 524.31 66.29 520.19 67.25 516.48 69.08 C 516.51 69.06 516.53 69.04 516.56 69.01 C 521.19 65.23 527.04 63.15 533.04 63.15 Z M 549.23 88.82 L 549.23 86.66 L 554.04 86.66 L 554.04 88.82 Z M 506.61 96.61 C 502.43 97.18 499.14 96.67 497.78 95.83 C 498.6 95.24 500.47 94.43 501.78 93.85 C 502.92 93.36 504.07 92.83 505.18 92.27 C 507 93.66 509.25 94.69 511.74 95.31 C 510.3 95.89 508.52 96.35 506.61 96.61 Z M 560.59 93.98 C 560.35 94 560.1 94.02 559.86 94.04 C 559.04 94.11 558.18 94.19 557.31 94.28 L 557.05 94.31 C 554.27 94.6 551.12 94.92 549.63 94.72 C 548.69 94.58 546.97 93.83 545.14 93.04 C 542.04 91.7 538.19 90.03 534.71 89.76 C 529.91 89.39 521.62 90.3 516.28 92.8 C 513.19 92.66 510.32 91.83 508.1 90.46 C 508.09 90.03 508.08 89.6 508.08 89.44 C 508.08 82.92 511.13 76.82 516.45 72.67 C 520.07 70.55 524.24 69.44 528.52 69.44 C 538.15 69.44 546.71 75.13 550.04 83.52 L 548.76 83.52 C 547.48 83.52 546.41 84.42 546.15 85.63 C 545.93 85.54 545.73 85.47 545.5 85.38 C 542.3 84.15 537.92 82.46 532.85 82.76 C 525.44 83.2 521.8 83.64 518.93 84.46 L 519.79 87.49 C 522.42 86.74 525.88 86.33 533.03 85.9 C 537.42 85.66 541.44 87.19 544.36 88.32 C 544.98 88.56 545.55 88.77 546.08 88.96 L 546.08 89.28 C 546.08 90.76 547.29 91.96 548.76 91.96 L 554.51 91.96 C 555.98 91.96 557.18 90.76 557.19 89.29 L 557.75 89.24 C 558.68 89.16 559.54 89.07 560.26 89 L 560.59 88.97 Z" fill="#bf0816" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 107px; margin-left: 529px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                IAM Role for A
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="529" y="119" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    IAM Role fo...
                </text>
            </switch>
        </g>
        <rect x="34" y="140" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 64.23 165.36 L 66.05 165.36 L 66.05 172.64 L 60.36 172.64 L 60.36 170.82 L 64.23 170.82 Z M 65.14 177.98 C 61.25 177.98 58.09 174.82 58.09 170.93 C 58.09 167.05 61.25 163.89 65.14 163.89 C 69.02 163.89 72.18 167.05 72.18 170.93 C 72.18 174.82 69.02 177.98 65.14 177.98 Z M 65.14 162.07 C 60.25 162.07 56.27 166.04 56.27 170.93 C 56.27 175.82 60.25 179.8 65.14 179.8 C 70.02 179.8 74 175.82 74 170.93 C 74 166.04 70.02 162.07 65.14 162.07 Z M 35.82 173.2 L 54.8 173.2 L 54.8 175.02 L 34.91 175.02 C 34.41 175.02 34 174.62 34 174.11 L 34 170.14 L 35.82 170.14 Z M 67.97 147.14 C 68.71 147.14 69.3 147.73 69.3 148.47 C 69.3 149.2 68.71 149.79 67.97 149.79 C 67.24 149.79 66.65 149.2 66.65 148.47 C 66.65 147.73 67.24 147.14 67.97 147.14 Z M 67.97 151.61 C 69.71 151.61 71.12 150.2 71.12 148.47 C 71.12 146.73 69.71 145.32 67.97 145.32 C 66.24 145.32 64.83 146.73 64.83 148.47 C 64.83 150.2 66.24 151.61 67.97 151.61 Z M 45.37 147.14 L 58.73 147.14 C 59.13 147.14 59.48 146.88 59.6 146.5 C 60.45 143.82 62.92 142.02 65.74 142.02 C 69.29 142.02 72.18 144.91 72.18 148.47 C 72.18 152.02 69.29 154.91 65.74 154.91 C 62.92 154.91 60.45 153.11 59.6 150.43 C 59.48 150.05 59.13 149.79 58.73 149.79 L 56.47 149.79 C 56.22 149.79 55.99 149.89 55.82 150.06 L 54.55 151.34 L 53.27 150.06 C 53.1 149.89 52.87 149.79 52.63 149.79 L 51.35 149.79 C 51.11 149.79 50.88 149.89 50.71 150.06 L 49.43 151.34 L 48.16 150.06 C 47.99 149.89 47.75 149.79 47.51 149.79 L 45.94 149.79 L 44.31 148.37 Z M 45 151.39 C 45.16 151.54 45.37 151.61 45.59 151.61 L 47.14 151.61 L 48.79 153.27 C 49.14 153.62 49.72 153.62 50.07 153.27 L 51.73 151.61 L 52.25 151.61 L 53.9 153.27 C 54.26 153.62 54.83 153.62 55.19 153.27 L 56.84 151.61 L 58.1 151.61 C 59.36 154.7 62.35 156.73 65.74 156.73 C 70.29 156.73 74 153.02 74 148.47 C 74 143.91 70.29 140.2 65.74 140.2 C 62.35 140.2 59.36 142.23 58.1 145.32 L 44.96 145.32 C 44.69 145.32 44.44 145.43 44.26 145.64 L 42.35 147.87 C 42.02 148.25 42.06 148.82 42.44 149.15 Z M 34 162.37 C 34 157.82 37.71 154.11 42.26 154.11 C 45.65 154.11 48.65 156.14 49.91 159.23 L 51.16 159.23 L 52.81 157.58 C 53.15 157.24 53.75 157.24 54.09 157.58 L 55.75 159.23 L 56.27 159.23 L 57.92 157.58 C 58.28 157.22 58.85 157.22 59.21 157.58 L 60.87 159.23 L 62.41 159.23 C 62.63 159.23 62.84 159.31 63 159.45 L 63.75 160.1 L 62.55 161.47 L 62.07 161.05 L 60.49 161.05 C 60.25 161.05 60.02 160.95 59.85 160.78 L 58.57 159.51 L 57.29 160.78 C 57.12 160.95 56.89 161.05 56.65 161.05 L 55.37 161.05 C 55.13 161.05 54.89 160.95 54.72 160.78 L 53.45 159.51 L 52.18 160.78 C 52.01 160.95 51.78 161.05 51.53 161.05 L 49.27 161.05 C 48.87 161.05 48.52 160.79 48.4 160.41 C 47.55 157.73 45.08 155.94 42.26 155.94 C 38.71 155.94 35.82 158.82 35.82 162.37 C 35.82 165.93 38.71 168.82 42.26 168.82 C 45.08 168.82 47.55 167.02 48.4 164.34 C 48.52 163.96 48.87 163.71 49.27 163.71 L 56.95 163.71 L 56.95 165.53 L 49.91 165.53 C 48.65 168.61 45.65 170.64 42.26 170.64 C 37.71 170.64 34 166.93 34 162.37 Z M 40.02 163.71 C 39.29 163.71 38.7 163.11 38.7 162.38 C 38.7 161.65 39.29 161.05 40.02 161.05 C 40.76 161.05 41.35 161.65 41.35 162.38 C 41.35 163.11 40.76 163.71 40.02 163.71 Z M 40.02 159.23 C 38.29 159.23 36.88 160.64 36.88 162.38 C 36.88 164.11 38.29 165.53 40.02 165.53 C 41.76 165.53 43.17 164.11 43.17 162.38 C 43.17 160.64 41.76 159.23 40.02 159.23 Z" fill="#bf0816" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 187px; margin-left: 54px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                一時クレデンシャル
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="54" y="199" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    一時クレデンシャル
                </text>
            </switch>
        </g>
        <path d="M 84 80 L 233.9 80" fill="none" stroke="#b85450" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 240.65 80 L 231.65 84.5 L 233.9 80 L 231.65 75.5 Z" fill="#b85450" stroke="#b85450" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 60px; margin-left: 135px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                ログイン
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="135" y="63" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    ログイン
                </text>
            </switch>
        </g>
        <rect x="34" y="60" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 37.66 98.18 C 38.11 89.17 45.27 81.98 54 81.98 C 56.95 81.98 59.85 82.81 62.37 84.38 C 67.05 87.3 70.05 92.53 70.34 98.18 Z M 45.08 70.8 C 45.08 65.85 49.08 61.82 54 61.82 C 58.92 61.82 62.92 65.85 62.92 70.8 C 62.92 75.75 58.92 79.78 54 79.78 C 49.08 79.78 45.08 75.75 45.08 70.8 Z M 63.33 82.84 C 61.75 81.86 60.05 81.15 58.27 80.7 C 62.07 79.04 64.73 75.23 64.73 70.8 C 64.73 64.84 59.92 60 54 60 C 48.08 60 43.27 64.84 43.27 70.8 C 43.27 75.23 45.94 79.05 49.74 80.71 C 41.77 82.71 35.82 90.18 35.82 99.09 C 35.82 99.59 36.22 100 36.73 100 L 71.27 100 C 71.77 100 72.18 99.59 72.18 99.09 C 72.18 92.47 68.79 86.25 63.33 82.84 Z" fill="#232f3d" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 107px; margin-left: 54px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                IAM ユーザー
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="54" y="119" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    IAM ユーザー
                </text>
            </switch>
        </g>
        <path d="M 304 80 L 484.16 80" fill="none" stroke="#b85450" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 490.91 80 L 481.91 84.5 L 484.16 80 L 481.91 75.5 Z" fill="#b85450" stroke="#b85450" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 60px; margin-left: 395px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Assume Role
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="395" y="63" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Assume Role
                </text>
            </switch>
        </g>
        <path d="M 604 150 L 609.83 150 L 614 155.92 L 618.17 150 L 624 150 L 616.92 160 L 624 170 L 618.17 170 L 614 164.08 L 609.83 170 L 604 170 L 610.67 160 Z" fill="#ff0000" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 714 55 L 744 55 L 744 85 L 714 85 Z" fill="url(#mx-gradient-f78e04-1-d05c17-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 738.89 73.26 L 735.52 71.24 L 735.52 66.43 C 735.52 66.28 735.44 66.15 735.31 66.07 L 730.47 63.25 L 730.47 59.18 L 738.89 64.15 Z M 739.52 63.55 L 730.27 58.08 C 730.14 58 729.98 58 729.84 58.07 C 729.71 58.15 729.63 58.29 729.63 58.44 L 729.63 63.49 C 729.63 63.64 729.71 63.78 729.84 63.85 L 734.68 66.68 L 734.68 71.48 C 734.68 71.63 734.76 71.77 734.88 71.84 L 739.09 74.37 C 739.16 74.41 739.23 74.43 739.31 74.43 C 739.38 74.43 739.45 74.41 739.51 74.37 C 739.65 74.3 739.73 74.16 739.73 74.01 L 739.73 63.91 C 739.73 63.76 739.65 63.62 739.52 63.55 Z M 728.98 81.1 L 719.11 75.86 L 719.11 64.15 L 727.53 59.18 L 727.53 63.26 L 723.09 66.08 C 722.97 66.16 722.9 66.29 722.9 66.43 L 722.9 73.59 C 722.9 73.74 722.99 73.89 723.13 73.96 L 728.79 76.9 C 728.91 76.97 729.05 76.97 729.17 76.9 L 734.66 74.07 L 738.04 76.09 Z M 739.1 75.75 L 734.9 73.22 C 734.77 73.15 734.62 73.14 734.49 73.21 L 728.98 76.06 L 723.74 73.33 L 723.74 66.66 L 728.17 63.84 C 728.3 63.77 728.37 63.63 728.37 63.49 L 728.37 58.44 C 728.37 58.29 728.29 58.15 728.16 58.07 C 728.03 58 727.86 58 727.73 58.08 L 718.48 63.55 C 718.35 63.62 718.27 63.76 718.27 63.91 L 718.27 76.11 C 718.27 76.27 718.36 76.41 718.49 76.48 L 728.78 81.95 C 728.84 81.98 728.91 82 728.98 82 C 729.05 82 729.12 81.98 729.18 81.95 L 739.09 76.48 C 739.22 76.41 739.3 76.27 739.31 76.12 C 739.31 75.97 739.23 75.83 739.1 75.75 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 92px; margin-left: 729px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ECS
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="729" y="104" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ECS
                </text>
            </switch>
        </g>
        <path d="M 774 55 L 804 55 L 804 85 L 774 85 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 793.71 79.02 L 793.71 76.52 C 792.63 77.22 790.67 77.55 788.81 77.55 C 786.78 77.55 785.19 77.19 784.29 76.59 L 784.29 79.02 C 784.29 79.75 785.98 80.5 788.81 80.5 C 791.7 80.5 793.71 79.72 793.71 79.02 Z M 788.81 73.74 C 786.78 73.74 785.19 73.38 784.29 72.78 L 784.29 75.22 C 784.3 75.95 785.99 76.69 788.81 76.69 C 791.69 76.69 793.7 75.92 793.71 75.22 L 793.71 72.7 C 792.63 73.41 790.67 73.74 788.81 73.74 Z M 793.71 71.41 L 793.71 68.51 C 792.63 69.21 790.67 69.55 788.81 69.55 C 786.78 69.55 785.19 69.19 784.29 68.58 L 784.29 71.41 C 784.3 72.14 785.99 72.88 788.81 72.88 C 791.69 72.88 793.7 72.11 793.71 71.41 Z M 784.28 67.21 C 784.28 67.21 784.28 67.21 784.28 67.22 L 784.29 67.22 L 784.29 67.22 C 784.3 67.94 785.99 68.69 788.81 68.69 C 791.96 68.69 793.7 67.82 793.71 67.22 L 793.71 67.22 L 793.72 67.22 C 793.72 67.21 793.72 67.21 793.72 67.21 C 793.72 66.61 791.97 65.73 788.81 65.73 C 785.98 65.73 784.28 66.49 784.28 67.21 Z M 794.57 67.22 L 794.57 71.4 L 794.57 71.4 C 794.57 71.41 794.57 71.41 794.57 71.41 L 794.57 75.21 L 794.57 75.21 C 794.57 75.22 794.57 75.22 794.57 75.23 L 794.57 79.02 C 794.57 80.63 791.58 81.36 788.81 81.36 C 785.54 81.36 783.43 80.44 783.43 79.02 L 783.43 75.23 C 783.43 75.22 783.43 75.22 783.43 75.21 L 783.43 75.21 L 783.43 71.42 C 783.43 71.41 783.43 71.41 783.43 71.4 L 783.43 71.4 L 783.43 67.22 C 783.43 67.22 783.43 67.22 783.43 67.21 C 783.43 65.79 785.54 64.88 788.81 64.88 C 791.59 64.88 794.57 65.61 794.57 67.21 C 794.57 67.22 794.57 67.22 794.57 67.22 Z M 800.57 61.69 C 800.81 61.69 801 61.5 801 61.27 L 801 59.07 C 801 58.83 800.81 58.64 800.57 58.64 L 777.43 58.64 C 777.19 58.64 777 58.83 777 59.07 L 777 61.27 C 777 61.5 777.19 61.69 777.43 61.69 C 777.95 61.69 778.38 62.12 778.38 62.64 C 778.38 63.16 777.95 63.58 777.43 63.58 C 777.19 63.58 777 63.77 777 64.01 L 777 72.79 C 777 73.02 777.19 73.22 777.43 73.22 L 781.71 73.22 L 781.71 72.36 L 779.57 72.36 L 779.57 71.07 L 781.71 71.07 L 781.71 70.22 L 779.14 70.22 C 778.91 70.22 778.71 70.41 778.71 70.64 L 778.71 72.36 L 777.86 72.36 L 777.86 64.39 C 778.65 64.19 779.23 63.48 779.23 62.64 C 779.23 61.79 778.65 61.08 777.86 60.89 L 777.86 59.5 L 800.14 59.5 L 800.14 60.89 C 799.35 61.08 798.77 61.79 798.77 62.64 C 798.77 63.48 799.35 64.19 800.14 64.39 L 800.14 72.36 L 799.29 72.36 L 799.29 70.64 C 799.29 70.41 799.09 70.22 798.86 70.22 L 796.29 70.22 L 796.29 71.07 L 798.43 71.07 L 798.43 72.36 L 796.29 72.36 L 796.29 73.22 L 800.57 73.22 C 800.81 73.22 801 73.02 801 72.79 L 801 64.01 C 801 63.77 800.81 63.58 800.57 63.58 C 800.05 63.58 799.62 63.16 799.62 62.64 C 799.62 62.12 800.05 61.69 800.57 61.69 Z M 783.86 64.64 L 783.86 61.21 C 783.86 60.98 783.67 60.79 783.43 60.79 L 780.86 60.79 C 780.62 60.79 780.43 60.98 780.43 61.21 L 780.43 68.5 C 780.43 68.74 780.62 68.93 780.86 68.93 L 782.14 68.93 L 782.14 68.07 L 781.29 68.07 L 781.29 61.64 L 783 61.64 L 783 64.64 Z M 796.71 68.07 L 796.29 68.07 L 796.29 68.93 L 797.14 68.93 C 797.38 68.93 797.57 68.74 797.57 68.5 L 797.57 61.21 C 797.57 60.98 797.38 60.79 797.14 60.79 L 794.57 60.79 C 794.33 60.79 794.14 60.98 794.14 61.21 L 794.14 64.64 L 795 64.64 L 795 61.64 L 796.71 61.64 Z M 793.29 64.21 L 793.29 61.21 C 793.29 60.98 793.09 60.79 792.86 60.79 L 789.86 60.79 C 789.62 60.79 789.43 60.98 789.43 61.21 L 789.43 63.79 L 790.29 63.79 L 790.29 61.64 L 792.43 61.64 L 792.43 64.21 Z M 787.71 63.79 L 787.71 61.64 L 785.57 61.64 L 785.57 64.21 L 784.71 64.21 L 784.71 61.21 C 784.71 60.98 784.91 60.79 785.14 60.79 L 788.14 60.79 C 788.38 60.79 788.57 60.98 788.57 61.21 L 788.57 63.79 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 92px; margin-left: 789px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ElastiCache
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="789" y="104" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Elast...
                </text>
            </switch>
        </g>
        <path d="M 834 55 L 864 55 L 864 85 L 834 85 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 845.31 63.15 C 848 62.03 849.9 63.51 850.59 64.19 C 851.16 64.76 851.6 65.47 851.9 66.32 C 851.95 66.47 852.08 66.58 852.24 66.6 C 852.4 66.62 852.55 66.56 852.65 66.44 C 852.98 65.99 853.48 65.73 854 65.73 C 854.78 65.73 855.73 66.32 855.83 68 C 855.84 68.19 855.98 68.35 856.17 68.39 C 858.11 68.8 859.1 69.88 859.1 71.59 C 859.1 73.19 858.37 74.17 856.93 74.52 L 857.13 75.36 C 858.95 74.91 859.96 73.58 859.96 71.59 C 859.96 69.57 858.79 68.17 856.66 67.62 C 856.42 65.73 855.18 64.87 854 64.87 C 853.44 64.87 852.9 65.06 852.46 65.41 C 852.13 64.71 851.71 64.09 851.2 63.59 C 849.5 61.9 847.17 61.45 844.98 62.36 C 843.11 63.15 841.69 65.32 841.69 67.41 C 841.69 67.51 841.69 67.62 841.7 67.73 C 839.98 68.29 838.96 69.71 838.96 71.57 C 838.96 71.66 838.97 71.75 838.97 71.85 C 839.06 73.46 840.14 74.85 841.71 75.39 L 841.99 74.58 C 840.75 74.15 839.9 73.06 839.83 71.8 C 839.83 71.72 839.82 71.65 839.82 71.57 C 839.82 69.39 841.35 68.68 842.26 68.46 C 842.47 68.4 842.61 68.2 842.59 67.99 C 842.56 67.78 842.55 67.59 842.55 67.41 C 842.55 65.67 843.76 63.8 845.31 63.15 Z M 854.14 72.14 C 854.14 71.97 854.11 71.84 854.04 71.78 C 853.97 71.71 853.84 71.71 853.74 71.71 L 848.14 71.71 C 847.91 71.71 847.71 71.52 847.71 71.29 C 847.71 71.17 847.67 70.84 847.62 70.64 L 846.14 70.64 C 846.07 70.84 846 71.17 846 71.29 C 845.99 71.52 845.8 71.71 845.57 71.71 L 844.29 71.71 C 844.16 71.71 844.03 71.71 843.96 71.78 C 843.89 71.84 843.86 71.97 843.86 72.14 L 843.86 78.14 L 854.14 78.14 Z M 855 73 L 855 76.1 L 856.11 73 Z M 854.98 78.72 L 854.97 78.72 C 854.91 78.88 854.76 79 854.57 79 L 843.43 79 C 843.19 79 843 78.81 843 78.57 L 843 72.14 C 843 71.62 843.2 71.32 843.37 71.16 C 843.54 71 843.83 70.82 844.31 70.86 L 845.19 70.86 C 845.27 70.41 845.46 69.79 845.92 69.79 L 847.87 69.79 C 847.89 69.79 847.92 69.79 847.95 69.79 C 848.35 69.87 848.49 70.44 848.54 70.86 L 853.71 70.86 C 854.17 70.83 854.46 71 854.62 71.16 C 854.8 71.32 855 71.62 855 72.14 L 856.71 72.14 C 856.85 72.14 856.98 72.21 857.06 72.32 C 857.14 72.44 857.16 72.58 857.12 72.72 Z M 860.14 80.54 L 856.88 77.28 L 856.28 77.88 L 859.54 81.14 L 857.18 81.14 L 857.18 82 L 860.57 82 C 860.81 82 861 81.81 861 81.57 L 861 78.18 L 860.14 78.18 Z M 841.56 77.26 L 837.86 80.61 L 837.86 78.18 L 837 78.18 L 837 81.57 C 837 81.81 837.19 82 837.43 82 L 841.25 82 L 841.25 81.14 L 838.54 81.14 L 842.14 77.9 Z M 860.57 58 L 856.75 58 L 856.75 58.86 L 859.61 58.86 L 856.26 62.56 L 856.9 63.14 L 860.14 59.54 L 860.14 62.25 L 861 62.25 L 861 58.43 C 861 58.19 860.81 58 860.57 58 Z M 837.86 62.25 L 837 62.25 L 837 58.43 C 837 58.19 837.19 58 837.43 58 L 841.25 58 L 841.25 58.86 L 838.46 58.86 L 842.15 62.55 L 841.54 63.15 L 837.86 59.46 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 92px; margin-left: 849px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                EFS
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="849" y="104" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    EFS
                </text>
            </switch>
        </g>
        <rect x="864" y="55" width="60" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 70px; margin-left: 865px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                ・・・
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="894" y="74" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ・・・
                </text>
            </switch>
        </g>
        <rect x="694" y="35" width="240" height="90" fill="none" stroke="#b85450" stroke-width="2" stroke-dasharray="16 16" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 238px; height: 1px; padding-top: 32px; margin-left: 695px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                担当プロジェクト管轄リソース
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="814" y="32" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    担当プロジェクト管轄リソース
                </text>
            </switch>
        </g>
        <path d="M 714 210 L 744 210 L 744 240 L 714 240 Z" fill="url(#mx-gradient-f78e04-1-d05c17-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 738.89 228.26 L 735.52 226.24 L 735.52 221.43 C 735.52 221.28 735.44 221.15 735.31 221.07 L 730.47 218.25 L 730.47 214.18 L 738.89 219.15 Z M 739.52 218.55 L 730.27 213.08 C 730.14 213 729.98 213 729.84 213.07 C 729.71 213.15 729.63 213.29 729.63 213.44 L 729.63 218.49 C 729.63 218.64 729.71 218.78 729.84 218.85 L 734.68 221.68 L 734.68 226.48 C 734.68 226.63 734.76 226.77 734.88 226.84 L 739.09 229.37 C 739.16 229.41 739.23 229.43 739.31 229.43 C 739.38 229.43 739.45 229.41 739.51 229.37 C 739.65 229.3 739.73 229.16 739.73 229.01 L 739.73 218.91 C 739.73 218.76 739.65 218.62 739.52 218.55 Z M 728.98 236.1 L 719.11 230.86 L 719.11 219.15 L 727.53 214.18 L 727.53 218.26 L 723.09 221.08 C 722.97 221.16 722.9 221.29 722.9 221.43 L 722.9 228.59 C 722.9 228.74 722.99 228.89 723.13 228.96 L 728.79 231.9 C 728.91 231.97 729.05 231.97 729.17 231.9 L 734.66 229.07 L 738.04 231.09 Z M 739.1 230.75 L 734.9 228.22 C 734.77 228.15 734.62 228.14 734.49 228.21 L 728.98 231.06 L 723.74 228.33 L 723.74 221.66 L 728.17 218.84 C 728.3 218.77 728.37 218.63 728.37 218.49 L 728.37 213.44 C 728.37 213.29 728.29 213.15 728.16 213.07 C 728.03 213 727.86 213 727.73 213.08 L 718.48 218.55 C 718.35 218.62 718.27 218.76 718.27 218.91 L 718.27 231.11 C 718.27 231.27 718.36 231.41 718.49 231.48 L 728.78 236.95 C 728.84 236.98 728.91 237 728.98 237 C 729.05 237 729.12 236.98 729.18 236.95 L 739.09 231.48 C 739.22 231.41 739.3 231.27 739.31 231.12 C 739.31 230.97 739.23 230.83 739.1 230.75 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 247px; margin-left: 729px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ECS
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="729" y="259" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ECS
                </text>
            </switch>
        </g>
        <path d="M 774 210 L 804 210 L 804 240 L 774 240 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 793.71 234.02 L 793.71 231.52 C 792.63 232.22 790.67 232.55 788.81 232.55 C 786.78 232.55 785.19 232.19 784.29 231.59 L 784.29 234.02 C 784.29 234.75 785.98 235.5 788.81 235.5 C 791.7 235.5 793.71 234.72 793.71 234.02 Z M 788.81 228.74 C 786.78 228.74 785.19 228.38 784.29 227.78 L 784.29 230.22 C 784.3 230.95 785.99 231.69 788.81 231.69 C 791.69 231.69 793.7 230.92 793.71 230.22 L 793.71 227.7 C 792.63 228.41 790.67 228.74 788.81 228.74 Z M 793.71 226.41 L 793.71 223.51 C 792.63 224.21 790.67 224.55 788.81 224.55 C 786.78 224.55 785.19 224.19 784.29 223.58 L 784.29 226.41 C 784.3 227.14 785.99 227.88 788.81 227.88 C 791.69 227.88 793.7 227.11 793.71 226.41 Z M 784.28 222.21 C 784.28 222.21 784.28 222.21 784.28 222.22 L 784.29 222.22 L 784.29 222.22 C 784.3 222.94 785.99 223.69 788.81 223.69 C 791.96 223.69 793.7 222.82 793.71 222.22 L 793.71 222.22 L 793.72 222.22 C 793.72 222.21 793.72 222.21 793.72 222.21 C 793.72 221.61 791.97 220.73 788.81 220.73 C 785.98 220.73 784.28 221.49 784.28 222.21 Z M 794.57 222.22 L 794.57 226.4 L 794.57 226.4 C 794.57 226.41 794.57 226.41 794.57 226.41 L 794.57 230.21 L 794.57 230.21 C 794.57 230.22 794.57 230.22 794.57 230.23 L 794.57 234.02 C 794.57 235.63 791.58 236.36 788.81 236.36 C 785.54 236.36 783.43 235.44 783.43 234.02 L 783.43 230.23 C 783.43 230.22 783.43 230.22 783.43 230.21 L 783.43 230.21 L 783.43 226.42 C 783.43 226.41 783.43 226.41 783.43 226.4 L 783.43 226.4 L 783.43 222.22 C 783.43 222.22 783.43 222.22 783.43 222.21 C 783.43 220.79 785.54 219.88 788.81 219.88 C 791.59 219.88 794.57 220.61 794.57 222.21 C 794.57 222.22 794.57 222.22 794.57 222.22 Z M 800.57 216.69 C 800.81 216.69 801 216.5 801 216.27 L 801 214.07 C 801 213.83 800.81 213.64 800.57 213.64 L 777.43 213.64 C 777.19 213.64 777 213.83 777 214.07 L 777 216.27 C 777 216.5 777.19 216.69 777.43 216.69 C 777.95 216.69 778.38 217.12 778.38 217.64 C 778.38 218.16 777.95 218.58 777.43 218.58 C 777.19 218.58 777 218.77 777 219.01 L 777 227.79 C 777 228.02 777.19 228.22 777.43 228.22 L 781.71 228.22 L 781.71 227.36 L 779.57 227.36 L 779.57 226.07 L 781.71 226.07 L 781.71 225.22 L 779.14 225.22 C 778.91 225.22 778.71 225.41 778.71 225.64 L 778.71 227.36 L 777.86 227.36 L 777.86 219.39 C 778.65 219.19 779.23 218.48 779.23 217.64 C 779.23 216.79 778.65 216.08 777.86 215.89 L 777.86 214.5 L 800.14 214.5 L 800.14 215.89 C 799.35 216.08 798.77 216.79 798.77 217.64 C 798.77 218.48 799.35 219.19 800.14 219.39 L 800.14 227.36 L 799.29 227.36 L 799.29 225.64 C 799.29 225.41 799.09 225.22 798.86 225.22 L 796.29 225.22 L 796.29 226.07 L 798.43 226.07 L 798.43 227.36 L 796.29 227.36 L 796.29 228.22 L 800.57 228.22 C 800.81 228.22 801 228.02 801 227.79 L 801 219.01 C 801 218.77 800.81 218.58 800.57 218.58 C 800.05 218.58 799.62 218.16 799.62 217.64 C 799.62 217.12 800.05 216.69 800.57 216.69 Z M 783.86 219.64 L 783.86 216.21 C 783.86 215.98 783.67 215.79 783.43 215.79 L 780.86 215.79 C 780.62 215.79 780.43 215.98 780.43 216.21 L 780.43 223.5 C 780.43 223.74 780.62 223.93 780.86 223.93 L 782.14 223.93 L 782.14 223.07 L 781.29 223.07 L 781.29 216.64 L 783 216.64 L 783 219.64 Z M 796.71 223.07 L 796.29 223.07 L 796.29 223.93 L 797.14 223.93 C 797.38 223.93 797.57 223.74 797.57 223.5 L 797.57 216.21 C 797.57 215.98 797.38 215.79 797.14 215.79 L 794.57 215.79 C 794.33 215.79 794.14 215.98 794.14 216.21 L 794.14 219.64 L 795 219.64 L 795 216.64 L 796.71 216.64 Z M 793.29 219.21 L 793.29 216.21 C 793.29 215.98 793.09 215.79 792.86 215.79 L 789.86 215.79 C 789.62 215.79 789.43 215.98 789.43 216.21 L 789.43 218.79 L 790.29 218.79 L 790.29 216.64 L 792.43 216.64 L 792.43 219.21 Z M 787.71 218.79 L 787.71 216.64 L 785.57 216.64 L 785.57 219.21 L 784.71 219.21 L 784.71 216.21 C 784.71 215.98 784.91 215.79 785.14 215.79 L 788.14 215.79 C 788.38 215.79 788.57 215.98 788.57 216.21 L 788.57 218.79 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 247px; margin-left: 789px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ElastiCache
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="789" y="259" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Elast...
                </text>
            </switch>
        </g>
        <path d="M 834 210 L 864 210 L 864 240 L 834 240 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 845.31 218.15 C 848 217.03 849.9 218.51 850.59 219.19 C 851.16 219.76 851.6 220.47 851.9 221.32 C 851.95 221.47 852.08 221.58 852.24 221.6 C 852.4 221.62 852.55 221.56 852.65 221.44 C 852.98 220.99 853.48 220.73 854 220.73 C 854.78 220.73 855.73 221.32 855.83 223 C 855.84 223.19 855.98 223.35 856.17 223.39 C 858.11 223.8 859.1 224.88 859.1 226.59 C 859.1 228.19 858.37 229.17 856.93 229.52 L 857.13 230.36 C 858.95 229.91 859.96 228.58 859.96 226.59 C 859.96 224.57 858.79 223.17 856.66 222.62 C 856.42 220.73 855.18 219.87 854 219.87 C 853.44 219.87 852.9 220.06 852.46 220.41 C 852.13 219.71 851.71 219.09 851.2 218.59 C 849.5 216.9 847.17 216.45 844.98 217.36 C 843.11 218.15 841.69 220.32 841.69 222.41 C 841.69 222.51 841.69 222.62 841.7 222.73 C 839.98 223.29 838.96 224.71 838.96 226.57 C 838.96 226.66 838.97 226.75 838.97 226.85 C 839.06 228.46 840.14 229.85 841.71 230.39 L 841.99 229.58 C 840.75 229.15 839.9 228.06 839.83 226.8 C 839.83 226.72 839.82 226.65 839.82 226.57 C 839.82 224.39 841.35 223.68 842.26 223.46 C 842.47 223.4 842.61 223.2 842.59 222.99 C 842.56 222.78 842.55 222.59 842.55 222.41 C 842.55 220.67 843.76 218.8 845.31 218.15 Z M 854.14 227.14 C 854.14 226.97 854.11 226.84 854.04 226.78 C 853.97 226.71 853.84 226.71 853.74 226.71 L 848.14 226.71 C 847.91 226.71 847.71 226.52 847.71 226.29 C 847.71 226.17 847.67 225.84 847.62 225.64 L 846.14 225.64 C 846.07 225.84 846 226.17 846 226.29 C 845.99 226.52 845.8 226.71 845.57 226.71 L 844.29 226.71 C 844.16 226.71 844.03 226.71 843.96 226.78 C 843.89 226.84 843.86 226.97 843.86 227.14 L 843.86 233.14 L 854.14 233.14 Z M 855 228 L 855 231.1 L 856.11 228 Z M 854.98 233.72 L 854.97 233.72 C 854.91 233.88 854.76 234 854.57 234 L 843.43 234 C 843.19 234 843 233.81 843 233.57 L 843 227.14 C 843 226.62 843.2 226.32 843.37 226.16 C 843.54 226 843.83 225.82 844.31 225.86 L 845.19 225.86 C 845.27 225.41 845.46 224.79 845.92 224.79 L 847.87 224.79 C 847.89 224.79 847.92 224.79 847.95 224.79 C 848.35 224.87 848.49 225.44 848.54 225.86 L 853.71 225.86 C 854.17 225.83 854.46 226 854.62 226.16 C 854.8 226.32 855 226.62 855 227.14 L 856.71 227.14 C 856.85 227.14 856.98 227.21 857.06 227.32 C 857.14 227.44 857.16 227.58 857.12 227.72 Z M 860.14 235.54 L 856.88 232.28 L 856.28 232.88 L 859.54 236.14 L 857.18 236.14 L 857.18 237 L 860.57 237 C 860.81 237 861 236.81 861 236.57 L 861 233.18 L 860.14 233.18 Z M 841.56 232.26 L 837.86 235.61 L 837.86 233.18 L 837 233.18 L 837 236.57 C 837 236.81 837.19 237 837.43 237 L 841.25 237 L 841.25 236.14 L 838.54 236.14 L 842.14 232.9 Z M 860.57 213 L 856.75 213 L 856.75 213.86 L 859.61 213.86 L 856.26 217.56 L 856.9 218.14 L 860.14 214.54 L 860.14 217.25 L 861 217.25 L 861 213.43 C 861 213.19 860.81 213 860.57 213 Z M 837.86 217.25 L 837 217.25 L 837 213.43 C 837 213.19 837.19 213 837.43 213 L 841.25 213 L 841.25 213.86 L 838.46 213.86 L 842.15 217.54 L 841.54 218.15 L 837.86 214.46 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 247px; margin-left: 849px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                EFS
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="849" y="259" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    EFS
                </text>
            </switch>
        </g>
        <rect x="864" y="210" width="60" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 225px; margin-left: 865px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                ・・・
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="894" y="229" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ・・・
                </text>
            </switch>
        </g>
        <rect x="694" y="190" width="240" height="90" fill="none" stroke="#6c8ebf" stroke-width="2" stroke-dasharray="16 16" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 238px; height: 1px; padding-top: 187px; margin-left: 695px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                他プロジェクト管轄リソース
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="814" y="187" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    他プロジェクト管轄リソース
                </text>
            </switch>
        </g>
        <rect x="494" y="205" width="70" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 562.86 231.44 C 562.64 231.24 562.37 231.09 562.08 230.97 C 560.43 216.31 547.94 205 533.04 205 C 526.32 205 519.76 207.34 514.57 211.57 C 507.71 217.15 503.78 225.42 503.78 234.26 C 503.78 234.32 503.79 234.39 503.79 234.45 C 502.74 234.98 501.63 235.48 500.52 235.97 C 497.16 237.44 494.73 238.5 494.37 240.42 C 494.28 240.9 494.26 241.86 495.19 242.78 C 496.82 244.41 499.95 245 503.14 245 C 504.46 245 505.79 244.9 507.03 244.73 C 509.14 244.44 513.08 243.67 515.75 241.71 C 519.97 238.66 529.5 237.51 534.46 237.9 C 537.42 238.13 541.01 239.68 543.89 240.93 C 546.09 241.89 547.83 242.64 549.19 242.83 C 551.06 243.09 554.13 242.78 557.37 242.44 L 557.64 242.42 C 558.49 242.32 559.32 242.25 560.15 242.18 C 560.49 242.15 560.82 242.12 561.27 242.09 C 562.66 241.99 563.74 240.83 563.74 239.44 L 563.74 233.42 C 563.74 232.66 563.42 231.94 562.86 231.44 Z M 533.04 208.15 C 546.22 208.15 557.28 218.06 558.91 230.97 C 558.46 231.01 557.98 231.06 557.47 231.1 L 557.18 231.13 C 557.15 229.68 555.96 228.52 554.51 228.52 L 553.39 228.52 C 549.92 218.32 539.88 211.29 528.52 211.29 C 524.31 211.29 520.19 212.25 516.48 214.08 C 516.51 214.06 516.53 214.04 516.56 214.01 C 521.19 210.23 527.04 208.15 533.04 208.15 Z M 549.23 233.82 L 549.23 231.66 L 554.04 231.66 L 554.04 233.82 Z M 506.61 241.61 C 502.43 242.18 499.14 241.67 497.78 240.83 C 498.6 240.24 500.47 239.43 501.78 238.85 C 502.92 238.36 504.07 237.83 505.18 237.27 C 507 238.66 509.25 239.69 511.74 240.31 C 510.3 240.89 508.52 241.35 506.61 241.61 Z M 560.59 238.98 C 560.35 239 560.1 239.02 559.86 239.04 C 559.04 239.11 558.18 239.19 557.31 239.28 L 557.05 239.31 C 554.27 239.6 551.12 239.92 549.63 239.72 C 548.69 239.58 546.97 238.83 545.14 238.04 C 542.04 236.7 538.19 235.03 534.71 234.76 C 529.91 234.39 521.62 235.3 516.28 237.8 C 513.19 237.66 510.32 236.83 508.1 235.46 C 508.09 235.03 508.08 234.6 508.08 234.44 C 508.08 227.92 511.13 221.82 516.45 217.67 C 520.07 215.55 524.24 214.44 528.52 214.44 C 538.15 214.44 546.71 220.13 550.04 228.52 L 548.76 228.52 C 547.48 228.52 546.41 229.42 546.15 230.63 C 545.93 230.54 545.73 230.47 545.5 230.38 C 542.3 229.15 537.92 227.46 532.85 227.76 C 525.44 228.2 521.8 228.64 518.93 229.46 L 519.79 232.49 C 522.42 231.74 525.88 231.33 533.03 230.9 C 537.42 230.66 541.44 232.19 544.36 233.32 C 544.98 233.56 545.55 233.77 546.08 233.96 L 546.08 234.28 C 546.08 235.76 547.29 236.96 548.76 236.96 L 554.51 236.96 C 555.98 236.96 557.18 235.76 557.19 234.29 L 557.75 234.24 C 558.68 234.16 559.54 234.07 560.26 234 L 560.59 233.97 Z" fill="#bf0816" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 252px; margin-left: 529px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                IAM Role for B
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="529" y="264" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    IAM Role fo...
                </text>
            </switch>
        </g>
        <path d="M 274 130 L 274.05 215.06 Q 274.06 225.06 284.06 225.06 L 484.16 225.06" fill="none" stroke="#6c8ebf" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 490.91 225.06 L 481.91 229.56 L 484.16 225.06 L 481.91 220.56 Z" fill="#6c8ebf" stroke="#6c8ebf" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 250px; margin-left: 394px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Assume Role
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="394" y="253" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Assume Role
                </text>
            </switch>
        </g>
        <rect x="254" y="150" width="40" height="20" fill="rgb(255, 255, 255)" stroke="none" pointer-events="all"/>
        <path d="M 529 130 L 529 150 Q 529 160 519 160 L 94.1 160" fill="none" stroke="#b85450" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 87.35 160 L 96.35 155.5 L 94.1 160 L 96.35 164.5 Z" fill="#b85450" stroke="#b85450" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 140px; margin-left: 394px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                払い出し
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="394" y="143" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    払い出し
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>