<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="412px" height="168px" viewBox="-0.5 -0.5 412 168" content="&lt;mxfile&gt;&lt;diagram name=&quot;240902&quot; id=&quot;Jz8Ql4FI9TLpKCp8wq6i&quot;&gt;7VjZduMoEP0aPTpHq5fHyEsvJ8n0tOfMPBMJS5wgoUF4y9dPyaAV7J4k7Z6lozgJdSkQcOsWYMubZ4cPHBXpPYsxtVw7PljewnLdmTeFvxVwlIDnBBJIOIkl5LTAmjxjBdoK3ZIYlz1HwRgVpOiDEctzHIkehjhn+77bhtH+WwuUYA1YR4jq6B8kFqlEp+6kxT9ikqT1m53xTNZkqHZWMylTFLN9B/KWljfnjAlZyg5zTKu1q9cl+uA7v4tkN/dH6CF7eH7+PF6PZGerlzRppsBxLl7d9er+LmfO12Axuiuc3dOq3ExQ3fUO0a1arzIvR8BFKfgW2JATF8d6NfcpEXhdoKiy9xAwlhemIqNgOVDkbJvHOFbWhlA6Z5RxsHOWQ5OQokdMv7CSCMJygCOYEYb6cIe5IMDa3cBBsOoViJLE6H6rKh6ZECyDCjUfqMaHQQB8Y/WchlKQAmYZFvwI7VQvQR0FSgZNVOw7QaWgtBtPtgKRiuOk6brlCgqKrhdQZ+vUpTDHEccl2/IIl6NSoOhJo9BIlw0WUM6ecE2Y5Xr26fm/EWn3iXRmOpGOa2Iy+A5MGgfsakzGjyO05YyjMxz+bRn+PLw2G84FXt3gh/Lqa7xaS8+autatLHjW7UwVwrkqTKca2TiGDU2ZjIuUJSxHdNmi56mXfVUdvGKpYRCnRHLBTx0FBOIJvtTfzEwdxxQJsusPzkTDqekt5+jYcSgYyUXZ6flLBbQR4fuDlD0ebJAv84eCHEEbEM1UXh8jgRYjSvjZsfyTtjtx+batWMsB/mLirjxzDohRmTYt/7sJwfv2ju3a19qyjQMea2TDuomEYwDXv95pFJdPWESp0rIKd6gIQvjAGOfyNwDXeYXcuIEBNGETHXR0N/jnmN4wBE3YRAcd3a2y6lH3QRM2CfQRD1s7htbOoDV8vJBtBSU5SKK+dpw2RJaLjkjgZ1XRGyYcxQT36hoB1XULwqEjqYG8ytMDcUEbePxwZpLj5vR0tWdfkFijnaGozmivkxaqGapLmuPWtoq46pWoLORybMihGkcIh8qiqswOSXU/vEH70r+pz5ifomo8IZiy1PeSmUzLRVcTfDAQvK8L3jfo3b+W3Cea3O+P7zJ/l/m7zN8ic2/yL5P5VJP5L181jcMiCC0i1HlLI9JwJutwO4yGjMTx6RpgOhGa7gNX4MQechJonIwNnHjX4mSmcbJ+WAPwGytIdDkBvyljKLYGCSG07am/OEf/P50LTjsO5ssdlhuPcy4diNPiDXWupZJrBVkwvJ9dT/hgtt/ryqte++W4t/wL&lt;/diagram&gt;&lt;diagram id=&quot;8Hscz_39ugt1WtNNDxFl&quot; name=&quot;240820&quot;&gt;7VjbctowEP0aPybjK4bHGEjamaRNS2f6rNjC1kS2XFnc+vVd2fJVhpIpTaczGBiksytL2rNHXjCcebp/4ChPnliEqWGb0d5wFoZtW5btwZdEDhXi+NMKiDmJlFMLrMhPrEBToRsS4aLnKBijguR9MGRZhkPRwxDnbNd3WzPanzVHMdaAVYiojn4nkUgqdGr7Lf4BkzipZ7Yms8qSotpZ7aRIUMR2HchZGs6cMyaqVrqfYyqDV8elGnd/xNosjONMnDVArWOL6EZtLnq5QRvOOLopBApf1TrFod78LiECr3IUyv4OCDacIBEphZ4FTc42WYQj1SsEZ694zijj5WjHLC+wrAmlNZ6xDG4WUPSC6TMriCAsAziETWCwB1vMBYHwPw4cBJOTI0riUfc7ZXhhQrAUDHp0VMDkCLzvQCpaD5ilWPADuChrEzGVutZM9XdtIrg1u0k3CTwFIpV8cXPvlh9oKIqO0GVpdCmuEEU8vYGEh4hvIOWvrHVZc/0+a43+OqxZ7hhr5iVYszXWjKVjTG3jrmo4xt1MNYK5akynGoc4glNIdRkXCYtZhuiyRccZNU/FsGAbHqr7q+NLIB5j5aUWIWc+GWeOKRJk2z8fx2JWDr3jHB06DjkjmSg6d36WQIc+d0DfZHCqvc0fGtUKWvaarZxFqH9Mhemh+EFbFRYXlqG78O17Z1yGESqSZuT/oEk4VX6rSdv8W5p0NQohGiLmGMDVl0eNuOIVizBRalL5CgYvgDcsZ159PHCdS+RWljgaOIb5OmjpbvBljc0wBMcwXwct3U326lX3wTHM9/QVD0dbI6OtwWh4OwHbCEoySPS6WiufNCwTndSH171kMog5igju2RpZ1LYF4XCjKrMzeVIOJANj4HKD2ZjI1uXVVZR5QjiNIoZSOaKojtjlDlVtC08I1VcZJ6dERV6FY032ch0BFIu5NKb7WNbVt2hXuLccV0f4x1CuJ4Bu1ep7VeeTdsJcQsbeQMbuSEE0omL3AiL2NBE/Ha7ivYr3Kt4zxesM6+J3FO9EE+/nr5pyYR9C41lVPBo9I1VRh7EhxymJorJoHqvJzqye3xBpcxhpT4v0ZCTSzgUiPdUivfq0AuAby0l4+rD8I3UrDgbiDUxz6i6OkfqvdVs+HTBfbnH1kLCOSVeUwRtqUpP9BVLHs99NpNBt/3uqfhi1/+A5y18=&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-4d72f3-1-3334b9-1-s-0">
            <stop offset="0%" style="stop-color: rgb(51, 52, 185); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(77, 114, 243); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="310" y="46" width="80" height="100" rx="12" ry="12" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 43px; margin-left: 311px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                sns-construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="350" y="43" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    sns-construct
                </text>
            </switch>
        </g>
        <rect x="290" y="16" width="120" height="150" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 13px; margin-left: 350px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                share-resources-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="350" y="13" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    share-resources-stack
                </text>
            </switch>
        </g>
        <rect x="0" y="16" width="250" height="150" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 248px; height: 1px; padding-top: 13px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                db-aurora-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="125" y="13" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    db-aurora-stack
                </text>
            </switch>
        </g>
        <path d="M 220 86 L 230 86 L 323.63 86" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 328.88 86 L 321.88 89.5 L 323.63 86 L 321.88 82.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 86px; margin-left: 275px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                アラート
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="275" y="89" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    アラート
                </text>
            </switch>
        </g>
        <rect x="20" y="46" width="200" height="100" rx="15" ry="15" fill="none" stroke="#4d72f3" stroke-dasharray="3 3" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 43px; margin-left: 21px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                aurora-mysql-constructs
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="120" y="43" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    aurora-mysql-constructs
                </text>
            </switch>
        </g>
        <path d="M 40 66 L 80 66 L 80 106 L 40 106 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 62.81 73.89 L 61.15 73.89 L 61.15 72.77 L 62.81 72.77 L 62.81 71.1 L 63.92 71.1 L 63.92 72.77 L 65.58 72.77 L 65.58 73.89 L 63.92 73.89 L 63.92 75.56 L 62.81 75.56 Z M 69.45 80.01 L 67.79 80.01 L 67.79 78.9 L 69.45 78.9 L 69.45 77.23 L 70.55 77.23 L 70.55 78.9 L 72.21 78.9 L 72.21 80.01 L 70.55 80.01 L 70.55 81.68 L 69.45 81.68 Z M 66.57 97.6 C 65.51 94.92 63.07 92.47 60.4 91.4 C 63.07 90.34 65.51 87.89 66.57 85.2 C 67.62 87.89 70.06 90.34 72.73 91.4 C 70.06 92.47 67.62 94.92 66.57 97.6 Z M 75.45 90.85 C 71.56 90.85 67.12 86.38 67.12 82.47 C 67.12 82.16 66.87 81.91 66.57 81.91 C 66.26 81.91 66.01 82.16 66.01 82.47 C 66.01 86.38 61.58 90.85 57.68 90.85 C 57.38 90.85 57.13 91.1 57.13 91.4 C 57.13 91.71 57.38 91.96 57.68 91.96 C 61.58 91.96 66.01 96.42 66.01 100.34 C 66.01 100.65 66.26 100.9 66.57 100.9 C 66.87 100.9 67.12 100.65 67.12 100.34 C 67.12 96.42 71.56 91.96 75.45 91.96 C 75.75 91.96 76 91.71 76 91.4 C 76 91.1 75.75 90.85 75.45 90.85 Z M 45.11 79.88 C 46.72 81.06 49.85 81.68 52.85 81.68 C 55.85 81.68 58.99 81.06 60.6 79.88 L 60.6 85.21 C 59.8 86.28 56.87 87.33 52.96 87.33 C 48.47 87.33 45.11 85.91 45.11 84.65 Z M 52.85 75.56 C 57.65 75.56 60.6 77.02 60.6 78.06 C 60.6 79.11 57.65 80.57 52.85 80.57 C 48.05 80.57 45.11 79.11 45.11 78.06 C 45.11 77.02 48.05 75.56 52.85 75.56 Z M 60.6 96.45 C 60.6 97.73 57.28 99.17 52.85 99.17 C 48.42 99.17 45.11 97.73 45.11 96.45 L 45.11 92.89 C 46.74 94.14 49.93 94.8 52.99 94.8 C 55.12 94.8 57.18 94.49 58.79 93.94 L 58.44 92.89 C 56.94 93.4 55.01 93.68 52.99 93.68 C 48.48 93.68 45.11 92.27 45.11 91.01 L 45.11 86.54 C 46.73 87.78 49.91 88.44 52.96 88.44 C 56.23 88.44 59.03 87.76 60.6 86.69 L 60.6 88.36 L 61.7 88.36 L 61.7 78.06 C 61.7 75.71 57.14 74.44 52.85 74.44 C 48.73 74.44 44.38 75.61 44.03 77.78 L 44 77.78 L 44 96.45 C 44 98.94 48.56 100.28 52.85 100.28 C 57.14 100.28 61.7 98.94 61.7 96.45 L 61.7 94.48 L 60.6 94.48 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 113px; margin-left: 60px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Postgre SQL
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="60" y="125" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Postgre...
                </text>
            </switch>
        </g>
        <path d="M 160 66 L 200 66 L 200 106 L 160 106 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 182.81 73.89 L 181.15 73.89 L 181.15 72.77 L 182.81 72.77 L 182.81 71.1 L 183.92 71.1 L 183.92 72.77 L 185.58 72.77 L 185.58 73.89 L 183.92 73.89 L 183.92 75.56 L 182.81 75.56 Z M 189.45 80.01 L 187.79 80.01 L 187.79 78.9 L 189.45 78.9 L 189.45 77.23 L 190.55 77.23 L 190.55 78.9 L 192.21 78.9 L 192.21 80.01 L 190.55 80.01 L 190.55 81.68 L 189.45 81.68 Z M 186.57 97.6 C 185.51 94.92 183.07 92.47 180.4 91.4 C 183.07 90.34 185.51 87.89 186.57 85.2 C 187.62 87.89 190.06 90.34 192.73 91.4 C 190.06 92.47 187.62 94.92 186.57 97.6 Z M 195.45 90.85 C 191.56 90.85 187.12 86.38 187.12 82.47 C 187.12 82.16 186.87 81.91 186.57 81.91 C 186.26 81.91 186.01 82.16 186.01 82.47 C 186.01 86.38 181.58 90.85 177.68 90.85 C 177.38 90.85 177.13 91.1 177.13 91.4 C 177.13 91.71 177.38 91.96 177.68 91.96 C 181.58 91.96 186.01 96.42 186.01 100.34 C 186.01 100.65 186.26 100.9 186.57 100.9 C 186.87 100.9 187.12 100.65 187.12 100.34 C 187.12 96.42 191.56 91.96 195.45 91.96 C 195.75 91.96 196 91.71 196 91.4 C 196 91.1 195.75 90.85 195.45 90.85 Z M 165.11 79.88 C 166.72 81.06 169.85 81.68 172.85 81.68 C 175.85 81.68 178.99 81.06 180.6 79.88 L 180.6 85.21 C 179.8 86.28 176.87 87.33 172.96 87.33 C 168.47 87.33 165.11 85.91 165.11 84.65 Z M 172.85 75.56 C 177.65 75.56 180.6 77.02 180.6 78.06 C 180.6 79.11 177.65 80.57 172.85 80.57 C 168.05 80.57 165.11 79.11 165.11 78.06 C 165.11 77.02 168.05 75.56 172.85 75.56 Z M 180.6 96.45 C 180.6 97.73 177.28 99.17 172.85 99.17 C 168.42 99.17 165.11 97.73 165.11 96.45 L 165.11 92.89 C 166.74 94.14 169.93 94.8 172.99 94.8 C 175.12 94.8 177.18 94.49 178.79 93.94 L 178.44 92.89 C 176.94 93.4 175.01 93.68 172.99 93.68 C 168.48 93.68 165.11 92.27 165.11 91.01 L 165.11 86.54 C 166.73 87.78 169.91 88.44 172.96 88.44 C 176.23 88.44 179.03 87.76 180.6 86.69 L 180.6 88.36 L 181.7 88.36 L 181.7 78.06 C 181.7 75.71 177.14 74.44 172.85 74.44 C 168.73 74.44 164.38 75.61 164.03 77.78 L 164 77.78 L 164 96.45 C 164 98.94 168.56 100.28 172.85 100.28 C 177.14 100.28 181.7 98.94 181.7 96.45 L 181.7 94.48 L 180.6 94.48 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 113px; margin-left: 180px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                MySQL
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="180" y="125" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    MySQL
                </text>
            </switch>
        </g>
        <rect x="90" y="71" width="60" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 86px; margin-left: 91px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                OR
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="120" y="90" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    OR
                </text>
            </switch>
        </g>
        <rect x="330" y="66" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 369.09 68.73 L 330.91 68.73 C 330.41 68.73 330 69.13 330 69.64 L 330 78.73 C 330 79.23 330.41 79.64 330.91 79.64 L 369.09 79.64 C 369.59 79.64 370 79.23 370 78.73 L 370 69.64 C 370 69.13 369.59 68.73 369.09 68.73 Z M 331.82 77.82 L 331.82 70.55 L 368.18 70.55 L 368.18 77.82 Z M 368.18 89.18 L 370 89.18 L 370 90.55 C 370 91.05 369.59 91.45 369.09 91.45 L 367.73 91.45 L 367.73 89.64 L 368.18 89.64 Z M 351.36 91.45 L 354.09 91.45 L 354.09 89.64 L 351.36 89.64 Z M 362.27 91.45 L 365 91.45 L 365 89.64 L 362.27 89.64 Z M 340.45 91.45 L 343.18 91.45 L 343.18 89.64 L 340.45 89.64 Z M 335 91.45 L 337.73 91.45 L 337.73 89.64 L 335 89.64 Z M 356.82 91.45 L 359.55 91.45 L 359.55 89.64 L 356.82 89.64 Z M 345.91 91.45 L 348.64 91.45 L 348.64 89.64 L 345.91 89.64 Z M 331.82 89.64 L 332.27 89.64 L 332.27 91.45 L 330.91 91.45 C 330.41 91.45 330 91.05 330 90.55 L 330 89.18 L 331.82 89.18 Z M 330 87.36 L 331.82 87.36 L 331.82 85.55 L 330 85.55 Z M 330.91 81.45 L 332.27 81.45 L 332.27 83.27 L 331.82 83.27 L 331.82 83.73 L 330 83.73 L 330 82.36 C 330 81.86 330.41 81.45 330.91 81.45 Z M 351.36 83.27 L 354.09 83.27 L 354.09 81.45 L 351.36 81.45 Z M 345.91 83.27 L 348.64 83.27 L 348.64 81.45 L 345.91 81.45 Z M 340.45 83.27 L 343.18 83.27 L 343.18 81.45 L 340.45 81.45 Z M 362.27 83.27 L 365 83.27 L 365 81.45 L 362.27 81.45 Z M 335 83.27 L 337.73 83.27 L 337.73 81.45 L 335 81.45 Z M 356.82 83.27 L 359.55 83.27 L 359.55 81.45 L 356.82 81.45 Z M 370 82.36 L 370 83.73 L 368.18 83.73 L 368.18 83.27 L 367.73 83.27 L 367.73 81.45 L 369.09 81.45 C 369.59 81.45 370 81.86 370 82.36 Z M 368.18 87.36 L 370 87.36 L 370 85.55 L 368.18 85.55 Z M 368.18 101 L 370 101 L 370 102.36 C 370 102.87 369.59 103.27 369.09 103.27 L 367.73 103.27 L 367.73 101.45 L 368.18 101.45 Z M 340.45 103.27 L 343.18 103.27 L 343.18 101.45 L 340.45 101.45 Z M 351.36 103.27 L 354.09 103.27 L 354.09 101.45 L 351.36 101.45 Z M 335 103.27 L 337.73 103.27 L 337.73 101.45 L 335 101.45 Z M 362.27 103.27 L 365 103.27 L 365 101.45 L 362.27 101.45 Z M 356.82 103.27 L 359.55 103.27 L 359.55 101.45 L 356.82 101.45 Z M 345.91 103.27 L 348.64 103.27 L 348.64 101.45 L 345.91 101.45 Z M 331.82 101.45 L 332.27 101.45 L 332.27 103.27 L 330.91 103.27 C 330.41 103.27 330 102.87 330 102.36 L 330 101 L 331.82 101 Z M 330 99.18 L 331.82 99.18 L 331.82 97.36 L 330 97.36 Z M 330.91 93.27 L 332.27 93.27 L 332.27 95.09 L 331.82 95.09 L 331.82 95.55 L 330 95.55 L 330 94.18 C 330 93.68 330.41 93.27 330.91 93.27 Z M 351.36 95.09 L 354.09 95.09 L 354.09 93.27 L 351.36 93.27 Z M 356.82 95.09 L 359.55 95.09 L 359.55 93.27 L 356.82 93.27 Z M 335 95.09 L 337.73 95.09 L 337.73 93.27 L 335 93.27 Z M 345.91 95.09 L 348.64 95.09 L 348.64 93.27 L 345.91 93.27 Z M 362.27 95.09 L 365 95.09 L 365 93.27 L 362.27 93.27 Z M 340.45 95.09 L 343.18 95.09 L 343.18 93.27 L 340.45 93.27 Z M 370 94.18 L 370 95.55 L 368.18 95.55 L 368.18 95.09 L 367.73 95.09 L 367.73 93.27 L 369.09 93.27 C 369.59 93.27 370 93.68 370 94.18 Z M 368.18 99.18 L 370 99.18 L 370 97.36 L 368.18 97.36 Z" fill="#b0084d" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 113px; margin-left: 350px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                SNS Topic
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="350" y="125" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    SNS Top...
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>