<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1071px" height="422px" viewBox="-0.5 -0.5 1071 422" content="&lt;mxfile&gt;&lt;diagram id=&quot;nABLbFnIvHvZXe0GfxQu&quot; name=&quot;ページ1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <rect x="0" y="0" width="1070" height="421" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <rect x="440" y="10" width="620" height="401" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <rect x="15" y="10" width="280" height="401" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <path d="M 585 208 L 510 135" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 585 208 L 670 135" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <rect x="450" y="208" width="270" height="193" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 268px; height: 1px; padding-top: 215px; margin-left: 452px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                本体アカウント(prod01環境)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="452" y="227" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    本体アカウント(prod01環境)
                </text>
            </switch>
        </g>
        <rect x="450" y="75" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 105px; margin-left: 451px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Route53アカウント
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="510" y="109" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Route53アカウント
                </text>
            </switch>
        </g>
        <rect x="610" y="75" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 105px; margin-left: 611px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                マーケットプレイスアカウント
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="670" y="109" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    マーケットプレイスアカウント
                </text>
            </switch>
        </g>
        <path d="M 910 208 L 670 135" fill="none" stroke="#cc0000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 910 208 L 510 135" fill="none" stroke="#cc0000" stroke-miterlimit="10" pointer-events="stroke"/>
        <rect x="25" y="20" width="50" height="50" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 25 45 C 25 31.19 36.19 20 50 20 C 63.81 20 75 31.19 75 45 C 75 58.81 63.81 70 50 70 C 36.19 70 25 58.81 25 45 Z M 27.4 45 C 27.34 55.01 33.9 63.85 43.5 66.7 L 43.5 62.35 C 43.59 60.79 44.41 59.37 45.7 58.5 C 41.9 58.08 38.47 56.4 36.22 53.85 C 33.98 51.3 33.1 48.1 33.8 45 C 34.16 42.85 35.07 40.84 36.45 39.15 C 35.6 36.98 35.67 34.56 36.65 32.45 C 39.18 32.55 41.6 33.52 43.5 35.2 C 47.71 33.74 52.28 33.72 56.5 35.15 C 58.43 33.47 60.89 32.51 63.45 32.45 C 64.44 34.65 64.52 37.15 63.65 39.4 C 64.95 41.03 65.83 42.95 66.2 45 C 66.9 48.09 66.03 51.28 63.79 53.83 C 61.55 56.38 58.14 58.07 54.35 58.5 C 55.57 59.28 56.39 60.56 56.6 62 L 56.6 66.8 C 66.21 63.92 72.76 55.03 72.65 45 C 72.65 39.01 70.26 33.26 66.01 29.04 C 61.75 24.81 55.99 22.46 50 22.5 C 44.02 22.47 38.27 24.83 34.03 29.05 C 29.78 33.28 27.4 39.02 27.4 45 Z" fill="#000000" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 77px; margin-left: 50px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                GitHub
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="50" y="89" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    GitHub
                </text>
            </switch>
        </g>
        <image x="439.5" y="9.5" width="50" height="50" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD8AAAA/CAMAAABggeDtAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURSQvPjI8SltjbpKXn9bY2/Hy8////+Tl55+kqrq+woSKkz9JVmlwe62xtsjLz01WYnZ9hxOS1AAAAAAJcEhZcwAAFxEAABcRAcom8z8AAAHZSURBVFhH7ZXbcusgDEW5WVxt+P+v7d7C6UOD3Z4+dc54TSYmCMFGkhXz8PDw8Eew9hysuTc7HzaRmDD0uXCm5tzOZzVWzdlzYkWFFR+RYMwuG6cK1vPpZbMmwxxh1h0X2MN3a+wuMkwTcZiCi1DygW0wVTCuhYY7IhZbLNZh5D741Uyakr7nkIMnQ3iXkAT3xdEV/tLPFVdYN4armf6Dwoekxn0SvyBC8rhLwNDoAfhjtYOIZqk6UoRxDJ7MhKxAsA/fht/orz4RsoP0TvmkIbbX8Q8SVJ3qZ8pwfWovg88TaLwKowgrhyfTHxfA9Rm7nFX+CVRexGDq5gI9IaKYIBUB4DWQDE6qrjl4A5dLowTWINciX+qHqKn8Q/YyChZdFbANjM5WcCJLDFFTP+yjHp5W2NN1CodPTHCf0XZuSnZuetjh9+S/q6E/jm2t/f4KTkO8zMCYxXMPXi/XjrPOvpDX0+8M9JcVSHCambvBuurPt+mNjlLL671PnI8yDm2JS1D77LDLDmfRnlF+o2t1XlD1Dccee3GfKm1vZWfvZnEbf1X/k8r35sUWY9xeXUmiZyFfl/+Lxrb9lS2juf+Y5g/tdsoWMv8Z/pnqUKyu11+4Pjw8/O8Y8wHjAA9cnAnpaQAAAABJRU5ErkJggg==" preserveAspectRatio="none"/>
        <rect x="80" y="30" width="210" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 208px; height: 1px; padding-top: 45px; margin-left: 81px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <span style="color: rgb(0, 0, 0); font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;">
                                    csys-infra-gevanni-infra
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="185" y="50" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="15px" text-anchor="middle">
                    csys-infra-gevanni-infra
                </text>
            </switch>
        </g>
        <rect x="95" y="61" width="180" height="310" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 178px; height: 1px; padding-top: 68px; margin-left: 97px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <p style="line-height: 140%;">
                                    usecases
                                    <br/>
                                    ├── common
                                    <br/>
                                    │   ├── bin
                                    <br/>
                                    │   ├── lib
                                    <br/>
                                    │   └── params
                                    <br/>
                                    │       ├── dev01
                                    <br/>
                                    │       ├── prod01
                                    <br/>
                                    │       ├──
                                    <font color="#cc0000">
                                        prod02
                                    </font>
                                    <br/>
                                    │       └── stg01
                                    <br/>
                                    ├── hostedzone
                                    <br/>
                                    └── services
                                </p>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="97" y="83" fill="#000000" font-family="Helvetica" font-size="15px">
                    usecases...
                </text>
            </switch>
        </g>
        <path d="M 310 172.25 L 310 151.5 Q 310 141.5 320 141.5 L 440 141.5 Q 450 141.5 450 151.5 L 450 193 Q 450 203 440 203 L 410 203 Q 400 203 408.57 208.14 L 450 233 L 389.19 206.94 Q 380 203 370 203 L 320 203 Q 310 203 310 193 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 172px; margin-left: 311px;">
                        <div data-drawio-colors="color: black; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: black; line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <span style="font-size: 13px;">
                                    ①クォータ抵触を検知
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="380" y="177" fill="black" font-family="Helvetica" font-size="15px" text-anchor="middle">
                    ①クォータ抵触を検知
                </text>
            </switch>
        </g>
        <rect x="770" y="208" width="270" height="193" fill="#f8cecc" stroke="#b85450" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 268px; height: 1px; padding-top: 215px; margin-left: 772px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                本体アカウント(prod02環境)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="772" y="227" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    本体アカウント(prod02環境)
                </text>
            </switch>
        </g>
        <rect x="460" y="233" width="250" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 248px; height: 1px; padding-top: 263px; margin-left: 461px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                共有リソース
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="585" y="267" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    共有リソース
                </text>
            </switch>
        </g>
        <rect x="780" y="233" width="250" height="60" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 248px; height: 1px; padding-top: 263px; margin-left: 781px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                共有リソース
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="905" y="267" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    共有リソース
                </text>
            </switch>
        </g>
        <rect x="460" y="301" width="250" height="90" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 248px; height: 1px; padding-top: 308px; margin-left: 461px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                専有リソース
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="585" y="320" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    専有リソース
                </text>
            </switch>
        </g>
        <rect x="470" y="331" width="50" height="50" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 48px; height: 1px; padding-top: 356px; margin-left: 471px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                PJ_A
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="495" y="360" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    PJ_A
                </text>
            </switch>
        </g>
        <rect x="530" y="331" width="50" height="50" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 48px; height: 1px; padding-top: 356px; margin-left: 531px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                PJ_B
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="555" y="360" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    PJ_B
                </text>
            </switch>
        </g>
        <rect x="590" y="331" width="50" height="50" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 48px; height: 1px; padding-top: 356px; margin-left: 591px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                PJ_C
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="615" y="360" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    PJ_C
                </text>
            </switch>
        </g>
        <rect x="650" y="331" width="50" height="50" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 48px; height: 1px; padding-top: 356px; margin-left: 651px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                PJ_D
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="675" y="360" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    PJ_D
                </text>
            </switch>
        </g>
        <rect x="790" y="331" width="50" height="50" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 48px; height: 1px; padding-top: 356px; margin-left: 791px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                PJ_E
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="815" y="360" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    PJ_E
                </text>
            </switch>
        </g>
        <rect x="780" y="301" width="250" height="90" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 248px; height: 1px; padding-top: 308px; margin-left: 781px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                専有リソース
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="905" y="320" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    専有リソース
                </text>
            </switch>
        </g>
        <path d="M 220 270.25 L 220 249.5 Q 220 239.5 230 239.5 L 350 239.5 Q 360 239.5 360 249.5 L 360 291 Q 360 301 350 301 L 320 301 Q 310 301 318.57 306.14 L 360 331 L 299.19 304.94 Q 290 301 280 301 L 230 301 Q 220 301 220 291 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" transform="rotate(180,290,285.25)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 300px; margin-left: 221px;">
                        <div data-drawio-colors="color: black; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: black; line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 13px;">
                                    ②環境ファイルを追加
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="290" y="305" fill="black" font-family="Helvetica" font-size="15px" text-anchor="middle">
                    ②環境ファイルを追加
                </text>
            </switch>
        </g>
        <path d="M 910 161.75 L 910 141 Q 910 131 920 131 L 1040 131 Q 1050 131 1050 141 L 1050 182.5 Q 1050 192.5 1040 192.5 L 1010 192.5 Q 1000 192.5 992.95 199.59 L 970.2 222.5 L 976.89 202.01 Q 980 192.5 970 192.5 L 920 192.5 Q 910 192.5 910 182.5 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 162px; margin-left: 911px;">
                        <div data-drawio-colors="color: black; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: black; line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <span style="font-size: 13px;">
                                    ③アカウント追加
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="980" y="166" fill="black" font-family="Helvetica" font-size="15px" text-anchor="middle">
                    ③アカウント追加
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>