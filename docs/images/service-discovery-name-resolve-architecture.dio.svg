<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="521px" height="447px" viewBox="-0.5 -0.5 521 447" content="&lt;mxfile&gt;&lt;diagram name=&quot;250207&quot; id=&quot;CKOOxijfYHcU8SdnqaXl&quot;&gt;7Vtbc+o2EP41PIbxFZPHmMtpZ87ppKHT9jxlFFsYN8byyCLA+fVdYcnYlgkYDCEZEpJYa3m92v12tVopHXMwX32jKJn9ID6OOobmrzrmsGMYlm3Cb05YZwRTNzJCQEM/I+lbwiT8hQVRE9RF6OO01JERErEwKRM9EsfYYyUaopQsy92mJCq/NUEBVggTD0Uq9Z/QZ7OM2jecLf03HAYz+Wa9d5/dmSPZWYwknSGfLAskc9QxB5QQll3NVwMccd1Jvfz5/b9x/OBOmWfZOPHH+O9fd3cZs3GTR8QQ3lC0EIN6Qd5rQMki9oVobC3HGxHvFXNuesd05Rgpjlm7MhmKTIoky1nI8CRBHm8vAVwg0IzNIyHbNIyiAYkIhXZMYujkpoySVyyJHcMcWrarOYcPRGoKU4ZXBfuLgX3DZI4ZXUMXcdfQhHEFug1HtJdbrNgSALMCTkxJRAKfQc57q0q4ENpsoFnza2jWrmjW7Kma1Xs1ms1N0rpmrctq1kfpLPdE3nhEjGEaA6UPb+2fE9a9q1O+rSh/NJgo+k9fMfO4gBpoJyFhzDZy2C58QLJB9mND1wGndA27hlhHc1SirnaDP3rdG6rEOpqjEnW1G29JqcvEOppjqxJXn9ZrntYrT8PHdMmCRWEMUJUTLdfxlMSsAF74HnOjugFFfohL98ZOf6RZhXvDkAKjkMQbb6AcWCUP4c6g2QPdqXOT6ear6CZcHA7xECbv7+gFR48kDQX7eej7HCN5h4coDEo3kCBEeMoqTgtDFHkJ5C6iLSDH34nSJNPHNFxxQVyY6BN+c74KeErURcvU6lKckgX18O8eF8iFZnZV7oU9wKsbVaSnmYN96Cxm1k1i53L2nuLsLmQsmKcr2gTTtxDi67uufxJWRWSuQlHG5TIURefrQ+Em+mE6esNZENR3IRMw95wKpe7G3obu5nlj0a83Xx+cChhW1+xfEKGOglCKI7TRWRWY50ui+8q7sA+LF9EUyCxgCASg6385YCDei+bP4r3hSqApa61FK0O8XP5w9OFVyAqMoPVTPgnXWza8IbkcqAeh2Sxa7s9zGaIBfo+fNABXzbtoywz4Vl731QFHPPrIHWyL0t4ulEoW2YjEU0VT72NkVRhlQ1YYbXCcj+d4aN8r0H7CQZhCLFHgVsTWEYhQcdUyRg62vVB9lhwVdH8nA0jL4DDujwSH3a8wqq4jd4Bjy0h2JNNpis8CIFm6KSDowfNwmjYMV6fCY6/VpQ7NVmxsOmXTWPqxNrb3MLoGG6tFpQlDsY8ibkZD+wulr7cUrc0UjXGNHpSHnbh8kKlNq9UCw+zKwu9F8jP5smYxiC9CSUBiFI221IuGJKHSO5iJdMcqz0VGO3OREqfamossp7049UApWhd6iFrOzkEpstyX8mm4yDi2GwTVWusThDUe/+S2x6029SlqU/eWPRwbzWpT9oOpufbptakXwhiZ10w5jCSF+cbDfK74uNIU5ch+Bly3WQtoPNfk+ybCz3VbnWvkeqk40VTXUO1NNOquwM7l0nWku+eaR/TG4X9Hvqsw2jshXSDfVfcf/kBzHumfwHuiN7Ue2dTYV1BSsZolKSCpo1dyFKslbFllSDRf5kpsVUHav0JsqeVuGZIVVB1o8WNOCaglzUFEFj6QfqDkls3cspmvlM14HNrP880G/YE+lbvp4RsXvWtLVvqKj+cba72I1wheIGvpBfwK0McQoFBNYz5BGeeSKDy4iuNJjT43OFt1Ouos7TDUGXa3b54LeOqmAigy2y67Ae+LAK+6NrsG4EmRSsDjILrB7svCrn8FsFO3RxRY8eJJcqpW8oPP6EXy1d7Vlq2XtWXWOKlep66zleqlak44V7n7EGXNicvKBs6BFsgtuh+XH6dJtR6dn5bShmHqEZB+fVvKfaKl3O3QZKNDk2fdGD0uBLR0cBKa238VySpP2/+3MUf/Aw==&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f78e04-1-d05c17-1-s-0">
            <stop offset="0%" style="stop-color: rgb(208, 92, 23); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(247, 142, 4); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-945df2-1-5a30b5-1-s-0">
            <stop offset="0%" style="stop-color: rgb(90, 48, 181); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(148, 93, 242); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="0" y="126" width="520" height="320" fill="none" stroke="#d45b07" pointer-events="all"/>
        <rect x="300" y="216" width="160" height="200" fill="none" stroke="#d45b07" pointer-events="all"/>
        <rect x="60" y="216" width="160" height="200" fill="none" stroke="#d45b07" stroke-dasharray="8 8" pointer-events="all"/>
        <path d="M 0 126 L 30 126 L 30 156 L 0 156 Z" fill="url(#mx-gradient-f78e04-1-d05c17-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 24.89 144.26 L 21.52 142.24 L 21.52 137.43 C 21.52 137.28 21.44 137.15 21.31 137.07 L 16.47 134.25 L 16.47 130.18 L 24.89 135.15 Z M 25.52 134.55 L 16.27 129.08 C 16.14 129 15.98 129 15.84 129.07 C 15.71 129.15 15.63 129.29 15.63 129.44 L 15.63 134.49 C 15.63 134.64 15.71 134.78 15.84 134.85 L 20.68 137.68 L 20.68 142.48 C 20.68 142.63 20.76 142.77 20.88 142.84 L 25.09 145.37 C 25.16 145.41 25.23 145.43 25.31 145.43 C 25.38 145.43 25.45 145.41 25.51 145.37 C 25.65 145.3 25.73 145.16 25.73 145.01 L 25.73 134.91 C 25.73 134.76 25.65 134.62 25.52 134.55 Z M 14.98 152.1 L 5.11 146.86 L 5.11 135.15 L 13.53 130.18 L 13.53 134.26 L 9.09 137.08 C 8.97 137.16 8.9 137.29 8.9 137.43 L 8.9 144.59 C 8.9 144.74 8.99 144.89 9.13 144.96 L 14.79 147.9 C 14.91 147.97 15.05 147.97 15.17 147.9 L 20.66 145.07 L 24.04 147.09 Z M 25.1 146.75 L 20.9 144.22 C 20.77 144.15 20.62 144.14 20.49 144.21 L 14.98 147.06 L 9.74 144.33 L 9.74 137.66 L 14.17 134.84 C 14.3 134.77 14.37 134.63 14.37 134.49 L 14.37 129.44 C 14.37 129.29 14.29 129.15 14.16 129.07 C 14.03 129 13.86 129 13.73 129.08 L 4.48 134.55 C 4.35 134.62 4.27 134.76 4.27 134.91 L 4.27 147.11 C 4.27 147.27 4.36 147.41 4.49 147.48 L 14.78 152.95 C 14.84 152.98 14.91 153 14.98 153 C 15.05 153 15.12 152.98 15.18 152.95 L 25.09 147.48 C 25.22 147.41 25.3 147.27 25.31 147.12 C 25.31 146.97 25.23 146.83 25.1 146.75 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 141px; margin-left: 32px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ECS
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="32" y="145" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    ECS
                </text>
            </switch>
        </g>
        <rect x="300" y="216" width="24.38" height="30" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 312.99 240.69 L 321.68 240.69 L 321.68 239.33 L 312.99 239.33 Z M 312.99 233.93 L 321.68 233.93 L 321.68 232.57 L 312.99 232.57 Z M 312.99 227.18 L 321.68 227.18 L 321.68 225.81 L 312.99 225.81 Z M 309.33 240.78 L 309.33 239.24 L 310.86 239.24 L 310.86 240.78 Z M 308.65 242.14 L 311.55 242.14 C 311.92 242.14 312.23 241.83 312.23 241.46 L 312.23 238.56 C 312.23 238.18 311.92 237.88 311.55 237.88 L 308.65 237.88 C 308.27 237.88 307.97 238.18 307.97 238.56 L 307.97 241.46 C 307.97 241.83 308.27 242.14 308.65 242.14 Z M 309.33 234.02 L 309.33 232.49 L 310.86 232.49 L 310.86 234.02 Z M 308.65 235.38 L 311.55 235.38 C 311.92 235.38 312.23 235.08 312.23 234.7 L 312.23 231.8 C 312.23 231.43 311.92 231.12 311.55 231.12 L 308.65 231.12 C 308.27 231.12 307.97 231.43 307.97 231.8 L 307.97 234.7 C 307.97 235.08 308.27 235.38 308.65 235.38 Z M 309.33 227.26 L 309.33 225.73 L 310.86 225.73 L 310.86 227.26 Z M 308.65 228.63 L 311.55 228.63 C 311.92 228.63 312.23 228.32 312.23 227.94 L 312.23 225.05 C 312.23 224.67 311.92 224.37 311.55 224.37 L 308.65 224.37 C 308.27 224.37 307.97 224.67 307.97 225.05 L 307.97 227.94 C 307.97 228.32 308.27 228.63 308.65 228.63 Z M 306.92 244.64 L 306.92 222.83 L 322.93 222.83 L 322.93 244.64 Z M 305.56 222.15 L 305.56 241.74 L 304.02 241.74 L 304.02 219.94 L 320.03 219.94 L 320.03 221.47 L 306.24 221.47 C 305.86 221.47 305.56 221.77 305.56 222.15 Z M 302.66 219.26 L 302.66 239.17 L 301.45 239.17 L 301.45 217.36 L 317.46 217.36 L 317.46 218.57 L 303.34 218.57 C 302.96 218.57 302.66 218.88 302.66 219.26 Z M 323.61 221.47 L 321.4 221.47 L 321.4 219.26 C 321.4 218.88 321.09 218.57 320.72 218.57 L 318.82 218.57 L 318.82 216.68 C 318.82 216.31 318.52 216 318.14 216 L 300.77 216 C 300.39 216 300.09 216.31 300.09 216.68 L 300.09 239.85 C 300.09 240.23 300.39 240.53 300.77 240.53 L 302.66 240.53 L 302.66 242.42 C 302.66 242.8 302.96 243.1 303.34 243.1 L 305.56 243.1 L 305.56 245.32 C 305.56 245.7 305.86 246 306.24 246 L 323.61 246 C 323.99 246 324.29 245.7 324.29 245.32 L 324.29 222.15 C 324.29 221.77 323.99 221.47 323.61 221.47 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 231px; margin-left: 326px;">
                        <div data-drawio-colors="color: #232F3E; background-color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Backend Service
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="326" y="235" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    Backe...
                </text>
            </switch>
        </g>
        <path d="M 380 216 L 380 194.24" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 380 188.24 L 384 196.24 L 380 194.24 L 376 196.24 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 380 156 L 380 84.24" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 380 78.24 L 384 86.24 L 380 84.24 L 376 86.24 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 106px; margin-left: 410px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Register
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="410" y="109" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Register
                </text>
            </switch>
        </g>
        <path d="M 170 266 L 341.76 266" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 347.76 266 L 339.76 270 L 341.76 266 L 339.76 262 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 243px; margin-left: 260px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Access
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="260" y="246" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Access
                </text>
            </switch>
        </g>
        <rect x="60" y="216" width="23.12" height="30" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 69.17 239.44 L 79.91 239.44 L 79.91 238.07 L 69.17 238.07 Z M 69.17 231.09 L 79.91 231.09 L 79.91 229.72 L 69.17 229.72 Z M 69.17 222.73 L 79.91 222.73 L 79.91 221.37 L 69.17 221.37 Z M 64.49 239.86 L 64.49 237.65 L 66.7 237.65 L 66.7 239.86 Z M 63.8 241.23 L 67.38 241.23 C 67.76 241.23 68.07 240.92 68.07 240.55 L 68.07 236.97 C 68.07 236.59 67.76 236.28 67.38 236.28 L 63.8 236.28 C 63.43 236.28 63.12 236.59 63.12 236.97 L 63.12 240.55 C 63.12 240.92 63.43 241.23 63.8 241.23 Z M 64.49 231.51 L 64.49 229.3 L 66.7 229.3 L 66.7 231.51 Z M 63.8 232.88 L 67.38 232.88 C 67.76 232.88 68.07 232.57 68.07 232.19 L 68.07 228.61 C 68.07 228.24 67.76 227.93 67.38 227.93 L 63.8 227.93 C 63.43 227.93 63.12 228.24 63.12 228.61 L 63.12 232.19 C 63.12 232.57 63.43 232.88 63.8 232.88 Z M 64.49 223.16 L 64.49 220.94 L 66.7 220.94 L 66.7 223.16 Z M 63.8 224.52 L 67.38 224.52 C 67.76 224.52 68.07 224.22 68.07 223.84 L 68.07 220.26 C 68.07 219.88 67.76 219.58 67.38 219.58 L 63.8 219.58 C 63.43 219.58 63.12 219.88 63.12 220.26 L 63.12 223.84 C 63.12 224.22 63.43 224.52 63.8 224.52 Z M 61.5 244.64 L 61.5 217.36 L 81.62 217.36 L 81.62 244.64 Z M 82.3 216 L 60.82 216 C 60.44 216 60.14 216.31 60.14 216.68 L 60.14 245.32 C 60.14 245.7 60.44 246 60.82 246 L 82.3 246 C 82.68 246 82.98 245.7 82.98 245.32 L 82.98 216.68 C 82.98 216.31 82.68 216 82.3 216 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 231px; margin-left: 85px;">
                        <div data-drawio-colors="color: #232F3E; background-color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Standalone Task
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="85" y="235" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    Stan...
                </text>
            </switch>
        </g>
        <path d="M 170 346 L 370 346 Q 380 346 380 340.12 L 380 334.24" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 380 328.24 L 384 336.24 L 380 334.24 L 376 336.24 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 366px; margin-left: 260px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Access
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="260" y="369" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Access
                </text>
            </switch>
        </g>
        <path d="M 120 6 L 160 6 L 160 46 L 120 46 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 144.96 26.42 C 145.38 26.81 145.59 27.33 145.59 27.99 C 145.59 28.7 145.33 29.27 144.82 29.7 C 144.31 30.12 143.62 30.34 142.75 30.34 C 142.07 30.34 141.39 30.19 140.72 29.9 L 140.72 29.03 C 141.51 29.29 142.19 29.42 142.75 29.42 C 143.3 29.42 143.73 29.29 144.03 29.05 C 144.33 28.8 144.48 28.45 144.48 27.99 C 144.48 27.11 143.92 26.67 142.81 26.67 C 142.46 26.67 142.11 26.69 141.77 26.72 L 141.77 26 L 144.06 23.5 L 140.83 23.5 L 140.83 22.61 L 145.27 22.61 L 145.27 23.47 L 143.02 25.85 C 143.06 25.84 143.1 25.84 143.13 25.84 L 143.24 25.84 C 143.96 25.84 144.53 26.03 144.96 26.42 M 138.62 26.23 C 139.06 26.65 139.28 27.22 139.28 27.94 C 139.28 28.66 139.02 29.24 138.51 29.68 C 137.99 30.12 137.31 30.34 136.47 30.34 C 135.73 30.34 135.03 30.19 134.38 29.9 L 134.38 29.03 C 135.19 29.29 135.88 29.42 136.46 29.42 C 137.01 29.42 137.43 29.29 137.73 29.04 C 138.02 28.79 138.17 28.43 138.17 27.96 C 138.17 27.44 138.03 27.06 137.75 26.83 C 137.48 26.6 137.03 26.48 136.4 26.48 C 135.95 26.48 135.39 26.52 134.71 26.59 L 134.71 25.87 L 134.92 22.61 L 138.9 22.61 L 138.9 23.5 L 135.83 23.5 L 135.69 25.72 C 136.09 25.65 136.45 25.61 136.77 25.61 C 137.56 25.61 138.17 25.82 138.62 26.23 M 146.82 34.23 C 144.05 34.73 141.63 35.86 140 36.78 C 138.37 35.86 135.95 34.73 133.18 34.23 C 132.4 34.09 128.52 33.28 128.52 31.04 C 128.52 30 128.89 29.31 129.61 28.08 C 130.46 26.6 131.53 24.77 131.53 22.13 C 131.53 20.25 131.03 18.44 130.06 16.75 C 130.17 16.61 130.29 16.47 130.4 16.32 C 131.85 17.04 133.36 17.41 134.89 17.41 C 136.76 17.41 138.47 16.92 140 15.95 C 141.52 16.92 143.24 17.41 145.11 17.41 C 146.64 17.41 148.15 17.04 149.6 16.32 C 149.71 16.47 149.83 16.61 149.94 16.75 C 148.97 18.44 148.47 20.25 148.47 22.13 C 148.47 24.77 149.54 26.6 150.39 28.08 C 151.11 29.31 151.48 30 151.48 31.04 C 151.48 33.28 147.6 34.09 146.82 34.23 M 149.61 22.13 C 149.61 20.33 150.12 18.61 151.12 17 C 151.25 16.8 151.24 16.53 151.08 16.34 C 150.79 15.99 150.49 15.61 150.19 15.24 C 150.01 15.02 149.71 14.97 149.47 15.1 C 148.06 15.88 146.6 16.27 145.11 16.27 C 143.32 16.27 141.75 15.79 140.32 14.8 C 140.13 14.66 139.87 14.66 139.68 14.8 C 138.25 15.79 136.68 16.27 134.89 16.27 C 133.4 16.27 131.94 15.88 130.53 15.1 C 130.29 14.97 129.99 15.02 129.81 15.24 C 129.51 15.61 129.21 15.99 128.92 16.34 C 128.76 16.53 128.75 16.8 128.88 17 C 129.88 18.61 130.39 20.33 130.39 22.13 C 130.39 24.47 129.41 26.15 128.62 27.51 C 127.85 28.83 127.38 29.71 127.38 31.04 C 127.38 34.11 131.67 35.12 132.98 35.36 C 135.75 35.85 138.17 37.03 139.71 37.93 C 139.8 37.98 139.9 38 140 38 C 140.1 38 140.2 37.98 140.29 37.93 C 141.83 37.03 144.25 35.85 147.02 35.36 C 148.33 35.12 152.62 34.11 152.62 31.04 C 152.62 29.71 152.15 28.83 151.38 27.51 C 150.59 26.15 149.61 24.47 149.61 22.13 M 147.41 37.53 C 143.91 38.16 140.98 40.04 140 40.73 C 139.02 40.04 136.09 38.16 132.59 37.53 C 125.71 36.29 125.18 32.25 125.18 31.04 C 125.18 29.05 125.96 27.7 126.71 26.4 C 127.43 25.16 128.18 23.87 128.18 22.13 C 128.18 19.38 126.59 17.44 125.81 16.65 C 126.63 15.65 128.71 13.12 129.74 11.79 C 131.29 13.26 133.1 14.06 134.89 14.06 C 136.87 14.06 138.51 13.25 140 11.53 C 141.49 13.25 143.13 14.06 145.11 14.06 C 146.9 14.06 148.71 13.26 150.26 11.79 C 151.29 13.12 153.37 15.65 154.19 16.65 C 153.41 17.44 151.82 19.38 151.82 22.13 C 151.82 23.87 152.57 25.16 153.29 26.4 C 154.04 27.7 154.82 29.05 154.82 31.04 C 154.82 32.25 154.29 36.29 147.41 37.53 M 154.27 25.83 C 153.57 24.61 152.96 23.56 152.96 22.13 C 152.96 19.12 155.31 17.17 155.33 17.15 C 155.45 17.05 155.52 16.91 155.54 16.76 C 155.55 16.61 155.51 16.46 155.41 16.34 C 155.37 16.3 151.64 11.77 150.78 10.59 C 150.68 10.45 150.52 10.37 150.35 10.36 C 150.18 10.35 150.02 10.41 149.9 10.54 C 148.47 12.07 146.77 12.92 145.11 12.92 C 143.29 12.92 141.85 12.11 140.45 10.28 C 140.24 10 139.76 10 139.55 10.28 C 138.15 12.11 136.71 12.92 134.89 12.92 C 133.23 12.92 131.53 12.07 130.1 10.54 C 129.98 10.41 129.82 10.34 129.65 10.36 C 129.48 10.37 129.32 10.45 129.22 10.59 C 128.36 11.77 124.63 16.3 124.59 16.34 C 124.49 16.46 124.45 16.61 124.46 16.76 C 124.48 16.91 124.55 17.05 124.67 17.15 C 124.69 17.17 127.04 19.12 127.04 22.13 C 127.04 23.56 126.43 24.61 125.73 25.83 C 124.93 27.19 124.04 28.74 124.04 31.04 C 124.04 34.87 127.16 37.71 132.39 38.65 C 136.33 39.36 139.62 41.86 139.65 41.88 C 139.75 41.96 139.88 42 140 42 C 140.12 42 140.24 41.96 140.35 41.88 C 140.38 41.86 143.66 39.36 147.61 38.65 C 152.84 37.71 155.96 34.87 155.96 31.04 C 155.96 28.74 155.07 27.19 154.27 25.83" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 53px; margin-left: 140px;">
                        <div data-drawio-colors="color: #232F3E; background-color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Route 53
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="140" y="65" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Route...
                </text>
            </switch>
        </g>
        <path d="M 350 26 L 178.24 26" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 172.24 26 L 180.24 22 L 178.24 26 L 180.24 30 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 6px; margin-left: 260px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Register
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="260" y="9" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Register
                </text>
            </switch>
        </g>
        <path d="M 140 216 L 140 84.24" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 140 78.24 L 144 86.24 L 140 84.24 L 136 86.24 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 106px; margin-left: 180px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Name Resolve
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="180" y="109" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Name Resolve
                </text>
            </switch>
        </g>
        <path d="M 360 6 L 400 6 L 400 46 L 360 46 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 387.43 27.97 C 387.62 27.97 387.79 27.88 387.9 27.72 C 388.26 27.2 391.43 22.55 391.43 20.45 C 391.43 18.15 389.71 16.41 387.43 16.41 C 385.15 16.41 383.43 18.15 383.43 20.45 C 383.43 22.55 386.6 27.2 386.96 27.72 C 387.07 27.88 387.24 27.97 387.43 27.97 Z M 387.43 17.56 C 389.06 17.56 390.29 18.8 390.29 20.45 C 390.29 21.72 388.52 24.69 387.43 26.36 C 386.34 24.7 384.57 21.72 384.57 20.45 C 384.57 18.8 385.8 17.56 387.43 17.56 Z M 389.72 20.45 C 389.72 19.24 388.63 18.14 387.43 18.14 C 386.23 18.14 385.14 19.24 385.14 20.45 C 385.14 21.66 386.23 22.77 387.43 22.77 C 388.63 22.77 389.72 21.66 389.72 20.45 Z M 386.29 20.45 C 386.29 19.88 386.86 19.3 387.43 19.3 C 388 19.3 388.57 19.88 388.57 20.45 C 388.57 21.03 388 21.61 387.43 21.61 C 386.86 21.61 386.29 21.03 386.29 20.45 Z M 391.62 24.83 L 391.24 25.92 C 392.2 26.26 393.62 27 394.36 28.55 L 385.14 28.55 C 384.83 28.55 384.57 28.8 384.57 29.12 L 384.57 36.06 L 382.86 36.05 L 382.86 33.75 C 382.86 33.43 382.6 33.17 382.29 33.17 L 378.29 33.17 C 377.97 33.17 377.72 33.43 377.72 33.75 L 377.72 36.05 L 376 36.05 L 376 29.7 C 376 29.47 375.87 29.26 375.66 29.17 C 375.46 29.08 375.21 29.12 375.05 29.28 L 368.4 35.44 C 368.02 35.25 367.66 35.02 367.33 34.76 L 375.81 27.25 C 375.93 27.14 376 26.98 376 26.81 L 376 25.08 C 376 24.76 375.75 24.5 375.43 24.5 L 369.14 24.5 C 369.14 24.45 369.14 24.41 369.14 24.37 C 369.14 24.24 369.15 24.12 369.15 24 C 369.15 23.41 369.25 22.79 369.43 22.19 L 375.43 22.19 C 375.75 22.19 376 21.93 376 21.61 L 376 16.68 C 376.58 16.65 377.15 16.69 377.72 16.8 L 377.72 26.23 C 377.72 26.55 377.97 26.81 378.29 26.81 L 382.29 26.81 C 382.6 26.81 382.86 26.55 382.86 26.23 L 382.86 22.77 L 381.72 22.77 L 381.72 25.66 L 378.86 25.66 L 378.86 17.12 C 379.94 17.53 380.97 18.2 381.88 19.13 L 382.69 18.31 C 380 15.58 376.44 14.78 373.17 16.18 C 370.27 17.43 368 20.86 368 24 C 368 24.1 368 24.21 368 24.33 C 367.99 24.43 367.99 24.54 367.99 24.65 C 366.33 25.2 364.01 26.65 364.01 30.51 L 364 30.63 C 364 30.71 364 30.78 364.01 30.89 C 364.2 34.02 366.86 36.73 370.2 37.19 C 370.22 37.19 370.25 37.2 370.28 37.2 L 378.23 37.2 C 378.25 37.21 378.27 37.22 378.29 37.22 L 382.29 37.22 C 382.3 37.22 382.32 37.21 382.33 37.21 L 390.28 37.22 L 390.28 37.22 C 390.3 37.22 390.32 37.22 390.34 37.21 C 390.39 37.21 396 36.56 396 30.86 C 396 26.81 392.94 25.29 391.62 24.83 Z M 378.86 34.33 L 381.72 34.33 L 381.72 36.05 L 378.86 36.05 Z M 373.62 17.25 C 374.03 17.07 374.44 16.94 374.86 16.85 L 374.86 21.03 L 369.88 21.03 C 370.68 19.37 372.06 17.92 373.62 17.25 Z M 365.15 30.67 L 365.15 30.51 C 365.15 27.92 366.33 26.28 368.65 25.66 L 374.86 25.66 L 374.86 26.55 L 366.49 33.96 C 365.71 33.08 365.22 31.98 365.15 30.82 C 365.14 30.75 365.14 30.71 365.15 30.67 Z M 369.61 35.89 L 374.86 31.02 L 374.86 36.05 L 370.32 36.04 C 370.07 36 369.84 35.95 369.61 35.89 Z M 390.25 36.06 L 385.72 36.06 L 385.72 29.7 L 394.75 29.7 C 394.82 30.06 394.86 30.44 394.86 30.86 C 394.86 35.38 390.66 36.01 390.25 36.06 Z M 382.29 27.39 L 378.25 27.39 C 377.93 27.39 377.67 27.65 377.67 27.97 L 377.67 32.01 C 377.67 32.33 377.93 32.59 378.25 32.59 L 382.29 32.59 C 382.6 32.59 382.86 32.33 382.86 32.01 L 382.86 27.97 C 382.86 27.65 382.6 27.39 382.29 27.39 Z M 378.82 31.44 L 378.82 28.55 L 381.72 28.55 L 381.72 31.44 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 53px; margin-left: 380px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Cloud Map
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="380" y="65" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Cloud...
                </text>
            </switch>
        </g>
        <rect x="360" y="256" width="40" height="25.83" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 399.09 256.07 L 360.91 256.07 C 360.41 256.07 360 256.48 360 256.98 L 360 280.85 C 360 281.35 360.41 281.76 360.91 281.76 L 399.09 281.76 C 399.59 281.76 400 281.35 400 280.85 L 400 256.98 C 400 256.48 399.59 256.07 399.09 256.07 Z M 361.82 279.94 L 361.82 257.89 L 398.18 257.89 L 398.18 279.94 Z M 364.77 277.67 L 366.59 277.67 L 366.59 260.17 L 364.77 260.17 Z M 369.55 277.67 L 371.36 277.67 L 371.36 260.17 L 369.55 260.17 Z M 374.32 277.67 L 376.14 277.67 L 376.14 260.17 L 374.32 260.17 Z M 379.09 277.67 L 380.91 277.67 L 380.91 260.17 L 379.09 260.17 Z M 383.86 277.67 L 385.68 277.67 L 385.68 260.17 L 383.86 260.17 Z M 388.64 277.67 L 390.45 277.67 L 390.45 260.17 L 388.64 260.17 Z M 393.41 277.67 L 395.23 277.67 L 395.23 260.17 L 393.41 260.17 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 289px; margin-left: 380px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Backend
                                <br/>
                                Container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="380" y="301" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Backen...
                </text>
            </switch>
        </g>
        <rect x="120" y="256" width="40" height="25.83" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 159.09 256.07 L 120.91 256.07 C 120.41 256.07 120 256.48 120 256.98 L 120 280.85 C 120 281.35 120.41 281.76 120.91 281.76 L 159.09 281.76 C 159.59 281.76 160 281.35 160 280.85 L 160 256.98 C 160 256.48 159.59 256.07 159.09 256.07 Z M 121.82 279.94 L 121.82 257.89 L 158.18 257.89 L 158.18 279.94 Z M 124.77 277.67 L 126.59 277.67 L 126.59 260.17 L 124.77 260.17 Z M 129.55 277.67 L 131.36 277.67 L 131.36 260.17 L 129.55 260.17 Z M 134.32 277.67 L 136.14 277.67 L 136.14 260.17 L 134.32 260.17 Z M 139.09 277.67 L 140.91 277.67 L 140.91 260.17 L 139.09 260.17 Z M 143.86 277.67 L 145.68 277.67 L 145.68 260.17 L 143.86 260.17 Z M 148.64 277.67 L 150.45 277.67 L 150.45 260.17 L 148.64 260.17 Z M 153.41 277.67 L 155.23 277.67 L 155.23 260.17 L 153.41 260.17 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 289px; margin-left: 140px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Bastion
                                <br/>
                                Container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="140" y="301" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Bastio...
                </text>
            </switch>
        </g>
        <rect x="120" y="336" width="40" height="25.83" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 159.09 336.07 L 120.91 336.07 C 120.41 336.07 120 336.48 120 336.98 L 120 360.85 C 120 361.35 120.41 361.76 120.91 361.76 L 159.09 361.76 C 159.59 361.76 160 361.35 160 360.85 L 160 336.98 C 160 336.48 159.59 336.07 159.09 336.07 Z M 121.82 359.94 L 121.82 337.89 L 158.18 337.89 L 158.18 359.94 Z M 124.77 357.67 L 126.59 357.67 L 126.59 340.17 L 124.77 340.17 Z M 129.55 357.67 L 131.36 357.67 L 131.36 340.17 L 129.55 340.17 Z M 134.32 357.67 L 136.14 357.67 L 136.14 340.17 L 134.32 340.17 Z M 139.09 357.67 L 140.91 357.67 L 140.91 340.17 L 139.09 340.17 Z M 143.86 357.67 L 145.68 357.67 L 145.68 340.17 L 143.86 340.17 Z M 148.64 357.67 L 150.45 357.67 L 150.45 340.17 L 148.64 340.17 Z M 153.41 357.67 L 155.23 357.67 L 155.23 340.17 L 153.41 340.17 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 369px; margin-left: 140px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Batch
                                <br/>
                                Container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="140" y="381" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Batch...
                </text>
            </switch>
        </g>
        <rect x="310" y="156" width="140" height="30" fill="none" stroke="#d45b07" pointer-events="none"/>
        <path d="M 310 156 L 340 156 L 340 186 L 310 186 Z" fill="url(#mx-gradient-f78e04-1-d05c17-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 334.89 174.26 L 331.52 172.24 L 331.52 167.43 C 331.52 167.28 331.44 167.15 331.31 167.07 L 326.47 164.25 L 326.47 160.18 L 334.89 165.15 Z M 335.52 164.55 L 326.27 159.08 C 326.14 159 325.98 159 325.84 159.07 C 325.71 159.15 325.63 159.29 325.63 159.44 L 325.63 164.49 C 325.63 164.64 325.71 164.78 325.84 164.85 L 330.68 167.68 L 330.68 172.48 C 330.68 172.63 330.76 172.77 330.88 172.84 L 335.09 175.37 C 335.16 175.41 335.23 175.43 335.31 175.43 C 335.38 175.43 335.45 175.41 335.51 175.37 C 335.65 175.3 335.73 175.16 335.73 175.01 L 335.73 164.91 C 335.73 164.76 335.65 164.62 335.52 164.55 Z M 324.98 182.1 L 315.11 176.86 L 315.11 165.15 L 323.53 160.18 L 323.53 164.26 L 319.09 167.08 C 318.97 167.16 318.9 167.29 318.9 167.43 L 318.9 174.59 C 318.9 174.74 318.99 174.89 319.13 174.96 L 324.79 177.9 C 324.91 177.97 325.05 177.97 325.17 177.9 L 330.66 175.07 L 334.04 177.09 Z M 335.1 176.75 L 330.9 174.22 C 330.77 174.15 330.62 174.14 330.49 174.21 L 324.98 177.06 L 319.74 174.33 L 319.74 167.66 L 324.17 164.84 C 324.3 164.77 324.37 164.63 324.37 164.49 L 324.37 159.44 C 324.37 159.29 324.29 159.15 324.16 159.07 C 324.03 159 323.86 159 323.73 159.08 L 314.48 164.55 C 314.35 164.62 314.27 164.76 314.27 164.91 L 314.27 177.11 C 314.27 177.27 314.36 177.41 314.49 177.48 L 324.78 182.95 C 324.84 182.98 324.91 183 324.98 183 C 325.05 183 325.12 182.98 325.18 182.95 L 335.09 177.48 C 335.22 177.41 335.3 177.27 335.31 177.12 C 335.31 176.97 335.23 176.83 335.1 176.75 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 171px; margin-left: 342px;">
                        <div data-drawio-colors="color: #232F3E; background-color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Service Discovery
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="342" y="175" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    Servi...
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>