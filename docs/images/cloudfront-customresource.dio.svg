<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" style="background: transparent; background-color: transparent;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1491px" height="491px" viewBox="-0.5 -0.5 1491 491" content="&lt;mxfile&gt;&lt;diagram id=&quot;cri7rlrUa9hT3uIwlw8c&quot; name=&quot;ページ1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <g>
            <rect x="0" y="0" width="1490" height="490" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="20" y="40" width="470" height="440" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 468px; height: 1px; padding-top: 47px; margin-left: 22px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    cloudfront-stack.ts
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="22" y="59" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        cloudfront-stack.ts
                    </text>
                </switch>
            </g>
        </g>
        <g/>
        <g>
            <rect x="60" y="242.5" width="410" height="227.5" fill="none" stroke="#e7157b" pointer-events="all" style="stroke: light-dark(rgb(231, 21, 123), rgb(153, 101, 0));"/>
        </g>
        <g>
            <path d="M 360 260 L 410 260 L 410 310 L 360 310 Z" fill="#8c4fff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));"/>
            <path d="M 395.8 293.66 C 395.8 292.48 394.84 291.52 393.66 291.52 C 392.48 291.52 391.52 292.48 391.52 293.66 C 391.52 294.84 392.48 295.8 393.66 295.8 C 394.84 295.8 395.8 294.84 395.8 293.66 Z M 397.23 293.66 C 397.23 295.63 395.63 297.23 393.66 297.23 C 391.69 297.23 390.09 295.63 390.09 293.66 C 390.09 291.69 391.69 290.09 393.66 290.09 C 395.63 290.09 397.23 291.69 397.23 293.66 Z M 378.14 283.35 C 378.14 282.17 377.18 281.21 375.99 281.21 C 374.81 281.21 373.85 282.17 373.85 283.35 C 373.85 284.53 374.81 285.49 375.99 285.49 C 377.18 285.49 378.14 284.53 378.14 283.35 Z M 379.57 283.35 C 379.57 285.32 377.96 286.92 375.99 286.92 C 374.02 286.92 372.42 285.32 372.42 283.35 C 372.42 281.38 374.02 279.78 375.99 279.78 C 377.96 279.78 379.57 281.38 379.57 283.35 Z M 384.81 272.15 C 384.81 273.33 385.77 274.29 386.95 274.29 C 388.13 274.29 389.09 273.33 389.09 272.15 C 389.09 270.97 388.13 270 386.95 270 C 385.77 270 384.81 270.97 384.81 272.15 Z M 383.38 272.15 C 383.38 270.18 384.98 268.58 386.95 268.58 C 388.92 268.58 390.52 270.18 390.52 272.15 C 390.52 274.12 388.92 275.72 386.95 275.72 C 384.98 275.72 383.38 274.12 383.38 272.15 Z M 403.57 285 C 403.57 278.38 400.02 272.25 394.29 268.94 C 393.27 269.14 392.27 269.43 391.04 269.87 L 390.55 268.53 C 391.2 268.3 391.78 268.11 392.33 267.95 C 390.03 266.95 387.53 266.43 385 266.43 C 383.79 266.43 382.61 266.55 381.45 266.78 C 382.29 267.27 383.03 267.76 383.75 268.31 L 382.89 269.45 C 381.87 268.68 380.81 268.02 379.43 267.29 C 372.41 269.5 367.36 275.68 366.56 282.95 C 368.03 282.65 369.44 282.49 371.01 282.45 L 371.05 283.88 C 369.4 283.92 367.99 284.09 366.45 284.43 C 366.44 284.62 366.43 284.81 366.43 285 C 366.43 291.18 369.49 296.88 374.52 300.32 C 373.62 297.65 373.18 295.14 373.18 292.68 C 373.18 291.27 373.42 290.12 373.68 288.9 C 373.73 288.62 373.79 288.33 373.85 288.03 L 375.25 288.31 C 375.19 288.61 375.13 288.91 375.07 289.19 C 374.82 290.39 374.61 291.42 374.61 292.68 C 374.61 295.47 375.22 298.35 376.47 301.49 C 379.12 302.87 381.99 303.57 385 303.57 C 386.97 303.57 388.89 303.26 390.72 302.65 C 391.44 301.23 391.98 299.89 392.42 298.36 L 393.79 298.75 C 393.47 299.88 393.09 300.89 392.65 301.91 C 393.8 301.39 394.89 300.75 395.92 300 C 395.67 299.4 395.41 298.8 395.11 298.21 L 396.39 297.57 C 396.64 298.07 396.87 298.58 397.09 299.09 C 401.22 295.55 403.57 290.47 403.57 285 Z M 405 285 C 405 291.23 402.17 297 397.23 300.82 C 396.01 301.77 394.68 302.56 393.28 303.19 C 392.68 303.46 392.08 303.71 391.45 303.92 C 389.4 304.64 387.22 305 385 305 C 381.71 305 378.45 304.18 375.56 302.63 C 369.05 299.15 365 292.39 365 285 C 365 284.51 365.01 284.14 365.04 283.79 C 365.53 275.45 371.25 268.22 379.29 265.83 C 381.12 265.28 383.04 265 385 265 C 388.43 265 391.81 265.88 394.77 267.56 C 401.08 271.09 405 277.77 405 285 Z M 383.08 274.62 L 382.14 273.54 C 380.54 274.94 379.3 276.42 377.84 278.66 L 379.04 279.44 C 380.42 277.32 381.59 275.92 383.08 274.62 Z M 380.91 283.88 L 380.45 285.23 C 383.73 286.35 386.59 288.15 389.45 290.88 L 390.44 289.85 C 387.42 286.97 384.39 285.07 380.91 283.88 Z M 390.46 275.48 C 393.14 279.56 394.65 284.05 394.95 288.81 L 393.52 288.9 C 393.24 284.39 391.81 280.14 389.27 276.26 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 317px; margin-left: 385px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    staging distribution
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="385" y="329" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        staging...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 240 285 L 353.63 285" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 358.88 285 L 351.88 288.5 L 353.63 285 L 351.88 281.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 285px; margin-left: 300px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ➂create
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="300" y="288" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        ➂create
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 214.82 310 L 214.27 383.64" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 214.23 388.89 L 210.78 381.87 L 214.27 383.64 L 217.78 381.92 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 350px; margin-left: 215px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ➄create ssm
                                    <div>
                                        <font color="#000000" style="color: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));">
                                            &amp;
                                            <br/>
                                        </font>
                                        <div>
                                            keep param
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="215" y="353" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        ➄create ssm...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 190 260 L 240 260 L 240 310 L 190 310 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
            <path d="M 206.45 303.57 L 197.58 303.57 L 207.39 283.07 L 211.84 292.22 Z M 208.03 281.11 C 207.91 280.86 207.66 280.71 207.39 280.71 L 207.39 280.71 C 207.11 280.71 206.86 280.87 206.74 281.11 L 195.8 303.98 C 195.7 304.2 195.71 304.46 195.84 304.67 C 195.97 304.87 196.2 305 196.45 305 L 206.91 305 C 207.18 305 207.43 304.84 207.55 304.59 L 213.28 292.52 C 213.37 292.32 213.37 292.1 213.27 291.9 Z M 232.87 303.57 L 224.06 303.57 L 209.92 273.98 C 209.81 273.73 209.55 273.57 209.28 273.57 L 203.51 273.57 L 203.52 266.43 L 214.82 266.43 L 228.89 296.02 C 229.01 296.27 229.26 296.43 229.54 296.43 L 232.87 296.43 Z M 233.59 295 L 229.99 295 L 215.92 265.41 C 215.8 265.16 215.55 265 215.27 265 L 202.8 265 C 202.41 265 202.09 265.32 202.09 265.71 L 202.08 274.29 C 202.08 274.48 202.15 274.66 202.29 274.79 C 202.42 274.93 202.6 275 202.79 275 L 208.82 275 L 222.96 304.59 C 223.08 304.84 223.33 305 223.61 305 L 233.59 305 C 233.98 305 234.3 304.68 234.3 304.29 L 234.3 295.71 C 234.3 295.32 233.98 295 233.59 295 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 317px; margin-left: 215px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    lambda
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="215" y="329" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        lambda
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 85 142.5 L 85 236.13" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 85 241.38 L 81.5 234.38 L 85 236.13 L 88.5 234.38 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 193px; margin-left: 85px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ➀triger
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="85" y="196" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        ➀triger
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="190" y="390" width="48.08" height="50" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 227.22 429.63 C 227.22 430.06 227.57 430.41 228 430.41 L 228 430.41 C 228.2 430.41 228.4 430.33 228.54 430.18 C 228.69 430.04 228.77 429.84 228.77 429.63 L 228.77 429.63 C 228.77 429.42 228.69 429.23 228.55 429.09 C 228.4 428.94 228.2 428.86 228 428.86 L 228 428.86 C 227.79 428.86 227.59 428.94 227.45 429.08 C 227.3 429.23 227.22 429.42 227.22 429.63 Z M 224.94 429.63 C 224.94 428.81 225.26 428.05 225.84 427.47 C 226.41 426.89 227.18 426.58 227.99 426.58 L 228 426.58 C 228.81 426.58 229.58 426.9 230.16 427.47 C 230.74 428.05 231.05 428.82 231.05 429.64 C 231.05 430.45 230.73 431.22 230.16 431.8 C 229.86 432.09 229.51 432.32 229.14 432.47 L 229.14 435.66 L 226.86 435.66 L 226.86 432.47 C 225.73 432.01 224.94 430.91 224.94 429.63 Z M 235.8 425.09 L 220.2 425.08 L 220.19 437.7 L 235.79 437.71 Z M 224.16 422.81 L 231.81 422.81 L 231.81 419.99 C 231.82 417.69 230.11 415.81 228.02 415.81 L 228 415.81 C 227 415.81 226.05 416.23 225.33 416.99 C 224.58 417.77 224.17 418.82 224.17 419.94 Z M 238.08 423.95 L 238.07 438.85 C 238.07 439.48 237.56 439.99 236.93 439.99 L 219.05 439.98 C 218.42 439.98 217.91 439.47 217.91 438.84 L 217.92 423.94 C 217.92 423.64 218.04 423.35 218.25 423.14 C 218.46 422.92 218.75 422.8 219.06 422.8 L 221.88 422.81 L 221.89 419.94 C 221.9 418.23 222.53 416.63 223.67 415.42 C 224.83 414.2 226.36 413.53 228 413.53 L 228.02 413.53 C 231.38 413.54 234.1 416.44 234.09 420 L 234.09 422.81 L 236.94 422.81 C 237.24 422.81 237.53 422.93 237.75 423.15 C 237.96 423.36 238.08 423.65 238.08 423.95 Z M 203.15 423.1 L 211.1 423.1 L 211.1 420.82 L 203.15 420.82 Z M 203.15 419.12 L 211.1 419.12 L 211.1 416.84 L 203.15 416.84 Z M 203.15 415.15 L 211.1 415.15 L 211.1 412.87 L 203.15 412.87 Z M 201.36 425.79 L 212.99 425.79 L 212.99 410.18 L 201.36 410.18 Z M 215.27 409.04 L 215.27 426.93 C 215.27 427.56 214.76 428.07 214.13 428.07 L 200.22 428.07 C 199.59 428.07 199.08 427.56 199.08 426.93 L 199.08 409.04 C 199.08 408.41 199.59 407.9 200.22 407.9 L 214.13 407.9 C 214.76 407.9 215.27 408.41 215.27 409.04 Z M 193.42 397.96 L 220.87 397.96 L 216.82 392.29 L 197.46 392.29 Z M 190.19 399.62 C 190 399.24 190.03 398.79 190.28 398.44 L 195.95 390.49 C 196.16 390.19 196.51 390.01 196.88 390.01 L 217.41 390.01 C 217.78 390.01 218.13 390.19 218.34 390.49 L 224.01 398.44 C 224.25 398.79 224.29 399.24 224.09 399.62 C 223.9 400 223.51 400.24 223.08 400.24 L 191.21 400.24 C 190.78 400.24 190.39 400 190.19 399.62 Z M 192.41 432.74 L 215.08 432.74 L 215.08 435.02 L 191.27 435.02 C 190.63 435.02 190.13 434.51 190.13 433.88 L 190.13 403.08 C 190.13 402.44 190.63 401.94 191.27 401.94 L 223.08 401.94 C 223.71 401.94 224.22 402.44 224.22 403.08 L 224.22 413.01 L 221.94 413.01 L 221.94 404.22 L 192.41 404.22 Z" fill="#e7157b" stroke="none" pointer-events="all" style="fill: light-dark(rgb(231, 21, 123), rgb(255, 129, 217));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 447px; margin-left: 214px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    parameter store
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="214" y="459" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        paramete...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 60 92.5 L 110 92.5 L 110 142.5 L 60 142.5 Z" fill="#8c4fff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));"/>
            <path d="M 95.8 126.16 C 95.8 124.98 94.84 124.02 93.66 124.02 C 92.48 124.02 91.52 124.98 91.52 126.16 C 91.52 127.34 92.48 128.3 93.66 128.3 C 94.84 128.3 95.8 127.34 95.8 126.16 Z M 97.23 126.16 C 97.23 128.13 95.63 129.73 93.66 129.73 C 91.69 129.73 90.09 128.13 90.09 126.16 C 90.09 124.19 91.69 122.59 93.66 122.59 C 95.63 122.59 97.23 124.19 97.23 126.16 Z M 78.14 115.85 C 78.14 114.67 77.18 113.71 75.99 113.71 C 74.81 113.71 73.85 114.67 73.85 115.85 C 73.85 117.03 74.81 117.99 75.99 117.99 C 77.18 117.99 78.14 117.03 78.14 115.85 Z M 79.57 115.85 C 79.57 117.82 77.96 119.42 75.99 119.42 C 74.03 119.42 72.42 117.82 72.42 115.85 C 72.42 113.88 74.03 112.28 75.99 112.28 C 77.96 112.28 79.57 113.88 79.57 115.85 Z M 84.81 104.65 C 84.81 105.83 85.77 106.79 86.95 106.79 C 88.13 106.79 89.09 105.83 89.09 104.65 C 89.09 103.47 88.13 102.5 86.95 102.5 C 85.77 102.5 84.81 103.47 84.81 104.65 Z M 83.38 104.65 C 83.38 102.68 84.98 101.08 86.95 101.08 C 88.92 101.08 90.52 102.68 90.52 104.65 C 90.52 106.62 88.92 108.22 86.95 108.22 C 84.98 108.22 83.38 106.62 83.38 104.65 Z M 103.57 117.5 C 103.57 110.88 100.02 104.75 94.29 101.44 C 93.27 101.64 92.28 101.93 91.04 102.37 L 90.55 101.03 C 91.2 100.8 91.78 100.61 92.33 100.45 C 90.03 99.45 87.53 98.93 85 98.93 C 83.79 98.93 82.61 99.05 81.45 99.28 C 82.29 99.77 83.03 100.26 83.75 100.81 L 82.89 101.95 C 81.87 101.17 80.81 100.52 79.43 99.79 C 72.41 102 67.36 108.18 66.56 115.45 C 68.03 115.15 69.44 114.99 71.01 114.95 L 71.05 116.38 C 69.4 116.42 67.99 116.59 66.45 116.93 C 66.44 117.12 66.43 117.31 66.43 117.5 C 66.43 123.68 69.49 129.38 74.52 132.82 C 73.62 130.15 73.18 127.64 73.18 125.18 C 73.18 123.77 73.42 122.62 73.67 121.4 C 73.73 121.12 73.79 120.83 73.85 120.53 L 75.25 120.81 C 75.19 121.11 75.13 121.41 75.07 121.69 C 74.82 122.89 74.61 123.92 74.61 125.18 C 74.61 127.97 75.22 130.85 76.47 133.99 C 79.12 135.37 81.99 136.07 85 136.07 C 86.97 136.07 88.89 135.76 90.72 135.15 C 91.44 133.73 91.98 132.39 92.42 130.86 L 93.79 131.25 C 93.47 132.38 93.09 133.4 92.65 134.41 C 93.8 133.89 94.89 133.25 95.92 132.5 C 95.67 131.9 95.41 131.3 95.11 130.71 L 96.39 130.07 C 96.64 130.57 96.87 131.08 97.09 131.59 C 101.22 128.05 103.57 122.97 103.57 117.5 Z M 105 117.5 C 105 123.73 102.17 129.5 97.23 133.32 C 96.01 134.27 94.68 135.06 93.28 135.69 C 92.68 135.96 92.08 136.21 91.45 136.42 C 89.4 137.14 87.22 137.5 85 137.5 C 81.71 137.5 78.45 136.68 75.56 135.13 C 69.05 131.65 65 124.89 65 117.5 C 65 117.01 65.01 116.64 65.04 116.29 C 65.53 107.95 71.25 100.72 79.29 98.33 C 81.12 97.78 83.04 97.5 85 97.5 C 88.43 97.5 91.81 98.38 94.77 100.06 C 101.08 103.59 105 110.27 105 117.5 Z M 83.08 107.12 L 82.14 106.04 C 80.54 107.44 79.3 108.92 77.84 111.16 L 79.04 111.94 C 80.42 109.82 81.59 108.42 83.08 107.12 Z M 80.91 116.38 L 80.45 117.73 C 83.73 118.85 86.59 120.65 89.45 123.38 L 90.44 122.35 C 87.42 119.47 84.39 117.57 80.91 116.38 Z M 90.46 107.98 C 93.14 112.06 94.65 116.55 94.95 121.31 L 93.52 121.4 C 93.24 116.89 91.81 112.64 89.27 108.76 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 150px; margin-left: 85px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    primary distribution
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="85" y="162" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        primary...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 60 242.5 L 110 242.5 L 110 292.5 L 60 292.5 Z" fill="#e7157b" stroke="none" pointer-events="all" style="fill: light-dark(rgb(231, 21, 123), rgb(255, 129, 217));"/>
            <path d="M 94.29 267.83 L 97.86 267.83 L 97.86 266.4 L 94.29 266.4 Z M 76.43 276.41 L 80 276.41 L 80 274.98 L 76.43 274.98 Z M 69.29 276.41 L 74.29 276.41 L 74.29 274.98 L 69.29 274.98 Z M 69.29 272.12 L 77.86 272.12 L 77.86 270.69 L 69.29 270.69 Z M 69.29 263.54 L 75.71 263.54 L 75.71 262.11 L 69.29 262.11 Z M 69.29 267.83 L 92.86 267.83 L 92.86 266.4 L 69.29 266.4 Z M 82.86 283.57 L 66.43 283.57 L 66.43 259.24 L 82.86 259.24 L 82.86 264.97 L 84.29 264.97 L 84.29 258.53 C 84.29 258.13 83.97 257.81 83.57 257.81 L 65.71 257.81 C 65.32 257.81 65 258.13 65 258.53 L 65 284.28 C 65 284.68 65.32 285 65.71 285 L 83.57 285 C 83.97 285 84.29 284.68 84.29 284.28 L 84.29 269.98 L 82.86 269.98 Z M 105 265.68 C 105 270.31 100.84 271.91 98.64 272.12 L 87.14 272.12 L 87.14 270.69 L 98.57 270.69 C 98.71 270.67 103.57 270.15 103.57 265.68 C 103.57 261.62 99.9 260.79 99.17 260.67 C 98.8 260.6 98.54 260.27 98.57 259.9 C 98.58 259.89 98.58 259.87 98.58 259.86 C 98.54 257.56 97.13 256.84 96.52 256.64 C 95.38 256.26 94.11 256.62 93.44 257.53 C 93.28 257.74 93.02 257.85 92.76 257.81 C 92.5 257.77 92.28 257.59 92.19 257.34 C 91.75 256.1 91.1 255.29 90.21 254.39 C 87.97 252.17 84.93 251.55 82.09 252.75 C 80.6 253.38 79.29 254.8 78.52 256.66 L 77.2 256.11 C 78.12 253.91 79.7 252.2 81.54 251.43 C 84.93 250 88.55 250.73 91.22 253.38 C 91.98 254.15 92.61 254.89 93.11 255.86 C 94.17 255.07 95.61 254.83 96.97 255.28 C 98.71 255.86 99.81 257.37 99.98 259.38 C 102.41 259.99 105 261.94 105 265.68 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 300px; margin-left: 85px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    custom resource
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="85" y="312" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        custom r...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 110 130 L 202.5 130 L 202.5 253.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 202.5 258.88 L 199 251.88 L 202.5 253.63 L 206 251.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 201px; margin-left: 161px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ➁get config
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="161" y="204" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        ➁get config
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 116.37 117.5 L 225.5 117.5 L 225 260" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 111.12 117.5 L 118.12 114 L 116.37 117.5 L 118.12 121 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 169px; margin-left: 266px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ➃create policy
                                    <div>
                                        <font color="#000000" style="color: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));">
                                            &amp;
                                            <br/>
                                        </font>
                                        <div>
                                            attach policy
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="266" y="172" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        ➃create policy...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="20" y="0" width="60" height="30" fill="none" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 15px; margin-left: 21px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    CREATE
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="50" y="19" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        CREATE
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="510" y="40" width="470" height="440" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 468px; height: 1px; padding-top: 47px; margin-left: 512px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    cloudfront-stack.ts
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="512" y="59" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        cloudfront-stack.ts
                    </text>
                </switch>
            </g>
        </g>
        <g/>
        <g>
            <rect x="550" y="242.5" width="410" height="227.5" fill="none" stroke="#e7157b" pointer-events="all" style="stroke: light-dark(rgb(231, 21, 123), rgb(153, 101, 0));"/>
        </g>
        <g>
            <path d="M 850 260 L 900 260 L 900 310 L 850 310 Z" fill="#8c4fff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));"/>
            <path d="M 885.8 293.66 C 885.8 292.48 884.84 291.52 883.66 291.52 C 882.48 291.52 881.52 292.48 881.52 293.66 C 881.52 294.84 882.48 295.8 883.66 295.8 C 884.84 295.8 885.8 294.84 885.8 293.66 Z M 887.23 293.66 C 887.23 295.63 885.63 297.23 883.66 297.23 C 881.69 297.23 880.09 295.63 880.09 293.66 C 880.09 291.69 881.69 290.09 883.66 290.09 C 885.63 290.09 887.23 291.69 887.23 293.66 Z M 868.14 283.35 C 868.14 282.17 867.18 281.21 865.99 281.21 C 864.81 281.21 863.85 282.17 863.85 283.35 C 863.85 284.53 864.81 285.49 865.99 285.49 C 867.18 285.49 868.14 284.53 868.14 283.35 Z M 869.57 283.35 C 869.57 285.32 867.96 286.92 865.99 286.92 C 864.02 286.92 862.42 285.32 862.42 283.35 C 862.42 281.38 864.02 279.78 865.99 279.78 C 867.96 279.78 869.57 281.38 869.57 283.35 Z M 874.81 272.15 C 874.81 273.33 875.77 274.29 876.95 274.29 C 878.13 274.29 879.09 273.33 879.09 272.15 C 879.09 270.97 878.13 270 876.95 270 C 875.77 270 874.81 270.97 874.81 272.15 Z M 873.38 272.15 C 873.38 270.18 874.98 268.58 876.95 268.58 C 878.92 268.58 880.52 270.18 880.52 272.15 C 880.52 274.12 878.92 275.72 876.95 275.72 C 874.98 275.72 873.38 274.12 873.38 272.15 Z M 893.57 285 C 893.57 278.38 890.02 272.25 884.29 268.94 C 883.27 269.14 882.27 269.43 881.04 269.87 L 880.55 268.53 C 881.2 268.3 881.78 268.11 882.33 267.95 C 880.03 266.95 877.53 266.43 875 266.43 C 873.79 266.43 872.61 266.55 871.45 266.78 C 872.29 267.27 873.03 267.76 873.75 268.31 L 872.89 269.45 C 871.87 268.68 870.81 268.02 869.43 267.29 C 862.41 269.5 857.36 275.68 856.56 282.95 C 858.03 282.65 859.44 282.49 861.01 282.45 L 861.04 283.88 C 859.4 283.92 857.99 284.09 856.45 284.43 C 856.44 284.62 856.43 284.81 856.43 285 C 856.43 291.18 859.49 296.88 864.52 300.32 C 863.62 297.65 863.18 295.14 863.18 292.68 C 863.18 291.27 863.42 290.12 863.67 288.9 C 863.73 288.62 863.79 288.33 863.85 288.03 L 865.25 288.31 C 865.2 288.61 865.13 288.91 865.07 289.19 C 864.82 290.39 864.61 291.42 864.61 292.68 C 864.61 295.47 865.22 298.35 866.47 301.49 C 869.12 302.87 871.99 303.57 875 303.57 C 876.97 303.57 878.89 303.26 880.72 302.65 C 881.44 301.23 881.98 299.89 882.42 298.36 L 883.79 298.75 C 883.47 299.88 883.09 300.89 882.65 301.91 C 883.8 301.39 884.89 300.75 885.92 300 C 885.67 299.4 885.41 298.8 885.11 298.21 L 886.39 297.57 C 886.64 298.07 886.87 298.58 887.09 299.09 C 891.22 295.55 893.57 290.47 893.57 285 Z M 895 285 C 895 291.23 892.17 297 887.23 300.82 C 886.01 301.77 884.68 302.56 883.28 303.19 C 882.68 303.46 882.08 303.71 881.45 303.92 C 879.4 304.64 877.22 305 875 305 C 871.71 305 868.45 304.18 865.56 302.63 C 859.05 299.15 855 292.39 855 285 C 855 284.51 855.01 284.14 855.04 283.79 C 855.53 275.45 861.25 268.22 869.29 265.83 C 871.12 265.28 873.04 265 875 265 C 878.43 265 881.81 265.88 884.77 267.56 C 891.08 271.09 895 277.77 895 285 Z M 873.08 274.62 L 872.14 273.54 C 870.54 274.94 869.3 276.42 867.84 278.66 L 869.04 279.44 C 870.42 277.32 871.59 275.92 873.08 274.62 Z M 870.91 283.88 L 870.45 285.23 C 873.73 286.35 876.59 288.15 879.45 290.88 L 880.44 289.85 C 877.42 286.97 874.39 285.07 870.91 283.88 Z M 880.46 275.48 C 883.14 279.56 884.65 284.05 884.95 288.81 L 883.52 288.9 C 883.24 284.39 881.81 280.14 879.27 276.26 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 317px; margin-left: 875px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    staging distribution
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="875" y="329" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        staging...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 730 285 L 843.63 285" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 848.88 285 L 841.88 288.5 L 843.63 285 L 841.88 281.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 285px; margin-left: 790px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ➄update
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="790" y="288" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        ➄update
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 690.55 316.37 L 690 390.01" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 690.59 311.12 L 694.04 318.14 L 690.55 316.37 L 687.04 318.09 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 350px; margin-left: 650px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    <span>
                                        ➁get param
                                    </span>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="650" y="353" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        ➁get param
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 575 142.5 L 575 236.13" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 575 241.38 L 571.5 234.38 L 575 236.13 L 578.5 234.38 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 193px; margin-left: 575px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ➀triger
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="575" y="196" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        ➀triger
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="680" y="390" width="48.08" height="50" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 717.22 429.63 C 717.22 430.06 717.57 430.41 718 430.41 L 718 430.41 C 718.2 430.41 718.4 430.33 718.54 430.18 C 718.69 430.04 718.77 429.84 718.77 429.63 L 718.77 429.63 C 718.77 429.42 718.69 429.23 718.55 429.09 C 718.4 428.94 718.2 428.86 718 428.86 L 718 428.86 C 717.79 428.86 717.59 428.94 717.45 429.08 C 717.3 429.23 717.22 429.42 717.22 429.63 Z M 714.94 429.63 C 714.94 428.81 715.26 428.05 715.84 427.47 C 716.41 426.89 717.18 426.58 717.99 426.58 L 718 426.58 C 718.81 426.58 719.58 426.9 720.16 427.47 C 720.74 428.05 721.05 428.82 721.05 429.64 C 721.05 430.45 720.73 431.22 720.16 431.8 C 719.86 432.09 719.51 432.32 719.14 432.47 L 719.14 435.66 L 716.86 435.66 L 716.86 432.47 C 715.73 432.01 714.94 430.91 714.94 429.63 Z M 725.8 425.09 L 710.2 425.08 L 710.19 437.7 L 725.79 437.71 Z M 714.16 422.81 L 721.81 422.81 L 721.81 419.99 C 721.82 417.69 720.11 415.81 718.02 415.81 L 718 415.81 C 717 415.81 716.05 416.23 715.33 416.99 C 714.58 417.77 714.17 418.82 714.17 419.94 Z M 728.08 423.95 L 728.07 438.85 C 728.07 439.48 727.56 439.99 726.93 439.99 L 709.05 439.98 C 708.42 439.98 707.91 439.47 707.91 438.84 L 707.92 423.94 C 707.92 423.64 708.04 423.35 708.25 423.14 C 708.46 422.92 708.75 422.8 709.06 422.8 L 711.88 422.81 L 711.89 419.94 C 711.9 418.23 712.53 416.63 713.67 415.42 C 714.83 414.2 716.36 413.53 718 413.53 L 718.02 413.53 C 721.38 413.54 724.1 416.44 724.09 420 L 724.09 422.81 L 726.94 422.81 C 727.24 422.81 727.53 422.93 727.75 423.15 C 727.96 423.36 728.08 423.65 728.08 423.95 Z M 693.15 423.1 L 701.1 423.1 L 701.1 420.82 L 693.15 420.82 Z M 693.15 419.12 L 701.1 419.12 L 701.1 416.84 L 693.15 416.84 Z M 693.15 415.15 L 701.1 415.15 L 701.1 412.87 L 693.15 412.87 Z M 691.36 425.79 L 702.99 425.79 L 702.99 410.18 L 691.36 410.18 Z M 705.27 409.04 L 705.27 426.93 C 705.27 427.56 704.76 428.07 704.13 428.07 L 690.22 428.07 C 689.59 428.07 689.08 427.56 689.08 426.93 L 689.08 409.04 C 689.08 408.41 689.59 407.9 690.22 407.9 L 704.13 407.9 C 704.76 407.9 705.27 408.41 705.27 409.04 Z M 683.42 397.96 L 710.87 397.96 L 706.82 392.29 L 687.46 392.29 Z M 680.19 399.62 C 680 399.24 680.03 398.79 680.28 398.44 L 685.95 390.49 C 686.16 390.19 686.51 390.01 686.88 390.01 L 707.41 390.01 C 707.78 390.01 708.13 390.19 708.34 390.49 L 714.01 398.44 C 714.25 398.79 714.29 399.24 714.09 399.62 C 713.9 400 713.51 400.24 713.08 400.24 L 681.21 400.24 C 680.78 400.24 680.39 400 680.19 399.62 Z M 682.41 432.74 L 705.08 432.74 L 705.08 435.02 L 681.27 435.02 C 680.63 435.02 680.13 434.51 680.13 433.88 L 680.13 403.08 C 680.13 402.44 680.63 401.94 681.27 401.94 L 713.08 401.94 C 713.71 401.94 714.22 402.44 714.22 403.08 L 714.22 413.01 L 711.94 413.01 L 711.94 404.22 L 682.41 404.22 Z" fill="#e7157b" stroke="none" pointer-events="all" style="fill: light-dark(rgb(231, 21, 123), rgb(255, 129, 217));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 447px; margin-left: 704px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    parameter store
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="704" y="459" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        paramete...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 550 92.5 L 600 92.5 L 600 142.5 L 550 142.5 Z" fill="#8c4fff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));"/>
            <path d="M 585.8 126.16 C 585.8 124.98 584.84 124.02 583.66 124.02 C 582.48 124.02 581.52 124.98 581.52 126.16 C 581.52 127.34 582.48 128.3 583.66 128.3 C 584.84 128.3 585.8 127.34 585.8 126.16 Z M 587.23 126.16 C 587.23 128.13 585.63 129.73 583.66 129.73 C 581.69 129.73 580.09 128.13 580.09 126.16 C 580.09 124.19 581.69 122.59 583.66 122.59 C 585.63 122.59 587.23 124.19 587.23 126.16 Z M 568.14 115.85 C 568.14 114.67 567.18 113.71 565.99 113.71 C 564.81 113.71 563.85 114.67 563.85 115.85 C 563.85 117.03 564.81 117.99 565.99 117.99 C 567.18 117.99 568.14 117.03 568.14 115.85 Z M 569.57 115.85 C 569.57 117.82 567.96 119.42 565.99 119.42 C 564.02 119.42 562.42 117.82 562.42 115.85 C 562.42 113.88 564.02 112.28 565.99 112.28 C 567.96 112.28 569.57 113.88 569.57 115.85 Z M 574.81 104.65 C 574.81 105.83 575.77 106.79 576.95 106.79 C 578.13 106.79 579.09 105.83 579.09 104.65 C 579.09 103.47 578.13 102.5 576.95 102.5 C 575.77 102.5 574.81 103.47 574.81 104.65 Z M 573.38 104.65 C 573.38 102.68 574.98 101.08 576.95 101.08 C 578.92 101.08 580.52 102.68 580.52 104.65 C 580.52 106.62 578.92 108.22 576.95 108.22 C 574.98 108.22 573.38 106.62 573.38 104.65 Z M 593.57 117.5 C 593.57 110.88 590.02 104.75 584.29 101.44 C 583.27 101.64 582.27 101.93 581.04 102.37 L 580.55 101.03 C 581.2 100.8 581.78 100.61 582.33 100.45 C 580.03 99.45 577.53 98.93 575 98.93 C 573.79 98.93 572.61 99.05 571.45 99.28 C 572.29 99.77 573.03 100.26 573.75 100.81 L 572.89 101.95 C 571.87 101.17 570.81 100.52 569.43 99.79 C 562.41 102 557.36 108.18 556.56 115.45 C 558.03 115.15 559.44 114.99 561.01 114.95 L 561.04 116.38 C 559.4 116.42 557.99 116.59 556.45 116.93 C 556.44 117.12 556.43 117.31 556.43 117.5 C 556.43 123.68 559.49 129.38 564.52 132.82 C 563.62 130.15 563.18 127.64 563.18 125.18 C 563.18 123.77 563.42 122.62 563.67 121.4 C 563.73 121.12 563.79 120.83 563.85 120.53 L 565.25 120.81 C 565.2 121.11 565.13 121.41 565.07 121.69 C 564.82 122.89 564.61 123.92 564.61 125.18 C 564.61 127.97 565.22 130.85 566.47 133.99 C 569.12 135.37 571.99 136.07 575 136.07 C 576.97 136.07 578.89 135.76 580.72 135.15 C 581.44 133.73 581.98 132.39 582.42 130.86 L 583.79 131.25 C 583.47 132.38 583.09 133.4 582.65 134.41 C 583.8 133.89 584.89 133.25 585.92 132.5 C 585.67 131.9 585.41 131.3 585.11 130.71 L 586.39 130.07 C 586.64 130.57 586.87 131.08 587.09 131.59 C 591.22 128.05 593.57 122.97 593.57 117.5 Z M 595 117.5 C 595 123.73 592.17 129.5 587.23 133.32 C 586.01 134.27 584.68 135.06 583.28 135.69 C 582.68 135.96 582.08 136.21 581.45 136.42 C 579.4 137.14 577.22 137.5 575 137.5 C 571.71 137.5 568.45 136.68 565.56 135.13 C 559.05 131.65 555 124.89 555 117.5 C 555 117.01 555.01 116.64 555.04 116.29 C 555.53 107.95 561.25 100.72 569.29 98.33 C 571.12 97.78 573.04 97.5 575 97.5 C 578.43 97.5 581.81 98.38 584.77 100.06 C 591.08 103.59 595 110.27 595 117.5 Z M 573.08 107.12 L 572.14 106.04 C 570.54 107.44 569.3 108.92 567.84 111.16 L 569.04 111.94 C 570.42 109.82 571.59 108.42 573.08 107.12 Z M 570.91 116.38 L 570.45 117.73 C 573.73 118.85 576.59 120.65 579.45 123.38 L 580.44 122.35 C 577.42 119.47 574.39 117.57 570.91 116.38 Z M 580.46 107.98 C 583.14 112.06 584.65 116.55 584.95 121.31 L 583.52 121.4 C 583.24 116.89 581.81 112.64 579.27 108.76 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 150px; margin-left: 575px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    primary distribution
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="575" y="162" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        primary...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 550 242.5 L 600 242.5 L 600 292.5 L 550 292.5 Z" fill="#e7157b" stroke="none" pointer-events="all" style="fill: light-dark(rgb(231, 21, 123), rgb(255, 129, 217));"/>
            <path d="M 584.29 267.83 L 587.86 267.83 L 587.86 266.4 L 584.29 266.4 Z M 566.43 276.41 L 570 276.41 L 570 274.98 L 566.43 274.98 Z M 559.29 276.41 L 564.29 276.41 L 564.29 274.98 L 559.29 274.98 Z M 559.29 272.12 L 567.86 272.12 L 567.86 270.69 L 559.29 270.69 Z M 559.29 263.54 L 565.71 263.54 L 565.71 262.11 L 559.29 262.11 Z M 559.29 267.83 L 582.86 267.83 L 582.86 266.4 L 559.29 266.4 Z M 572.86 283.57 L 556.43 283.57 L 556.43 259.24 L 572.86 259.24 L 572.86 264.97 L 574.29 264.97 L 574.29 258.53 C 574.29 258.13 573.97 257.81 573.57 257.81 L 555.71 257.81 C 555.32 257.81 555 258.13 555 258.53 L 555 284.28 C 555 284.68 555.32 285 555.71 285 L 573.57 285 C 573.97 285 574.29 284.68 574.29 284.28 L 574.29 269.98 L 572.86 269.98 Z M 595 265.68 C 595 270.31 590.84 271.91 588.64 272.12 L 577.14 272.12 L 577.14 270.69 L 588.57 270.69 C 588.71 270.67 593.57 270.15 593.57 265.68 C 593.57 261.62 589.9 260.79 589.17 260.67 C 588.8 260.6 588.54 260.27 588.57 259.9 C 588.58 259.89 588.58 259.87 588.58 259.86 C 588.54 257.56 587.13 256.84 586.52 256.64 C 585.38 256.26 584.11 256.62 583.44 257.53 C 583.28 257.74 583.02 257.85 582.76 257.81 C 582.5 257.77 582.28 257.59 582.19 257.34 C 581.75 256.1 581.1 255.29 580.21 254.39 C 577.97 252.17 574.93 251.55 572.09 252.75 C 570.6 253.38 569.29 254.8 568.52 256.66 L 567.2 256.11 C 568.12 253.91 569.7 252.2 571.54 251.43 C 574.93 250 578.55 250.73 581.22 253.38 C 581.98 254.15 582.61 254.89 583.11 255.86 C 584.17 255.07 585.61 254.83 586.97 255.28 C 588.71 255.86 589.8 257.37 589.98 259.38 C 592.41 259.99 595 261.94 595 265.68 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 300px; margin-left: 575px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    custom resource
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="575" y="312" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        custom r...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 740 200 L 860 200 L 860 247.5 L 820 247.5 L 860 277.5 L 800 247.5 L 740 247.5 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" transform="translate(800,0)scale(-1,1)translate(-800,0)" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 224px; margin-left: 741px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    <span style="font-weight: 600; color: light-dark(rgb(66, 66, 66), rgb(180, 180, 180)); font-family: &quot;Segoe Sans&quot;, &quot;Segoe UI&quot;, &quot;Segoe UI Web (West European)&quot;, -apple-system, BlinkMacSystemFont, Roboto, &quot;Helvetica Neue&quot;, sans-serif; text-align: start; background-color: light-dark(rgb(250, 250, 250), rgb(22, 22, 22));">
                                        <font style="font-size: 11px;">
                                            ➂Check for changes in PrimaryDistribution
                                        </font>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="800" y="227" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ➂Check for changes in...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 720.6 310 L 720.05 383.64" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 720.01 388.89 L 716.56 381.87 L 720.05 383.64 L 723.56 381.92 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 350px; margin-left: 760px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ➅keep param
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="760" y="354" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        ➅keep param
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 680 260 L 730 260 L 730 310 L 680 310 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
            <path d="M 696.45 303.57 L 687.58 303.57 L 697.39 283.07 L 701.84 292.22 Z M 698.03 281.11 C 697.91 280.86 697.66 280.71 697.39 280.71 L 697.39 280.71 C 697.11 280.71 696.86 280.87 696.74 281.11 L 685.8 303.98 C 685.7 304.2 685.71 304.46 685.84 304.67 C 685.97 304.87 686.2 305 686.45 305 L 696.9 305 C 697.18 305 697.43 304.84 697.55 304.59 L 703.28 292.52 C 703.37 292.32 703.37 292.1 703.27 291.9 Z M 722.87 303.57 L 714.06 303.57 L 699.92 273.98 C 699.8 273.73 699.55 273.57 699.28 273.57 L 693.51 273.57 L 693.52 266.43 L 704.82 266.43 L 718.89 296.02 C 719.01 296.27 719.26 296.43 719.54 296.43 L 722.87 296.43 Z M 723.59 295 L 719.99 295 L 705.92 265.41 C 705.8 265.16 705.55 265 705.27 265 L 692.8 265 C 692.41 265 692.09 265.32 692.09 265.71 L 692.08 274.29 C 692.08 274.48 692.15 274.66 692.29 274.79 C 692.42 274.93 692.6 275 692.79 275 L 698.83 275 L 712.96 304.59 C 713.08 304.84 713.33 305 713.61 305 L 723.59 305 C 723.98 305 724.3 304.68 724.3 304.29 L 724.3 295.71 C 724.3 295.32 723.98 295 723.59 295 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 317px; margin-left: 705px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    lambda
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="705" y="329" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        lambda
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="510" y="0" width="60" height="30" fill="none" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 15px; margin-left: 511px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    UPDATE
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="540" y="19" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        UPDATE
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1000" y="40" width="470" height="440" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 468px; height: 1px; padding-top: 47px; margin-left: 1002px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    cloudfront-stack.ts
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1002" y="59" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        cloudfront-stack.ts
                    </text>
                </switch>
            </g>
        </g>
        <g/>
        <g>
            <rect x="1040" y="242.5" width="410" height="227.5" fill="none" stroke="#e7157b" pointer-events="all" style="stroke: light-dark(rgb(231, 21, 123), rgb(153, 101, 0));"/>
        </g>
        <g>
            <path d="M 1340 260 L 1390 260 L 1390 310 L 1340 310 Z" fill="#8c4fff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));"/>
            <path d="M 1375.8 293.66 C 1375.8 292.48 1374.84 291.52 1373.66 291.52 C 1372.48 291.52 1371.52 292.48 1371.52 293.66 C 1371.52 294.84 1372.48 295.8 1373.66 295.8 C 1374.84 295.8 1375.8 294.84 1375.8 293.66 Z M 1377.23 293.66 C 1377.23 295.63 1375.63 297.23 1373.66 297.23 C 1371.69 297.23 1370.09 295.63 1370.09 293.66 C 1370.09 291.69 1371.69 290.09 1373.66 290.09 C 1375.63 290.09 1377.23 291.69 1377.23 293.66 Z M 1358.14 283.35 C 1358.14 282.17 1357.18 281.21 1355.99 281.21 C 1354.81 281.21 1353.85 282.17 1353.85 283.35 C 1353.85 284.53 1354.81 285.49 1355.99 285.49 C 1357.18 285.49 1358.14 284.53 1358.14 283.35 Z M 1359.57 283.35 C 1359.57 285.32 1357.96 286.92 1355.99 286.92 C 1354.03 286.92 1352.42 285.32 1352.42 283.35 C 1352.42 281.38 1354.03 279.78 1355.99 279.78 C 1357.96 279.78 1359.57 281.38 1359.57 283.35 Z M 1364.81 272.15 C 1364.81 273.33 1365.77 274.29 1366.95 274.29 C 1368.13 274.29 1369.09 273.33 1369.09 272.15 C 1369.09 270.97 1368.13 270 1366.95 270 C 1365.77 270 1364.81 270.97 1364.81 272.15 Z M 1363.38 272.15 C 1363.38 270.18 1364.98 268.58 1366.95 268.58 C 1368.92 268.58 1370.52 270.18 1370.52 272.15 C 1370.52 274.12 1368.92 275.72 1366.95 275.72 C 1364.98 275.72 1363.38 274.12 1363.38 272.15 Z M 1383.57 285 C 1383.57 278.38 1380.02 272.25 1374.29 268.94 C 1373.27 269.14 1372.28 269.43 1371.04 269.87 L 1370.55 268.53 C 1371.2 268.3 1371.78 268.11 1372.33 267.95 C 1370.03 266.95 1367.53 266.43 1365 266.43 C 1363.79 266.43 1362.61 266.55 1361.45 266.78 C 1362.29 267.27 1363.03 267.76 1363.76 268.31 L 1362.89 269.45 C 1361.87 268.68 1360.81 268.02 1359.43 267.29 C 1352.41 269.5 1347.36 275.68 1346.56 282.95 C 1348.03 282.65 1349.44 282.49 1351.01 282.45 L 1351.05 283.88 C 1349.4 283.92 1347.99 284.09 1346.45 284.43 C 1346.44 284.62 1346.43 284.81 1346.43 285 C 1346.43 291.18 1349.49 296.88 1354.52 300.32 C 1353.62 297.65 1353.18 295.14 1353.18 292.68 C 1353.18 291.27 1353.42 290.12 1353.67 288.9 C 1353.73 288.62 1353.79 288.33 1353.85 288.03 L 1355.26 288.31 C 1355.19 288.61 1355.13 288.91 1355.07 289.19 C 1354.82 290.39 1354.61 291.42 1354.61 292.68 C 1354.61 295.47 1355.22 298.35 1356.47 301.49 C 1359.12 302.87 1361.99 303.57 1365 303.57 C 1366.97 303.57 1368.89 303.26 1370.72 302.65 C 1371.44 301.23 1371.98 299.89 1372.42 298.36 L 1373.79 298.75 C 1373.47 299.88 1373.09 300.89 1372.65 301.91 C 1373.8 301.39 1374.89 300.75 1375.92 300 C 1375.67 299.4 1375.41 298.8 1375.11 298.21 L 1376.39 297.57 C 1376.64 298.07 1376.87 298.58 1377.09 299.09 C 1381.22 295.55 1383.57 290.47 1383.57 285 Z M 1385 285 C 1385 291.23 1382.17 297 1377.23 300.82 C 1376.01 301.77 1374.68 302.56 1373.28 303.19 C 1372.68 303.46 1372.08 303.71 1371.45 303.92 C 1369.4 304.64 1367.22 305 1365 305 C 1361.71 305 1358.45 304.18 1355.56 302.63 C 1349.05 299.15 1345 292.39 1345 285 C 1345 284.51 1345.01 284.14 1345.04 283.79 C 1345.53 275.45 1351.25 268.22 1359.29 265.83 C 1361.12 265.28 1363.04 265 1365 265 C 1368.43 265 1371.81 265.88 1374.77 267.56 C 1381.08 271.09 1385 277.77 1385 285 Z M 1363.08 274.62 L 1362.14 273.54 C 1360.54 274.94 1359.3 276.42 1357.84 278.66 L 1359.04 279.44 C 1360.42 277.32 1361.59 275.92 1363.08 274.62 Z M 1360.91 283.88 L 1360.45 285.23 C 1363.73 286.35 1366.59 288.15 1369.45 290.88 L 1370.44 289.85 C 1367.42 286.97 1364.39 285.07 1360.91 283.88 Z M 1370.46 275.48 C 1373.14 279.56 1374.65 284.05 1374.95 288.81 L 1373.52 288.9 C 1373.24 284.39 1371.81 280.14 1369.27 276.26 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 317px; margin-left: 1365px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    staging distribution
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1365" y="329" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        staging...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1220 285 L 1333.63 285" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1338.88 285 L 1331.88 288.5 L 1333.63 285 L 1331.88 281.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 285px; margin-left: 1280px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ➃delete
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1280" y="288" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        ➃delete
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1065 142.5 L 1065 236.13" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1065 241.38 L 1061.5 234.38 L 1065 236.13 L 1068.5 234.38 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 193px; margin-left: 1065px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ➀triger
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1065" y="196" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        ➀triger
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1170" y="390" width="48.08" height="50" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 1207.22 429.63 C 1207.22 430.06 1207.57 430.41 1208 430.41 L 1208 430.41 C 1208.2 430.41 1208.4 430.33 1208.54 430.18 C 1208.69 430.04 1208.77 429.84 1208.77 429.63 L 1208.77 429.63 C 1208.77 429.42 1208.69 429.23 1208.55 429.09 C 1208.4 428.94 1208.2 428.86 1208 428.86 L 1208 428.86 C 1207.79 428.86 1207.59 428.94 1207.45 429.08 C 1207.3 429.23 1207.22 429.42 1207.22 429.63 Z M 1204.94 429.63 C 1204.94 428.81 1205.26 428.05 1205.84 427.47 C 1206.41 426.89 1207.18 426.58 1207.99 426.58 L 1208 426.58 C 1208.81 426.58 1209.58 426.9 1210.16 427.47 C 1210.74 428.05 1211.05 428.82 1211.05 429.64 C 1211.05 430.45 1210.73 431.22 1210.16 431.8 C 1209.86 432.09 1209.51 432.32 1209.14 432.47 L 1209.14 435.66 L 1206.86 435.66 L 1206.86 432.47 C 1205.73 432.01 1204.94 430.91 1204.94 429.63 Z M 1215.8 425.09 L 1200.2 425.08 L 1200.19 437.7 L 1215.79 437.71 Z M 1204.16 422.81 L 1211.81 422.81 L 1211.81 419.99 C 1211.82 417.69 1210.11 415.81 1208.02 415.81 L 1208 415.81 C 1207 415.81 1206.05 416.23 1205.33 416.99 C 1204.58 417.77 1204.17 418.82 1204.17 419.94 Z M 1218.08 423.95 L 1218.07 438.85 C 1218.07 439.48 1217.56 439.99 1216.93 439.99 L 1199.05 439.98 C 1198.42 439.98 1197.91 439.47 1197.91 438.84 L 1197.92 423.94 C 1197.92 423.64 1198.04 423.35 1198.25 423.14 C 1198.46 422.92 1198.75 422.8 1199.06 422.8 L 1201.88 422.81 L 1201.89 419.94 C 1201.9 418.23 1202.53 416.63 1203.67 415.42 C 1204.83 414.2 1206.36 413.53 1208 413.53 L 1208.02 413.53 C 1211.38 413.54 1214.1 416.44 1214.09 420 L 1214.09 422.81 L 1216.94 422.81 C 1217.24 422.81 1217.53 422.93 1217.75 423.15 C 1217.96 423.36 1218.08 423.65 1218.08 423.95 Z M 1183.15 423.1 L 1191.1 423.1 L 1191.1 420.82 L 1183.15 420.82 Z M 1183.15 419.12 L 1191.1 419.12 L 1191.1 416.84 L 1183.15 416.84 Z M 1183.15 415.15 L 1191.1 415.15 L 1191.1 412.87 L 1183.15 412.87 Z M 1181.36 425.79 L 1192.99 425.79 L 1192.99 410.18 L 1181.36 410.18 Z M 1195.27 409.04 L 1195.27 426.93 C 1195.27 427.56 1194.76 428.07 1194.13 428.07 L 1180.22 428.07 C 1179.59 428.07 1179.08 427.56 1179.08 426.93 L 1179.08 409.04 C 1179.08 408.41 1179.59 407.9 1180.22 407.9 L 1194.13 407.9 C 1194.76 407.9 1195.27 408.41 1195.27 409.04 Z M 1173.42 397.96 L 1200.87 397.96 L 1196.82 392.29 L 1177.46 392.29 Z M 1170.19 399.62 C 1170 399.24 1170.03 398.79 1170.28 398.44 L 1175.95 390.49 C 1176.16 390.19 1176.51 390.01 1176.88 390.01 L 1197.41 390.01 C 1197.78 390.01 1198.13 390.19 1198.34 390.49 L 1204.01 398.44 C 1204.25 398.79 1204.29 399.24 1204.09 399.62 C 1203.9 400 1203.51 400.24 1203.08 400.24 L 1171.21 400.24 C 1170.78 400.24 1170.39 400 1170.19 399.62 Z M 1172.41 432.74 L 1195.08 432.74 L 1195.08 435.02 L 1171.27 435.02 C 1170.63 435.02 1170.13 434.51 1170.13 433.88 L 1170.13 403.08 C 1170.13 402.44 1170.63 401.94 1171.27 401.94 L 1203.08 401.94 C 1203.71 401.94 1204.22 402.44 1204.22 403.08 L 1204.22 413.01 L 1201.94 413.01 L 1201.94 404.22 L 1172.41 404.22 Z" fill="#e7157b" stroke="none" pointer-events="all" style="fill: light-dark(rgb(231, 21, 123), rgb(255, 129, 217));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 447px; margin-left: 1194px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    parameter store
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1194" y="459" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        paramete...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1040 92.5 L 1090 92.5 L 1090 142.5 L 1040 142.5 Z" fill="#8c4fff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));"/>
            <path d="M 1075.8 126.16 C 1075.8 124.98 1074.84 124.02 1073.66 124.02 C 1072.48 124.02 1071.52 124.98 1071.52 126.16 C 1071.52 127.34 1072.48 128.3 1073.66 128.3 C 1074.84 128.3 1075.8 127.34 1075.8 126.16 Z M 1077.23 126.16 C 1077.23 128.13 1075.63 129.73 1073.66 129.73 C 1071.69 129.73 1070.09 128.13 1070.09 126.16 C 1070.09 124.19 1071.69 122.59 1073.66 122.59 C 1075.63 122.59 1077.23 124.19 1077.23 126.16 Z M 1058.14 115.85 C 1058.14 114.67 1057.18 113.71 1055.99 113.71 C 1054.81 113.71 1053.85 114.67 1053.85 115.85 C 1053.85 117.03 1054.81 117.99 1055.99 117.99 C 1057.18 117.99 1058.14 117.03 1058.14 115.85 Z M 1059.57 115.85 C 1059.57 117.82 1057.96 119.42 1055.99 119.42 C 1054.03 119.42 1052.42 117.82 1052.42 115.85 C 1052.42 113.88 1054.03 112.28 1055.99 112.28 C 1057.96 112.28 1059.57 113.88 1059.57 115.85 Z M 1064.81 104.65 C 1064.81 105.83 1065.77 106.79 1066.95 106.79 C 1068.13 106.79 1069.09 105.83 1069.09 104.65 C 1069.09 103.47 1068.13 102.5 1066.95 102.5 C 1065.77 102.5 1064.81 103.47 1064.81 104.65 Z M 1063.38 104.65 C 1063.38 102.68 1064.98 101.08 1066.95 101.08 C 1068.92 101.08 1070.52 102.68 1070.52 104.65 C 1070.52 106.62 1068.92 108.22 1066.95 108.22 C 1064.98 108.22 1063.38 106.62 1063.38 104.65 Z M 1083.57 117.5 C 1083.57 110.88 1080.02 104.75 1074.29 101.44 C 1073.27 101.64 1072.28 101.93 1071.04 102.37 L 1070.55 101.03 C 1071.2 100.8 1071.78 100.61 1072.33 100.45 C 1070.03 99.45 1067.53 98.93 1065 98.93 C 1063.79 98.93 1062.61 99.05 1061.45 99.28 C 1062.29 99.77 1063.03 100.26 1063.76 100.81 L 1062.89 101.95 C 1061.87 101.17 1060.81 100.52 1059.43 99.79 C 1052.41 102 1047.36 108.18 1046.56 115.45 C 1048.03 115.15 1049.44 114.99 1051.01 114.95 L 1051.05 116.38 C 1049.4 116.42 1047.99 116.59 1046.45 116.93 C 1046.44 117.12 1046.43 117.31 1046.43 117.5 C 1046.43 123.68 1049.49 129.38 1054.52 132.82 C 1053.62 130.15 1053.18 127.64 1053.18 125.18 C 1053.18 123.77 1053.42 122.62 1053.67 121.4 C 1053.73 121.12 1053.79 120.83 1053.85 120.53 L 1055.26 120.81 C 1055.19 121.11 1055.13 121.41 1055.07 121.69 C 1054.82 122.89 1054.61 123.92 1054.61 125.18 C 1054.61 127.97 1055.22 130.85 1056.47 133.99 C 1059.12 135.37 1061.99 136.07 1065 136.07 C 1066.97 136.07 1068.89 135.76 1070.72 135.15 C 1071.44 133.73 1071.98 132.39 1072.42 130.86 L 1073.79 131.25 C 1073.47 132.38 1073.09 133.4 1072.65 134.41 C 1073.8 133.89 1074.89 133.25 1075.92 132.5 C 1075.67 131.9 1075.41 131.3 1075.11 130.71 L 1076.39 130.07 C 1076.64 130.57 1076.87 131.08 1077.09 131.59 C 1081.22 128.05 1083.57 122.97 1083.57 117.5 Z M 1085 117.5 C 1085 123.73 1082.17 129.5 1077.23 133.32 C 1076.01 134.27 1074.68 135.06 1073.28 135.69 C 1072.68 135.96 1072.08 136.21 1071.45 136.42 C 1069.4 137.14 1067.22 137.5 1065 137.5 C 1061.71 137.5 1058.45 136.68 1055.56 135.13 C 1049.05 131.65 1045 124.89 1045 117.5 C 1045 117.01 1045.01 116.64 1045.04 116.29 C 1045.53 107.95 1051.25 100.72 1059.29 98.33 C 1061.12 97.78 1063.04 97.5 1065 97.5 C 1068.43 97.5 1071.81 98.38 1074.77 100.06 C 1081.08 103.59 1085 110.27 1085 117.5 Z M 1063.08 107.12 L 1062.14 106.04 C 1060.54 107.44 1059.3 108.92 1057.84 111.16 L 1059.04 111.94 C 1060.42 109.82 1061.59 108.42 1063.08 107.12 Z M 1060.91 116.38 L 1060.45 117.73 C 1063.73 118.85 1066.59 120.65 1069.45 123.38 L 1070.44 122.35 C 1067.42 119.47 1064.39 117.57 1060.91 116.38 Z M 1070.46 107.98 C 1073.14 112.06 1074.65 116.55 1074.95 121.31 L 1073.52 121.4 C 1073.24 116.89 1071.81 112.64 1069.27 108.76 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 150px; margin-left: 1065px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    primary distribution
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1065" y="162" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        primary...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1040 242.5 L 1090 242.5 L 1090 292.5 L 1040 292.5 Z" fill="#e7157b" stroke="none" pointer-events="all" style="fill: light-dark(rgb(231, 21, 123), rgb(255, 129, 217));"/>
            <path d="M 1074.29 267.83 L 1077.86 267.83 L 1077.86 266.4 L 1074.29 266.4 Z M 1056.43 276.41 L 1060 276.41 L 1060 274.98 L 1056.43 274.98 Z M 1049.29 276.41 L 1054.29 276.41 L 1054.29 274.98 L 1049.29 274.98 Z M 1049.29 272.12 L 1057.86 272.12 L 1057.86 270.69 L 1049.29 270.69 Z M 1049.29 263.54 L 1055.71 263.54 L 1055.71 262.11 L 1049.29 262.11 Z M 1049.29 267.83 L 1072.86 267.83 L 1072.86 266.4 L 1049.29 266.4 Z M 1062.86 283.57 L 1046.43 283.57 L 1046.43 259.24 L 1062.86 259.24 L 1062.86 264.97 L 1064.29 264.97 L 1064.29 258.53 C 1064.29 258.13 1063.97 257.81 1063.57 257.81 L 1045.71 257.81 C 1045.32 257.81 1045 258.13 1045 258.53 L 1045 284.28 C 1045 284.68 1045.32 285 1045.71 285 L 1063.57 285 C 1063.97 285 1064.29 284.68 1064.29 284.28 L 1064.29 269.98 L 1062.86 269.98 Z M 1085 265.68 C 1085 270.31 1080.84 271.91 1078.64 272.12 L 1067.14 272.12 L 1067.14 270.69 L 1078.57 270.69 C 1078.71 270.67 1083.57 270.15 1083.57 265.68 C 1083.57 261.62 1079.9 260.79 1079.17 260.67 C 1078.8 260.6 1078.54 260.27 1078.57 259.9 C 1078.58 259.89 1078.58 259.87 1078.58 259.86 C 1078.54 257.56 1077.13 256.84 1076.52 256.64 C 1075.38 256.26 1074.11 256.62 1073.44 257.53 C 1073.28 257.74 1073.02 257.85 1072.76 257.81 C 1072.5 257.77 1072.28 257.59 1072.19 257.34 C 1071.75 256.1 1071.1 255.29 1070.21 254.39 C 1067.97 252.17 1064.93 251.55 1062.09 252.75 C 1060.6 253.38 1059.29 254.8 1058.52 256.66 L 1057.2 256.11 C 1058.12 253.91 1059.7 252.2 1061.54 251.43 C 1064.93 250 1068.55 250.73 1071.22 253.38 C 1071.98 254.15 1072.61 254.89 1073.11 255.86 C 1074.17 255.07 1075.61 254.83 1076.97 255.28 C 1078.71 255.86 1079.81 257.37 1079.98 259.38 C 1082.41 259.99 1085 261.94 1085 265.68 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 300px; margin-left: 1065px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    custom resource
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1065" y="312" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        custom r...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1179.09 316.37 L 1178.54 390.01" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1179.13 311.12 L 1182.58 318.14 L 1179.09 316.37 L 1175.58 318.09 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 350px; margin-left: 1139px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    <span>
                                        ➁get param
                                    </span>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1139" y="353" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        ➁get param
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1209.14 310 L 1208.59 383.64" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1208.55 388.89 L 1205.1 381.87 L 1208.59 383.64 L 1212.1 381.92 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 350px; margin-left: 1249px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ➄delete ssm
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1249" y="354" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        ➄delete ssm
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1170 260 L 1220 260 L 1220 310 L 1170 310 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
            <path d="M 1186.45 303.57 L 1177.58 303.57 L 1187.39 283.07 L 1191.84 292.22 Z M 1188.03 281.11 C 1187.91 280.86 1187.66 280.71 1187.39 280.71 L 1187.39 280.71 C 1187.11 280.71 1186.86 280.87 1186.74 281.11 L 1175.8 303.98 C 1175.7 304.2 1175.71 304.46 1175.84 304.67 C 1175.97 304.87 1176.2 305 1176.45 305 L 1186.9 305 C 1187.18 305 1187.43 304.84 1187.55 304.59 L 1193.28 292.52 C 1193.37 292.32 1193.37 292.1 1193.27 291.9 Z M 1212.87 303.57 L 1204.06 303.57 L 1189.92 273.98 C 1189.81 273.73 1189.55 273.57 1189.28 273.57 L 1183.51 273.57 L 1183.52 266.43 L 1194.82 266.43 L 1208.89 296.02 C 1209.01 296.27 1209.26 296.43 1209.54 296.43 L 1212.87 296.43 Z M 1213.59 295 L 1209.99 295 L 1195.92 265.41 C 1195.8 265.16 1195.55 265 1195.27 265 L 1182.8 265 C 1182.41 265 1182.09 265.32 1182.09 265.71 L 1182.08 274.29 C 1182.08 274.48 1182.15 274.66 1182.29 274.79 C 1182.42 274.93 1182.6 275 1182.79 275 L 1188.83 275 L 1202.96 304.59 C 1203.08 304.84 1203.33 305 1203.61 305 L 1213.59 305 C 1213.98 305 1214.3 304.68 1214.3 304.29 L 1214.3 295.71 C 1214.3 295.32 1213.98 295 1213.59 295 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 317px; margin-left: 1195px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    lambda
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1195" y="329" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        lambda
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1000" y="0" width="60" height="30" fill="none" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 15px; margin-left: 1001px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    DELETE
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1030" y="19" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        DELETE
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="110" y="70" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 125 90.32 C 122.06 90.32 119.68 87.93 119.68 85 C 119.68 82.07 122.06 79.68 125 79.68 C 127.94 79.68 130.32 82.07 130.32 85 C 130.32 87.93 127.94 90.32 125 90.32 Z M 125 78.31 C 121.31 78.31 118.31 81.31 118.31 85 C 118.31 88.69 121.31 91.69 125 91.69 C 128.69 91.69 131.69 88.69 131.69 85 C 131.69 81.31 128.69 78.31 125 78.31 Z M 138.64 87.55 L 136.23 87.55 C 135.92 87.55 135.65 87.76 135.57 88.06 C 135.32 89.01 134.92 89.69 134.45 90.49 C 134.29 90.76 134.34 91.1 134.56 91.32 L 136.26 93.01 L 132.99 96.26 L 131.28 94.56 C 131.06 94.35 130.73 94.3 130.46 94.46 L 130.31 94.54 C 129.51 95.01 128.93 95.34 128.06 95.57 C 127.76 95.65 127.55 95.92 127.55 96.23 L 127.55 98.64 L 122.45 98.64 L 122.45 96.23 C 122.45 95.92 122.24 95.65 121.94 95.57 C 121 95.33 120.33 94.95 119.48 94.45 C 119.21 94.3 118.87 94.34 118.65 94.56 L 116.97 96.26 L 113.72 92.99 L 115.41 91.29 C 115.63 91.07 115.67 90.73 115.52 90.47 L 115.44 90.34 C 114.97 89.53 114.63 88.94 114.4 88.06 C 114.32 87.76 114.05 87.55 113.74 87.55 L 111.36 87.55 L 111.36 82.45 L 113.77 82.45 C 114.08 82.45 114.35 82.24 114.43 81.94 C 114.68 80.99 115.07 80.28 115.55 79.46 C 115.7 79.19 115.66 78.85 115.44 78.63 L 113.74 76.94 L 117.01 73.69 L 118.71 75.38 C 118.93 75.6 119.27 75.64 119.53 75.49 L 119.65 75.42 C 120.47 74.95 121.06 74.6 121.94 74.37 C 122.24 74.29 122.45 74.02 122.45 73.71 L 122.45 71.36 L 127.55 71.36 L 127.55 73.76 C 127.55 74.06 127.76 74.33 128.06 74.41 C 128.92 74.65 129.5 74.99 130.3 75.46 L 130.42 75.53 C 130.69 75.69 131.03 75.65 131.25 75.42 L 132.91 73.74 L 136.12 76.99 L 134.44 78.69 C 134.23 78.9 134.19 79.24 134.34 79.5 C 134.82 80.34 135.19 80.99 135.44 81.94 C 135.52 82.24 135.79 82.45 136.1 82.45 L 138.64 82.45 Z M 138.72 81.09 L 136.61 81.09 C 136.38 80.4 136.1 79.85 135.78 79.28 L 137.15 77.89 C 137.64 77.39 137.64 76.59 137.15 76.09 L 133.82 72.72 C 133.34 72.23 132.49 72.23 132 72.72 L 130.65 74.09 C 130.1 73.76 129.57 73.48 128.91 73.25 L 128.91 71.28 C 128.91 70.57 128.34 70 127.64 70 L 122.36 70 C 121.66 70 121.09 70.57 121.09 71.28 L 121.09 73.21 C 120.42 73.43 119.87 73.72 119.3 74.05 L 117.91 72.66 C 117.41 72.16 116.6 72.17 116.11 72.66 L 112.72 76.04 C 112.48 76.28 112.34 76.6 112.34 76.94 C 112.34 77.28 112.48 77.61 112.72 77.85 L 114.11 79.23 C 113.78 79.81 113.49 80.39 113.26 81.09 L 111.28 81.09 C 110.57 81.09 110 81.66 110 82.36 L 110 87.64 C 110 88.34 110.57 88.91 111.28 88.91 L 113.24 88.91 C 113.46 89.58 113.75 90.13 114.08 90.7 L 112.69 92.09 C 112.2 92.59 112.2 93.39 112.69 93.89 L 116.06 97.28 C 116.3 97.52 116.62 97.66 116.96 97.66 L 116.97 97.66 C 117.31 97.66 117.63 97.52 117.87 97.28 L 119.25 95.89 C 119.84 96.23 120.41 96.51 121.09 96.74 L 121.09 98.72 C 121.09 99.43 121.66 100 122.36 100 L 127.64 100 C 128.34 100 128.91 99.43 128.91 98.72 L 128.91 96.74 C 129.58 96.51 130.12 96.22 130.69 95.9 L 132.08 97.28 C 132.58 97.78 133.39 97.78 133.89 97.28 L 137.28 93.91 C 137.52 93.67 137.66 93.35 137.66 93.01 C 137.66 92.66 137.52 92.34 137.28 92.1 L 135.89 90.72 C 136.21 90.17 136.51 89.6 136.74 88.91 L 138.72 88.91 C 139.43 88.91 140 88.34 140 87.64 L 140 82.36 C 140 81.66 139.43 81.09 138.72 81.09 Z" fill="#232f3d" stroke="none" pointer-events="all" style="fill: light-dark(rgb(35, 47, 61), rgb(190, 200, 212));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 85px; margin-left: 142px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    <span style="background-color: light-dark(#ffffff, var(--ge-dark-color, #121212));">
                                        Continuous
                                    </span>
                                    <div>
                                        <span style="background-color: light-dark(#ffffff, var(--ge-dark-color, #121212));">
                                            DeploymentPolicy
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="142" y="89" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Conti...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="600" y="70" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 615 90.32 C 612.06 90.32 609.68 87.93 609.68 85 C 609.68 82.07 612.06 79.68 615 79.68 C 617.94 79.68 620.32 82.07 620.32 85 C 620.32 87.93 617.94 90.32 615 90.32 Z M 615 78.31 C 611.31 78.31 608.31 81.31 608.31 85 C 608.31 88.69 611.31 91.69 615 91.69 C 618.69 91.69 621.69 88.69 621.69 85 C 621.69 81.31 618.69 78.31 615 78.31 Z M 628.64 87.55 L 626.23 87.55 C 625.92 87.55 625.65 87.76 625.57 88.06 C 625.32 89.01 624.92 89.69 624.45 90.49 C 624.29 90.76 624.34 91.1 624.56 91.32 L 626.26 93.01 L 622.99 96.26 L 621.28 94.56 C 621.06 94.35 620.73 94.3 620.46 94.46 L 620.31 94.54 C 619.51 95.01 618.93 95.34 618.06 95.57 C 617.76 95.65 617.55 95.92 617.55 96.23 L 617.55 98.64 L 612.45 98.64 L 612.45 96.23 C 612.45 95.92 612.24 95.65 611.94 95.57 C 611 95.33 610.33 94.95 609.48 94.45 C 609.21 94.3 608.87 94.34 608.65 94.56 L 606.97 96.26 L 603.72 92.99 L 605.41 91.29 C 605.63 91.07 605.67 90.73 605.52 90.47 L 605.44 90.34 C 604.97 89.53 604.63 88.94 604.4 88.06 C 604.32 87.76 604.05 87.55 603.74 87.55 L 601.36 87.55 L 601.36 82.45 L 603.77 82.45 C 604.08 82.45 604.35 82.24 604.43 81.94 C 604.68 80.99 605.07 80.28 605.55 79.46 C 605.7 79.19 605.66 78.85 605.44 78.63 L 603.74 76.94 L 607.01 73.69 L 608.71 75.38 C 608.93 75.6 609.27 75.64 609.53 75.49 L 609.65 75.42 C 610.47 74.95 611.06 74.6 611.94 74.37 C 612.24 74.29 612.45 74.02 612.45 73.71 L 612.45 71.36 L 617.55 71.36 L 617.55 73.76 C 617.55 74.06 617.76 74.33 618.06 74.41 C 618.92 74.65 619.5 74.99 620.3 75.46 L 620.42 75.53 C 620.69 75.69 621.03 75.65 621.25 75.42 L 622.91 73.74 L 626.12 76.99 L 624.44 78.69 C 624.23 78.9 624.19 79.24 624.34 79.5 C 624.82 80.34 625.19 80.99 625.44 81.94 C 625.52 82.24 625.79 82.45 626.1 82.45 L 628.64 82.45 Z M 628.72 81.09 L 626.61 81.09 C 626.38 80.4 626.1 79.85 625.78 79.28 L 627.15 77.89 C 627.64 77.39 627.64 76.59 627.15 76.09 L 623.82 72.72 C 623.34 72.23 622.49 72.23 622 72.72 L 620.65 74.09 C 620.1 73.76 619.57 73.48 618.91 73.25 L 618.91 71.28 C 618.91 70.57 618.34 70 617.64 70 L 612.36 70 C 611.66 70 611.09 70.57 611.09 71.28 L 611.09 73.21 C 610.42 73.43 609.87 73.72 609.3 74.05 L 607.91 72.66 C 607.41 72.16 606.6 72.17 606.11 72.66 L 602.72 76.04 C 602.48 76.28 602.34 76.6 602.34 76.94 C 602.34 77.28 602.48 77.61 602.72 77.85 L 604.11 79.23 C 603.78 79.81 603.49 80.39 603.26 81.09 L 601.28 81.09 C 600.57 81.09 600 81.66 600 82.36 L 600 87.64 C 600 88.34 600.57 88.91 601.28 88.91 L 603.24 88.91 C 603.46 89.58 603.75 90.13 604.08 90.7 L 602.69 92.09 C 602.2 92.59 602.2 93.39 602.69 93.89 L 606.06 97.28 C 606.3 97.52 606.62 97.66 606.96 97.66 L 606.97 97.66 C 607.31 97.66 607.63 97.52 607.87 97.28 L 609.25 95.89 C 609.84 96.23 610.41 96.51 611.09 96.74 L 611.09 98.72 C 611.09 99.43 611.66 100 612.36 100 L 617.64 100 C 618.34 100 618.91 99.43 618.91 98.72 L 618.91 96.74 C 619.58 96.51 620.12 96.22 620.69 95.9 L 622.08 97.28 C 622.58 97.78 623.39 97.78 623.89 97.28 L 627.28 93.91 C 627.52 93.67 627.66 93.35 627.66 93.01 C 627.66 92.66 627.52 92.34 627.28 92.1 L 625.89 90.72 C 626.21 90.17 626.51 89.6 626.74 88.91 L 628.72 88.91 C 629.43 88.91 630 88.34 630 87.64 L 630 82.36 C 630 81.66 629.43 81.09 628.72 81.09 Z" fill="#232f3d" stroke="none" pointer-events="all" style="fill: light-dark(rgb(35, 47, 61), rgb(190, 200, 212));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 85px; margin-left: 632px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    <span style="background-color: light-dark(#ffffff, var(--ge-dark-color, #121212));">
                                        Continuous
                                    </span>
                                    <div>
                                        <span style="background-color: light-dark(#ffffff, var(--ge-dark-color, #121212));">
                                            DeploymentPolicy
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="632" y="89" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Conti...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1090" y="70" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 1105 90.32 C 1102.06 90.32 1099.68 87.93 1099.68 85 C 1099.68 82.07 1102.06 79.68 1105 79.68 C 1107.94 79.68 1110.32 82.07 1110.32 85 C 1110.32 87.93 1107.94 90.32 1105 90.32 Z M 1105 78.31 C 1101.31 78.31 1098.31 81.31 1098.31 85 C 1098.31 88.69 1101.31 91.69 1105 91.69 C 1108.69 91.69 1111.69 88.69 1111.69 85 C 1111.69 81.31 1108.69 78.31 1105 78.31 Z M 1118.64 87.55 L 1116.23 87.55 C 1115.92 87.55 1115.65 87.76 1115.57 88.06 C 1115.32 89.01 1114.92 89.69 1114.45 90.49 C 1114.29 90.76 1114.34 91.1 1114.56 91.32 L 1116.26 93.01 L 1112.99 96.26 L 1111.28 94.56 C 1111.06 94.35 1110.73 94.3 1110.46 94.46 L 1110.31 94.54 C 1109.51 95.01 1108.93 95.34 1108.06 95.57 C 1107.76 95.65 1107.55 95.92 1107.55 96.23 L 1107.55 98.64 L 1102.45 98.64 L 1102.45 96.23 C 1102.45 95.92 1102.24 95.65 1101.94 95.57 C 1101 95.33 1100.33 94.95 1099.48 94.45 C 1099.21 94.3 1098.87 94.34 1098.65 94.56 L 1096.97 96.26 L 1093.72 92.99 L 1095.41 91.29 C 1095.63 91.07 1095.67 90.73 1095.52 90.47 L 1095.44 90.34 C 1094.97 89.53 1094.63 88.94 1094.4 88.06 C 1094.32 87.76 1094.05 87.55 1093.74 87.55 L 1091.36 87.55 L 1091.36 82.45 L 1093.77 82.45 C 1094.08 82.45 1094.35 82.24 1094.43 81.94 C 1094.68 80.99 1095.07 80.28 1095.55 79.46 C 1095.7 79.19 1095.66 78.85 1095.44 78.63 L 1093.74 76.94 L 1097.01 73.69 L 1098.71 75.38 C 1098.93 75.6 1099.27 75.64 1099.53 75.49 L 1099.65 75.42 C 1100.47 74.95 1101.06 74.6 1101.94 74.37 C 1102.24 74.29 1102.45 74.02 1102.45 73.71 L 1102.45 71.36 L 1107.55 71.36 L 1107.55 73.76 C 1107.55 74.06 1107.76 74.33 1108.06 74.41 C 1108.92 74.65 1109.5 74.99 1110.3 75.46 L 1110.42 75.53 C 1110.69 75.69 1111.03 75.65 1111.25 75.42 L 1112.91 73.74 L 1116.12 76.99 L 1114.44 78.69 C 1114.23 78.9 1114.19 79.24 1114.34 79.5 C 1114.82 80.34 1115.19 80.99 1115.44 81.94 C 1115.52 82.24 1115.79 82.45 1116.1 82.45 L 1118.64 82.45 Z M 1118.72 81.09 L 1116.61 81.09 C 1116.38 80.4 1116.1 79.85 1115.78 79.28 L 1117.15 77.89 C 1117.64 77.39 1117.64 76.59 1117.15 76.09 L 1113.82 72.72 C 1113.34 72.23 1112.49 72.23 1112 72.72 L 1110.65 74.09 C 1110.1 73.76 1109.57 73.48 1108.91 73.25 L 1108.91 71.28 C 1108.91 70.57 1108.34 70 1107.64 70 L 1102.36 70 C 1101.66 70 1101.09 70.57 1101.09 71.28 L 1101.09 73.21 C 1100.42 73.43 1099.87 73.72 1099.3 74.05 L 1097.91 72.66 C 1097.41 72.16 1096.6 72.17 1096.11 72.66 L 1092.72 76.04 C 1092.48 76.28 1092.34 76.6 1092.34 76.94 C 1092.34 77.28 1092.48 77.61 1092.72 77.85 L 1094.11 79.23 C 1093.78 79.81 1093.49 80.39 1093.26 81.09 L 1091.28 81.09 C 1090.57 81.09 1090 81.66 1090 82.36 L 1090 87.64 C 1090 88.34 1090.57 88.91 1091.28 88.91 L 1093.24 88.91 C 1093.46 89.58 1093.75 90.13 1094.08 90.7 L 1092.69 92.09 C 1092.2 92.59 1092.2 93.39 1092.69 93.89 L 1096.06 97.28 C 1096.3 97.52 1096.62 97.66 1096.96 97.66 L 1096.97 97.66 C 1097.31 97.66 1097.63 97.52 1097.87 97.28 L 1099.25 95.89 C 1099.84 96.23 1100.41 96.51 1101.09 96.74 L 1101.09 98.72 C 1101.09 99.43 1101.66 100 1102.36 100 L 1107.64 100 C 1108.34 100 1108.91 99.43 1108.91 98.72 L 1108.91 96.74 C 1109.58 96.51 1110.12 96.22 1110.69 95.9 L 1112.08 97.28 C 1112.58 97.78 1113.39 97.78 1113.89 97.28 L 1117.28 93.91 C 1117.52 93.67 1117.66 93.35 1117.66 93.01 C 1117.66 92.66 1117.52 92.34 1117.28 92.1 L 1115.89 90.72 C 1116.21 90.17 1116.51 89.6 1116.74 88.91 L 1118.72 88.91 C 1119.43 88.91 1120 88.34 1120 87.64 L 1120 82.36 C 1120 81.66 1119.43 81.09 1118.72 81.09 Z" fill="#232f3d" stroke="none" pointer-events="all" style="fill: light-dark(rgb(35, 47, 61), rgb(190, 200, 212));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 85px; margin-left: 1122px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    <span style="background-color: light-dark(#ffffff, var(--ge-dark-color, #121212));">
                                        Continuous
                                    </span>
                                    <div>
                                        <span style="background-color: light-dark(#ffffff, var(--ge-dark-color, #121212));">
                                            DeploymentPolicy
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1122" y="89" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Conti...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1096.37 117.53 L 1195.5 118 L 1195 260.5" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1091.12 117.51 L 1098.13 114.04 L 1096.37 117.53 L 1098.1 121.04 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 190px; margin-left: 1195px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ➂detach policy
                                    <div>
                                        &amp;
                                    </div>
                                    <div>
                                        delete policy
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1195" y="194" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        ➂detach policy...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 600 117.5 L 715.5 118 L 715.02 253.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 715 258.88 L 711.53 251.87 L 715.02 253.63 L 718.53 251.89 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 168px; margin-left: 756px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ➃get config
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="756" y="171" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        ➃get config
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 600 130.5 L 693.5 131 L 693.02 254.13" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 693 259.38 L 689.53 252.37 L 693.02 254.13 L 696.53 252.4 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 200px; margin-left: 651px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ➁get Etag
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="651" y="204" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        ➁get Etag
                    </text>
                </switch>
            </g>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>