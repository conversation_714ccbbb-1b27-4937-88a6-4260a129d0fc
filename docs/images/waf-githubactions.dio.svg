<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="881px" height="551px" viewBox="-0.5 -0.5 881 551" content="&lt;mxfile&gt;&lt;diagram id=&quot;EKTkNQlnH3Mt4uukSo2Q&quot; name=&quot;Page-1&quot;&gt;7Vxbb6s4EP41vEZgbuYRcuk5Uo+22mrV3aeKBCdBJSEC0sv59WuDTfAlt4YkTQ9ppOKxsY1nvpnx4Ilm9hfvd1m4mv9KI5RoQI/eNXOgAWDYton/EcpHRXEctyLMsjiijTaEx/g3okSdUtdxhHKuYZGmSRGveOIkXS7RpOBoYZalb3yzaZrwo67CGZIIj5MwkalPcVTMKyoE7ob+A8WzORvZcLyqZhxOXmZZul7S8TRgTstPVb0IWV/0QfN5GKVvDZI51Mx+lqZFdbV476OErC1btuq+0Zbaet4ZWhYH3QDoxPPigz08ivBa0OIyXeJ/wbxYJLhk4EvccfbxLy7orPAfKfRsVhy8NysHH83SA8riBSpQRonydOkT5Ok6m9D5mHTVizCbIdrMpu3IXBs30oe8QykeJfvADTKUhEX8yvM1pOIxq9ttlghf0FVSrxibzWuYrBFjsrCC6bpI4iXq19JJnnWaLot+mqRZ2cbEfyMyQjDLwihGmzq65NM4ScTmA0zPiyx9QULjKMznKKIDvaKsiLEs34djlDykeVzE6RLXjdOiSBeNBn4Sz0hFka4wNaSlCZ4LYVCT6WTuFKUGYGX6vGTIMF9VDzqN38k8glUak16Gr7iznHaCZX1Fbli8z4jW6IVvudVb5+VYJWTqJ9gqFmTm6H0nw1mtQxH2IZTfNni2KGnegDKjnSIiBpORg0F1EA6YzDdxYJhXAwKbTgMId3HxYz32J0TgcmkJ9splY0EOFFElGATk6HowHAGV/P1eZ6g3i4v5evw8wTZsi9y2LpsWvJ5s2o7ENf/pUeJVuQ7lMHaAv3jgvq7ZuKZPSj1gCwSx7PIEQy6RPniCWHZ5giF2bwjjG+IEGwSpxHWvC+PrjQnaROkqFfoWxd2Q4rd5XKDHVVgi+A0L3QGqFLs0RYjHymgfokRWbZIkXOXxuL4rQ5N1lmN4/43yqnN9m8Yl7smqnP7PSYk/ufYZXz5PknQdSRhrWq4m0OjD78ZtgqYF6RGvSLyc3ZelgbnLOHKGrXUgOjoPREuBQxUQgdOKlQD7PYn8BRWTOX1iJSiVwFSBUwlQGaRcsxI2ihFEoormykRDbsaQJhNVNJVaEe82FHcbwt3bQX2ol4brRiMb2KBRN4gxDKlRW6YZESHRGPVdbLFHKlTRbcIX9OWUWiRDlWtS6RCsf3KVNqn3Zwq01pbocJ/O63ncx9mL3nN5eJZ5oW3T9nVruoa1G8j5hha4lm9Yz6eh2376v7ShqUFT8wf0IugzSrBb851hQxWMdGg4329DlaWkk1bwZsKei93n+gN4rxVAGW+gZ1sKe2m1IVJQEqknf9RZzFuymLblWl5nMXdYzLdw2hJ6gb3fuT2beTzAtVXsHiQWj0Ym/jRZvD04IHJFkEvbd/rQudzaGlC1cdBbWV1dNq4P2tDWINQCRxt6moe/+sa2dtb2XNYWw/U5W7dncQ1erqAsV8zqcmIF25AqS5KqPt5wF5Ks4Gcp+PXdG4ajJIlHIisXcRSVi6mKnPDbf0EeKw9F4K3idcgneCJgXRGscxVQN1tBui3xJEjSyUvHEyGCatgXZIocQsUaJJ5oQ6j5vgYH5QXk9G2ngW9GAwu7HPeSKlh+t9ipYBXcbXBBuMv7zk4HK5hSH964BFNs2QWWNSpVF9MEvfvkUEgZXIvo5WCShDnR2vwbGzp3lwbm2NkUs2cbDvepqtnpEMPq8fVw53ZG0M01fTtf9sbwGqtuK1ad0Q4O9dERHogeboaErJ4NGzEhi5MBT+BtteOlfWzYq+jW5gEOhI6quKbUUSkn9SIcKDpyaLITnQuJjmM3ovc8x+HnRaenQ7Um2iM9mJ3hR6MZjRbueAJ5KJ07X4Uvqk4/L52qyImTEMMSxa/4cla+TRkCzTM0X7+P8+IJjf3+PZ63Ey6IPViO81UpENVteMjmnTs6u0O0L9ZonIm3cZTThjP+WUVhgfgRxbuPNLEYEDXEBKBMbfKnSS+j2zO11UdyblswtQY7eMjAYsmhd8MwZehC0IKthfJ+568VWv6M2Pbkdh0hfQsnVAzbHgzcG7Mx9HP5QabMG4kdNx1o3R9nVa1tK3FWV6GK6fbdYBt6k17gB6YXPl8FtMDThq7mWVoQlKHZgeaDMkJgkCABrvJxlU8b+4BdjNjtLhvU3fQsa+Oho0FPg0EZBg40zyYX/lDzRnQs2L99nJ502EeUJcNT7OwNpTC1AFR2Ir4FYZL4inmPvSkoI/92GCzGiNT64+RTwAr9cb6XYK5CN3+S5ZCg3zfpqx2sMDqWH8py01Tw3DwXz+GxJ7/PkE4h7Pf2nis3IPUmucNDnq5e5vbPCkE5wCYtYpdR0UZGxR4/90hdasvAOpsuhcemKX0SCswn4KFgXQoKbPg/MqfiJOkUcyouKp2eHFLskiq6pIqbTao4CYliUoXKATtjVoWnCqCK4f3ujOjXPSPaZVUcm1WxD67ezaRVeJdNq9jtHnpKB9FSOYhbAsRncBDlQ3pdXsXl8ypORNzXSqzw5EOGXWLFjRnNLrHimMSKE+F7zcwK78u/8Dv/4p4xtcKTT2B2qRVf5GDvqUb3irkVnhzi/iYHe09lyhWTK9gJtO93sLdlplwyu6JW4112xfdUwsJe54LpFUCXw/SdFlYC/oL5FYDtfjs1/IUSLIAuBwW6c8i3fw751FCxKSgKS/YMDNUZujYOIgP9Tz+IvHfvfL2TyEA/4HdRbzswccWjyBhU+1f3ZpOWDnkNc82sJZd/IVZvh45PVZL6gkJXLea5uQccq7tZkVG+qPtCiW4Cm6WMtBNERsqUa09mgC6HREjME4/o+SQKGkDy7dIZLmPtd5sjwKfeWipr7ykN0tH2CBc3P0lfydXmd//N4f8=&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-ff5252-1-c7131f-1-s-0">
            <stop offset="0%" style="stop-color: rgb(199, 19, 31); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(255, 82, 82); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f54749-1-c7131f-1-s-0">
            <stop offset="0%" style="stop-color: rgb(199, 19, 31); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(245, 71, 73); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <path d="M 38.18 140 L 313.63 140" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 318.88 140 L 311.88 143.5 L 313.63 140 L 311.88 136.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="0" y="120" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 3.66 158.18 C 4.11 149.17 11.27 141.98 20 141.98 C 22.95 141.98 25.85 142.81 28.37 144.38 C 33.05 147.3 36.05 152.53 36.34 158.18 Z M 11.08 130.8 C 11.08 125.85 15.08 121.82 20 121.82 C 24.92 121.82 28.92 125.85 28.92 130.8 C 28.92 135.75 24.92 139.78 20 139.78 C 15.08 139.78 11.08 135.75 11.08 130.8 Z M 29.33 142.84 C 27.75 141.86 26.05 141.15 24.27 140.7 C 28.07 139.04 30.73 135.23 30.73 130.8 C 30.73 124.84 25.92 120 20 120 C 14.08 120 9.27 124.84 9.27 130.8 C 9.27 135.23 11.94 139.05 15.74 140.71 C 7.77 142.71 1.82 150.18 1.82 159.09 C 1.82 159.59 2.22 160 2.73 160 L 37.27 160 C 37.77 160 38.18 159.59 38.18 159.09 C 38.18 152.47 34.79 146.25 29.33 142.84 Z" fill="#232f3d" stroke="none" pointer-events="all"/>
        <path d="M 360 140 L 472.79 140" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 478.04 140 L 471.04 143.5 L 472.79 140 L 471.04 136.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="320" y="120" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 320 140 C 320 128.95 328.95 120 340 120 C 351.05 120 360 128.95 360 140 C 360 151.05 351.05 160 340 160 C 328.95 160 320 151.05 320 140 Z M 321.92 140 C 321.87 148.01 327.12 155.08 334.8 157.36 L 334.8 153.88 C 334.87 152.64 335.53 151.5 336.56 150.8 C 333.52 150.46 330.78 149.12 328.98 147.08 C 327.18 145.04 326.48 142.48 327.04 140 C 327.33 138.28 328.06 136.67 329.16 135.32 C 328.48 133.59 328.53 131.65 329.32 129.96 C 331.35 130.04 333.28 130.82 334.8 132.16 C 338.17 130.99 341.83 130.97 345.2 132.12 C 346.75 130.77 348.71 130.01 350.76 129.96 C 351.56 131.72 351.61 133.72 350.92 135.52 C 351.96 136.82 352.66 138.36 352.96 140 C 353.52 142.47 352.83 145.03 351.03 147.07 C 349.24 149.11 346.51 150.46 343.48 150.8 C 344.46 151.43 345.12 152.45 345.28 153.6 L 345.28 157.44 C 352.97 155.14 358.21 148.03 358.12 140 C 358.12 135.21 356.21 130.61 352.81 127.23 C 349.4 123.85 344.79 121.97 340 122 C 335.21 121.98 330.61 123.87 327.22 127.24 C 323.83 130.62 321.92 135.21 321.92 140 Z" fill="#00bef2" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 167px; margin-left: 340px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                GitHubActions
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="340" y="179" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    GitHub...
                </text>
            </switch>
        </g>
        <path d="M 440 0 L 880 0 L 880 260 L 440 260 Z" fill="none" stroke="#232f3e" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 450.73 6.89 C 448.19 6.86 446.05 9.34 446.28 11.84 C 444.63 12.3 443.58 14.09 443.93 15.76 C 444.17 17.45 445.84 18.8 447.55 18.58 C 451.3 18.56 455.05 18.61 458.79 18.55 C 460.71 18.38 462.05 16.29 461.58 14.45 C 461.34 13.13 460.21 11.98 458.87 11.81 C 458.86 10.3 457.26 8.82 455.79 9.6 C 454.97 10.39 454.82 8.44 454.02 8.13 C 453.14 7.28 451.95 6.82 450.73 6.89 Z M 450.75 7.63 C 452.68 7.51 454.32 9.05 454.91 10.79 C 455.31 11.19 455.67 10.52 455.97 10.34 C 457.25 9.57 458.21 11.17 458.23 12.29 C 458.58 12.77 459.39 12.38 459.77 12.91 C 461.21 13.93 461.3 16.3 459.88 17.37 C 458.9 18.09 457.63 17.75 456.5 17.83 C 453.28 17.82 450.06 17.84 446.84 17.82 C 445.2 17.58 444.19 15.69 444.79 14.17 C 445.07 13.16 446.05 12.67 446.94 12.36 C 447.19 11.62 446.85 10.71 447.33 9.99 C 447.92 8.65 449.25 7.61 450.75 7.63 Z M 440 25 C 440 16.67 440 8.33 440 0 C 448.33 0 456.67 0 465 0 C 465 8.33 465 16.67 465 25 C 456.67 25 448.33 25 440 25 Z" fill="#232f3e" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 408px; height: 1px; padding-top: 7px; margin-left: 472px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                AWS
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="472" y="19" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    AWS
                </text>
            </switch>
        </g>
        <path d="M 460 40 L 500 40 L 500 80 L 460 80 Z" fill="url(#mx-gradient-ff5252-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 486.61 58.36 C 485.99 58.36 485.49 57.86 485.49 57.24 C 485.49 56.62 485.99 56.12 486.61 56.12 C 487.23 56.12 487.74 56.62 487.74 57.24 C 487.74 57.86 487.23 58.36 486.61 58.36 M 481.56 63.96 C 480.94 63.96 480.44 63.46 480.44 62.84 C 480.44 62.22 480.94 61.72 481.56 61.72 C 482.18 61.72 482.68 62.22 482.68 62.84 C 482.68 63.46 482.18 63.96 481.56 63.96 M 477.07 57.24 C 476.45 57.24 475.95 56.74 475.95 56.12 C 475.95 55.5 476.45 55 477.07 55 C 477.69 55 478.19 55.5 478.19 56.12 C 478.19 56.74 477.69 57.24 477.07 57.24 M 471.45 62.84 C 470.84 62.84 470.33 62.34 470.33 61.72 C 470.33 61.1 470.84 60.6 471.45 60.6 C 472.07 60.6 472.58 61.1 472.58 61.72 C 472.58 62.34 472.07 62.84 471.45 62.84 M 486.61 55 C 485.37 55 484.37 56.01 484.37 57.24 C 484.37 57.83 484.6 58.37 484.98 58.77 L 482.94 61.09 C 482.56 60.79 482.08 60.6 481.56 60.6 C 481.33 60.6 481.11 60.65 480.91 60.71 L 478.81 57.52 C 479.12 57.13 479.31 56.65 479.31 56.12 C 479.31 54.89 478.31 53.88 477.07 53.88 C 475.83 53.88 474.82 54.89 474.82 56.12 C 474.82 56.61 474.98 57.06 475.25 57.43 L 472.76 59.9 C 472.39 59.64 471.94 59.48 471.45 59.48 C 470.22 59.48 469.21 60.49 469.21 61.72 C 469.21 62.96 470.22 63.96 471.45 63.96 C 472.69 63.96 473.7 62.96 473.7 61.72 C 473.7 61.38 473.62 61.06 473.48 60.77 L 476.12 58.14 C 476.41 58.28 476.73 58.36 477.07 58.36 C 477.37 58.36 477.65 58.3 477.91 58.19 L 479.95 61.29 C 479.56 61.69 479.31 62.24 479.31 62.84 C 479.31 64.08 480.32 65.08 481.56 65.08 C 482.8 65.08 483.81 64.08 483.81 62.84 C 483.81 62.54 483.74 62.26 483.64 62 L 485.95 59.37 C 486.16 59.44 486.38 59.48 486.61 59.48 C 487.85 59.48 488.86 58.48 488.86 57.24 C 488.86 56.01 487.85 55 486.61 55 M 490.22 66.2 L 485.09 66.2 C 484.97 66.2 484.86 66.24 484.76 66.31 L 475.95 72.72 L 475.95 66.76 C 475.95 66.45 475.69 66.2 475.38 66.2 L 469.05 66.2 C 468.49 66.2 468.04 66.09 467.68 65.87 C 466.51 65.13 465.12 63.72 465.12 61.15 C 465.12 57.73 467.51 56.61 468.93 56.25 C 469.19 56.18 469.37 55.94 469.35 55.67 L 469.3 54.73 C 469.3 52 471.18 49.07 473.58 48.06 C 474.66 47.6 475.66 47.42 476.56 47.42 C 479.14 47.42 480.96 48.89 481.75 49.69 C 482.62 50.56 483.31 51.68 483.78 53.02 C 483.85 53.21 484.02 53.35 484.22 53.38 C 484.43 53.41 484.63 53.33 484.76 53.16 C 485.47 52.2 486.7 51.79 487.82 52.16 C 489.07 52.57 489.79 53.78 489.8 55.5 L 489.8 55.52 C 489.75 55.82 489.96 56.1 490.26 56.16 C 491.34 56.34 494.88 57.26 494.88 61.2 C 494.88 65.79 490.37 66.19 490.22 66.2 M 490.91 55.15 C 490.8 53.13 489.79 51.63 488.17 51.09 C 486.89 50.68 485.51 50.96 484.5 51.81 C 484 50.68 483.35 49.7 482.55 48.9 C 479.98 46.32 476.46 45.62 473.14 47.02 C 470.31 48.22 468.17 51.54 468.17 54.76 L 468.21 55.29 C 466.66 55.79 464 57.23 464 61.15 C 464 64.39 465.93 66.09 467.08 66.82 C 467.62 67.16 468.27 67.32 469.05 67.32 L 474.82 67.32 L 474.82 73.82 C 474.82 74.03 474.94 74.23 475.13 74.32 C 475.21 74.36 475.3 74.38 475.38 74.38 C 475.5 74.38 475.62 74.35 475.71 74.27 L 485.27 67.32 L 490.26 67.32 C 490.32 67.32 496 66.84 496 61.2 C 496 56.86 492.55 55.53 490.91 55.15" fill="#ffffff" stroke="none" pointer-events="all"/>
        <path d="M 520.84 140 L 683.63 140" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 688.88 140 L 681.88 143.5 L 683.63 140 L 681.88 136.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="478.73" y="128" width="42.54" height="24" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 520.32 143.87 C 520.18 143.74 520.02 143.65 519.85 143.58 C 518.86 134.79 511.37 128 502.42 128 C 498.39 128 494.45 129.4 491.34 131.94 C 487.23 135.29 484.87 140.25 484.87 145.55 C 484.87 145.59 484.87 145.63 484.87 145.67 C 484.24 145.99 483.58 146.29 482.91 146.58 C 480.9 147.46 479.44 148.1 479.22 149.25 C 479.17 149.54 479.16 150.11 479.71 150.67 C 480.69 151.65 482.57 152 484.48 152 C 485.28 152 486.08 151.94 486.82 151.84 C 488.08 151.67 490.45 151.2 492.05 150.03 C 494.58 148.19 500.3 147.51 503.28 147.74 C 505.05 147.88 507.2 148.81 508.93 149.56 C 510.25 150.13 511.3 150.58 512.11 150.7 C 513.24 150.85 515.08 150.67 517.02 150.47 L 517.18 150.45 C 517.69 150.39 518.19 150.35 518.69 150.31 C 518.89 150.29 519.09 150.27 519.36 150.25 C 520.19 150.2 520.84 149.5 520.84 148.67 L 520.84 145.05 C 520.84 144.6 520.65 144.17 520.32 143.87 Z M 502.42 129.89 C 510.33 129.89 516.97 135.84 517.95 143.58 C 517.68 143.61 517.39 143.64 517.08 143.66 L 516.91 143.68 C 516.89 142.81 516.18 142.11 515.3 142.11 L 514.63 142.11 C 512.55 135.99 506.53 131.77 499.71 131.77 C 497.18 131.77 494.71 132.35 492.49 133.45 C 492.51 133.44 492.52 133.42 492.53 133.41 C 495.31 131.14 498.82 129.89 502.42 129.89 Z M 512.14 145.29 L 512.14 144 L 515.02 144 L 515.02 145.29 Z M 486.56 149.96 C 484.06 150.31 482.08 150 481.27 149.5 C 481.76 149.15 482.88 148.66 483.67 148.31 C 484.35 148.01 485.04 147.7 485.71 147.36 C 486.8 148.19 488.15 148.82 489.64 149.19 C 488.78 149.54 487.71 149.81 486.56 149.96 Z M 518.95 148.39 C 518.81 148.4 518.66 148.41 518.52 148.42 C 518.02 148.47 517.51 148.51 516.99 148.57 L 516.83 148.59 C 515.16 148.76 513.27 148.95 512.38 148.83 C 511.81 148.75 510.78 148.3 509.68 147.83 C 507.83 147.02 505.51 146.02 503.42 145.86 C 500.55 145.64 495.57 146.18 492.37 147.68 C 490.51 147.6 488.79 147.1 487.46 146.27 C 487.45 146.02 487.45 145.76 487.45 145.66 C 487.45 141.75 489.28 138.09 492.47 135.6 C 494.64 134.33 497.14 133.66 499.71 133.66 C 505.49 133.66 510.62 137.08 512.63 142.11 L 511.86 142.11 C 511.09 142.11 510.44 142.65 510.29 143.38 C 510.16 143.33 510.04 143.28 509.9 143.23 C 507.98 142.49 505.35 141.48 502.31 141.66 C 497.87 141.92 495.68 142.19 493.96 142.68 L 494.47 144.49 C 496.05 144.04 498.13 143.8 502.42 143.54 C 505.05 143.39 507.46 144.32 509.22 144.99 C 509.59 145.14 509.93 145.26 510.25 145.38 L 510.25 145.57 C 510.25 146.46 510.97 147.18 511.86 147.18 L 515.3 147.18 C 516.19 147.18 516.91 146.46 516.91 145.57 L 517.25 145.54 C 517.81 145.49 518.32 145.44 518.76 145.4 L 518.95 145.38 Z" fill="#bf0816" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 159px; margin-left: 500px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                IAMロール
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="500" y="171" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    IAMロール
                </text>
            </switch>
        </g>
        <path d="M 690 40 L 730 40 L 730 80 L 690 80 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 695.37 60.57 L 694 60.57 L 694 59.43 L 695.37 59.43 C 695.49 56.54 696.42 53.79 698.12 51.44 L 699.05 52.11 C 697.49 54.26 696.63 56.78 696.51 59.43 L 698 59.43 L 698 60.57 L 696.51 60.57 C 696.62 63.24 697.48 65.77 699.05 67.93 L 698.12 68.6 C 696.41 66.24 695.48 63.48 695.37 60.57 Z M 718.58 71.9 C 716.23 73.6 713.47 74.54 710.57 74.65 L 710.57 76 L 709.43 76 L 709.43 74.65 C 706.53 74.54 703.77 73.6 701.42 71.9 L 702.09 70.98 C 704.25 72.54 706.77 73.4 709.43 73.51 L 709.43 72 L 710.57 72 L 710.57 73.51 C 713.23 73.4 715.75 72.53 717.91 70.98 Z M 701.42 48.14 C 703.77 46.44 706.53 45.5 709.43 45.39 L 709.43 44 L 710.57 44 L 710.57 45.39 C 713.47 45.5 716.23 46.44 718.58 48.14 L 717.91 49.07 C 715.75 47.51 713.23 46.64 710.57 46.53 L 710.57 48 L 709.43 48 L 709.43 46.53 C 706.77 46.64 704.25 47.51 702.09 49.07 Z M 726 59.43 L 726 60.57 L 724.63 60.57 C 724.52 63.48 723.59 66.24 721.88 68.6 L 720.95 67.93 C 722.52 65.77 723.38 63.24 723.49 60.57 L 722 60.57 L 722 59.43 L 723.49 59.43 C 723.37 56.78 722.51 54.26 720.95 52.11 L 721.88 51.44 C 723.58 53.79 724.51 56.54 724.63 59.43 Z M 718.56 50.66 L 723.04 46.18 L 723.84 46.99 L 719.36 51.47 Z M 701.44 69.39 L 696.96 73.87 L 696.16 73.06 L 700.64 68.58 Z M 702.72 53.52 L 694.24 45.04 L 695.04 44.24 L 703.52 52.72 Z M 717.26 66.45 L 725.76 74.96 L 724.96 75.76 L 716.45 67.26 Z M 705.24 60.42 C 705.28 60.34 705.33 60.27 705.37 60.19 C 706.33 58.68 706.06 56.62 705.75 55.38 C 706.58 55.92 707.32 57.04 707.57 57.51 C 707.68 57.71 707.89 57.83 708.12 57.81 C 708.35 57.79 708.54 57.64 708.62 57.43 C 709.47 55.01 709.03 53.19 708.43 52.01 C 709.16 52.44 709.73 53.05 710.1 53.82 C 710.91 55.51 710.73 57.81 709.63 59.83 C 708.13 62.59 708.43 65.49 708.77 67.05 C 707.91 66.68 707.15 66.26 706.49 65.8 C 704.76 64.6 704.21 62.23 705.24 60.42 Z M 712.91 60.23 C 712.87 60.46 712.98 60.69 713.19 60.81 C 713.39 60.93 713.65 60.91 713.83 60.76 C 713.88 60.73 714.82 59.95 715.27 58.08 C 715.82 58.88 716.39 60.48 715.63 63.43 C 714.88 66.35 711.3 67.17 710.03 67.37 C 709.72 66.27 709.1 63.2 710.63 60.37 C 711.72 58.37 712.01 56.1 711.47 54.23 C 712.5 55.44 713.37 57.36 712.91 60.23 Z M 704.24 59.86 C 702.93 62.18 703.63 65.2 705.83 66.74 C 706.83 67.44 708.05 68.04 709.43 68.54 C 709.5 68.56 709.56 68.57 709.63 68.57 C 709.64 68.57 709.65 68.57 709.66 68.56 L 709.66 68.57 C 709.9 68.55 715.61 68.05 716.73 63.72 C 718.18 58.14 715.29 56.48 715.17 56.41 C 715 56.32 714.8 56.32 714.63 56.41 C 714.46 56.5 714.34 56.66 714.32 56.85 C 714.28 57.24 714.22 57.58 714.14 57.89 C 713.75 53.51 710.27 51.72 709.57 51.41 C 708.85 50.85 707.98 50.47 706.96 50.29 C 706.71 50.25 706.45 50.38 706.35 50.62 C 706.24 50.85 706.3 51.13 706.5 51.3 C 706.58 51.37 708.36 52.91 707.85 55.85 C 707.19 54.98 706.13 53.93 704.89 53.93 C 704.7 53.93 704.53 54.02 704.42 54.18 C 704.31 54.34 704.29 54.53 704.36 54.71 C 704.37 54.74 705.54 57.81 704.41 59.58 C 704.35 59.67 704.29 59.77 704.24 59.86 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 87px; margin-left: 710px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                WAF
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="710" y="99" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    WAF
                </text>
            </switch>
        </g>
        <rect x="690" y="40" width="180" height="200" fill="none" stroke="#ff3333" stroke-dasharray="3 3" pointer-events="all"/>
        <rect x="750" y="80" width="28" height="28" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 771.64 102.05 L 770.14 100.55 L 769.24 101.45 L 770.74 102.95 L 769.24 104.45 L 770.14 105.35 L 771.64 103.85 L 773.13 105.35 L 774.03 104.45 L 772.54 102.95 L 774.03 101.45 L 773.13 100.55 Z M 771.1 94.64 L 769.46 93.48 L 768.72 94.52 L 771.24 96.3 L 774.63 92.91 L 773.73 92.01 Z M 771.1 85.73 L 769.46 84.57 L 768.72 85.61 L 771.24 87.39 L 774.63 84 L 773.73 83.1 Z M 751.27 106.73 L 751.27 99.09 L 776.73 99.09 L 776.73 106.73 Z M 751.27 97.82 L 751.27 90.18 L 776.73 90.18 L 776.73 97.82 Z M 751.27 88.91 L 751.27 81.27 L 776.73 81.27 L 776.73 88.91 Z M 750 80 L 750 88.91 L 750 89.55 L 750 97.82 L 750 98.45 L 750 108 L 778 108 L 778 80 Z" fill="#bf0816" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 115px; margin-left: 764px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                IP制限ルール
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="764" y="127" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    IP制限ルール
                </text>
            </switch>
        </g>
        <rect x="790" y="60" width="70" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 68px; height: 1px; padding-top: 75px; margin-left: 791px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                Count
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="825" y="79" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle" font-weight="bold">
                    Count
                </text>
            </switch>
        </g>
        <rect x="790" y="115" width="70" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 68px; height: 1px; padding-top: 130px; margin-left: 791px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                Block
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="825" y="134" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle" font-weight="bold">
                    Block
                </text>
            </switch>
        </g>
        <rect x="752" y="170" width="28" height="28" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 773.64 192.05 L 772.14 190.55 L 771.24 191.45 L 772.74 192.95 L 771.24 194.45 L 772.14 195.35 L 773.64 193.85 L 775.13 195.35 L 776.03 194.45 L 774.54 192.95 L 776.03 191.45 L 775.13 190.55 Z M 773.1 184.64 L 771.46 183.48 L 770.72 184.52 L 773.24 186.3 L 776.63 182.91 L 775.73 182.01 Z M 773.1 175.73 L 771.46 174.57 L 770.72 175.61 L 773.24 177.39 L 776.63 174 L 775.73 173.1 Z M 753.27 196.73 L 753.27 189.09 L 778.73 189.09 L 778.73 196.73 Z M 753.27 187.82 L 753.27 180.18 L 778.73 180.18 L 778.73 187.82 Z M 753.27 178.91 L 753.27 171.27 L 778.73 171.27 L 778.73 178.91 Z M 752 170 L 752 178.91 L 752 179.55 L 752 187.82 L 752 188.45 L 752 198 L 780 198 L 780 170 Z" fill="#bf0816" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 205px; margin-left: 766px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Basic認証ルール
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="766" y="217" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Basi...
                </text>
            </switch>
        </g>
        <rect x="790" y="152" width="70" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 68px; height: 1px; padding-top: 167px; margin-left: 791px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                Count
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="825" y="171" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle" font-weight="bold">
                    Count
                </text>
            </switch>
        </g>
        <rect x="790" y="210" width="70" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 68px; height: 1px; padding-top: 225px; margin-left: 791px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                Block
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="825" y="229" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle" font-weight="bold">
                    Block
                </text>
            </switch>
        </g>
        <path d="M 820.84 90.55 L 828.34 90.45 L 828.58 107.9 L 836.16 107.79 L 824.99 119.5 L 813.5 108.11 L 821.08 108 Z" fill="#ff3333" stroke="#ff3333" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 820.92 180.58 L 828.42 180.42 L 828.83 199.88 Q 828.83 199.96 828.83 200.04 L 828.83 197.95 L 836.41 197.95 L 825.08 209.5 L 813.75 197.95 L 821.33 197.95 L 821.33 200.04 Z" fill="#ff3333" stroke="#ff3333" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 828.83 197.95 L 836.41 197.95 L 825.08 209.5 L 813.75 197.95 L 821.33 197.95" fill="none" stroke="#ff3333" stroke-linejoin="flat" stroke-miterlimit="4" pointer-events="all"/>
        <rect x="567" y="148" width="113" height="82" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 111px; height: 1px; padding-top: 189px; margin-left: 569px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <div>
                                    ①ListWebACLs
                                </div>
                                <div>
                                    ①GetWebACL
                                    <br/>
                                    <br/>
                                </div>
                                <div>
                                    ②UpdateWebACL
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="569" y="193" fill="#333333" font-family="Helvetica" font-size="12px">
                    ①ListWebACLs...
                </text>
            </switch>
        </g>
        <rect x="460" y="80" width="100" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 95px; margin-left: 461px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                OpenIdConnect
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="510" y="99" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    OpenIdConnect
                </text>
            </switch>
        </g>
        <rect x="460" y="40" width="100" height="200" fill="none" stroke="#ff3333" stroke-dasharray="3 3" pointer-events="all"/>
        <rect x="60" y="150" width="210" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 208px; height: 1px; padding-top: 160px; margin-left: 61px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                メンテナンス画面表示アクション
                                <br/>
                                手動実行
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="165" y="164" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    メンテナンス画面表示アクション
手動実行
                </text>
            </switch>
        </g>
        <rect x="0" y="0" width="240" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 238px; height: 1px; padding-top: 20px; margin-left: 1px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                メンテナンス実行時
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="120" y="25" fill="#000000" font-family="Helvetica" font-size="16px" text-anchor="middle" font-weight="bold">
                    メンテナンス実行時
                </text>
            </switch>
        </g>
        <rect x="0" y="290" width="230" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 228px; height: 1px; padding-top: 310px; margin-left: 1px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                メンテナンス解除時
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="115" y="315" fill="#000000" font-family="Helvetica" font-size="16px" text-anchor="middle" font-weight="bold">
                    メンテナンス解除時
                </text>
            </switch>
        </g>
        <path d="M 38.18 430 L 313.63 430" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 318.88 430 L 311.88 433.5 L 313.63 430 L 311.88 426.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="0" y="410" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 3.66 448.18 C 4.11 439.17 11.27 431.98 20 431.98 C 22.95 431.98 25.85 432.81 28.37 434.38 C 33.05 437.3 36.05 442.53 36.34 448.18 Z M 11.08 420.8 C 11.08 415.85 15.08 411.82 20 411.82 C 24.92 411.82 28.92 415.85 28.92 420.8 C 28.92 425.75 24.92 429.78 20 429.78 C 15.08 429.78 11.08 425.75 11.08 420.8 Z M 29.33 432.84 C 27.75 431.86 26.05 431.15 24.27 430.7 C 28.07 429.04 30.73 425.23 30.73 420.8 C 30.73 414.84 25.92 410 20 410 C 14.08 410 9.27 414.84 9.27 420.8 C 9.27 425.23 11.94 429.05 15.74 430.71 C 7.77 432.71 1.82 440.18 1.82 449.09 C 1.82 449.59 2.22 450 2.73 450 L 37.27 450 C 37.77 450 38.18 449.59 38.18 449.09 C 38.18 442.47 34.79 436.25 29.33 432.84 Z" fill="#232f3d" stroke="none" pointer-events="all"/>
        <path d="M 360 430 L 472.79 430" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 478.04 430 L 471.04 433.5 L 472.79 430 L 471.04 426.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="320" y="410" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 320 430 C 320 418.95 328.95 410 340 410 C 351.05 410 360 418.95 360 430 C 360 441.05 351.05 450 340 450 C 328.95 450 320 441.05 320 430 Z M 321.92 430 C 321.87 438.01 327.12 445.08 334.8 447.36 L 334.8 443.88 C 334.87 442.64 335.53 441.5 336.56 440.8 C 333.52 440.46 330.78 439.12 328.98 437.08 C 327.18 435.04 326.48 432.48 327.04 430 C 327.33 428.28 328.06 426.67 329.16 425.32 C 328.48 423.59 328.53 421.65 329.32 419.96 C 331.35 420.04 333.28 420.82 334.8 422.16 C 338.17 420.99 341.83 420.97 345.2 422.12 C 346.75 420.77 348.71 420.01 350.76 419.96 C 351.56 421.72 351.61 423.72 350.92 425.52 C 351.96 426.82 352.66 428.36 352.96 430 C 353.52 432.47 352.83 435.03 351.03 437.07 C 349.24 439.11 346.51 440.46 343.48 440.8 C 344.46 441.43 345.12 442.45 345.28 443.6 L 345.28 447.44 C 352.97 445.14 358.21 438.03 358.12 430 C 358.12 425.21 356.21 420.61 352.81 417.23 C 349.4 413.85 344.79 411.97 340 412 C 335.21 411.98 330.61 413.87 327.22 417.24 C 323.83 420.62 321.92 425.21 321.92 430 Z" fill="#00bef2" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 457px; margin-left: 340px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                GitHubActions
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="340" y="469" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    GitHub...
                </text>
            </switch>
        </g>
        <path d="M 440 290 L 880 290 L 880 550 L 440 550 Z" fill="none" stroke="#232f3e" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 450.73 296.89 C 448.19 296.86 446.05 299.34 446.28 301.84 C 444.63 302.3 443.58 304.09 443.93 305.76 C 444.17 307.45 445.84 308.8 447.55 308.58 C 451.3 308.56 455.05 308.61 458.79 308.55 C 460.71 308.38 462.05 306.29 461.58 304.45 C 461.34 303.13 460.21 301.98 458.87 301.81 C 458.86 300.3 457.26 298.82 455.79 299.6 C 454.97 300.39 454.82 298.44 454.02 298.13 C 453.14 297.28 451.95 296.82 450.73 296.89 Z M 450.75 297.63 C 452.68 297.51 454.32 299.05 454.91 300.79 C 455.31 301.19 455.67 300.52 455.97 300.34 C 457.25 299.57 458.21 301.17 458.23 302.29 C 458.58 302.77 459.39 302.38 459.77 302.91 C 461.21 303.93 461.3 306.3 459.88 307.37 C 458.9 308.09 457.63 307.75 456.5 307.83 C 453.28 307.82 450.06 307.84 446.84 307.82 C 445.2 307.58 444.19 305.69 444.79 304.17 C 445.07 303.16 446.05 302.67 446.94 302.36 C 447.19 301.62 446.85 300.71 447.33 299.99 C 447.92 298.65 449.25 297.61 450.75 297.63 Z M 440 315 C 440 306.67 440 298.33 440 290 C 448.33 290 456.67 290 465 290 C 465 298.33 465 306.67 465 315 C 456.67 315 448.33 315 440 315 Z" fill="#232f3e" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 408px; height: 1px; padding-top: 297px; margin-left: 472px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                AWS
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="472" y="309" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    AWS
                </text>
            </switch>
        </g>
        <path d="M 460 330 L 500 330 L 500 370 L 460 370 Z" fill="url(#mx-gradient-ff5252-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 486.61 348.36 C 485.99 348.36 485.49 347.86 485.49 347.24 C 485.49 346.62 485.99 346.12 486.61 346.12 C 487.23 346.12 487.74 346.62 487.74 347.24 C 487.74 347.86 487.23 348.36 486.61 348.36 M 481.56 353.96 C 480.94 353.96 480.44 353.46 480.44 352.84 C 480.44 352.22 480.94 351.72 481.56 351.72 C 482.18 351.72 482.68 352.22 482.68 352.84 C 482.68 353.46 482.18 353.96 481.56 353.96 M 477.07 347.24 C 476.45 347.24 475.95 346.74 475.95 346.12 C 475.95 345.5 476.45 345 477.07 345 C 477.69 345 478.19 345.5 478.19 346.12 C 478.19 346.74 477.69 347.24 477.07 347.24 M 471.45 352.84 C 470.84 352.84 470.33 352.34 470.33 351.72 C 470.33 351.1 470.84 350.6 471.45 350.6 C 472.07 350.6 472.58 351.1 472.58 351.72 C 472.58 352.34 472.07 352.84 471.45 352.84 M 486.61 345 C 485.37 345 484.37 346.01 484.37 347.24 C 484.37 347.83 484.6 348.37 484.98 348.77 L 482.94 351.09 C 482.56 350.79 482.08 350.6 481.56 350.6 C 481.33 350.6 481.11 350.65 480.91 350.71 L 478.81 347.52 C 479.12 347.13 479.31 346.65 479.31 346.12 C 479.31 344.89 478.31 343.88 477.07 343.88 C 475.83 343.88 474.82 344.89 474.82 346.12 C 474.82 346.61 474.98 347.06 475.25 347.43 L 472.76 349.9 C 472.39 349.64 471.94 349.48 471.45 349.48 C 470.22 349.48 469.21 350.49 469.21 351.72 C 469.21 352.96 470.22 353.96 471.45 353.96 C 472.69 353.96 473.7 352.96 473.7 351.72 C 473.7 351.38 473.62 351.06 473.48 350.77 L 476.12 348.14 C 476.41 348.28 476.73 348.36 477.07 348.36 C 477.37 348.36 477.65 348.3 477.91 348.19 L 479.95 351.29 C 479.56 351.69 479.31 352.24 479.31 352.84 C 479.31 354.08 480.32 355.08 481.56 355.08 C 482.8 355.08 483.81 354.08 483.81 352.84 C 483.81 352.54 483.74 352.26 483.64 352 L 485.95 349.37 C 486.16 349.44 486.38 349.48 486.61 349.48 C 487.85 349.48 488.86 348.48 488.86 347.24 C 488.86 346.01 487.85 345 486.61 345 M 490.22 356.2 L 485.09 356.2 C 484.97 356.2 484.86 356.24 484.76 356.31 L 475.95 362.72 L 475.95 356.76 C 475.95 356.45 475.69 356.2 475.38 356.2 L 469.05 356.2 C 468.49 356.2 468.04 356.09 467.68 355.87 C 466.51 355.13 465.12 353.72 465.12 351.15 C 465.12 347.73 467.51 346.61 468.93 346.25 C 469.19 346.18 469.37 345.94 469.35 345.67 L 469.3 344.73 C 469.3 342 471.18 339.07 473.58 338.06 C 474.66 337.6 475.66 337.42 476.56 337.42 C 479.14 337.42 480.96 338.89 481.75 339.69 C 482.62 340.56 483.31 341.68 483.78 343.02 C 483.85 343.21 484.02 343.35 484.22 343.38 C 484.43 343.41 484.63 343.33 484.76 343.16 C 485.47 342.2 486.7 341.79 487.82 342.16 C 489.07 342.57 489.79 343.78 489.8 345.5 L 489.8 345.52 C 489.75 345.82 489.96 346.1 490.26 346.16 C 491.34 346.34 494.88 347.26 494.88 351.2 C 494.88 355.79 490.37 356.19 490.22 356.2 M 490.91 345.15 C 490.8 343.13 489.79 341.63 488.17 341.09 C 486.89 340.68 485.51 340.96 484.5 341.81 C 484 340.68 483.35 339.7 482.55 338.9 C 479.98 336.32 476.46 335.62 473.14 337.02 C 470.31 338.22 468.17 341.54 468.17 344.76 L 468.21 345.29 C 466.66 345.79 464 347.23 464 351.15 C 464 354.39 465.93 356.09 467.08 356.82 C 467.62 357.16 468.27 357.32 469.05 357.32 L 474.82 357.32 L 474.82 363.82 C 474.82 364.03 474.94 364.23 475.13 364.32 C 475.21 364.36 475.3 364.38 475.38 364.38 C 475.5 364.38 475.62 364.35 475.71 364.27 L 485.27 357.32 L 490.26 357.32 C 490.32 357.32 496 356.84 496 351.2 C 496 346.86 492.55 345.53 490.91 345.15" fill="#ffffff" stroke="none" pointer-events="all"/>
        <path d="M 520.84 430 L 683.63 430" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 688.88 430 L 681.88 433.5 L 683.63 430 L 681.88 426.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="478.73" y="418" width="42.54" height="24" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 520.32 433.87 C 520.18 433.74 520.02 433.65 519.85 433.58 C 518.86 424.79 511.37 418 502.42 418 C 498.39 418 494.45 419.4 491.34 421.94 C 487.23 425.29 484.87 430.25 484.87 435.55 C 484.87 435.59 484.87 435.63 484.87 435.67 C 484.24 435.99 483.58 436.29 482.91 436.58 C 480.9 437.46 479.44 438.1 479.22 439.25 C 479.17 439.54 479.16 440.11 479.71 440.67 C 480.69 441.65 482.57 442 484.48 442 C 485.28 442 486.08 441.94 486.82 441.84 C 488.08 441.67 490.45 441.2 492.05 440.03 C 494.58 438.19 500.3 437.51 503.28 437.74 C 505.05 437.88 507.2 438.81 508.93 439.56 C 510.25 440.13 511.3 440.58 512.11 440.7 C 513.24 440.85 515.08 440.67 517.02 440.47 L 517.18 440.45 C 517.69 440.39 518.19 440.35 518.69 440.31 C 518.89 440.29 519.09 440.27 519.36 440.25 C 520.19 440.2 520.84 439.5 520.84 438.67 L 520.84 435.05 C 520.84 434.6 520.65 434.17 520.32 433.87 Z M 502.42 419.89 C 510.33 419.89 516.97 425.84 517.95 433.58 C 517.68 433.61 517.39 433.64 517.08 433.66 L 516.91 433.68 C 516.89 432.81 516.18 432.11 515.3 432.11 L 514.63 432.11 C 512.55 425.99 506.53 421.77 499.71 421.77 C 497.18 421.77 494.71 422.35 492.49 423.45 C 492.51 423.44 492.52 423.42 492.53 423.41 C 495.31 421.14 498.82 419.89 502.42 419.89 Z M 512.14 435.29 L 512.14 434 L 515.02 434 L 515.02 435.29 Z M 486.56 439.96 C 484.06 440.31 482.08 440 481.27 439.5 C 481.76 439.15 482.88 438.66 483.67 438.31 C 484.35 438.01 485.04 437.7 485.71 437.36 C 486.8 438.19 488.15 438.82 489.64 439.19 C 488.78 439.54 487.71 439.81 486.56 439.96 Z M 518.95 438.39 C 518.81 438.4 518.66 438.41 518.52 438.42 C 518.02 438.47 517.51 438.51 516.99 438.57 L 516.83 438.59 C 515.16 438.76 513.27 438.95 512.38 438.83 C 511.81 438.75 510.78 438.3 509.68 437.83 C 507.83 437.02 505.51 436.02 503.42 435.86 C 500.55 435.64 495.57 436.18 492.37 437.68 C 490.51 437.6 488.79 437.1 487.46 436.27 C 487.45 436.02 487.45 435.76 487.45 435.66 C 487.45 431.75 489.28 428.09 492.47 425.6 C 494.64 424.33 497.14 423.66 499.71 423.66 C 505.49 423.66 510.62 427.08 512.63 432.11 L 511.86 432.11 C 511.09 432.11 510.44 432.65 510.29 433.38 C 510.16 433.33 510.04 433.28 509.9 433.23 C 507.98 432.49 505.35 431.48 502.31 431.66 C 497.87 431.92 495.68 432.19 493.96 432.68 L 494.47 434.49 C 496.05 434.04 498.13 433.8 502.42 433.54 C 505.05 433.39 507.46 434.32 509.22 434.99 C 509.59 435.14 509.93 435.26 510.25 435.38 L 510.25 435.57 C 510.25 436.46 510.97 437.18 511.86 437.18 L 515.3 437.18 C 516.19 437.18 516.91 436.46 516.91 435.57 L 517.25 435.54 C 517.81 435.49 518.32 435.44 518.76 435.4 L 518.95 435.38 Z" fill="#bf0816" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 449px; margin-left: 500px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                IAMロール
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="500" y="461" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    IAMロール
                </text>
            </switch>
        </g>
        <path d="M 690 330 L 730 330 L 730 370 L 690 370 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 695.37 350.57 L 694 350.57 L 694 349.43 L 695.37 349.43 C 695.49 346.54 696.42 343.79 698.12 341.44 L 699.05 342.11 C 697.49 344.26 696.63 346.78 696.51 349.43 L 698 349.43 L 698 350.57 L 696.51 350.57 C 696.62 353.24 697.48 355.77 699.05 357.93 L 698.12 358.6 C 696.41 356.24 695.48 353.48 695.37 350.57 Z M 718.58 361.9 C 716.23 363.6 713.47 364.54 710.57 364.65 L 710.57 366 L 709.43 366 L 709.43 364.65 C 706.53 364.54 703.77 363.6 701.42 361.9 L 702.09 360.98 C 704.25 362.54 706.77 363.4 709.43 363.51 L 709.43 362 L 710.57 362 L 710.57 363.51 C 713.23 363.4 715.75 362.53 717.91 360.98 Z M 701.42 338.14 C 703.77 336.44 706.53 335.5 709.43 335.39 L 709.43 334 L 710.57 334 L 710.57 335.39 C 713.47 335.5 716.23 336.44 718.58 338.14 L 717.91 339.07 C 715.75 337.51 713.23 336.64 710.57 336.53 L 710.57 338 L 709.43 338 L 709.43 336.53 C 706.77 336.64 704.25 337.51 702.09 339.07 Z M 726 349.43 L 726 350.57 L 724.63 350.57 C 724.52 353.48 723.59 356.24 721.88 358.6 L 720.95 357.93 C 722.52 355.77 723.38 353.24 723.49 350.57 L 722 350.57 L 722 349.43 L 723.49 349.43 C 723.37 346.78 722.51 344.26 720.95 342.11 L 721.88 341.44 C 723.58 343.79 724.51 346.54 724.63 349.43 Z M 718.56 340.66 L 723.04 336.18 L 723.84 336.99 L 719.36 341.47 Z M 701.44 359.39 L 696.96 363.87 L 696.16 363.06 L 700.64 358.58 Z M 702.72 343.52 L 694.24 335.04 L 695.04 334.24 L 703.52 342.72 Z M 717.26 356.45 L 725.76 364.96 L 724.96 365.76 L 716.45 357.26 Z M 705.24 350.42 C 705.28 350.34 705.33 350.27 705.37 350.19 C 706.33 348.68 706.06 346.62 705.75 345.38 C 706.58 345.92 707.32 347.04 707.57 347.51 C 707.68 347.71 707.89 347.83 708.12 347.81 C 708.35 347.79 708.54 347.64 708.62 347.43 C 709.47 345.01 709.03 343.19 708.43 342.01 C 709.16 342.44 709.73 343.05 710.1 343.82 C 710.91 345.51 710.73 347.81 709.63 349.83 C 708.13 352.59 708.43 355.49 708.77 357.05 C 707.91 356.68 707.15 356.26 706.49 355.8 C 704.76 354.6 704.21 352.23 705.24 350.42 Z M 712.91 350.23 C 712.87 350.46 712.98 350.69 713.19 350.81 C 713.39 350.93 713.65 350.91 713.83 350.76 C 713.88 350.73 714.82 349.95 715.27 348.08 C 715.82 348.88 716.39 350.48 715.63 353.43 C 714.88 356.35 711.3 357.17 710.03 357.37 C 709.72 356.27 709.1 353.2 710.63 350.37 C 711.72 348.37 712.01 346.1 711.47 344.23 C 712.5 345.44 713.37 347.36 712.91 350.23 Z M 704.24 349.86 C 702.93 352.18 703.63 355.2 705.83 356.74 C 706.83 357.44 708.05 358.04 709.43 358.54 C 709.5 358.56 709.56 358.57 709.63 358.57 C 709.64 358.57 709.65 358.57 709.66 358.56 L 709.66 358.57 C 709.9 358.55 715.61 358.05 716.73 353.72 C 718.18 348.14 715.29 346.48 715.17 346.41 C 715 346.32 714.8 346.32 714.63 346.41 C 714.46 346.5 714.34 346.66 714.32 346.85 C 714.28 347.24 714.22 347.58 714.14 347.89 C 713.75 343.51 710.27 341.72 709.57 341.41 C 708.85 340.85 707.98 340.47 706.96 340.29 C 706.71 340.25 706.45 340.38 706.35 340.62 C 706.24 340.85 706.3 341.13 706.5 341.3 C 706.58 341.37 708.36 342.91 707.85 345.85 C 707.19 344.98 706.13 343.93 704.89 343.93 C 704.7 343.93 704.53 344.02 704.42 344.18 C 704.31 344.34 704.29 344.53 704.36 344.71 C 704.37 344.74 705.54 347.81 704.41 349.58 C 704.35 349.67 704.29 349.77 704.24 349.86 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 377px; margin-left: 710px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                WAF
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="710" y="389" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    WAF
                </text>
            </switch>
        </g>
        <rect x="690" y="330" width="180" height="200" fill="none" stroke="#ff3333" stroke-dasharray="3 3" pointer-events="all"/>
        <rect x="750" y="370" width="28" height="28" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 771.64 392.05 L 770.14 390.55 L 769.24 391.45 L 770.74 392.95 L 769.24 394.45 L 770.14 395.35 L 771.64 393.85 L 773.13 395.35 L 774.03 394.45 L 772.54 392.95 L 774.03 391.45 L 773.13 390.55 Z M 771.1 384.64 L 769.46 383.48 L 768.72 384.52 L 771.24 386.3 L 774.63 382.91 L 773.73 382.01 Z M 771.1 375.73 L 769.46 374.57 L 768.72 375.61 L 771.24 377.39 L 774.63 374 L 773.73 373.1 Z M 751.27 396.73 L 751.27 389.09 L 776.73 389.09 L 776.73 396.73 Z M 751.27 387.82 L 751.27 380.18 L 776.73 380.18 L 776.73 387.82 Z M 751.27 378.91 L 751.27 371.27 L 776.73 371.27 L 776.73 378.91 Z M 750 370 L 750 378.91 L 750 379.55 L 750 387.82 L 750 388.45 L 750 398 L 778 398 L 778 370 Z" fill="#bf0816" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 405px; margin-left: 764px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                IP制限ルール
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="764" y="417" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    IP制限ルール
                </text>
            </switch>
        </g>
        <rect x="790" y="350" width="70" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 68px; height: 1px; padding-top: 365px; margin-left: 791px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                Count
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="825" y="369" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle" font-weight="bold">
                    Count
                </text>
            </switch>
        </g>
        <rect x="790" y="405" width="70" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 68px; height: 1px; padding-top: 420px; margin-left: 791px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                Block
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="825" y="424" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle" font-weight="bold">
                    Block
                </text>
            </switch>
        </g>
        <rect x="752" y="460" width="28" height="28" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 773.64 482.05 L 772.14 480.55 L 771.24 481.45 L 772.74 482.95 L 771.24 484.45 L 772.14 485.35 L 773.64 483.85 L 775.13 485.35 L 776.03 484.45 L 774.54 482.95 L 776.03 481.45 L 775.13 480.55 Z M 773.1 474.64 L 771.46 473.48 L 770.72 474.52 L 773.24 476.3 L 776.63 472.91 L 775.73 472.01 Z M 773.1 465.73 L 771.46 464.57 L 770.72 465.61 L 773.24 467.39 L 776.63 464 L 775.73 463.1 Z M 753.27 486.73 L 753.27 479.09 L 778.73 479.09 L 778.73 486.73 Z M 753.27 477.82 L 753.27 470.18 L 778.73 470.18 L 778.73 477.82 Z M 753.27 468.91 L 753.27 461.27 L 778.73 461.27 L 778.73 468.91 Z M 752 460 L 752 468.91 L 752 469.55 L 752 477.82 L 752 478.45 L 752 488 L 780 488 L 780 460 Z" fill="#bf0816" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 495px; margin-left: 766px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Basic認証ルール
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="766" y="507" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Basi...
                </text>
            </switch>
        </g>
        <rect x="790" y="442" width="70" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 68px; height: 1px; padding-top: 457px; margin-left: 791px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                Count
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="825" y="461" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle" font-weight="bold">
                    Count
                </text>
            </switch>
        </g>
        <rect x="790" y="500" width="70" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 68px; height: 1px; padding-top: 515px; margin-left: 791px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                Block
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="825" y="519" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle" font-weight="bold">
                    Block
                </text>
            </switch>
        </g>
        <rect x="570" y="435" width="110" height="82" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 108px; height: 1px; padding-top: 476px; margin-left: 572px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <div>
                                    ①ListWebACLs
                                </div>
                                <div>
                                    ①GetWebACL
                                    <br/>
                                    <br/>
                                </div>
                                <div>
                                    ②UpdateWebACL
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="572" y="480" fill="#333333" font-family="Helvetica" font-size="12px">
                    ①ListWebACLs...
                </text>
            </switch>
        </g>
        <rect x="460" y="370" width="100" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 385px; margin-left: 461px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                OpenIdConnect
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="510" y="389" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    OpenIdConnect
                </text>
            </switch>
        </g>
        <rect x="460" y="330" width="100" height="200" fill="none" stroke="#ff3333" stroke-dasharray="3 3" pointer-events="all"/>
        <path d="M 828.51 404.5 L 821.01 404.5 L 821.01 387.05 L 813.43 387.05 L 824.76 375.5 L 836.09 387.05 L 828.51 387.05 Z" fill="#ff3333" stroke="#ff3333" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 828.51 499.5 L 821.01 499.5 L 821.01 482.05 L 813.43 482.05 L 824.76 470.5 L 836.09 482.05 L 828.51 482.05 Z" fill="#ff3333" stroke="#ff3333" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="64" y="440" width="190" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 188px; height: 1px; padding-top: 450px; margin-left: 65px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                通常画面表示アクション
                                <br/>
                                手動実行
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="159" y="454" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    通常画面表示アクション
手動実行
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>