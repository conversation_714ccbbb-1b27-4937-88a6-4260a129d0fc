<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" style="background: transparent; background-color: transparent;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1101px" height="471px" viewBox="-0.5 -0.5 1101 471" content="&lt;mxfile&gt;&lt;diagram id=&quot;lmR-1-miq4CBir2RgOSI&quot; name=&quot;ページ1&quot;&gt;7Vxdj+MmFP01eY2CMTZ+nGRmtpVaadSRtt2nlccmibXERDbZJP31hRgntmE6aeMJCvF8wjXYcC5czoXrjOBstftSxOvl7ywldORN0t0IPo48D08i8VcK9pUgQEqwKLK0EoGT4DX7myjhREk3WUrKVkHOGOXZui1MWJ6ThLdkcVGwbbvYnNH2U9fxgmiC1ySmuvTPLOVL1S0vPMl/IdliqZ6MMKwurOK6rOpIuYxTtm2I4NMIzgrGeJVa7WaESuhqWKp6z+9cPbarIDk/p0LkVTV+xnSj+qYaxvd1Zwu2yVMiK0xGcLpdZpy8ruNEXt0K7QrZkq+oyAGRVLcjBSe7d9sEjj0VA4SwFeHFXhRRFUKFzb4eLFV2ewIagIkSLhso+3XFWGl3cbz1CQGRUCCYAdHx+AouQ2SeUTpjlBWHujCNCZ4nQl7ygv0gjStBgsnb/IihBpgB1ncxPAKkQPR8HUVswrAHCKEbEB7xsQCh7waEILIHIf7YsAnju5bJOSW7B7kqiF6TPFXJx4TGZZklJvtmQIWkrSVDx6TRZ2Tocy0rCI159rO90JiAUE94YZloyRFy2IEcedE4CNt3KdmmSIiq2FwcOvfyg4/vxeNiQbh2r4N2jp0/z/L6moIkpq8qywq+ZAuWx/TpJG1qRiik2P8lZ8MY1dlvanIcMo+7Vm5f53YZb1QTuW/1HUX6VElmmnVeSJGJ7pFCydrT8d1xUoHfpjMViK0F6MPhdPY4OXvlQ3bw7wk33xpuwYW49TT+LOMPbeFfN+ZfLf0PwpOl6uJaGqvDA9FU/Ijqs+oXiaIzKRnLuaAJTbJQFwK9mPgHTE/oCk2yUBcCvZjM1a1uC02yEOkt7tYGhtqgU1v8wCnbcJrlgkLUrpbEeM5y3iAV4vtZaq9LQ/DMf35+NtGQ+eFLXEnjcnkcnZJ6ZMIN+y1+I/SFlRnPWC6uvTHO2apR4IFmC3mBM8mDYpVLxKCW86VFjERLlYMJvDqvRo58ZFyuq27Ns51sx7RmDavdQvq243hb+uOCVPPj10S2ZyqyVapdSgz9UjTvO2Vx+v0tpnGeZPmiJ7o6CceotVwDqLMtHxnYFurBCIZuMFbfImM1UNavF3rjXQwRwalvwhB7bzAI+sEwCC1iGLkxDpFF5xOes6A66DqFXczR/3edMPr4Xv25TlBXkOuuE0Q6Ba0XoetzUAjtKKAv4CJrwF3q9N+e82RUALamAHSGsR+8p8F7ctt7QpFN7wk64j1h7HVQvCpvdcR/ApPAKowGF+oWYYxCmyj690fJfQOxgdYouX87lNwInDVK7t8fJTcqwBol9wdKPlDygZJHna34axJyFN6dDQygbgPr+I/r20CEBwVImWdNAdGgACk794iidwXUc+/W9wT8zmkg8gxm3OCEeT04YbX2bh1DZBNDzxEMPYsY6iHRA6MfGP29MXp9k90H1+P0gR5Ur09CB8MauhHhl4Q1dCPCPzesIdA3Qm5yJzqAFpeewA0MQ5sYGo7nbhHDAFvE8E7fx+kGlV3yPk43qOxz38cJHSH+WHuL6XqjPnTkXcjIJoaOvAwZAYsYOrWbl2yKn/9tay80HPCFwZkLRe9be/WSMGijoQ1rx61YNyaDNqyFbYS6z6epZ9gqa8mGrTL3tsq6h9/X3CgLHdkyABOvGwF4Vc7lyK4BAHZhdCaqV4tHvSaMWMfshlnOWcQGY53YYGSNZl4aj+qIAqwxS3x/ca1GBdhzew3HObe4kWNxFx0bqNktQghsYujIy1bA4okYPuc0Z/DSBy/dbS9d/8yda/rp9RZBYxL+wSiVvXuCI8E3haWrElGdeHg8JLzRg06GRJ95W021CimZc4OeV1maHkiSyTq27WdT4bCjcND/kmz6yFjPYAphD6Yw0mNMpyL1pSAkv3c9wODzFCGyp49Jro63Tx81DZ/+AQ==&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <g>
            <rect x="0" y="0" width="1100" height="470" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="30" y="160" width="80" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 180px; margin-left: 31px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V1
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="70" y="184" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V1
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="210" y="160" width="80" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 180px; margin-left: 211px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V1
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="250" y="184" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V1
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="120" y="160" width="80" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 180px; margin-left: 121px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V1
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="160" y="184" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V1
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 320.5 454.67 L 320.5 444.67 L 370.5 444.67 L 370.5 434.17 L 389.5 449.67 L 370.5 465.17 L 370.5 454.67 Z" fill="none" stroke="#000000" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 160 95 L 160 127.5 L 70 127.5 L 70 153.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 70 158.88 L 66.5 151.88 L 70 153.63 L 73.5 151.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 160 95 L 160 153.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 160 158.88 L 156.5 151.88 L 160 153.63 L 163.5 151.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 160 95 L 160 127.5 L 250 127.5 L 250 153.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 250 158.88 L 246.5 151.88 L 250 153.63 L 253.5 151.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 137.5 50 L 182.5 50 L 182.5 95 L 137.5 95 Z" fill="#8c4fff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));"/>
            <path d="M 152.8 81.86 C 147.64 81.86 143.44 77.66 143.44 72.5 C 143.44 67.34 147.64 63.14 152.8 63.14 C 157.96 63.14 162.16 67.34 162.16 72.5 C 162.16 77.66 157.96 81.86 152.8 81.86 M 174.4 86.18 C 174.4 87.77 173.11 89.06 171.52 89.06 C 169.93 89.06 168.64 87.77 168.64 86.18 C 168.64 84.59 169.93 83.3 171.52 83.3 C 173.11 83.3 174.4 84.59 174.4 86.18 M 171.52 55.94 C 173.11 55.94 174.4 57.23 174.4 58.82 C 174.4 60.41 173.11 61.7 171.52 61.7 C 169.93 61.7 168.64 60.41 168.64 58.82 C 168.64 57.23 169.93 55.94 171.52 55.94 M 173.68 69.62 C 175.27 69.62 176.56 70.91 176.56 72.5 C 176.56 74.09 175.27 75.38 173.68 75.38 C 172.09 75.38 170.8 74.09 170.8 72.5 C 170.8 70.91 172.09 69.62 173.68 69.62 M 163.56 73.22 L 169.42 73.22 C 169.77 75.26 171.54 76.82 173.68 76.82 C 176.06 76.82 178 74.88 178 72.5 C 178 70.12 176.06 68.18 173.68 68.18 C 171.54 68.18 169.77 69.74 169.42 71.78 L 163.56 71.78 C 163.47 70.41 163.13 69.11 162.57 67.93 L 168.96 62.29 C 169.68 62.82 170.56 63.14 171.52 63.14 C 173.9 63.14 175.84 61.2 175.84 58.82 C 175.84 56.44 173.9 54.5 171.52 54.5 C 169.14 54.5 167.2 56.44 167.2 58.82 C 167.2 59.72 167.48 60.56 167.96 61.26 L 161.86 66.64 C 159.93 63.67 156.6 61.7 152.8 61.7 C 146.84 61.7 142 66.54 142 72.5 C 142 78.46 146.84 83.3 152.8 83.3 C 156.6 83.3 159.93 81.33 161.86 78.36 L 167.96 83.74 C 167.48 84.44 167.2 85.28 167.2 86.18 C 167.2 88.56 169.14 90.5 171.52 90.5 C 173.9 90.5 175.84 88.56 175.84 86.18 C 175.84 83.8 173.9 81.86 171.52 81.86 C 170.56 81.86 169.68 82.18 168.96 82.71 L 162.57 77.07 C 163.13 75.89 163.47 74.59 163.56 73.22" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <rect x="420" y="160" width="80" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 180px; margin-left: 421px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V1
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="460" y="184" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V1
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="600" y="160" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="all" style="fill: light-dark(rgb(213, 232, 212), rgb(31, 47, 30)); stroke: light-dark(rgb(130, 179, 102), rgb(68, 110, 44));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 180px; margin-left: 601px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V2
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="640" y="184" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V2
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="510" y="160" width="80" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 180px; margin-left: 511px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V1
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="550" y="184" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V1
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 710.5 184.67 L 710.5 174.67 L 760.5 174.67 L 760.5 164.17 L 779.5 179.67 L 760.5 195.17 L 760.5 184.67 Z" fill="none" stroke="#000000" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 550 95 L 550 127.5 L 460 127.5 L 460 153.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 460 158.88 L 456.5 151.88 L 460 153.63 L 463.5 151.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 550 95 L 550 153.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 550 158.88 L 546.5 151.88 L 550 153.63 L 553.5 151.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 550 95 L 550 127.5 L 640 127.5 L 640 153.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 640 158.88 L 636.5 151.88 L 640 153.63 L 643.5 151.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 527.5 50 L 572.5 50 L 572.5 95 L 527.5 95 Z" fill="#8c4fff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));"/>
            <path d="M 542.8 81.86 C 537.64 81.86 533.44 77.66 533.44 72.5 C 533.44 67.34 537.64 63.14 542.8 63.14 C 547.96 63.14 552.16 67.34 552.16 72.5 C 552.16 77.66 547.96 81.86 542.8 81.86 M 564.4 86.18 C 564.4 87.77 563.11 89.06 561.52 89.06 C 559.93 89.06 558.64 87.77 558.64 86.18 C 558.64 84.59 559.93 83.3 561.52 83.3 C 563.11 83.3 564.4 84.59 564.4 86.18 M 561.52 55.94 C 563.11 55.94 564.4 57.23 564.4 58.82 C 564.4 60.41 563.11 61.7 561.52 61.7 C 559.93 61.7 558.64 60.41 558.64 58.82 C 558.64 57.23 559.93 55.94 561.52 55.94 M 563.68 69.62 C 565.27 69.62 566.56 70.91 566.56 72.5 C 566.56 74.09 565.27 75.38 563.68 75.38 C 562.09 75.38 560.8 74.09 560.8 72.5 C 560.8 70.91 562.09 69.62 563.68 69.62 M 553.56 73.22 L 559.42 73.22 C 559.77 75.26 561.54 76.82 563.68 76.82 C 566.06 76.82 568 74.88 568 72.5 C 568 70.12 566.06 68.18 563.68 68.18 C 561.54 68.18 559.77 69.74 559.42 71.78 L 553.56 71.78 C 553.47 70.41 553.13 69.11 552.57 67.93 L 558.96 62.29 C 559.68 62.82 560.56 63.14 561.52 63.14 C 563.9 63.14 565.84 61.2 565.84 58.82 C 565.84 56.44 563.9 54.5 561.52 54.5 C 559.14 54.5 557.2 56.44 557.2 58.82 C 557.2 59.72 557.48 60.56 557.96 61.26 L 551.86 66.64 C 549.93 63.67 546.6 61.7 542.8 61.7 C 536.84 61.7 532 66.54 532 72.5 C 532 78.46 536.84 83.3 542.8 83.3 C 546.6 83.3 549.93 81.33 551.86 78.36 L 557.96 83.74 C 557.48 84.44 557.2 85.28 557.2 86.18 C 557.2 88.56 559.14 90.5 561.52 90.5 C 563.9 90.5 565.84 88.56 565.84 86.18 C 565.84 83.8 563.9 81.86 561.52 81.86 C 560.56 81.86 559.68 82.18 558.96 82.71 L 552.57 77.07 C 553.13 75.89 553.47 74.59 553.56 73.22" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <rect x="812.5" y="160" width="80" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 180px; margin-left: 814px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V1
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="853" y="184" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V1
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="992.5" y="160" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="all" style="fill: light-dark(rgb(213, 232, 212), rgb(31, 47, 30)); stroke: light-dark(rgb(130, 179, 102), rgb(68, 110, 44));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 180px; margin-left: 994px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V2
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1033" y="184" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V2
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="902.5" y="160" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="all" style="fill: light-dark(rgb(213, 232, 212), rgb(31, 47, 30)); stroke: light-dark(rgb(130, 179, 102), rgb(68, 110, 44));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 180px; margin-left: 904px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V2
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="943" y="184" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V2
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 942.5 95 L 942.5 127.5 L 852.5 127.5 L 852.5 153.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 852.5 158.88 L 849 151.88 L 852.5 153.63 L 856 151.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 942.5 95 L 942.5 153.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 942.5 158.88 L 939 151.88 L 942.5 153.63 L 946 151.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 942.5 95 L 942.5 127.5 L 1032.5 127.5 L 1032.5 153.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1032.5 158.88 L 1029 151.88 L 1032.5 153.63 L 1036 151.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 920 50 L 965 50 L 965 95 L 920 95 Z" fill="#8c4fff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));"/>
            <path d="M 935.3 81.86 C 930.14 81.86 925.94 77.66 925.94 72.5 C 925.94 67.34 930.14 63.14 935.3 63.14 C 940.46 63.14 944.66 67.34 944.66 72.5 C 944.66 77.66 940.46 81.86 935.3 81.86 M 956.9 86.18 C 956.9 87.77 955.61 89.06 954.02 89.06 C 952.43 89.06 951.14 87.77 951.14 86.18 C 951.14 84.59 952.43 83.3 954.02 83.3 C 955.61 83.3 956.9 84.59 956.9 86.18 M 954.02 55.94 C 955.61 55.94 956.9 57.23 956.9 58.82 C 956.9 60.41 955.61 61.7 954.02 61.7 C 952.43 61.7 951.14 60.41 951.14 58.82 C 951.14 57.23 952.43 55.94 954.02 55.94 M 956.18 69.62 C 957.77 69.62 959.06 70.91 959.06 72.5 C 959.06 74.09 957.77 75.38 956.18 75.38 C 954.59 75.38 953.3 74.09 953.3 72.5 C 953.3 70.91 954.59 69.62 956.18 69.62 M 946.06 73.22 L 951.92 73.22 C 952.27 75.26 954.04 76.82 956.18 76.82 C 958.56 76.82 960.5 74.88 960.5 72.5 C 960.5 70.12 958.56 68.18 956.18 68.18 C 954.04 68.18 952.27 69.74 951.92 71.78 L 946.06 71.78 C 945.97 70.41 945.63 69.11 945.07 67.93 L 951.46 62.29 C 952.18 62.82 953.06 63.14 954.02 63.14 C 956.4 63.14 958.34 61.2 958.34 58.82 C 958.34 56.44 956.4 54.5 954.02 54.5 C 951.64 54.5 949.7 56.44 949.7 58.82 C 949.7 59.72 949.98 60.56 950.46 61.26 L 944.36 66.64 C 942.43 63.67 939.1 61.7 935.3 61.7 C 929.34 61.7 924.5 66.54 924.5 72.5 C 924.5 78.46 929.34 83.3 935.3 83.3 C 939.1 83.3 942.43 81.33 944.36 78.36 L 950.46 83.74 C 949.98 84.44 949.7 85.28 949.7 86.18 C 949.7 88.56 951.64 90.5 954.02 90.5 C 956.4 90.5 958.34 88.56 958.34 86.18 C 958.34 83.8 956.4 81.86 954.02 81.86 C 953.06 81.86 952.18 82.18 951.46 82.71 L 945.07 77.07 C 945.63 75.89 945.97 74.59 946.06 73.22" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <path d="M 550 375 L 550 407.5 L 420 407.5 L 420 433.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 420 438.88 L 416.5 431.88 L 420 433.63 L 423.5 431.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 550 375 L 550 407.5 L 470 407.5 L 470 433.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 470 438.88 L 466.5 431.88 L 470 433.63 L 473.5 431.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 550 375 L 550 407.5 L 520 407.5 L 520 433.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 520 438.88 L 516.5 431.88 L 520 433.63 L 523.5 431.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="400" y="440" width="40" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 450px; margin-left: 401px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V1
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="420" y="454" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V1
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="500" y="440" width="40" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 450px; margin-left: 501px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V1
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="520" y="454" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V1
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="450" y="440" width="40" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 450px; margin-left: 451px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V1
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="470" y="454" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V1
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 527.5 330 L 572.5 330 L 572.5 375 L 527.5 375 Z" fill="#8c4fff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));"/>
            <path d="M 542.8 361.86 C 537.64 361.86 533.44 357.66 533.44 352.5 C 533.44 347.34 537.64 343.14 542.8 343.14 C 547.96 343.14 552.16 347.34 552.16 352.5 C 552.16 357.66 547.96 361.86 542.8 361.86 M 564.4 366.18 C 564.4 367.77 563.11 369.06 561.52 369.06 C 559.93 369.06 558.64 367.77 558.64 366.18 C 558.64 364.59 559.93 363.3 561.52 363.3 C 563.11 363.3 564.4 364.59 564.4 366.18 M 561.52 335.94 C 563.11 335.94 564.4 337.23 564.4 338.82 C 564.4 340.41 563.11 341.7 561.52 341.7 C 559.93 341.7 558.64 340.41 558.64 338.82 C 558.64 337.23 559.93 335.94 561.52 335.94 M 563.68 349.62 C 565.27 349.62 566.56 350.91 566.56 352.5 C 566.56 354.09 565.27 355.38 563.68 355.38 C 562.09 355.38 560.8 354.09 560.8 352.5 C 560.8 350.91 562.09 349.62 563.68 349.62 M 553.56 353.22 L 559.42 353.22 C 559.77 355.26 561.54 356.82 563.68 356.82 C 566.06 356.82 568 354.88 568 352.5 C 568 350.12 566.06 348.18 563.68 348.18 C 561.54 348.18 559.77 349.74 559.42 351.78 L 553.56 351.78 C 553.47 350.41 553.13 349.11 552.57 347.93 L 558.96 342.29 C 559.68 342.82 560.56 343.14 561.52 343.14 C 563.9 343.14 565.84 341.2 565.84 338.82 C 565.84 336.44 563.9 334.5 561.52 334.5 C 559.14 334.5 557.2 336.44 557.2 338.82 C 557.2 339.72 557.48 340.56 557.96 341.26 L 551.86 346.64 C 549.93 343.67 546.6 341.7 542.8 341.7 C 536.84 341.7 532 346.54 532 352.5 C 532 358.46 536.84 363.3 542.8 363.3 C 546.6 363.3 549.93 361.33 551.86 358.36 L 557.96 363.74 C 557.48 364.44 557.2 365.28 557.2 366.18 C 557.2 368.56 559.14 370.5 561.52 370.5 C 563.9 370.5 565.84 368.56 565.84 366.18 C 565.84 363.8 563.9 361.86 561.52 361.86 C 560.56 361.86 559.68 362.18 558.96 362.71 L 552.57 357.07 C 553.13 355.89 553.47 354.59 553.56 353.22" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <path d="M 320.5 184.67 L 320.5 174.67 L 370.5 174.67 L 370.5 164.17 L 389.5 179.67 L 370.5 195.17 L 370.5 184.67 Z" fill="none" stroke="#000000" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="560" y="440" width="40" height="20" fill="#d5e8d4" stroke="#82b366" pointer-events="all" style="fill: light-dark(rgb(213, 232, 212), rgb(31, 47, 30)); stroke: light-dark(rgb(130, 179, 102), rgb(68, 110, 44));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 450px; margin-left: 561px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V2
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="580" y="454" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V2
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="660" y="440" width="40" height="20" fill="#d5e8d4" stroke="#82b366" pointer-events="all" style="fill: light-dark(rgb(213, 232, 212), rgb(31, 47, 30)); stroke: light-dark(rgb(130, 179, 102), rgb(68, 110, 44));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 450px; margin-left: 661px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V2
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="680" y="454" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V2
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="610" y="440" width="40" height="20" fill="#d5e8d4" stroke="#82b366" pointer-events="all" style="fill: light-dark(rgb(213, 232, 212), rgb(31, 47, 30)); stroke: light-dark(rgb(130, 179, 102), rgb(68, 110, 44));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 450px; margin-left: 611px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V2
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="630" y="454" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V2
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 710.5 454.67 L 710.5 444.67 L 760.5 444.67 L 760.5 434.17 L 779.5 449.67 L 760.5 465.17 L 760.5 454.67 Z" fill="none" stroke="#000000" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="790" y="440" width="40" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 450px; margin-left: 791px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V1
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="810" y="454" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V1
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="890" y="440" width="40" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 450px; margin-left: 891px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V1
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="910" y="454" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V1
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="840" y="440" width="40" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 450px; margin-left: 841px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V1
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="860" y="454" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V1
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 942.5 375 L 942.5 407.5 L 972.5 407.5 L 972.5 433.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 972.5 438.88 L 969 431.88 L 972.5 433.63 L 976 431.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 942.5 375 L 942.5 407.5 L 1022.5 407.5 L 1022.5 433.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1022.5 438.88 L 1019 431.88 L 1022.5 433.63 L 1026 431.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 942.5 375 L 942.5 407.5 L 1072.5 407.5 L 1072.5 433.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1072.5 438.88 L 1069 431.88 L 1072.5 433.63 L 1076 431.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 920 330 L 965 330 L 965 375 L 920 375 Z" fill="#8c4fff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));"/>
            <path d="M 935.3 361.86 C 930.14 361.86 925.94 357.66 925.94 352.5 C 925.94 347.34 930.14 343.14 935.3 343.14 C 940.46 343.14 944.66 347.34 944.66 352.5 C 944.66 357.66 940.46 361.86 935.3 361.86 M 956.9 366.18 C 956.9 367.77 955.61 369.06 954.02 369.06 C 952.43 369.06 951.14 367.77 951.14 366.18 C 951.14 364.59 952.43 363.3 954.02 363.3 C 955.61 363.3 956.9 364.59 956.9 366.18 M 954.02 335.94 C 955.61 335.94 956.9 337.23 956.9 338.82 C 956.9 340.41 955.61 341.7 954.02 341.7 C 952.43 341.7 951.14 340.41 951.14 338.82 C 951.14 337.23 952.43 335.94 954.02 335.94 M 956.18 349.62 C 957.77 349.62 959.06 350.91 959.06 352.5 C 959.06 354.09 957.77 355.38 956.18 355.38 C 954.59 355.38 953.3 354.09 953.3 352.5 C 953.3 350.91 954.59 349.62 956.18 349.62 M 946.06 353.22 L 951.92 353.22 C 952.27 355.26 954.04 356.82 956.18 356.82 C 958.56 356.82 960.5 354.88 960.5 352.5 C 960.5 350.12 958.56 348.18 956.18 348.18 C 954.04 348.18 952.27 349.74 951.92 351.78 L 946.06 351.78 C 945.97 350.41 945.63 349.11 945.07 347.93 L 951.46 342.29 C 952.18 342.82 953.06 343.14 954.02 343.14 C 956.4 343.14 958.34 341.2 958.34 338.82 C 958.34 336.44 956.4 334.5 954.02 334.5 C 951.64 334.5 949.7 336.44 949.7 338.82 C 949.7 339.72 949.98 340.56 950.46 341.26 L 944.36 346.64 C 942.43 343.67 939.1 341.7 935.3 341.7 C 929.34 341.7 924.5 346.54 924.5 352.5 C 924.5 358.46 929.34 363.3 935.3 363.3 C 939.1 363.3 942.43 361.33 944.36 358.36 L 950.46 363.74 C 949.98 364.44 949.7 365.28 949.7 366.18 C 949.7 368.56 951.64 370.5 954.02 370.5 C 956.4 370.5 958.34 368.56 958.34 366.18 C 958.34 363.8 956.4 361.86 954.02 361.86 C 953.06 361.86 952.18 362.18 951.46 362.71 L 945.07 357.07 C 945.63 355.89 945.97 354.59 946.06 353.22" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <rect x="952.5" y="440" width="40" height="20" fill="#d5e8d4" stroke="#82b366" pointer-events="all" style="fill: light-dark(rgb(213, 232, 212), rgb(31, 47, 30)); stroke: light-dark(rgb(130, 179, 102), rgb(68, 110, 44));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 450px; margin-left: 954px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V2
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="973" y="454" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V2
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1052.5" y="440" width="40" height="20" fill="#d5e8d4" stroke="#82b366" pointer-events="all" style="fill: light-dark(rgb(213, 232, 212), rgb(31, 47, 30)); stroke: light-dark(rgb(130, 179, 102), rgb(68, 110, 44));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 450px; margin-left: 1054px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V2
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1073" y="454" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V2
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1002.5" y="440" width="40" height="20" fill="#d5e8d4" stroke="#82b366" pointer-events="all" style="fill: light-dark(rgb(213, 232, 212), rgb(31, 47, 30)); stroke: light-dark(rgb(130, 179, 102), rgb(68, 110, 44));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 450px; margin-left: 1004px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V2
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1023" y="454" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V2
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 160 375 L 160 407.5 L 30 407.5 L 30 433.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 30 438.88 L 26.5 431.88 L 30 433.63 L 33.5 431.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 160 375 L 160 407.5 L 80 407.5 L 80 433.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 80 438.88 L 76.5 431.88 L 80 433.63 L 83.5 431.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 160 375 L 160 407.5 L 130 407.5 L 130 433.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 130 438.88 L 126.5 431.88 L 130 433.63 L 133.5 431.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="10" y="440" width="40" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 450px; margin-left: 11px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V1
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="30" y="454" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V1
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="110" y="440" width="40" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 450px; margin-left: 111px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V1
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="130" y="454" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V1
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="60" y="440" width="40" height="20" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 450px; margin-left: 61px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    V1
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="80" y="454" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        V1
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 137.5 330 L 182.5 330 L 182.5 375 L 137.5 375 Z" fill="#8c4fff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));"/>
            <path d="M 152.8 361.86 C 147.64 361.86 143.44 357.66 143.44 352.5 C 143.44 347.34 147.64 343.14 152.8 343.14 C 157.96 343.14 162.16 347.34 162.16 352.5 C 162.16 357.66 157.96 361.86 152.8 361.86 M 174.4 366.18 C 174.4 367.77 173.11 369.06 171.52 369.06 C 169.93 369.06 168.64 367.77 168.64 366.18 C 168.64 364.59 169.93 363.3 171.52 363.3 C 173.11 363.3 174.4 364.59 174.4 366.18 M 171.52 335.94 C 173.11 335.94 174.4 337.23 174.4 338.82 C 174.4 340.41 173.11 341.7 171.52 341.7 C 169.93 341.7 168.64 340.41 168.64 338.82 C 168.64 337.23 169.93 335.94 171.52 335.94 M 173.68 349.62 C 175.27 349.62 176.56 350.91 176.56 352.5 C 176.56 354.09 175.27 355.38 173.68 355.38 C 172.09 355.38 170.8 354.09 170.8 352.5 C 170.8 350.91 172.09 349.62 173.68 349.62 M 163.56 353.22 L 169.42 353.22 C 169.77 355.26 171.54 356.82 173.68 356.82 C 176.06 356.82 178 354.88 178 352.5 C 178 350.12 176.06 348.18 173.68 348.18 C 171.54 348.18 169.77 349.74 169.42 351.78 L 163.56 351.78 C 163.47 350.41 163.13 349.11 162.57 347.93 L 168.96 342.29 C 169.68 342.82 170.56 343.14 171.52 343.14 C 173.9 343.14 175.84 341.2 175.84 338.82 C 175.84 336.44 173.9 334.5 171.52 334.5 C 169.14 334.5 167.2 336.44 167.2 338.82 C 167.2 339.72 167.48 340.56 167.96 341.26 L 161.86 346.64 C 159.93 343.67 156.6 341.7 152.8 341.7 C 146.84 341.7 142 346.54 142 352.5 C 142 358.46 146.84 363.3 152.8 363.3 C 156.6 363.3 159.93 361.33 161.86 358.36 L 167.96 363.74 C 167.48 364.44 167.2 365.28 167.2 366.18 C 167.2 368.56 169.14 370.5 171.52 370.5 C 173.9 370.5 175.84 368.56 175.84 366.18 C 175.84 363.8 173.9 361.86 171.52 361.86 C 170.56 361.86 169.68 362.18 168.96 362.71 L 162.57 357.07 C 163.13 355.89 163.47 354.59 163.56 353.22" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <rect x="10" y="0" width="120" height="30" fill="none" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 15px; margin-left: 12px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 13px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">
                                    Rollingデプロイ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="12" y="19" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="13px" font-weight="bold">
                        Rollingデプロイ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="10" y="280" width="120" height="30" fill="none" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 295px; margin-left: 12px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 13px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">
                                    BlueGreenデプロイ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="12" y="299" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="13px" font-weight="bold">
                        BlueGreenデプロイ
                    </text>
                </switch>
            </g>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>