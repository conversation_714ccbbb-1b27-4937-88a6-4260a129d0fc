<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1701px" height="641px" viewBox="-0.5 -0.5 1701 641" content="&lt;mxfile&gt;&lt;diagram id=&quot;rxyWzuQO3aeQyH_nDuKp&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-4d72f3-1-3334b9-1-s-0">
            <stop offset="0%" style="stop-color: rgb(51, 52, 185); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(77, 114, 243); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="0" y="0" width="1700" height="640" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <rect x="43.52" y="75.81" width="763.6" height="175.58" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="none"/>
        <g fill="#147EBA" font-family="Helvetica" pointer-events="none" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="373" y="83" width="106" height="15" stroke-width="0"/>
            <text x="424.82" y="93.31">
                Availability Zone 1a
            </text>
        </g>
        <path d="M 7 60 L 837 60 L 837 640 L 7 640 Z" fill="none" stroke="#248814" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 17.59 66.65 C 17.53 66.65 17.48 66.65 17.42 66.65 L 17.42 66.65 C 16.11 66.68 15.03 67.24 14.14 68.25 C 14.13 68.25 14.13 68.25 14.13 68.25 C 13.2 69.36 12.87 70.52 12.96 71.73 C 11.81 72.06 11.12 72.92 10.76 73.74 C 10.75 73.75 10.75 73.76 10.74 73.78 C 10.33 75.05 10.68 76.36 11.24 77.16 C 11.25 77.17 11.25 77.17 11.26 77.18 C 11.94 78.05 12.97 78.53 14.02 78.53 L 25.17 78.53 C 26.19 78.53 27.07 78.16 27.8 77.37 C 28.25 76.94 28.49 76.29 28.58 75.59 C 28.67 74.9 28.61 74.16 28.32 73.55 C 28.31 73.54 28.31 73.53 28.31 73.52 C 27.8 72.62 26.95 71.81 25.76 71.64 C 25.74 70.79 25.28 69.99 24.68 69.56 C 24.67 69.55 24.66 69.55 24.65 69.54 C 24.01 69.18 23.4 69.14 22.91 69.3 C 22.6 69.4 22.36 69.56 22.14 69.74 C 21.51 68.36 20.43 67.18 18.81 66.79 C 18.81 66.79 18.81 66.79 18.81 66.79 C 18.38 66.7 17.97 66.65 17.59 66.65 Z M 17.43 67.38 C 17.8 67.38 18.2 67.43 18.64 67.53 C 20.16 67.89 21.15 69.07 21.66 70.48 C 21.71 70.6 21.81 70.69 21.94 70.72 C 22.07 70.74 22.2 70.7 22.29 70.61 C 22.54 70.34 22.83 70.11 23.14 70.01 C 23.44 69.91 23.78 69.92 24.26 70.18 C 24.67 70.49 25.11 71.31 25.03 71.9 C 25.01 72.01 25.05 72.12 25.12 72.2 C 25.19 72.28 25.29 72.33 25.39 72.33 C 26.46 72.34 27.16 73.02 27.64 73.88 C 27.85 74.3 27.91 74.92 27.84 75.5 C 27.76 76.07 27.53 76.59 27.28 76.83 C 27.27 76.84 27.27 76.85 27.26 76.85 C 26.65 77.53 26.03 77.78 25.17 77.78 L 14.02 77.78 C 13.2 77.78 12.39 77.41 11.85 76.73 C 11.44 76.13 11.14 75.02 11.46 74.02 C 11.79 73.27 12.36 72.55 13.41 72.36 C 13.6 72.32 13.74 72.14 13.71 71.94 C 13.56 70.79 13.8 69.81 14.7 68.74 C 15.49 67.85 16.33 67.39 17.43 67.38 Z M 19.2 70.7 C 18.77 70.7 18.4 70.93 18.13 71.21 C 17.85 71.5 17.64 71.85 17.64 72.25 L 17.64 72.71 L 17.14 72.71 C 17.04 72.71 16.94 72.75 16.87 72.82 C 16.8 72.89 16.76 72.98 16.76 73.08 L 16.76 75.7 C 16.76 75.8 16.8 75.89 16.87 75.96 C 16.94 76.03 17.04 76.07 17.14 76.07 L 21.16 76.07 C 21.26 76.07 21.35 76.03 21.42 75.96 C 21.49 75.89 21.53 75.8 21.53 75.7 L 21.53 73.08 C 21.53 72.98 21.49 72.89 21.42 72.82 C 21.35 72.75 21.26 72.71 21.16 72.71 L 20.68 72.71 L 20.68 72.25 C 20.68 71.84 20.47 71.47 20.21 71.2 C 19.94 70.92 19.61 70.7 19.2 70.7 Z M 19.2 71.45 C 19.29 71.45 19.5 71.54 19.67 71.72 C 19.83 71.89 19.93 72.11 19.93 72.25 L 19.93 72.71 L 18.39 72.71 L 18.39 72.25 C 18.39 72.15 18.49 71.91 18.66 71.74 C 18.83 71.56 19.06 71.45 19.2 71.45 Z M 17.51 73.46 L 20.78 73.46 L 20.78 75.32 L 17.51 75.32 Z M 7 85 L 7 60 L 32 60 L 32 85 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 798px; height: 1px; padding-top: 67px; margin-left: 39px;">
                        <div data-drawio-colors="color: #AAB7B8; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(170, 183, 184); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                VPC
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="39" y="79" fill="#AAB7B8" font-family="Helvetica" font-size="12px">
                    VPC
                </text>
            </switch>
        </g>
        <path d="M 63.03 95.37 L 275.92 95.37 L 275.92 242.65 L 63.03 242.65 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 63.03 95.37 L 88.03 95.37 L 88.03 120.37 L 63.03 120.37 Z M 75.55 98.58 C 74.42 98.57 73.34 98.99 72.51 99.75 C 71.7 100.48 71.23 101.52 71.23 102.61 L 71.23 105.14 L 68.92 105.14 C 68.82 105.14 68.73 105.18 68.66 105.25 C 68.6 105.32 68.56 105.41 68.56 105.5 L 68.56 116.79 C 68.56 116.99 68.72 117.15 68.92 117.15 L 82.13 117.15 C 82.33 117.15 82.49 116.99 82.49 116.79 L 82.49 105.52 C 82.49 105.42 82.46 105.33 82.39 105.27 C 82.32 105.2 82.23 105.16 82.14 105.16 L 79.84 105.16 L 79.84 102.66 C 79.83 101.57 79.37 100.54 78.58 99.8 C 77.76 99.02 76.68 98.58 75.55 98.58 Z M 75.54 99.29 C 76.49 99.29 77.4 99.65 78.09 100.29 C 78.75 100.9 79.12 101.76 79.12 102.66 L 79.12 105.16 L 71.9 105.16 L 71.92 102.62 C 71.92 101.73 72.31 100.87 72.98 100.27 C 73.68 99.63 74.59 99.29 75.54 99.29 Z M 69.27 105.87 L 81.78 105.87 L 81.78 116.44 L 69.27 116.44 Z M 75.53 108.11 C 74.5 108.09 73.63 108.87 73.54 109.9 C 73.44 110.92 74.15 111.85 75.17 112.02 L 75.17 114.81 L 75.88 114.81 L 75.88 112.03 C 76.82 111.86 77.5 111.04 77.5 110.09 C 77.5 109 76.62 108.11 75.53 108.11 Z M 75.42 108.82 C 75.46 108.82 75.49 108.82 75.53 108.82 C 75.87 108.82 76.19 108.96 76.43 109.19 C 76.66 109.43 76.79 109.75 76.79 110.09 C 76.79 110.42 76.66 110.74 76.43 110.98 C 76.19 111.22 75.87 111.35 75.53 111.34 C 75.07 111.38 74.62 111.17 74.36 110.78 C 74.11 110.39 74.09 109.9 74.31 109.49 C 74.53 109.08 74.95 108.82 75.42 108.82 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 181px; height: 1px; padding-top: 102px; margin-left: 95px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="95" y="114" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 309.12 95.37 L 522.01 95.37 L 522.01 242.65 L 309.12 242.65 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 309.12 95.37 L 334.12 95.37 L 334.12 120.37 L 309.12 120.37 Z M 321.64 98.58 C 320.52 98.57 319.43 98.99 318.6 99.75 C 317.79 100.48 317.32 101.52 317.32 102.61 L 317.32 105.14 L 315.01 105.14 C 314.92 105.14 314.82 105.18 314.76 105.25 C 314.69 105.32 314.66 105.41 314.66 105.5 L 314.66 116.79 C 314.66 116.99 314.82 117.15 315.01 117.15 L 328.23 117.15 C 328.42 117.15 328.58 116.99 328.58 116.79 L 328.58 105.52 C 328.59 105.42 328.55 105.33 328.48 105.27 C 328.42 105.2 328.33 105.16 328.23 105.16 L 325.93 105.16 L 325.93 102.66 C 325.92 101.57 325.47 100.54 324.68 99.8 C 323.86 99.02 322.77 98.58 321.64 98.58 Z M 321.63 99.29 C 322.58 99.29 323.49 99.65 324.18 100.29 C 324.84 100.9 325.22 101.76 325.22 102.66 L 325.22 105.16 L 318 105.16 L 318.01 102.62 C 318.02 101.73 318.4 100.87 319.07 100.27 C 319.77 99.63 320.69 99.29 321.63 99.29 Z M 315.36 105.87 L 327.88 105.87 L 327.87 116.44 L 315.36 116.44 Z M 321.63 108.11 C 320.6 108.09 319.73 108.87 319.63 109.9 C 319.54 110.92 320.25 111.85 321.26 112.02 L 321.26 114.81 L 321.98 114.81 L 321.98 112.03 C 322.91 111.86 323.59 111.04 323.6 110.09 C 323.6 109 322.72 108.11 321.63 108.11 Z M 321.51 108.82 C 321.55 108.82 321.59 108.82 321.63 108.82 C 321.96 108.82 322.28 108.96 322.52 109.19 C 322.76 109.43 322.89 109.75 322.88 110.09 C 322.89 110.42 322.76 110.74 322.52 110.98 C 322.28 111.22 321.96 111.35 321.63 111.34 C 321.16 111.38 320.72 111.17 320.46 110.78 C 320.2 110.39 320.18 109.9 320.4 109.49 C 320.62 109.08 321.05 108.82 321.51 108.82 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 181px; height: 1px; padding-top: 102px; margin-left: 341px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="341" y="114" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 558.12 95.37 L 771.01 95.37 L 771.01 242.65 L 558.12 242.65 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 558.12 95.37 L 583.12 95.37 L 583.12 120.37 L 558.12 120.37 Z M 570.64 98.58 C 569.52 98.57 568.43 98.99 567.6 99.75 C 566.79 100.48 566.32 101.52 566.32 102.61 L 566.32 105.14 L 564.01 105.14 C 563.92 105.14 563.82 105.18 563.76 105.25 C 563.69 105.32 563.66 105.41 563.66 105.5 L 563.66 116.79 C 563.66 116.99 563.82 117.15 564.01 117.15 L 577.23 117.15 C 577.42 117.15 577.58 116.99 577.58 116.79 L 577.58 105.52 C 577.59 105.42 577.55 105.33 577.48 105.27 C 577.42 105.2 577.33 105.16 577.23 105.16 L 574.93 105.16 L 574.93 102.66 C 574.92 101.57 574.47 100.54 573.68 99.8 C 572.86 99.02 571.77 98.58 570.64 98.58 Z M 570.63 99.29 C 571.58 99.29 572.49 99.65 573.18 100.29 C 573.84 100.9 574.22 101.76 574.22 102.66 L 574.22 105.16 L 567 105.16 L 567.01 102.62 C 567.02 101.73 567.4 100.87 568.07 100.27 C 568.77 99.63 569.69 99.29 570.63 99.29 Z M 564.36 105.87 L 576.88 105.87 L 576.87 116.44 L 564.36 116.44 Z M 570.63 108.11 C 569.6 108.09 568.73 108.87 568.63 109.9 C 568.54 110.92 569.25 111.85 570.26 112.02 L 570.26 114.81 L 570.98 114.81 L 570.98 112.03 C 571.91 111.86 572.59 111.04 572.6 110.09 C 572.6 109 571.72 108.11 570.63 108.11 Z M 570.51 108.82 C 570.55 108.82 570.59 108.82 570.63 108.82 C 570.96 108.82 571.28 108.96 571.52 109.19 C 571.76 109.43 571.89 109.75 571.88 110.09 C 571.89 110.42 571.76 110.74 571.52 110.98 C 571.28 111.22 570.96 111.35 570.63 111.34 C 570.16 111.38 569.72 111.17 569.46 110.78 C 569.2 110.39 569.18 109.9 569.4 109.49 C 569.62 109.08 570.05 108.82 570.51 108.82 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 181px; height: 1px; padding-top: 102px; margin-left: 590px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="590" y="114" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 90 123.24 L 123.2 123.24 L 123.2 156.44 L 90 156.44 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 111.82 149.83 L 111.82 147.05 C 110.62 147.83 108.45 148.2 106.39 148.2 C 104.14 148.2 102.39 147.8 101.38 147.13 L 101.38 149.83 C 101.38 150.63 103.26 151.46 106.39 151.46 C 109.59 151.46 111.82 150.6 111.82 149.83 Z M 106.39 143.98 C 104.14 143.98 102.39 143.59 101.38 142.91 L 101.38 145.62 C 101.4 146.42 103.27 147.25 106.39 147.25 C 109.58 147.25 111.8 146.39 111.82 145.62 L 111.82 142.84 C 110.62 143.61 108.45 143.98 106.39 143.98 Z M 111.82 141.4 L 111.82 138.2 C 110.62 138.97 108.45 139.34 106.39 139.34 C 104.14 139.34 102.39 138.95 101.38 138.27 L 101.38 141.41 C 101.4 142.21 103.27 143.03 106.39 143.03 C 109.58 143.03 111.8 142.17 111.82 141.4 Z M 101.38 136.76 C 101.38 136.76 101.38 136.76 101.38 136.76 L 101.38 136.76 L 101.38 136.77 C 101.4 137.57 103.27 138.39 106.39 138.39 C 109.87 138.39 111.8 137.43 111.82 136.76 L 111.82 136.76 L 111.82 136.76 C 111.82 136.76 111.82 136.76 111.82 136.76 C 111.82 136.09 109.89 135.12 106.39 135.12 C 103.25 135.12 101.38 135.95 101.38 136.76 Z M 112.77 136.77 L 112.77 141.4 L 112.77 141.4 C 112.77 141.4 112.77 141.4 112.77 141.41 L 112.77 145.61 L 112.77 145.61 C 112.77 145.62 112.77 145.62 112.77 145.62 L 112.77 149.83 C 112.77 151.6 109.46 152.41 106.39 152.41 C 102.77 152.41 100.43 151.4 100.43 149.83 L 100.43 145.63 C 100.43 145.62 100.43 145.62 100.43 145.61 L 100.43 145.61 L 100.43 141.41 C 100.43 141.41 100.43 141.4 100.43 141.4 L 100.43 141.4 L 100.43 136.77 C 100.43 136.77 100.43 136.76 100.43 136.76 C 100.43 135.19 102.77 134.17 106.39 134.17 C 109.46 134.17 112.77 134.98 112.77 136.76 C 112.77 136.76 112.77 136.76 112.77 136.77 Z M 119.41 130.65 C 119.67 130.65 119.88 130.44 119.88 130.18 L 119.88 127.75 C 119.88 127.49 119.67 127.27 119.41 127.27 L 93.79 127.27 C 93.53 127.27 93.32 127.49 93.32 127.75 L 93.32 130.18 C 93.32 130.44 93.53 130.65 93.79 130.65 C 94.37 130.65 94.84 131.12 94.84 131.69 C 94.84 132.27 94.37 132.74 93.79 132.74 C 93.53 132.74 93.32 132.95 93.32 133.21 L 93.32 142.93 C 93.32 143.19 93.53 143.4 93.79 143.4 L 98.54 143.4 L 98.54 142.45 L 96.17 142.45 L 96.17 141.03 L 98.54 141.03 L 98.54 140.08 L 95.69 140.08 C 95.43 140.08 95.22 140.29 95.22 140.56 L 95.22 142.45 L 94.27 142.45 L 94.27 133.63 C 95.14 133.42 95.79 132.63 95.79 131.69 C 95.79 130.76 95.14 129.97 94.27 129.76 L 94.27 128.22 L 118.93 128.22 L 118.93 129.76 C 118.06 129.97 117.41 130.76 117.41 131.69 C 117.41 132.63 118.06 133.42 118.93 133.63 L 118.93 142.45 L 117.98 142.45 L 117.98 140.56 C 117.98 140.29 117.77 140.08 117.51 140.08 L 114.66 140.08 L 114.66 141.03 L 117.03 141.03 L 117.03 142.45 L 114.66 142.45 L 114.66 143.4 L 119.41 143.4 C 119.67 143.4 119.88 143.19 119.88 142.93 L 119.88 133.21 C 119.88 132.95 119.67 132.74 119.41 132.74 C 118.83 132.74 118.36 132.27 118.36 131.69 C 118.36 131.12 118.83 130.65 119.41 130.65 Z M 100.91 133.91 L 100.91 130.12 C 100.91 129.86 100.7 129.65 100.43 129.65 L 97.59 129.65 C 97.33 129.65 97.11 129.86 97.11 130.12 L 97.11 138.18 C 97.11 138.45 97.33 138.66 97.59 138.66 L 99.01 138.66 L 99.01 137.71 L 98.06 137.71 L 98.06 130.59 L 99.96 130.59 L 99.96 133.91 Z M 115.14 137.71 L 114.66 137.71 L 114.66 138.66 L 115.61 138.66 C 115.87 138.66 116.09 138.45 116.09 138.18 L 116.09 130.12 C 116.09 129.86 115.87 129.65 115.61 129.65 L 112.77 129.65 C 112.5 129.65 112.29 129.86 112.29 130.12 L 112.29 133.91 L 113.24 133.91 L 113.24 130.59 L 115.14 130.59 Z M 111.34 133.44 L 111.34 130.12 C 111.34 129.86 111.13 129.65 110.87 129.65 L 107.55 129.65 C 107.29 129.65 107.07 129.86 107.07 130.12 L 107.07 132.97 L 108.02 132.97 L 108.02 130.59 L 110.39 130.59 L 110.39 133.44 Z M 105.18 132.97 L 105.18 130.59 L 102.81 130.59 L 102.81 133.44 L 101.86 133.44 L 101.86 130.12 C 101.86 129.86 102.07 129.65 102.33 129.65 L 105.65 129.65 C 105.91 129.65 106.13 129.86 106.13 130.12 L 106.13 132.97 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <rect x="43.52" y="261.38" width="763.6" height="178.91" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="373" y="269" width="105" height="15" stroke-width="0"/>
            <text x="424.82" y="278.88">
                Availability Zone 1c
            </text>
        </g>
        <path d="M 64.68 282.6 L 277.58 282.6 L 277.58 429.89 L 64.68 429.89 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 64.68 282.6 L 89.68 282.6 L 89.68 307.6 L 64.68 307.6 Z M 77.21 285.81 C 76.08 285.8 75 286.22 74.17 286.98 C 73.36 287.71 72.89 288.75 72.88 289.84 L 72.88 292.37 L 70.58 292.37 C 70.48 292.37 70.39 292.41 70.32 292.48 C 70.26 292.55 70.22 292.64 70.22 292.73 L 70.22 304.03 C 70.22 304.22 70.38 304.38 70.58 304.38 L 83.79 304.38 C 83.99 304.38 84.15 304.22 84.15 304.03 L 84.15 292.75 C 84.15 292.65 84.12 292.56 84.05 292.5 C 83.98 292.43 83.89 292.39 83.8 292.39 L 81.5 292.39 L 81.5 289.89 C 81.49 288.81 81.03 287.77 80.24 287.03 C 79.42 286.25 78.34 285.81 77.21 285.81 Z M 77.2 286.53 C 78.15 286.52 79.06 286.88 79.75 287.53 C 80.41 288.14 80.78 288.99 80.78 289.89 L 80.78 292.39 L 73.56 292.39 L 73.58 289.85 C 73.58 288.96 73.97 288.1 74.63 287.5 C 75.34 286.87 76.25 286.52 77.2 286.53 Z M 70.93 293.1 L 83.44 293.1 L 83.43 303.67 L 70.93 303.67 Z M 77.19 295.34 C 76.16 295.32 75.29 296.1 75.2 297.13 C 75.1 298.15 75.81 299.08 76.83 299.25 L 76.83 302.04 L 77.54 302.04 L 77.54 299.26 C 78.48 299.09 79.16 298.27 79.16 297.32 C 79.16 296.23 78.28 295.34 77.19 295.34 Z M 77.08 296.05 C 77.12 296.05 77.15 296.05 77.19 296.05 C 77.53 296.06 77.85 296.19 78.08 296.42 C 78.32 296.66 78.45 296.98 78.45 297.32 C 78.45 297.65 78.32 297.97 78.09 298.21 C 77.85 298.45 77.53 298.58 77.19 298.57 C 76.73 298.62 76.28 298.4 76.02 298.01 C 75.77 297.62 75.75 297.13 75.97 296.72 C 76.19 296.31 76.61 296.05 77.08 296.05 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 181px; height: 1px; padding-top: 290px; margin-left: 97px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="97" y="302" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 312.44 282.6 L 525.34 282.6 L 525.34 429.89 L 312.44 429.89 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 312.44 282.6 L 337.44 282.6 L 337.44 307.6 L 312.44 307.6 Z M 324.96 285.81 C 323.84 285.8 322.75 286.22 321.93 286.98 C 321.11 287.71 320.64 288.75 320.64 289.84 L 320.64 292.37 L 318.33 292.37 C 318.24 292.37 318.14 292.41 318.08 292.48 C 318.01 292.55 317.98 292.64 317.98 292.73 L 317.98 304.03 C 317.98 304.22 318.14 304.38 318.33 304.38 L 331.55 304.38 C 331.74 304.38 331.9 304.22 331.9 304.03 L 331.9 292.75 C 331.91 292.65 331.87 292.56 331.8 292.5 C 331.74 292.43 331.65 292.39 331.55 292.39 L 329.25 292.39 L 329.25 289.89 C 329.24 288.81 328.79 287.77 328 287.03 C 327.18 286.25 326.09 285.81 324.96 285.81 Z M 324.95 286.53 C 325.9 286.52 326.81 286.88 327.5 287.53 C 328.16 288.14 328.54 288.99 328.54 289.89 L 328.54 292.39 L 321.32 292.39 L 321.33 289.85 C 321.34 288.96 321.72 288.1 322.39 287.5 C 323.09 286.87 324.01 286.52 324.95 286.53 Z M 318.68 293.1 L 331.2 293.1 L 331.19 303.67 L 318.68 303.67 Z M 324.95 295.34 C 323.92 295.32 323.05 296.1 322.95 297.13 C 322.86 298.15 323.57 299.08 324.58 299.25 L 324.58 302.04 L 325.3 302.04 L 325.3 299.26 C 326.23 299.09 326.91 298.27 326.92 297.32 C 326.92 296.23 326.04 295.34 324.95 295.34 Z M 324.83 296.05 C 324.87 296.05 324.91 296.05 324.95 296.05 C 325.28 296.06 325.6 296.19 325.84 296.42 C 326.08 296.66 326.21 296.98 326.2 297.32 C 326.21 297.65 326.08 297.97 325.84 298.21 C 325.6 298.45 325.28 298.58 324.95 298.57 C 324.48 298.62 324.04 298.4 323.78 298.01 C 323.52 297.62 323.5 297.13 323.72 296.72 C 323.94 296.31 324.37 296.05 324.83 296.05 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 181px; height: 1px; padding-top: 290px; margin-left: 344px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="344" y="302" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 559.78 282.6 L 772.68 282.6 L 772.68 429.89 L 559.78 429.89 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 559.78 282.6 L 584.78 282.6 L 584.78 307.6 L 559.78 307.6 Z M 572.3 285.81 C 571.18 285.8 570.09 286.22 569.27 286.98 C 568.45 287.71 567.98 288.75 567.98 289.84 L 567.98 292.37 L 565.67 292.37 C 565.58 292.37 565.48 292.41 565.42 292.48 C 565.35 292.55 565.32 292.64 565.32 292.73 L 565.32 304.03 C 565.32 304.22 565.48 304.38 565.67 304.38 L 578.89 304.38 C 579.08 304.38 579.24 304.22 579.24 304.03 L 579.24 292.75 C 579.25 292.65 579.21 292.56 579.14 292.5 C 579.08 292.43 578.99 292.39 578.89 292.39 L 576.59 292.39 L 576.59 289.89 C 576.58 288.81 576.13 287.77 575.34 287.03 C 574.52 286.25 573.43 285.81 572.3 285.81 Z M 572.29 286.53 C 573.24 286.52 574.15 286.88 574.84 287.53 C 575.5 288.14 575.88 288.99 575.88 289.89 L 575.88 292.39 L 568.66 292.39 L 568.67 289.85 C 568.68 288.96 569.06 288.1 569.73 287.5 C 570.43 286.87 571.35 286.52 572.29 286.53 Z M 566.02 293.1 L 578.54 293.1 L 578.53 303.67 L 566.02 303.67 Z M 572.29 295.34 C 571.26 295.32 570.39 296.1 570.29 297.13 C 570.2 298.15 570.91 299.08 571.92 299.25 L 571.92 302.04 L 572.64 302.04 L 572.64 299.26 C 573.57 299.09 574.25 298.27 574.26 297.32 C 574.26 296.23 573.38 295.34 572.29 295.34 Z M 572.17 296.05 C 572.21 296.05 572.25 296.05 572.29 296.05 C 572.62 296.06 572.94 296.19 573.18 296.42 C 573.42 296.66 573.55 296.98 573.54 297.32 C 573.55 297.65 573.42 297.97 573.18 298.21 C 572.94 298.45 572.62 298.58 572.29 298.57 C 571.82 298.62 571.38 298.4 571.12 298.01 C 570.86 297.62 570.84 297.13 571.06 296.72 C 571.28 296.31 571.71 296.05 572.17 296.05 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 181px; height: 1px; padding-top: 290px; margin-left: 592px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="592" y="302" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <rect x="43.52" y="451.1" width="763.6" height="178.91" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="373" y="459" width="106" height="15" stroke-width="0"/>
            <text x="424.82" y="468.6">
                Availability Zone 1d
            </text>
        </g>
        <path d="M 64.68 473.16 L 277.58 473.16 L 277.58 620.44 L 64.68 620.44 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 64.68 473.16 L 89.68 473.16 L 89.68 498.16 L 64.68 498.16 Z M 77.21 476.37 C 76.08 476.36 75 476.78 74.17 477.54 C 73.36 478.27 72.89 479.31 72.88 480.4 L 72.88 482.93 L 70.58 482.93 C 70.48 482.93 70.39 482.97 70.32 483.04 C 70.26 483.11 70.22 483.2 70.22 483.29 L 70.22 494.58 C 70.22 494.78 70.38 494.94 70.58 494.94 L 83.79 494.94 C 83.99 494.94 84.15 494.78 84.15 494.58 L 84.15 483.31 C 84.15 483.21 84.12 483.12 84.05 483.06 C 83.98 482.99 83.89 482.95 83.8 482.95 L 81.5 482.95 L 81.5 480.45 C 81.49 479.37 81.03 478.33 80.24 477.59 C 79.42 476.81 78.34 476.37 77.21 476.37 Z M 77.2 477.08 C 78.15 477.08 79.06 477.44 79.75 478.08 C 80.41 478.7 80.78 479.55 80.78 480.45 L 80.78 482.95 L 73.56 482.95 L 73.58 480.41 C 73.58 479.52 73.97 478.66 74.63 478.06 C 75.34 477.43 76.25 477.08 77.2 477.08 Z M 70.93 483.66 L 83.44 483.66 L 83.43 494.23 L 70.93 494.23 Z M 77.19 485.9 C 76.16 485.88 75.29 486.66 75.2 487.69 C 75.1 488.71 75.81 489.64 76.83 489.81 L 76.83 492.6 L 77.54 492.6 L 77.54 489.82 C 78.48 489.65 79.16 488.83 79.16 487.88 C 79.16 486.79 78.28 485.9 77.19 485.9 Z M 77.08 486.61 C 77.12 486.61 77.15 486.61 77.19 486.61 C 77.53 486.62 77.85 486.75 78.08 486.98 C 78.32 487.22 78.45 487.54 78.45 487.88 C 78.45 488.21 78.32 488.53 78.09 488.77 C 77.85 489.01 77.53 489.14 77.19 489.13 C 76.73 489.18 76.28 488.96 76.02 488.57 C 75.77 488.18 75.75 487.69 75.97 487.28 C 76.19 486.87 76.61 486.61 77.08 486.61 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 181px; height: 1px; padding-top: 480px; margin-left: 97px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="97" y="492" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 312.44 472.32 L 525.34 472.32 L 525.34 619.61 L 312.44 619.61 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 312.44 472.32 L 337.44 472.32 L 337.44 497.32 L 312.44 497.32 Z M 324.96 475.54 C 323.84 475.53 322.75 475.95 321.93 476.71 C 321.11 477.44 320.64 478.48 320.64 479.57 L 320.64 482.1 L 318.33 482.1 C 318.24 482.1 318.14 482.14 318.08 482.21 C 318.01 482.28 317.98 482.37 317.98 482.46 L 317.98 493.75 C 317.98 493.95 318.14 494.11 318.33 494.11 L 331.55 494.11 C 331.74 494.11 331.9 493.95 331.9 493.75 L 331.9 482.47 C 331.91 482.38 331.87 482.29 331.8 482.22 C 331.74 482.15 331.65 482.12 331.55 482.12 L 329.25 482.12 L 329.25 479.62 C 329.24 478.53 328.79 477.5 328 476.76 C 327.18 475.98 326.09 475.54 324.96 475.54 Z M 324.95 476.25 C 325.9 476.25 326.81 476.6 327.5 477.25 C 328.16 477.86 328.54 478.72 328.54 479.62 L 328.54 482.12 L 321.32 482.12 L 321.33 479.58 C 321.34 478.68 321.72 477.83 322.39 477.23 C 323.09 476.59 324.01 476.24 324.95 476.25 Z M 318.68 482.82 L 331.2 482.82 L 331.19 493.4 L 318.68 493.4 Z M 324.95 485.07 C 323.92 485.05 323.05 485.83 322.95 486.86 C 322.86 487.88 323.57 488.81 324.58 488.98 L 324.58 491.77 L 325.3 491.77 L 325.3 488.99 C 326.23 488.81 326.91 488 326.92 487.04 C 326.92 485.96 326.04 485.07 324.95 485.07 Z M 324.83 485.78 C 324.87 485.78 324.91 485.78 324.95 485.78 C 325.28 485.78 325.6 485.92 325.84 486.15 C 326.08 486.39 326.21 486.71 326.2 487.04 C 326.21 487.38 326.08 487.7 325.84 487.94 C 325.6 488.17 325.28 488.31 324.95 488.3 C 324.48 488.34 324.04 488.13 323.78 487.74 C 323.52 487.35 323.5 486.85 323.72 486.45 C 323.94 486.04 324.37 485.78 324.83 485.78 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 181px; height: 1px; padding-top: 479px; margin-left: 344px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="344" y="491" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 559.78 472.32 L 772.68 472.32 L 772.68 619.61 L 559.78 619.61 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 559.78 472.32 L 584.78 472.32 L 584.78 497.32 L 559.78 497.32 Z M 572.3 475.54 C 571.18 475.53 570.09 475.95 569.27 476.71 C 568.45 477.44 567.98 478.48 567.98 479.57 L 567.98 482.1 L 565.67 482.1 C 565.58 482.1 565.48 482.14 565.42 482.21 C 565.35 482.28 565.32 482.37 565.32 482.46 L 565.32 493.75 C 565.32 493.95 565.48 494.11 565.67 494.11 L 578.89 494.11 C 579.08 494.11 579.24 493.95 579.24 493.75 L 579.24 482.47 C 579.25 482.38 579.21 482.29 579.14 482.22 C 579.08 482.15 578.99 482.12 578.89 482.12 L 576.59 482.12 L 576.59 479.62 C 576.58 478.53 576.13 477.5 575.34 476.76 C 574.52 475.98 573.43 475.54 572.3 475.54 Z M 572.29 476.25 C 573.24 476.25 574.15 476.6 574.84 477.25 C 575.5 477.86 575.88 478.72 575.88 479.62 L 575.88 482.12 L 568.66 482.12 L 568.67 479.58 C 568.68 478.68 569.06 477.83 569.73 477.23 C 570.43 476.59 571.35 476.24 572.29 476.25 Z M 566.02 482.82 L 578.54 482.82 L 578.53 493.4 L 566.02 493.4 Z M 572.29 485.07 C 571.26 485.05 570.39 485.83 570.29 486.86 C 570.2 487.88 570.91 488.81 571.92 488.98 L 571.92 491.77 L 572.64 491.77 L 572.64 488.99 C 573.57 488.81 574.25 488 574.26 487.04 C 574.26 485.96 573.38 485.07 572.29 485.07 Z M 572.17 485.78 C 572.21 485.78 572.25 485.78 572.29 485.78 C 572.62 485.78 572.94 485.92 573.18 486.15 C 573.42 486.39 573.55 486.71 573.54 487.04 C 573.55 487.38 573.42 487.7 573.18 487.94 C 572.94 488.17 572.62 488.31 572.29 488.3 C 571.82 488.34 571.38 488.13 571.12 487.74 C 570.86 487.35 570.84 486.85 571.06 486.45 C 571.28 486.04 571.71 485.78 572.17 485.78 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 181px; height: 1px; padding-top: 479px; margin-left: 592px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="592" y="491" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <rect x="90.83" y="124.07" width="664" height="485.14" fill="none" stroke="#3333ff" stroke-width="2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 612px; height: 1px; padding-top: 131px; margin-left: 143px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                クラスター
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="143" y="143" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    クラスター
                </text>
            </switch>
        </g>
        <rect x="157.23" y="517.68" width="33.2" height="33.2" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 181.41 521.69 L 184.43 521.69 L 184.43 522.36 L 182.25 522.36 L 182.25 524.23 L 184.05 524.23 L 184.05 524.9 L 182.25 524.9 L 182.25 526.96 L 184.43 526.96 L 184.43 527.63 L 181.41 527.63 Z M 179.29 521.69 L 180.13 521.69 L 180.13 527.63 L 179.29 527.63 L 179.29 524.93 L 177.13 524.93 L 177.13 527.63 L 176.3 527.63 L 176.3 521.69 L 177.13 521.69 L 177.13 524.25 L 179.29 524.25 Z M 174.26 526.99 C 174.51 526.99 174.89 526.92 175.37 526.78 L 175.37 527.45 C 175 527.63 174.6 527.72 174.16 527.72 C 172.63 527.72 171.86 526.71 171.86 524.68 C 171.86 523.68 172.06 522.92 172.45 522.4 C 172.85 521.88 173.43 521.62 174.2 521.62 C 174.56 521.62 174.93 521.7 175.3 521.85 L 175.3 522.53 C 174.92 522.41 174.58 522.35 174.29 522.35 C 173.75 522.35 173.36 522.52 173.11 522.88 C 172.86 523.23 172.74 523.79 172.74 524.57 L 172.74 524.78 C 172.74 525.55 172.86 526.12 173.1 526.47 C 173.34 526.82 173.72 526.99 174.26 526.99 Z M 168.57 525.39 L 169.31 522.49 L 170.04 525.39 Z M 168.88 521.69 L 167.16 527.63 L 168.01 527.63 L 168.41 526.03 L 170.21 526.03 L 170.62 527.63 L 171.49 527.63 L 169.78 521.69 Z M 165.68 526.99 C 165.94 526.99 166.31 526.92 166.8 526.78 L 166.8 527.45 C 166.43 527.63 166.02 527.72 165.59 527.72 C 164.06 527.72 163.29 526.71 163.29 524.68 C 163.29 523.68 163.49 522.92 163.88 522.4 C 164.28 521.88 164.86 521.62 165.62 521.62 C 165.99 521.62 166.36 521.7 166.73 521.85 L 166.73 522.53 C 166.35 522.41 166.01 522.35 165.72 522.35 C 165.18 522.35 164.79 522.52 164.54 522.88 C 164.29 523.23 164.17 523.79 164.17 524.57 L 164.17 524.78 C 164.17 525.55 164.29 526.12 164.53 526.47 C 164.77 526.82 165.15 526.99 165.68 526.99 Z M 188.91 545.99 C 188.91 547.85 187.4 549.36 185.54 549.36 L 162.12 549.36 C 160.26 549.36 158.75 547.85 158.75 545.99 L 158.75 522.57 C 158.75 520.71 160.26 519.19 162.12 519.19 L 185.54 519.19 C 187.4 519.19 188.91 520.71 188.91 522.57 Z M 185.54 517.68 L 162.12 517.68 C 159.42 517.68 157.23 519.87 157.23 522.57 L 157.23 545.99 C 157.23 548.68 159.42 550.88 162.12 550.88 L 185.54 550.88 C 188.24 550.88 190.43 548.68 190.43 545.99 L 190.43 522.57 C 190.43 519.87 188.24 517.68 185.54 517.68 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 558px; margin-left: 174px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Replica
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="174" y="570" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Repli...
                </text>
            </switch>
        </g>
        <rect x="111.09" y="158.9" width="124.5" height="424.39" fill="none" stroke="#3333ff" stroke-width="2" stroke-dasharray="6 6" pointer-events="all"/>
        <rect x="356.3" y="158.9" width="124.5" height="424.39" fill="none" stroke="#3333ff" stroke-width="2" stroke-dasharray="6 6" pointer-events="all"/>
        <rect x="602" y="158.9" width="124.5" height="424.39" fill="none" stroke="#3333ff" stroke-width="2" stroke-dasharray="6 6" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 73px; height: 1px; padding-top: 166px; margin-left: 653px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <div style="">
                                    <br/>
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="689" y="178" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                </text>
            </switch>
        </g>
        <rect x="157.23" y="336.27" width="33.2" height="33.2" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 181.41 340.28 L 184.43 340.28 L 184.43 340.95 L 182.25 340.95 L 182.25 342.82 L 184.05 342.82 L 184.05 343.49 L 182.25 343.49 L 182.25 345.56 L 184.43 345.56 L 184.43 346.23 L 181.41 346.23 Z M 179.29 340.28 L 180.13 340.28 L 180.13 346.23 L 179.29 346.23 L 179.29 343.52 L 177.13 343.52 L 177.13 346.23 L 176.3 346.23 L 176.3 340.28 L 177.13 340.28 L 177.13 342.85 L 179.29 342.85 Z M 174.26 345.58 C 174.51 345.58 174.89 345.51 175.37 345.37 L 175.37 346.05 C 175 346.23 174.6 346.31 174.16 346.31 C 172.63 346.31 171.86 345.3 171.86 343.28 C 171.86 342.28 172.06 341.51 172.45 340.99 C 172.85 340.47 173.43 340.21 174.2 340.21 C 174.56 340.21 174.93 340.29 175.3 340.44 L 175.3 341.12 C 174.92 341 174.58 340.94 174.29 340.94 C 173.75 340.94 173.36 341.12 173.11 341.47 C 172.86 341.82 172.74 342.39 172.74 343.17 L 172.74 343.37 C 172.74 344.15 172.86 344.71 173.1 345.06 C 173.34 345.41 173.72 345.58 174.26 345.58 Z M 168.57 343.99 L 169.31 341.08 L 170.04 343.99 Z M 168.88 340.28 L 167.16 346.23 L 168.01 346.23 L 168.41 344.62 L 170.21 344.62 L 170.62 346.23 L 171.49 346.23 L 169.78 340.28 Z M 165.68 345.58 C 165.94 345.58 166.31 345.51 166.8 345.37 L 166.8 346.05 C 166.43 346.23 166.02 346.31 165.59 346.31 C 164.06 346.31 163.29 345.3 163.29 343.28 C 163.29 342.28 163.49 341.51 163.88 340.99 C 164.28 340.47 164.86 340.21 165.62 340.21 C 165.99 340.21 166.36 340.29 166.73 340.44 L 166.73 341.12 C 166.35 341 166.01 340.94 165.72 340.94 C 165.18 340.94 164.79 341.12 164.54 341.47 C 164.29 341.82 164.17 342.39 164.17 343.17 L 164.17 343.37 C 164.17 344.15 164.29 344.71 164.53 345.06 C 164.77 345.41 165.15 345.58 165.68 345.58 Z M 188.91 364.58 C 188.91 366.44 187.4 367.95 185.54 367.95 L 162.12 367.95 C 160.26 367.95 158.75 366.44 158.75 364.58 L 158.75 341.16 C 158.75 339.3 160.26 337.79 162.12 337.79 L 185.54 337.79 C 187.4 337.79 188.91 339.3 188.91 341.16 Z M 185.54 336.27 L 162.12 336.27 C 159.42 336.27 157.23 338.46 157.23 341.16 L 157.23 364.58 C 157.23 367.28 159.42 369.47 162.12 369.47 L 185.54 369.47 C 188.24 369.47 190.43 367.28 190.43 364.58 L 190.43 341.16 C 190.43 338.46 188.24 336.27 185.54 336.27 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 376px; margin-left: 174px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Replica
                                <br/>
                                <font color="#ff3333">
                                    <b>
                                        →Primary
                                    </b>
                                </font>
                                <font color="#ff3333">
                                    <b>
                                        <br/>
                                    </b>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="174" y="388" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Repli...
                </text>
            </switch>
        </g>
        <rect x="157.23" y="178.16" width="33.2" height="33.2" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 181.41 182.17 L 184.43 182.17 L 184.43 182.84 L 182.25 182.84 L 182.25 184.72 L 184.05 184.72 L 184.05 185.38 L 182.25 185.38 L 182.25 187.45 L 184.43 187.45 L 184.43 188.12 L 181.41 188.12 Z M 179.29 182.17 L 180.13 182.17 L 180.13 188.12 L 179.29 188.12 L 179.29 185.42 L 177.13 185.42 L 177.13 188.12 L 176.3 188.12 L 176.3 182.17 L 177.13 182.17 L 177.13 184.74 L 179.29 184.74 Z M 174.26 187.48 C 174.51 187.48 174.89 187.41 175.37 187.26 L 175.37 187.94 C 175 188.12 174.6 188.21 174.16 188.21 C 172.63 188.21 171.86 187.2 171.86 185.17 C 171.86 184.17 172.06 183.41 172.45 182.89 C 172.85 182.37 173.43 182.11 174.2 182.11 C 174.56 182.11 174.93 182.18 175.3 182.34 L 175.3 183.02 C 174.92 182.9 174.58 182.84 174.29 182.84 C 173.75 182.84 173.36 183.01 173.11 183.36 C 172.86 183.72 172.74 184.28 172.74 185.06 L 172.74 185.26 C 172.74 186.04 172.86 186.61 173.1 186.95 C 173.34 187.3 173.72 187.48 174.26 187.48 Z M 168.57 185.88 L 169.31 182.97 L 170.04 185.88 Z M 168.88 182.17 L 167.16 188.12 L 168.01 188.12 L 168.41 186.52 L 170.21 186.52 L 170.62 188.12 L 171.49 188.12 L 169.78 182.17 Z M 165.68 187.48 C 165.94 187.48 166.31 187.41 166.8 187.26 L 166.8 187.94 C 166.43 188.12 166.02 188.21 165.59 188.21 C 164.06 188.21 163.29 187.2 163.29 185.17 C 163.29 184.17 163.49 183.41 163.88 182.89 C 164.28 182.37 164.86 182.11 165.62 182.11 C 165.99 182.11 166.36 182.18 166.73 182.34 L 166.73 183.02 C 166.35 182.9 166.01 182.84 165.72 182.84 C 165.18 182.84 164.79 183.01 164.54 183.36 C 164.29 183.72 164.17 184.28 164.17 185.06 L 164.17 185.26 C 164.17 186.04 164.29 186.61 164.53 186.95 C 164.77 187.3 165.15 187.48 165.68 187.48 Z M 188.91 206.47 C 188.91 208.33 187.4 209.85 185.54 209.85 L 162.12 209.85 C 160.26 209.85 158.75 208.33 158.75 206.47 L 158.75 183.05 C 158.75 181.19 160.26 179.68 162.12 179.68 L 185.54 179.68 C 187.4 179.68 188.91 181.19 188.91 183.05 Z M 185.54 178.16 L 162.12 178.16 C 159.42 178.16 157.23 180.36 157.23 183.05 L 157.23 206.47 C 157.23 209.17 159.42 211.36 162.12 211.36 L 185.54 211.36 C 188.24 211.36 190.43 209.17 190.43 206.47 L 190.43 183.05 C 190.43 180.36 188.24 178.16 185.54 178.16 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 218px; margin-left: 174px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Primary
                                <br/>
                                <font color="#ff3333">
                                    →Replica(復旧後)
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="174" y="230" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Prima...
                </text>
            </switch>
        </g>
        <rect x="403.1" y="517.68" width="33.2" height="33.2" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 427.28 521.69 L 430.3 521.69 L 430.3 522.36 L 428.12 522.36 L 428.12 524.23 L 429.92 524.23 L 429.92 524.9 L 428.12 524.9 L 428.12 526.96 L 430.3 526.96 L 430.3 527.63 L 427.28 527.63 Z M 425.16 521.69 L 426 521.69 L 426 527.63 L 425.16 527.63 L 425.16 524.93 L 423 524.93 L 423 527.63 L 422.17 527.63 L 422.17 521.69 L 423 521.69 L 423 524.25 L 425.16 524.25 Z M 420.13 526.99 C 420.38 526.99 420.76 526.92 421.24 526.78 L 421.24 527.45 C 420.87 527.63 420.47 527.72 420.03 527.72 C 418.5 527.72 417.73 526.71 417.73 524.68 C 417.73 523.68 417.93 522.92 418.32 522.4 C 418.72 521.88 419.3 521.62 420.07 521.62 C 420.43 521.62 420.8 521.7 421.17 521.85 L 421.17 522.53 C 420.79 522.41 420.45 522.35 420.16 522.35 C 419.62 522.35 419.23 522.52 418.98 522.88 C 418.73 523.23 418.61 523.79 418.61 524.57 L 418.61 524.78 C 418.61 525.55 418.73 526.12 418.97 526.47 C 419.21 526.82 419.59 526.99 420.13 526.99 Z M 414.44 525.39 L 415.18 522.49 L 415.91 525.39 Z M 414.75 521.69 L 413.03 527.63 L 413.88 527.63 L 414.28 526.03 L 416.08 526.03 L 416.49 527.63 L 417.36 527.63 L 415.65 521.69 Z M 411.55 526.99 C 411.81 526.99 412.18 526.92 412.67 526.78 L 412.67 527.45 C 412.3 527.63 411.89 527.72 411.46 527.72 C 409.93 527.72 409.16 526.71 409.16 524.68 C 409.16 523.68 409.36 522.92 409.75 522.4 C 410.15 521.88 410.73 521.62 411.49 521.62 C 411.86 521.62 412.23 521.7 412.6 521.85 L 412.6 522.53 C 412.22 522.41 411.88 522.35 411.59 522.35 C 411.05 522.35 410.66 522.52 410.41 522.88 C 410.16 523.23 410.04 523.79 410.04 524.57 L 410.04 524.78 C 410.04 525.55 410.16 526.12 410.4 526.47 C 410.64 526.82 411.02 526.99 411.55 526.99 Z M 434.78 545.99 C 434.78 547.85 433.27 549.36 431.41 549.36 L 407.99 549.36 C 406.13 549.36 404.62 547.85 404.62 545.99 L 404.62 522.57 C 404.62 520.71 406.13 519.19 407.99 519.19 L 431.41 519.19 C 433.27 519.19 434.78 520.71 434.78 522.57 Z M 431.41 517.68 L 407.99 517.68 C 405.29 517.68 403.1 519.87 403.1 522.57 L 403.1 545.99 C 403.1 548.68 405.29 550.88 407.99 550.88 L 431.41 550.88 C 434.11 550.88 436.3 548.68 436.3 545.99 L 436.3 522.57 C 436.3 519.87 434.11 517.68 431.41 517.68 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 558px; margin-left: 420px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Replica
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="420" y="570" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Repli...
                </text>
            </switch>
        </g>
        <rect x="403.1" y="336.27" width="33.2" height="33.2" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 427.28 340.28 L 430.3 340.28 L 430.3 340.95 L 428.12 340.95 L 428.12 342.82 L 429.92 342.82 L 429.92 343.49 L 428.12 343.49 L 428.12 345.56 L 430.3 345.56 L 430.3 346.23 L 427.28 346.23 Z M 425.16 340.28 L 426 340.28 L 426 346.23 L 425.16 346.23 L 425.16 343.52 L 423 343.52 L 423 346.23 L 422.17 346.23 L 422.17 340.28 L 423 340.28 L 423 342.85 L 425.16 342.85 Z M 420.13 345.58 C 420.38 345.58 420.76 345.51 421.24 345.37 L 421.24 346.05 C 420.87 346.23 420.47 346.31 420.03 346.31 C 418.5 346.31 417.73 345.3 417.73 343.28 C 417.73 342.28 417.93 341.51 418.32 340.99 C 418.72 340.47 419.3 340.21 420.07 340.21 C 420.43 340.21 420.8 340.29 421.17 340.44 L 421.17 341.12 C 420.79 341 420.45 340.94 420.16 340.94 C 419.62 340.94 419.23 341.12 418.98 341.47 C 418.73 341.82 418.61 342.39 418.61 343.17 L 418.61 343.37 C 418.61 344.15 418.73 344.71 418.97 345.06 C 419.21 345.41 419.59 345.58 420.13 345.58 Z M 414.44 343.99 L 415.18 341.08 L 415.91 343.99 Z M 414.75 340.28 L 413.03 346.23 L 413.88 346.23 L 414.28 344.62 L 416.08 344.62 L 416.49 346.23 L 417.36 346.23 L 415.65 340.28 Z M 411.55 345.58 C 411.81 345.58 412.18 345.51 412.67 345.37 L 412.67 346.05 C 412.3 346.23 411.89 346.31 411.46 346.31 C 409.93 346.31 409.16 345.3 409.16 343.28 C 409.16 342.28 409.36 341.51 409.75 340.99 C 410.15 340.47 410.73 340.21 411.49 340.21 C 411.86 340.21 412.23 340.29 412.6 340.44 L 412.6 341.12 C 412.22 341 411.88 340.94 411.59 340.94 C 411.05 340.94 410.66 341.12 410.41 341.47 C 410.16 341.82 410.04 342.39 410.04 343.17 L 410.04 343.37 C 410.04 344.15 410.16 344.71 410.4 345.06 C 410.64 345.41 411.02 345.58 411.55 345.58 Z M 434.78 364.58 C 434.78 366.44 433.27 367.95 431.41 367.95 L 407.99 367.95 C 406.13 367.95 404.62 366.44 404.62 364.58 L 404.62 341.16 C 404.62 339.3 406.13 337.79 407.99 337.79 L 431.41 337.79 C 433.27 337.79 434.78 339.3 434.78 341.16 Z M 431.41 336.27 L 407.99 336.27 C 405.29 336.27 403.1 338.46 403.1 341.16 L 403.1 364.58 C 403.1 367.28 405.29 369.47 407.99 369.47 L 431.41 369.47 C 434.11 369.47 436.3 367.28 436.3 364.58 L 436.3 341.16 C 436.3 338.46 434.11 336.27 431.41 336.27 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 376px; margin-left: 420px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Primary
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="420" y="388" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Prima...
                </text>
            </switch>
        </g>
        <rect x="403.1" y="178.16" width="33.2" height="33.2" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 427.28 182.17 L 430.3 182.17 L 430.3 182.84 L 428.12 182.84 L 428.12 184.72 L 429.92 184.72 L 429.92 185.38 L 428.12 185.38 L 428.12 187.45 L 430.3 187.45 L 430.3 188.12 L 427.28 188.12 Z M 425.16 182.17 L 426 182.17 L 426 188.12 L 425.16 188.12 L 425.16 185.42 L 423 185.42 L 423 188.12 L 422.17 188.12 L 422.17 182.17 L 423 182.17 L 423 184.74 L 425.16 184.74 Z M 420.13 187.48 C 420.38 187.48 420.76 187.41 421.24 187.26 L 421.24 187.94 C 420.87 188.12 420.47 188.21 420.03 188.21 C 418.5 188.21 417.73 187.2 417.73 185.17 C 417.73 184.17 417.93 183.41 418.32 182.89 C 418.72 182.37 419.3 182.11 420.07 182.11 C 420.43 182.11 420.8 182.18 421.17 182.34 L 421.17 183.02 C 420.79 182.9 420.45 182.84 420.16 182.84 C 419.62 182.84 419.23 183.01 418.98 183.36 C 418.73 183.72 418.61 184.28 418.61 185.06 L 418.61 185.26 C 418.61 186.04 418.73 186.61 418.97 186.95 C 419.21 187.3 419.59 187.48 420.13 187.48 Z M 414.44 185.88 L 415.18 182.97 L 415.91 185.88 Z M 414.75 182.17 L 413.03 188.12 L 413.88 188.12 L 414.28 186.52 L 416.08 186.52 L 416.49 188.12 L 417.36 188.12 L 415.65 182.17 Z M 411.55 187.48 C 411.81 187.48 412.18 187.41 412.67 187.26 L 412.67 187.94 C 412.3 188.12 411.89 188.21 411.46 188.21 C 409.93 188.21 409.16 187.2 409.16 185.17 C 409.16 184.17 409.36 183.41 409.75 182.89 C 410.15 182.37 410.73 182.11 411.49 182.11 C 411.86 182.11 412.23 182.18 412.6 182.34 L 412.6 183.02 C 412.22 182.9 411.88 182.84 411.59 182.84 C 411.05 182.84 410.66 183.01 410.41 183.36 C 410.16 183.72 410.04 184.28 410.04 185.06 L 410.04 185.26 C 410.04 186.04 410.16 186.61 410.4 186.95 C 410.64 187.3 411.02 187.48 411.55 187.48 Z M 434.78 206.47 C 434.78 208.33 433.27 209.85 431.41 209.85 L 407.99 209.85 C 406.13 209.85 404.62 208.33 404.62 206.47 L 404.62 183.05 C 404.62 181.19 406.13 179.68 407.99 179.68 L 431.41 179.68 C 433.27 179.68 434.78 181.19 434.78 183.05 Z M 431.41 178.16 L 407.99 178.16 C 405.29 178.16 403.1 180.36 403.1 183.05 L 403.1 206.47 C 403.1 209.17 405.29 211.36 407.99 211.36 L 431.41 211.36 C 434.11 211.36 436.3 209.17 436.3 206.47 L 436.3 183.05 C 436.3 180.36 434.11 178.16 431.41 178.16 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 218px; margin-left: 420px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Replica
                                <br/>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="420" y="230" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Repli...
                </text>
            </switch>
        </g>
        <rect x="649.8" y="517.68" width="33.2" height="33.2" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 673.98 521.69 L 677 521.69 L 677 522.36 L 674.82 522.36 L 674.82 524.23 L 676.62 524.23 L 676.62 524.9 L 674.82 524.9 L 674.82 526.96 L 677 526.96 L 677 527.63 L 673.98 527.63 Z M 671.86 521.69 L 672.7 521.69 L 672.7 527.63 L 671.86 527.63 L 671.86 524.93 L 669.7 524.93 L 669.7 527.63 L 668.87 527.63 L 668.87 521.69 L 669.7 521.69 L 669.7 524.25 L 671.86 524.25 Z M 666.83 526.99 C 667.08 526.99 667.46 526.92 667.94 526.78 L 667.94 527.45 C 667.57 527.63 667.17 527.72 666.73 527.72 C 665.2 527.72 664.43 526.71 664.43 524.68 C 664.43 523.68 664.63 522.92 665.02 522.4 C 665.42 521.88 666 521.62 666.77 521.62 C 667.13 521.62 667.5 521.7 667.87 521.85 L 667.87 522.53 C 667.49 522.41 667.15 522.35 666.86 522.35 C 666.32 522.35 665.93 522.52 665.68 522.88 C 665.43 523.23 665.31 523.79 665.31 524.57 L 665.31 524.78 C 665.31 525.55 665.43 526.12 665.67 526.47 C 665.91 526.82 666.29 526.99 666.83 526.99 Z M 661.14 525.39 L 661.88 522.49 L 662.61 525.39 Z M 661.45 521.69 L 659.73 527.63 L 660.58 527.63 L 660.98 526.03 L 662.78 526.03 L 663.19 527.63 L 664.06 527.63 L 662.35 521.69 Z M 658.25 526.99 C 658.51 526.99 658.88 526.92 659.37 526.78 L 659.37 527.45 C 659 527.63 658.59 527.72 658.16 527.72 C 656.63 527.72 655.86 526.71 655.86 524.68 C 655.86 523.68 656.06 522.92 656.45 522.4 C 656.85 521.88 657.43 521.62 658.19 521.62 C 658.56 521.62 658.93 521.7 659.3 521.85 L 659.3 522.53 C 658.92 522.41 658.58 522.35 658.29 522.35 C 657.75 522.35 657.36 522.52 657.11 522.88 C 656.86 523.23 656.74 523.79 656.74 524.57 L 656.74 524.78 C 656.74 525.55 656.86 526.12 657.1 526.47 C 657.34 526.82 657.72 526.99 658.25 526.99 Z M 681.48 545.99 C 681.48 547.85 679.97 549.36 678.11 549.36 L 654.69 549.36 C 652.83 549.36 651.32 547.85 651.32 545.99 L 651.32 522.57 C 651.32 520.71 652.83 519.19 654.69 519.19 L 678.11 519.19 C 679.97 519.19 681.48 520.71 681.48 522.57 Z M 678.11 517.68 L 654.69 517.68 C 651.99 517.68 649.8 519.87 649.8 522.57 L 649.8 545.99 C 649.8 548.68 651.99 550.88 654.69 550.88 L 678.11 550.88 C 680.81 550.88 683 548.68 683 545.99 L 683 522.57 C 683 519.87 680.81 517.68 678.11 517.68 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 558px; margin-left: 666px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Primary
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="666" y="570" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Prima...
                </text>
            </switch>
        </g>
        <rect x="649.8" y="336.27" width="33.2" height="33.2" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 673.98 340.28 L 677 340.28 L 677 340.95 L 674.82 340.95 L 674.82 342.82 L 676.62 342.82 L 676.62 343.49 L 674.82 343.49 L 674.82 345.56 L 677 345.56 L 677 346.23 L 673.98 346.23 Z M 671.86 340.28 L 672.7 340.28 L 672.7 346.23 L 671.86 346.23 L 671.86 343.52 L 669.7 343.52 L 669.7 346.23 L 668.87 346.23 L 668.87 340.28 L 669.7 340.28 L 669.7 342.85 L 671.86 342.85 Z M 666.83 345.58 C 667.08 345.58 667.46 345.51 667.94 345.37 L 667.94 346.05 C 667.57 346.23 667.17 346.31 666.73 346.31 C 665.2 346.31 664.43 345.3 664.43 343.28 C 664.43 342.28 664.63 341.51 665.02 340.99 C 665.42 340.47 666 340.21 666.77 340.21 C 667.13 340.21 667.5 340.29 667.87 340.44 L 667.87 341.12 C 667.49 341 667.15 340.94 666.86 340.94 C 666.32 340.94 665.93 341.12 665.68 341.47 C 665.43 341.82 665.31 342.39 665.31 343.17 L 665.31 343.37 C 665.31 344.15 665.43 344.71 665.67 345.06 C 665.91 345.41 666.29 345.58 666.83 345.58 Z M 661.14 343.99 L 661.88 341.08 L 662.61 343.99 Z M 661.45 340.28 L 659.73 346.23 L 660.58 346.23 L 660.98 344.62 L 662.78 344.62 L 663.19 346.23 L 664.06 346.23 L 662.35 340.28 Z M 658.25 345.58 C 658.51 345.58 658.88 345.51 659.37 345.37 L 659.37 346.05 C 659 346.23 658.59 346.31 658.16 346.31 C 656.63 346.31 655.86 345.3 655.86 343.28 C 655.86 342.28 656.06 341.51 656.45 340.99 C 656.85 340.47 657.43 340.21 658.19 340.21 C 658.56 340.21 658.93 340.29 659.3 340.44 L 659.3 341.12 C 658.92 341 658.58 340.94 658.29 340.94 C 657.75 340.94 657.36 341.12 657.11 341.47 C 656.86 341.82 656.74 342.39 656.74 343.17 L 656.74 343.37 C 656.74 344.15 656.86 344.71 657.1 345.06 C 657.34 345.41 657.72 345.58 658.25 345.58 Z M 681.48 364.58 C 681.48 366.44 679.97 367.95 678.11 367.95 L 654.69 367.95 C 652.83 367.95 651.32 366.44 651.32 364.58 L 651.32 341.16 C 651.32 339.3 652.83 337.79 654.69 337.79 L 678.11 337.79 C 679.97 337.79 681.48 339.3 681.48 341.16 Z M 678.11 336.27 L 654.69 336.27 C 651.99 336.27 649.8 338.46 649.8 341.16 L 649.8 364.58 C 649.8 367.28 651.99 369.47 654.69 369.47 L 678.11 369.47 C 680.81 369.47 683 367.28 683 364.58 L 683 341.16 C 683 338.46 680.81 336.27 678.11 336.27 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 376px; margin-left: 666px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Replica
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="666" y="388" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Repli...
                </text>
            </switch>
        </g>
        <rect x="649.8" y="178.16" width="33.2" height="33.2" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 673.98 182.17 L 677 182.17 L 677 182.84 L 674.82 182.84 L 674.82 184.72 L 676.62 184.72 L 676.62 185.38 L 674.82 185.38 L 674.82 187.45 L 677 187.45 L 677 188.12 L 673.98 188.12 Z M 671.86 182.17 L 672.7 182.17 L 672.7 188.12 L 671.86 188.12 L 671.86 185.42 L 669.7 185.42 L 669.7 188.12 L 668.87 188.12 L 668.87 182.17 L 669.7 182.17 L 669.7 184.74 L 671.86 184.74 Z M 666.83 187.48 C 667.08 187.48 667.46 187.41 667.94 187.26 L 667.94 187.94 C 667.57 188.12 667.17 188.21 666.73 188.21 C 665.2 188.21 664.43 187.2 664.43 185.17 C 664.43 184.17 664.63 183.41 665.02 182.89 C 665.42 182.37 666 182.11 666.77 182.11 C 667.13 182.11 667.5 182.18 667.87 182.34 L 667.87 183.02 C 667.49 182.9 667.15 182.84 666.86 182.84 C 666.32 182.84 665.93 183.01 665.68 183.36 C 665.43 183.72 665.31 184.28 665.31 185.06 L 665.31 185.26 C 665.31 186.04 665.43 186.61 665.67 186.95 C 665.91 187.3 666.29 187.48 666.83 187.48 Z M 661.14 185.88 L 661.88 182.97 L 662.61 185.88 Z M 661.45 182.17 L 659.73 188.12 L 660.58 188.12 L 660.98 186.52 L 662.78 186.52 L 663.19 188.12 L 664.06 188.12 L 662.35 182.17 Z M 658.25 187.48 C 658.51 187.48 658.88 187.41 659.37 187.26 L 659.37 187.94 C 659 188.12 658.59 188.21 658.16 188.21 C 656.63 188.21 655.86 187.2 655.86 185.17 C 655.86 184.17 656.06 183.41 656.45 182.89 C 656.85 182.37 657.43 182.11 658.19 182.11 C 658.56 182.11 658.93 182.18 659.3 182.34 L 659.3 183.02 C 658.92 182.9 658.58 182.84 658.29 182.84 C 657.75 182.84 657.36 183.01 657.11 183.36 C 656.86 183.72 656.74 184.28 656.74 185.06 L 656.74 185.26 C 656.74 186.04 656.86 186.61 657.1 186.95 C 657.34 187.3 657.72 187.48 658.25 187.48 Z M 681.48 206.47 C 681.48 208.33 679.97 209.85 678.11 209.85 L 654.69 209.85 C 652.83 209.85 651.32 208.33 651.32 206.47 L 651.32 183.05 C 651.32 181.19 652.83 179.68 654.69 179.68 L 678.11 179.68 C 679.97 179.68 681.48 181.19 681.48 183.05 Z M 678.11 178.16 L 654.69 178.16 C 651.99 178.16 649.8 180.36 649.8 183.05 L 649.8 206.47 C 649.8 209.17 651.99 211.36 654.69 211.36 L 678.11 211.36 C 680.81 211.36 683 209.17 683 206.47 L 683 183.05 C 683 180.36 680.81 178.16 678.11 178.16 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 218px; margin-left: 666px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Replica
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="666" y="230" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Repli...
                </text>
            </switch>
        </g>
        <path d="M 157.23 192.32 L 170.51 192.32 L 170.51 179 L 177.15 179 L 177.15 192.32 L 190.43 192.32 L 190.43 198.96 L 177.15 198.96 L 177.15 212.28 L 170.51 212.28 L 170.51 198.96 L 157.23 198.96 Z" fill="#e51400" stroke="#b20000" stroke-miterlimit="10" transform="rotate(-45,173.83,195.64)" pointer-events="all"/>
        <rect x="134.47" y="154.01" width="80" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 169px; margin-left: 174px;">
                        <div data-drawio-colors="color: #FF3333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 51, 51); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <font color="#000000" style="font-size: 12px;">
                                    シャードA
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="174" y="173" fill="#FF3333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャードA
                </text>
            </switch>
        </g>
        <rect x="379" y="154.01" width="80" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 169px; margin-left: 419px;">
                        <div data-drawio-colors="color: #FF3333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 51, 51); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <font color="#000000" style="font-size: 12px;">
                                    シャードB
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="419" y="173" fill="#FF3333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャードB
                </text>
            </switch>
        </g>
        <rect x="624" y="154.01" width="80" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 169px; margin-left: 664px;">
                        <div data-drawio-colors="color: #FF3333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 51, 51); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <font color="#000000" style="font-size: 12px;">
                                    シャードC
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="664" y="173" fill="#FF3333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャードC
                </text>
            </switch>
        </g>
        <rect x="312" y="11" width="220" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 31px; margin-left: 422px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <font style="font-size: 18px;">
                                    プライマリノード障害時
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="422" y="35" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    プライマリノード障害時
                </text>
            </switch>
        </g>
        <rect x="896.52" y="75.81" width="763.6" height="175.58" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="none"/>
        <g fill="#147EBA" font-family="Helvetica" pointer-events="none" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="1226" y="83" width="106" height="15" stroke-width="0"/>
            <text x="1277.82" y="93.31">
                Availability Zone 1a
            </text>
        </g>
        <path d="M 860 60 L 1690 60 L 1690 640 L 860 640 Z" fill="none" stroke="#248814" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 870.59 66.65 C 870.53 66.65 870.48 66.65 870.42 66.65 L 870.42 66.65 C 869.11 66.68 868.03 67.24 867.14 68.25 C 867.13 68.25 867.13 68.25 867.13 68.25 C 866.2 69.36 865.87 70.52 865.96 71.73 C 864.81 72.06 864.12 72.92 863.76 73.74 C 863.75 73.75 863.75 73.76 863.74 73.78 C 863.33 75.05 863.68 76.36 864.24 77.16 C 864.25 77.17 864.25 77.17 864.26 77.18 C 864.94 78.05 865.97 78.53 867.02 78.53 L 878.17 78.53 C 879.19 78.53 880.07 78.16 880.8 77.37 C 881.25 76.94 881.49 76.29 881.58 75.59 C 881.67 74.9 881.61 74.16 881.32 73.55 C 881.31 73.54 881.31 73.53 881.31 73.52 C 880.8 72.62 879.95 71.81 878.76 71.64 C 878.74 70.79 878.28 69.99 877.68 69.56 C 877.67 69.55 877.66 69.55 877.65 69.54 C 877.01 69.18 876.4 69.14 875.91 69.3 C 875.6 69.4 875.36 69.56 875.14 69.74 C 874.51 68.36 873.43 67.18 871.81 66.79 C 871.81 66.79 871.81 66.79 871.81 66.79 C 871.38 66.7 870.97 66.65 870.59 66.65 Z M 870.43 67.38 C 870.8 67.38 871.2 67.43 871.64 67.53 C 873.16 67.89 874.15 69.07 874.66 70.48 C 874.71 70.6 874.81 70.69 874.94 70.72 C 875.07 70.74 875.2 70.7 875.29 70.61 C 875.54 70.34 875.83 70.11 876.14 70.01 C 876.44 69.91 876.78 69.92 877.26 70.18 C 877.67 70.49 878.11 71.31 878.03 71.9 C 878.01 72.01 878.05 72.12 878.12 72.2 C 878.19 72.28 878.29 72.33 878.39 72.33 C 879.46 72.34 880.16 73.02 880.64 73.88 C 880.85 74.3 880.91 74.92 880.84 75.5 C 880.76 76.07 880.53 76.59 880.28 76.83 C 880.27 76.84 880.27 76.85 880.26 76.85 C 879.65 77.53 879.03 77.78 878.17 77.78 L 867.02 77.78 C 866.2 77.78 865.39 77.41 864.85 76.73 C 864.44 76.13 864.14 75.02 864.46 74.02 C 864.79 73.27 865.36 72.55 866.41 72.36 C 866.6 72.32 866.74 72.14 866.71 71.94 C 866.56 70.79 866.8 69.81 867.7 68.74 C 868.49 67.85 869.33 67.39 870.43 67.38 Z M 872.2 70.7 C 871.77 70.7 871.4 70.93 871.13 71.21 C 870.85 71.5 870.64 71.85 870.64 72.25 L 870.64 72.71 L 870.14 72.71 C 870.04 72.71 869.94 72.75 869.87 72.82 C 869.8 72.89 869.76 72.98 869.76 73.08 L 869.76 75.7 C 869.76 75.8 869.8 75.89 869.87 75.96 C 869.94 76.03 870.04 76.07 870.14 76.07 L 874.16 76.07 C 874.26 76.07 874.35 76.03 874.42 75.96 C 874.49 75.89 874.53 75.8 874.53 75.7 L 874.53 73.08 C 874.53 72.98 874.49 72.89 874.42 72.82 C 874.35 72.75 874.26 72.71 874.16 72.71 L 873.68 72.71 L 873.68 72.25 C 873.68 71.84 873.47 71.47 873.21 71.2 C 872.94 70.92 872.61 70.7 872.2 70.7 Z M 872.2 71.45 C 872.29 71.45 872.5 71.54 872.67 71.72 C 872.83 71.89 872.93 72.11 872.93 72.25 L 872.93 72.71 L 871.39 72.71 L 871.39 72.25 C 871.39 72.15 871.49 71.91 871.66 71.74 C 871.83 71.56 872.06 71.45 872.2 71.45 Z M 870.51 73.46 L 873.78 73.46 L 873.78 75.32 L 870.51 75.32 Z M 860 85 L 860 60 L 885 60 L 885 85 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 798px; height: 1px; padding-top: 67px; margin-left: 892px;">
                        <div data-drawio-colors="color: #AAB7B8; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(170, 183, 184); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                VPC
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="892" y="79" fill="#AAB7B8" font-family="Helvetica" font-size="12px">
                    VPC
                </text>
            </switch>
        </g>
        <path d="M 916.03 95.37 L 1128.92 95.37 L 1128.92 242.65 L 916.03 242.65 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 916.03 95.37 L 941.03 95.37 L 941.03 120.37 L 916.03 120.37 Z M 928.55 98.58 C 927.42 98.57 926.34 98.99 925.51 99.75 C 924.7 100.48 924.23 101.52 924.23 102.61 L 924.23 105.14 L 921.92 105.14 C 921.82 105.14 921.73 105.18 921.66 105.25 C 921.6 105.32 921.56 105.41 921.56 105.5 L 921.56 116.79 C 921.56 116.99 921.72 117.15 921.92 117.15 L 935.13 117.15 C 935.33 117.15 935.49 116.99 935.49 116.79 L 935.49 105.52 C 935.49 105.42 935.46 105.33 935.39 105.27 C 935.32 105.2 935.23 105.16 935.14 105.16 L 932.84 105.16 L 932.84 102.66 C 932.83 101.57 932.37 100.54 931.58 99.8 C 930.76 99.02 929.68 98.58 928.55 98.58 Z M 928.54 99.29 C 929.49 99.29 930.4 99.65 931.09 100.29 C 931.75 100.9 932.12 101.76 932.12 102.66 L 932.12 105.16 L 924.9 105.16 L 924.92 102.62 C 924.92 101.73 925.31 100.87 925.98 100.27 C 926.68 99.63 927.59 99.29 928.54 99.29 Z M 922.27 105.87 L 934.78 105.87 L 934.78 116.44 L 922.27 116.44 Z M 928.53 108.11 C 927.5 108.09 926.63 108.87 926.54 109.9 C 926.44 110.92 927.15 111.85 928.17 112.02 L 928.17 114.81 L 928.88 114.81 L 928.88 112.03 C 929.82 111.86 930.5 111.04 930.5 110.09 C 930.5 109 929.62 108.11 928.53 108.11 Z M 928.42 108.82 C 928.46 108.82 928.49 108.82 928.53 108.82 C 928.87 108.82 929.19 108.96 929.43 109.19 C 929.66 109.43 929.79 109.75 929.79 110.09 C 929.79 110.42 929.66 110.74 929.43 110.98 C 929.19 111.22 928.87 111.35 928.53 111.34 C 928.07 111.38 927.62 111.17 927.36 110.78 C 927.11 110.39 927.09 109.9 927.31 109.49 C 927.53 109.08 927.95 108.82 928.42 108.82 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 181px; height: 1px; padding-top: 102px; margin-left: 948px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="948" y="114" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 1162.12 95.37 L 1375.01 95.37 L 1375.01 242.65 L 1162.12 242.65 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 1162.12 95.37 L 1187.12 95.37 L 1187.12 120.37 L 1162.12 120.37 Z M 1174.64 98.58 C 1173.52 98.57 1172.43 98.99 1171.6 99.75 C 1170.79 100.48 1170.32 101.52 1170.32 102.61 L 1170.32 105.14 L 1168.01 105.14 C 1167.92 105.14 1167.82 105.18 1167.76 105.25 C 1167.69 105.32 1167.66 105.41 1167.66 105.5 L 1167.66 116.79 C 1167.66 116.99 1167.82 117.15 1168.01 117.15 L 1181.23 117.15 C 1181.42 117.15 1181.58 116.99 1181.58 116.79 L 1181.58 105.52 C 1181.59 105.42 1181.55 105.33 1181.48 105.27 C 1181.42 105.2 1181.33 105.16 1181.23 105.16 L 1178.93 105.16 L 1178.93 102.66 C 1178.92 101.57 1178.47 100.54 1177.68 99.8 C 1176.86 99.02 1175.77 98.58 1174.64 98.58 Z M 1174.63 99.29 C 1175.58 99.29 1176.49 99.65 1177.18 100.29 C 1177.84 100.9 1178.22 101.76 1178.22 102.66 L 1178.22 105.16 L 1171 105.16 L 1171.01 102.62 C 1171.02 101.73 1171.4 100.87 1172.07 100.27 C 1172.77 99.63 1173.69 99.29 1174.63 99.29 Z M 1168.36 105.87 L 1180.88 105.87 L 1180.87 116.44 L 1168.36 116.44 Z M 1174.63 108.11 C 1173.6 108.09 1172.73 108.87 1172.63 109.9 C 1172.54 110.92 1173.25 111.85 1174.26 112.02 L 1174.26 114.81 L 1174.98 114.81 L 1174.98 112.03 C 1175.91 111.86 1176.59 111.04 1176.6 110.09 C 1176.6 109 1175.72 108.11 1174.63 108.11 Z M 1174.51 108.82 C 1174.55 108.82 1174.59 108.82 1174.63 108.82 C 1174.96 108.82 1175.28 108.96 1175.52 109.19 C 1175.76 109.43 1175.89 109.75 1175.88 110.09 C 1175.89 110.42 1175.76 110.74 1175.52 110.98 C 1175.28 111.22 1174.96 111.35 1174.63 111.34 C 1174.16 111.38 1173.72 111.17 1173.46 110.78 C 1173.2 110.39 1173.18 109.9 1173.4 109.49 C 1173.62 109.08 1174.05 108.82 1174.51 108.82 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 181px; height: 1px; padding-top: 102px; margin-left: 1194px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1194" y="114" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 1411.12 95.37 L 1624.01 95.37 L 1624.01 242.65 L 1411.12 242.65 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 1411.12 95.37 L 1436.12 95.37 L 1436.12 120.37 L 1411.12 120.37 Z M 1423.64 98.58 C 1422.52 98.57 1421.43 98.99 1420.6 99.75 C 1419.79 100.48 1419.32 101.52 1419.32 102.61 L 1419.32 105.14 L 1417.01 105.14 C 1416.92 105.14 1416.82 105.18 1416.76 105.25 C 1416.69 105.32 1416.66 105.41 1416.66 105.5 L 1416.66 116.79 C 1416.66 116.99 1416.82 117.15 1417.01 117.15 L 1430.23 117.15 C 1430.42 117.15 1430.58 116.99 1430.58 116.79 L 1430.58 105.52 C 1430.59 105.42 1430.55 105.33 1430.48 105.27 C 1430.42 105.2 1430.33 105.16 1430.23 105.16 L 1427.93 105.16 L 1427.93 102.66 C 1427.92 101.57 1427.47 100.54 1426.68 99.8 C 1425.86 99.02 1424.77 98.58 1423.64 98.58 Z M 1423.63 99.29 C 1424.58 99.29 1425.49 99.65 1426.18 100.29 C 1426.84 100.9 1427.22 101.76 1427.22 102.66 L 1427.22 105.16 L 1420 105.16 L 1420.01 102.62 C 1420.02 101.73 1420.4 100.87 1421.07 100.27 C 1421.77 99.63 1422.69 99.29 1423.63 99.29 Z M 1417.36 105.87 L 1429.88 105.87 L 1429.87 116.44 L 1417.36 116.44 Z M 1423.63 108.11 C 1422.6 108.09 1421.73 108.87 1421.63 109.9 C 1421.54 110.92 1422.25 111.85 1423.26 112.02 L 1423.26 114.81 L 1423.98 114.81 L 1423.98 112.03 C 1424.91 111.86 1425.59 111.04 1425.6 110.09 C 1425.6 109 1424.72 108.11 1423.63 108.11 Z M 1423.51 108.82 C 1423.55 108.82 1423.59 108.82 1423.63 108.82 C 1423.96 108.82 1424.28 108.96 1424.52 109.19 C 1424.76 109.43 1424.89 109.75 1424.88 110.09 C 1424.89 110.42 1424.76 110.74 1424.52 110.98 C 1424.28 111.22 1423.96 111.35 1423.63 111.34 C 1423.16 111.38 1422.72 111.17 1422.46 110.78 C 1422.2 110.39 1422.18 109.9 1422.4 109.49 C 1422.62 109.08 1423.05 108.82 1423.51 108.82 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 181px; height: 1px; padding-top: 102px; margin-left: 1443px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1443" y="114" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 943 123.24 L 976.2 123.24 L 976.2 156.44 L 943 156.44 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 964.82 149.83 L 964.82 147.05 C 963.62 147.83 961.45 148.2 959.39 148.2 C 957.14 148.2 955.39 147.8 954.38 147.13 L 954.38 149.83 C 954.38 150.63 956.26 151.46 959.39 151.46 C 962.59 151.46 964.82 150.6 964.82 149.83 Z M 959.39 143.98 C 957.14 143.98 955.39 143.59 954.38 142.91 L 954.38 145.62 C 954.4 146.42 956.27 147.25 959.39 147.25 C 962.58 147.25 964.8 146.39 964.82 145.62 L 964.82 142.84 C 963.62 143.61 961.45 143.98 959.39 143.98 Z M 964.82 141.4 L 964.82 138.2 C 963.62 138.97 961.45 139.34 959.39 139.34 C 957.14 139.34 955.39 138.95 954.38 138.27 L 954.38 141.41 C 954.4 142.21 956.27 143.03 959.39 143.03 C 962.58 143.03 964.8 142.17 964.82 141.4 Z M 954.38 136.76 C 954.38 136.76 954.38 136.76 954.38 136.76 L 954.38 136.76 L 954.38 136.77 C 954.4 137.57 956.27 138.39 959.39 138.39 C 962.87 138.39 964.8 137.43 964.82 136.76 L 964.82 136.76 L 964.82 136.76 C 964.82 136.76 964.82 136.76 964.82 136.76 C 964.82 136.09 962.89 135.12 959.39 135.12 C 956.25 135.12 954.38 135.95 954.38 136.76 Z M 965.77 136.77 L 965.77 141.4 L 965.77 141.4 C 965.77 141.4 965.77 141.4 965.77 141.41 L 965.77 145.61 L 965.77 145.61 C 965.77 145.62 965.77 145.62 965.77 145.62 L 965.77 149.83 C 965.77 151.6 962.46 152.41 959.39 152.41 C 955.77 152.41 953.43 151.4 953.43 149.83 L 953.43 145.63 C 953.43 145.62 953.43 145.62 953.43 145.61 L 953.43 145.61 L 953.43 141.41 C 953.43 141.41 953.43 141.4 953.43 141.4 L 953.43 141.4 L 953.43 136.77 C 953.43 136.77 953.43 136.76 953.43 136.76 C 953.43 135.19 955.77 134.17 959.39 134.17 C 962.46 134.17 965.77 134.98 965.77 136.76 C 965.77 136.76 965.77 136.76 965.77 136.77 Z M 972.41 130.65 C 972.67 130.65 972.88 130.44 972.88 130.18 L 972.88 127.75 C 972.88 127.49 972.67 127.27 972.41 127.27 L 946.79 127.27 C 946.53 127.27 946.32 127.49 946.32 127.75 L 946.32 130.18 C 946.32 130.44 946.53 130.65 946.79 130.65 C 947.37 130.65 947.84 131.12 947.84 131.69 C 947.84 132.27 947.37 132.74 946.79 132.74 C 946.53 132.74 946.32 132.95 946.32 133.21 L 946.32 142.93 C 946.32 143.19 946.53 143.4 946.79 143.4 L 951.54 143.4 L 951.54 142.45 L 949.17 142.45 L 949.17 141.03 L 951.54 141.03 L 951.54 140.08 L 948.69 140.08 C 948.43 140.08 948.22 140.29 948.22 140.56 L 948.22 142.45 L 947.27 142.45 L 947.27 133.63 C 948.14 133.42 948.79 132.63 948.79 131.69 C 948.79 130.76 948.14 129.97 947.27 129.76 L 947.27 128.22 L 971.93 128.22 L 971.93 129.76 C 971.06 129.97 970.41 130.76 970.41 131.69 C 970.41 132.63 971.06 133.42 971.93 133.63 L 971.93 142.45 L 970.98 142.45 L 970.98 140.56 C 970.98 140.29 970.77 140.08 970.51 140.08 L 967.66 140.08 L 967.66 141.03 L 970.03 141.03 L 970.03 142.45 L 967.66 142.45 L 967.66 143.4 L 972.41 143.4 C 972.67 143.4 972.88 143.19 972.88 142.93 L 972.88 133.21 C 972.88 132.95 972.67 132.74 972.41 132.74 C 971.83 132.74 971.36 132.27 971.36 131.69 C 971.36 131.12 971.83 130.65 972.41 130.65 Z M 953.91 133.91 L 953.91 130.12 C 953.91 129.86 953.7 129.65 953.43 129.65 L 950.59 129.65 C 950.33 129.65 950.11 129.86 950.11 130.12 L 950.11 138.18 C 950.11 138.45 950.33 138.66 950.59 138.66 L 952.01 138.66 L 952.01 137.71 L 951.06 137.71 L 951.06 130.59 L 952.96 130.59 L 952.96 133.91 Z M 968.14 137.71 L 967.66 137.71 L 967.66 138.66 L 968.61 138.66 C 968.87 138.66 969.09 138.45 969.09 138.18 L 969.09 130.12 C 969.09 129.86 968.87 129.65 968.61 129.65 L 965.77 129.65 C 965.5 129.65 965.29 129.86 965.29 130.12 L 965.29 133.91 L 966.24 133.91 L 966.24 130.59 L 968.14 130.59 Z M 964.34 133.44 L 964.34 130.12 C 964.34 129.86 964.13 129.65 963.87 129.65 L 960.55 129.65 C 960.29 129.65 960.07 129.86 960.07 130.12 L 960.07 132.97 L 961.02 132.97 L 961.02 130.59 L 963.39 130.59 L 963.39 133.44 Z M 958.18 132.97 L 958.18 130.59 L 955.81 130.59 L 955.81 133.44 L 954.86 133.44 L 954.86 130.12 C 954.86 129.86 955.07 129.65 955.33 129.65 L 958.65 129.65 C 958.91 129.65 959.13 129.86 959.13 130.12 L 959.13 132.97 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <rect x="896.52" y="261.38" width="763.6" height="178.91" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="1226" y="269" width="105" height="15" stroke-width="0"/>
            <text x="1277.82" y="278.88">
                Availability Zone 1c
            </text>
        </g>
        <path d="M 917.68 282.6 L 1130.58 282.6 L 1130.58 429.89 L 917.68 429.89 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 917.68 282.6 L 942.68 282.6 L 942.68 307.6 L 917.68 307.6 Z M 930.21 285.81 C 929.08 285.8 928 286.22 927.17 286.98 C 926.36 287.71 925.89 288.75 925.88 289.84 L 925.88 292.37 L 923.58 292.37 C 923.48 292.37 923.39 292.41 923.32 292.48 C 923.26 292.55 923.22 292.64 923.22 292.73 L 923.22 304.03 C 923.22 304.22 923.38 304.38 923.58 304.38 L 936.79 304.38 C 936.99 304.38 937.15 304.22 937.15 304.03 L 937.15 292.75 C 937.15 292.65 937.12 292.56 937.05 292.5 C 936.98 292.43 936.89 292.39 936.8 292.39 L 934.5 292.39 L 934.5 289.89 C 934.49 288.81 934.03 287.77 933.24 287.03 C 932.42 286.25 931.34 285.81 930.21 285.81 Z M 930.2 286.53 C 931.15 286.52 932.06 286.88 932.75 287.53 C 933.41 288.14 933.78 288.99 933.78 289.89 L 933.78 292.39 L 926.56 292.39 L 926.58 289.85 C 926.58 288.96 926.97 288.1 927.63 287.5 C 928.34 286.87 929.25 286.52 930.2 286.53 Z M 923.93 293.1 L 936.44 293.1 L 936.43 303.67 L 923.93 303.67 Z M 930.19 295.34 C 929.16 295.32 928.29 296.1 928.2 297.13 C 928.1 298.15 928.81 299.08 929.83 299.25 L 929.83 302.04 L 930.54 302.04 L 930.54 299.26 C 931.48 299.09 932.16 298.27 932.16 297.32 C 932.16 296.23 931.28 295.34 930.19 295.34 Z M 930.08 296.05 C 930.12 296.05 930.15 296.05 930.19 296.05 C 930.53 296.06 930.85 296.19 931.08 296.42 C 931.32 296.66 931.45 296.98 931.45 297.32 C 931.45 297.65 931.32 297.97 931.09 298.21 C 930.85 298.45 930.53 298.58 930.19 298.57 C 929.73 298.62 929.28 298.4 929.02 298.01 C 928.77 297.62 928.75 297.13 928.97 296.72 C 929.19 296.31 929.61 296.05 930.08 296.05 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 181px; height: 1px; padding-top: 290px; margin-left: 950px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="950" y="302" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 1165.44 282.6 L 1378.34 282.6 L 1378.34 429.89 L 1165.44 429.89 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 1165.44 282.6 L 1190.44 282.6 L 1190.44 307.6 L 1165.44 307.6 Z M 1177.96 285.81 C 1176.84 285.8 1175.75 286.22 1174.92 286.98 C 1174.11 287.71 1173.64 288.75 1173.64 289.84 L 1173.64 292.37 L 1171.33 292.37 C 1171.24 292.37 1171.14 292.41 1171.08 292.48 C 1171.01 292.55 1170.98 292.64 1170.98 292.73 L 1170.98 304.03 C 1170.98 304.22 1171.14 304.38 1171.33 304.38 L 1184.55 304.38 C 1184.74 304.38 1184.9 304.22 1184.9 304.03 L 1184.9 292.75 C 1184.91 292.65 1184.87 292.56 1184.8 292.5 C 1184.74 292.43 1184.65 292.39 1184.55 292.39 L 1182.25 292.39 L 1182.25 289.89 C 1182.24 288.81 1181.79 287.77 1181 287.03 C 1180.18 286.25 1179.09 285.81 1177.96 285.81 Z M 1177.95 286.53 C 1178.9 286.52 1179.81 286.88 1180.5 287.53 C 1181.16 288.14 1181.54 288.99 1181.54 289.89 L 1181.54 292.39 L 1174.32 292.39 L 1174.33 289.85 C 1174.34 288.96 1174.72 288.1 1175.39 287.5 C 1176.09 286.87 1177.01 286.52 1177.95 286.53 Z M 1171.68 293.1 L 1184.2 293.1 L 1184.19 303.67 L 1171.68 303.67 Z M 1177.95 295.34 C 1176.92 295.32 1176.05 296.1 1175.95 297.13 C 1175.86 298.15 1176.57 299.08 1177.58 299.25 L 1177.58 302.04 L 1178.3 302.04 L 1178.3 299.26 C 1179.23 299.09 1179.91 298.27 1179.92 297.32 C 1179.92 296.23 1179.04 295.34 1177.95 295.34 Z M 1177.83 296.05 C 1177.87 296.05 1177.91 296.05 1177.95 296.05 C 1178.28 296.06 1178.6 296.19 1178.84 296.42 C 1179.08 296.66 1179.21 296.98 1179.2 297.32 C 1179.21 297.65 1179.08 297.97 1178.84 298.21 C 1178.6 298.45 1178.28 298.58 1177.95 298.57 C 1177.48 298.62 1177.04 298.4 1176.78 298.01 C 1176.52 297.62 1176.5 297.13 1176.72 296.72 C 1176.94 296.31 1177.37 296.05 1177.83 296.05 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 181px; height: 1px; padding-top: 290px; margin-left: 1197px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1197" y="302" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 1412.78 282.6 L 1625.68 282.6 L 1625.68 429.89 L 1412.78 429.89 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 1412.78 282.6 L 1437.78 282.6 L 1437.78 307.6 L 1412.78 307.6 Z M 1425.3 285.81 C 1424.18 285.8 1423.09 286.22 1422.27 286.98 C 1421.45 287.71 1420.98 288.75 1420.98 289.84 L 1420.98 292.37 L 1418.67 292.37 C 1418.58 292.37 1418.48 292.41 1418.42 292.48 C 1418.35 292.55 1418.32 292.64 1418.32 292.73 L 1418.32 304.03 C 1418.32 304.22 1418.48 304.38 1418.67 304.38 L 1431.89 304.38 C 1432.08 304.38 1432.24 304.22 1432.24 304.03 L 1432.24 292.75 C 1432.25 292.65 1432.21 292.56 1432.14 292.5 C 1432.08 292.43 1431.99 292.39 1431.89 292.39 L 1429.59 292.39 L 1429.59 289.89 C 1429.58 288.81 1429.13 287.77 1428.34 287.03 C 1427.52 286.25 1426.43 285.81 1425.3 285.81 Z M 1425.29 286.53 C 1426.24 286.52 1427.15 286.88 1427.84 287.53 C 1428.5 288.14 1428.88 288.99 1428.88 289.89 L 1428.88 292.39 L 1421.66 292.39 L 1421.67 289.85 C 1421.68 288.96 1422.06 288.1 1422.73 287.5 C 1423.43 286.87 1424.35 286.52 1425.29 286.53 Z M 1419.02 293.1 L 1431.54 293.1 L 1431.53 303.67 L 1419.02 303.67 Z M 1425.29 295.34 C 1424.26 295.32 1423.39 296.1 1423.29 297.13 C 1423.2 298.15 1423.91 299.08 1424.92 299.25 L 1424.92 302.04 L 1425.64 302.04 L 1425.64 299.26 C 1426.57 299.09 1427.25 298.27 1427.26 297.32 C 1427.26 296.23 1426.38 295.34 1425.29 295.34 Z M 1425.17 296.05 C 1425.21 296.05 1425.25 296.05 1425.29 296.05 C 1425.62 296.06 1425.94 296.19 1426.18 296.42 C 1426.42 296.66 1426.55 296.98 1426.54 297.32 C 1426.55 297.65 1426.42 297.97 1426.18 298.21 C 1425.94 298.45 1425.62 298.58 1425.29 298.57 C 1424.82 298.62 1424.38 298.4 1424.12 298.01 C 1423.86 297.62 1423.84 297.13 1424.06 296.72 C 1424.28 296.31 1424.71 296.05 1425.17 296.05 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 181px; height: 1px; padding-top: 290px; margin-left: 1445px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1445" y="302" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <rect x="896.52" y="451.1" width="763.6" height="178.91" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="1226" y="459" width="106" height="15" stroke-width="0"/>
            <text x="1277.82" y="468.6">
                Availability Zone 1d
            </text>
        </g>
        <path d="M 917.68 473.16 L 1130.58 473.16 L 1130.58 620.44 L 917.68 620.44 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 917.68 473.16 L 942.68 473.16 L 942.68 498.16 L 917.68 498.16 Z M 930.21 476.37 C 929.08 476.36 928 476.78 927.17 477.54 C 926.36 478.27 925.89 479.31 925.88 480.4 L 925.88 482.93 L 923.58 482.93 C 923.48 482.93 923.39 482.97 923.32 483.04 C 923.26 483.11 923.22 483.2 923.22 483.29 L 923.22 494.58 C 923.22 494.78 923.38 494.94 923.58 494.94 L 936.79 494.94 C 936.99 494.94 937.15 494.78 937.15 494.58 L 937.15 483.31 C 937.15 483.21 937.12 483.12 937.05 483.06 C 936.98 482.99 936.89 482.95 936.8 482.95 L 934.5 482.95 L 934.5 480.45 C 934.49 479.37 934.03 478.33 933.24 477.59 C 932.42 476.81 931.34 476.37 930.21 476.37 Z M 930.2 477.08 C 931.15 477.08 932.06 477.44 932.75 478.08 C 933.41 478.7 933.78 479.55 933.78 480.45 L 933.78 482.95 L 926.56 482.95 L 926.58 480.41 C 926.58 479.52 926.97 478.66 927.63 478.06 C 928.34 477.43 929.25 477.08 930.2 477.08 Z M 923.93 483.66 L 936.44 483.66 L 936.43 494.23 L 923.93 494.23 Z M 930.19 485.9 C 929.16 485.88 928.29 486.66 928.2 487.69 C 928.1 488.71 928.81 489.64 929.83 489.81 L 929.83 492.6 L 930.54 492.6 L 930.54 489.82 C 931.48 489.65 932.16 488.83 932.16 487.88 C 932.16 486.79 931.28 485.9 930.19 485.9 Z M 930.08 486.61 C 930.12 486.61 930.15 486.61 930.19 486.61 C 930.53 486.62 930.85 486.75 931.08 486.98 C 931.32 487.22 931.45 487.54 931.45 487.88 C 931.45 488.21 931.32 488.53 931.09 488.77 C 930.85 489.01 930.53 489.14 930.19 489.13 C 929.73 489.18 929.28 488.96 929.02 488.57 C 928.77 488.18 928.75 487.69 928.97 487.28 C 929.19 486.87 929.61 486.61 930.08 486.61 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 181px; height: 1px; padding-top: 480px; margin-left: 950px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="950" y="492" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 1165.44 472.32 L 1378.34 472.32 L 1378.34 619.61 L 1165.44 619.61 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 1165.44 472.32 L 1190.44 472.32 L 1190.44 497.32 L 1165.44 497.32 Z M 1177.96 475.54 C 1176.84 475.53 1175.75 475.95 1174.92 476.71 C 1174.11 477.44 1173.64 478.48 1173.64 479.57 L 1173.64 482.1 L 1171.33 482.1 C 1171.24 482.1 1171.14 482.14 1171.08 482.21 C 1171.01 482.28 1170.98 482.37 1170.98 482.46 L 1170.98 493.75 C 1170.98 493.95 1171.14 494.11 1171.33 494.11 L 1184.55 494.11 C 1184.74 494.11 1184.9 493.95 1184.9 493.75 L 1184.9 482.47 C 1184.91 482.38 1184.87 482.29 1184.8 482.22 C 1184.74 482.15 1184.65 482.12 1184.55 482.12 L 1182.25 482.12 L 1182.25 479.62 C 1182.24 478.53 1181.79 477.5 1181 476.76 C 1180.18 475.98 1179.09 475.54 1177.96 475.54 Z M 1177.95 476.25 C 1178.9 476.25 1179.81 476.6 1180.5 477.25 C 1181.16 477.86 1181.54 478.72 1181.54 479.62 L 1181.54 482.12 L 1174.32 482.12 L 1174.33 479.58 C 1174.34 478.68 1174.72 477.83 1175.39 477.23 C 1176.09 476.59 1177.01 476.24 1177.95 476.25 Z M 1171.68 482.82 L 1184.2 482.82 L 1184.19 493.4 L 1171.68 493.4 Z M 1177.95 485.07 C 1176.92 485.05 1176.05 485.83 1175.95 486.86 C 1175.86 487.88 1176.57 488.81 1177.58 488.98 L 1177.58 491.77 L 1178.3 491.77 L 1178.3 488.99 C 1179.23 488.81 1179.91 488 1179.92 487.04 C 1179.92 485.96 1179.04 485.07 1177.95 485.07 Z M 1177.83 485.78 C 1177.87 485.78 1177.91 485.78 1177.95 485.78 C 1178.28 485.78 1178.6 485.92 1178.84 486.15 C 1179.08 486.39 1179.21 486.71 1179.2 487.04 C 1179.21 487.38 1179.08 487.7 1178.84 487.94 C 1178.6 488.17 1178.28 488.31 1177.95 488.3 C 1177.48 488.34 1177.04 488.13 1176.78 487.74 C 1176.52 487.35 1176.5 486.85 1176.72 486.45 C 1176.94 486.04 1177.37 485.78 1177.83 485.78 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 181px; height: 1px; padding-top: 479px; margin-left: 1197px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1197" y="491" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 1412.78 472.32 L 1625.68 472.32 L 1625.68 619.61 L 1412.78 619.61 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 1412.78 472.32 L 1437.78 472.32 L 1437.78 497.32 L 1412.78 497.32 Z M 1425.3 475.54 C 1424.18 475.53 1423.09 475.95 1422.27 476.71 C 1421.45 477.44 1420.98 478.48 1420.98 479.57 L 1420.98 482.1 L 1418.67 482.1 C 1418.58 482.1 1418.48 482.14 1418.42 482.21 C 1418.35 482.28 1418.32 482.37 1418.32 482.46 L 1418.32 493.75 C 1418.32 493.95 1418.48 494.11 1418.67 494.11 L 1431.89 494.11 C 1432.08 494.11 1432.24 493.95 1432.24 493.75 L 1432.24 482.47 C 1432.25 482.38 1432.21 482.29 1432.14 482.22 C 1432.08 482.15 1431.99 482.12 1431.89 482.12 L 1429.59 482.12 L 1429.59 479.62 C 1429.58 478.53 1429.13 477.5 1428.34 476.76 C 1427.52 475.98 1426.43 475.54 1425.3 475.54 Z M 1425.29 476.25 C 1426.24 476.25 1427.15 476.6 1427.84 477.25 C 1428.5 477.86 1428.88 478.72 1428.88 479.62 L 1428.88 482.12 L 1421.66 482.12 L 1421.67 479.58 C 1421.68 478.68 1422.06 477.83 1422.73 477.23 C 1423.43 476.59 1424.35 476.24 1425.29 476.25 Z M 1419.02 482.82 L 1431.54 482.82 L 1431.53 493.4 L 1419.02 493.4 Z M 1425.29 485.07 C 1424.26 485.05 1423.39 485.83 1423.29 486.86 C 1423.2 487.88 1423.91 488.81 1424.92 488.98 L 1424.92 491.77 L 1425.64 491.77 L 1425.64 488.99 C 1426.57 488.81 1427.25 488 1427.26 487.04 C 1427.26 485.96 1426.38 485.07 1425.29 485.07 Z M 1425.17 485.78 C 1425.21 485.78 1425.25 485.78 1425.29 485.78 C 1425.62 485.78 1425.94 485.92 1426.18 486.15 C 1426.42 486.39 1426.55 486.71 1426.54 487.04 C 1426.55 487.38 1426.42 487.7 1426.18 487.94 C 1425.94 488.17 1425.62 488.31 1425.29 488.3 C 1424.82 488.34 1424.38 488.13 1424.12 487.74 C 1423.86 487.35 1423.84 486.85 1424.06 486.45 C 1424.28 486.04 1424.71 485.78 1425.17 485.78 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 181px; height: 1px; padding-top: 479px; margin-left: 1445px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1445" y="491" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <rect x="943.83" y="124.07" width="664" height="485.14" fill="none" stroke="#3333ff" stroke-width="2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 612px; height: 1px; padding-top: 131px; margin-left: 996px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                クラスター
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="996" y="143" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    クラスター
                </text>
            </switch>
        </g>
        <rect x="1010.23" y="517.68" width="33.2" height="33.2" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 1034.41 521.69 L 1037.43 521.69 L 1037.43 522.36 L 1035.25 522.36 L 1035.25 524.23 L 1037.05 524.23 L 1037.05 524.9 L 1035.25 524.9 L 1035.25 526.96 L 1037.43 526.96 L 1037.43 527.63 L 1034.41 527.63 Z M 1032.29 521.69 L 1033.13 521.69 L 1033.13 527.63 L 1032.29 527.63 L 1032.29 524.93 L 1030.13 524.93 L 1030.13 527.63 L 1029.3 527.63 L 1029.3 521.69 L 1030.13 521.69 L 1030.13 524.25 L 1032.29 524.25 Z M 1027.26 526.99 C 1027.51 526.99 1027.89 526.92 1028.37 526.78 L 1028.37 527.45 C 1028 527.63 1027.6 527.72 1027.16 527.72 C 1025.63 527.72 1024.86 526.71 1024.86 524.68 C 1024.86 523.68 1025.06 522.92 1025.45 522.4 C 1025.85 521.88 1026.43 521.62 1027.2 521.62 C 1027.56 521.62 1027.93 521.7 1028.3 521.85 L 1028.3 522.53 C 1027.92 522.41 1027.58 522.35 1027.29 522.35 C 1026.75 522.35 1026.36 522.52 1026.11 522.88 C 1025.86 523.23 1025.74 523.79 1025.74 524.57 L 1025.74 524.78 C 1025.74 525.55 1025.86 526.12 1026.1 526.47 C 1026.34 526.82 1026.72 526.99 1027.26 526.99 Z M 1021.57 525.39 L 1022.31 522.49 L 1023.04 525.39 Z M 1021.88 521.69 L 1020.16 527.63 L 1021.01 527.63 L 1021.41 526.03 L 1023.21 526.03 L 1023.62 527.63 L 1024.49 527.63 L 1022.78 521.69 Z M 1018.68 526.99 C 1018.94 526.99 1019.31 526.92 1019.8 526.78 L 1019.8 527.45 C 1019.43 527.63 1019.02 527.72 1018.59 527.72 C 1017.06 527.72 1016.29 526.71 1016.29 524.68 C 1016.29 523.68 1016.49 522.92 1016.88 522.4 C 1017.28 521.88 1017.86 521.62 1018.62 521.62 C 1018.99 521.62 1019.36 521.7 1019.73 521.85 L 1019.73 522.53 C 1019.35 522.41 1019.01 522.35 1018.72 522.35 C 1018.18 522.35 1017.79 522.52 1017.54 522.88 C 1017.29 523.23 1017.17 523.79 1017.17 524.57 L 1017.17 524.78 C 1017.17 525.55 1017.29 526.12 1017.53 526.47 C 1017.77 526.82 1018.15 526.99 1018.68 526.99 Z M 1041.91 545.99 C 1041.91 547.85 1040.4 549.36 1038.54 549.36 L 1015.12 549.36 C 1013.26 549.36 1011.75 547.85 1011.75 545.99 L 1011.75 522.57 C 1011.75 520.71 1013.26 519.19 1015.12 519.19 L 1038.54 519.19 C 1040.4 519.19 1041.91 520.71 1041.91 522.57 Z M 1038.54 517.68 L 1015.12 517.68 C 1012.42 517.68 1010.23 519.87 1010.23 522.57 L 1010.23 545.99 C 1010.23 548.68 1012.42 550.88 1015.12 550.88 L 1038.54 550.88 C 1041.24 550.88 1043.43 548.68 1043.43 545.99 L 1043.43 522.57 C 1043.43 519.87 1041.24 517.68 1038.54 517.68 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 558px; margin-left: 1027px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Replica
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1027" y="570" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Repli...
                </text>
            </switch>
        </g>
        <rect x="964.09" y="158.9" width="124.5" height="424.39" fill="none" stroke="#3333ff" stroke-width="2" stroke-dasharray="6 6" pointer-events="all"/>
        <rect x="1209.3" y="158.9" width="124.5" height="424.39" fill="none" stroke="#3333ff" stroke-width="2" stroke-dasharray="6 6" pointer-events="all"/>
        <rect x="1455" y="158.9" width="124.5" height="424.39" fill="none" stroke="#3333ff" stroke-width="2" stroke-dasharray="6 6" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 73px; height: 1px; padding-top: 166px; margin-left: 1506px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <div style="">
                                    <br/>
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1542" y="178" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                </text>
            </switch>
        </g>
        <rect x="1010.23" y="317.27" width="33.2" height="33.2" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 1034.41 321.28 L 1037.43 321.28 L 1037.43 321.95 L 1035.25 321.95 L 1035.25 323.82 L 1037.05 323.82 L 1037.05 324.49 L 1035.25 324.49 L 1035.25 326.56 L 1037.43 326.56 L 1037.43 327.23 L 1034.41 327.23 Z M 1032.29 321.28 L 1033.13 321.28 L 1033.13 327.23 L 1032.29 327.23 L 1032.29 324.52 L 1030.13 324.52 L 1030.13 327.23 L 1029.3 327.23 L 1029.3 321.28 L 1030.13 321.28 L 1030.13 323.85 L 1032.29 323.85 Z M 1027.26 326.58 C 1027.51 326.58 1027.89 326.51 1028.37 326.37 L 1028.37 327.05 C 1028 327.23 1027.6 327.31 1027.16 327.31 C 1025.63 327.31 1024.86 326.3 1024.86 324.28 C 1024.86 323.28 1025.06 322.51 1025.45 321.99 C 1025.85 321.47 1026.43 321.21 1027.2 321.21 C 1027.56 321.21 1027.93 321.29 1028.3 321.44 L 1028.3 322.12 C 1027.92 322 1027.58 321.94 1027.29 321.94 C 1026.75 321.94 1026.36 322.12 1026.11 322.47 C 1025.86 322.82 1025.74 323.39 1025.74 324.17 L 1025.74 324.37 C 1025.74 325.15 1025.86 325.71 1026.1 326.06 C 1026.34 326.41 1026.72 326.58 1027.26 326.58 Z M 1021.57 324.99 L 1022.31 322.08 L 1023.04 324.99 Z M 1021.88 321.28 L 1020.16 327.23 L 1021.01 327.23 L 1021.41 325.62 L 1023.21 325.62 L 1023.62 327.23 L 1024.49 327.23 L 1022.78 321.28 Z M 1018.68 326.58 C 1018.94 326.58 1019.31 326.51 1019.8 326.37 L 1019.8 327.05 C 1019.43 327.23 1019.02 327.31 1018.59 327.31 C 1017.06 327.31 1016.29 326.3 1016.29 324.28 C 1016.29 323.28 1016.49 322.51 1016.88 321.99 C 1017.28 321.47 1017.86 321.21 1018.62 321.21 C 1018.99 321.21 1019.36 321.29 1019.73 321.44 L 1019.73 322.12 C 1019.35 322 1019.01 321.94 1018.72 321.94 C 1018.18 321.94 1017.79 322.12 1017.54 322.47 C 1017.29 322.82 1017.17 323.39 1017.17 324.17 L 1017.17 324.37 C 1017.17 325.15 1017.29 325.71 1017.53 326.06 C 1017.77 326.41 1018.15 326.58 1018.68 326.58 Z M 1041.91 345.58 C 1041.91 347.44 1040.4 348.95 1038.54 348.95 L 1015.12 348.95 C 1013.26 348.95 1011.75 347.44 1011.75 345.58 L 1011.75 322.16 C 1011.75 320.3 1013.26 318.79 1015.12 318.79 L 1038.54 318.79 C 1040.4 318.79 1041.91 320.3 1041.91 322.16 Z M 1038.54 317.27 L 1015.12 317.27 C 1012.42 317.27 1010.23 319.46 1010.23 322.16 L 1010.23 345.58 C 1010.23 348.28 1012.42 350.47 1015.12 350.47 L 1038.54 350.47 C 1041.24 350.47 1043.43 348.28 1043.43 345.58 L 1043.43 322.16 C 1043.43 319.46 1041.24 317.27 1038.54 317.27 Z" fill="#b3b3b3" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 357px; margin-left: 1027px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Replica
                                <font color="#ff3333">
                                    <b>
                                        <br/>
                                    </b>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1027" y="369" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Repli...
                </text>
            </switch>
        </g>
        <rect x="1010.23" y="178.16" width="33.2" height="33.2" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 1034.41 182.17 L 1037.43 182.17 L 1037.43 182.84 L 1035.25 182.84 L 1035.25 184.72 L 1037.05 184.72 L 1037.05 185.38 L 1035.25 185.38 L 1035.25 187.45 L 1037.43 187.45 L 1037.43 188.12 L 1034.41 188.12 Z M 1032.29 182.17 L 1033.13 182.17 L 1033.13 188.12 L 1032.29 188.12 L 1032.29 185.42 L 1030.13 185.42 L 1030.13 188.12 L 1029.3 188.12 L 1029.3 182.17 L 1030.13 182.17 L 1030.13 184.74 L 1032.29 184.74 Z M 1027.26 187.48 C 1027.51 187.48 1027.89 187.41 1028.37 187.26 L 1028.37 187.94 C 1028 188.12 1027.6 188.21 1027.16 188.21 C 1025.63 188.21 1024.86 187.2 1024.86 185.17 C 1024.86 184.17 1025.06 183.41 1025.45 182.89 C 1025.85 182.37 1026.43 182.11 1027.2 182.11 C 1027.56 182.11 1027.93 182.18 1028.3 182.34 L 1028.3 183.02 C 1027.92 182.9 1027.58 182.84 1027.29 182.84 C 1026.75 182.84 1026.36 183.01 1026.11 183.36 C 1025.86 183.72 1025.74 184.28 1025.74 185.06 L 1025.74 185.26 C 1025.74 186.04 1025.86 186.61 1026.1 186.95 C 1026.34 187.3 1026.72 187.48 1027.26 187.48 Z M 1021.57 185.88 L 1022.31 182.97 L 1023.04 185.88 Z M 1021.88 182.17 L 1020.16 188.12 L 1021.01 188.12 L 1021.41 186.52 L 1023.21 186.52 L 1023.62 188.12 L 1024.49 188.12 L 1022.78 182.17 Z M 1018.68 187.48 C 1018.94 187.48 1019.31 187.41 1019.8 187.26 L 1019.8 187.94 C 1019.43 188.12 1019.02 188.21 1018.59 188.21 C 1017.06 188.21 1016.29 187.2 1016.29 185.17 C 1016.29 184.17 1016.49 183.41 1016.88 182.89 C 1017.28 182.37 1017.86 182.11 1018.62 182.11 C 1018.99 182.11 1019.36 182.18 1019.73 182.34 L 1019.73 183.02 C 1019.35 182.9 1019.01 182.84 1018.72 182.84 C 1018.18 182.84 1017.79 183.01 1017.54 183.36 C 1017.29 183.72 1017.17 184.28 1017.17 185.06 L 1017.17 185.26 C 1017.17 186.04 1017.29 186.61 1017.53 186.95 C 1017.77 187.3 1018.15 187.48 1018.68 187.48 Z M 1041.91 206.47 C 1041.91 208.33 1040.4 209.85 1038.54 209.85 L 1015.12 209.85 C 1013.26 209.85 1011.75 208.33 1011.75 206.47 L 1011.75 183.05 C 1011.75 181.19 1013.26 179.68 1015.12 179.68 L 1038.54 179.68 C 1040.4 179.68 1041.91 181.19 1041.91 183.05 Z M 1038.54 178.16 L 1015.12 178.16 C 1012.42 178.16 1010.23 180.36 1010.23 183.05 L 1010.23 206.47 C 1010.23 209.17 1012.42 211.36 1015.12 211.36 L 1038.54 211.36 C 1041.24 211.36 1043.43 209.17 1043.43 206.47 L 1043.43 183.05 C 1043.43 180.36 1041.24 178.16 1038.54 178.16 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 218px; margin-left: 1027px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Primary
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1027" y="230" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Prima...
                </text>
            </switch>
        </g>
        <rect x="1256.1" y="517.68" width="33.2" height="33.2" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 1280.28 521.69 L 1283.3 521.69 L 1283.3 522.36 L 1281.12 522.36 L 1281.12 524.23 L 1282.92 524.23 L 1282.92 524.9 L 1281.12 524.9 L 1281.12 526.96 L 1283.3 526.96 L 1283.3 527.63 L 1280.28 527.63 Z M 1278.16 521.69 L 1279 521.69 L 1279 527.63 L 1278.16 527.63 L 1278.16 524.93 L 1276 524.93 L 1276 527.63 L 1275.17 527.63 L 1275.17 521.69 L 1276 521.69 L 1276 524.25 L 1278.16 524.25 Z M 1273.13 526.99 C 1273.38 526.99 1273.76 526.92 1274.24 526.78 L 1274.24 527.45 C 1273.87 527.63 1273.47 527.72 1273.03 527.72 C 1271.5 527.72 1270.73 526.71 1270.73 524.68 C 1270.73 523.68 1270.93 522.92 1271.32 522.4 C 1271.72 521.88 1272.3 521.62 1273.07 521.62 C 1273.43 521.62 1273.8 521.7 1274.17 521.85 L 1274.17 522.53 C 1273.79 522.41 1273.45 522.35 1273.16 522.35 C 1272.62 522.35 1272.23 522.52 1271.98 522.88 C 1271.73 523.23 1271.61 523.79 1271.61 524.57 L 1271.61 524.78 C 1271.61 525.55 1271.73 526.12 1271.97 526.47 C 1272.21 526.82 1272.59 526.99 1273.13 526.99 Z M 1267.44 525.39 L 1268.18 522.49 L 1268.91 525.39 Z M 1267.75 521.69 L 1266.03 527.63 L 1266.88 527.63 L 1267.28 526.03 L 1269.08 526.03 L 1269.49 527.63 L 1270.36 527.63 L 1268.65 521.69 Z M 1264.55 526.99 C 1264.81 526.99 1265.18 526.92 1265.67 526.78 L 1265.67 527.45 C 1265.3 527.63 1264.89 527.72 1264.46 527.72 C 1262.93 527.72 1262.16 526.71 1262.16 524.68 C 1262.16 523.68 1262.36 522.92 1262.75 522.4 C 1263.15 521.88 1263.73 521.62 1264.49 521.62 C 1264.86 521.62 1265.23 521.7 1265.6 521.85 L 1265.6 522.53 C 1265.22 522.41 1264.88 522.35 1264.59 522.35 C 1264.05 522.35 1263.66 522.52 1263.41 522.88 C 1263.16 523.23 1263.04 523.79 1263.04 524.57 L 1263.04 524.78 C 1263.04 525.55 1263.16 526.12 1263.4 526.47 C 1263.64 526.82 1264.02 526.99 1264.55 526.99 Z M 1287.78 545.99 C 1287.78 547.85 1286.27 549.36 1284.41 549.36 L 1260.99 549.36 C 1259.13 549.36 1257.62 547.85 1257.62 545.99 L 1257.62 522.57 C 1257.62 520.71 1259.13 519.19 1260.99 519.19 L 1284.41 519.19 C 1286.27 519.19 1287.78 520.71 1287.78 522.57 Z M 1284.41 517.68 L 1260.99 517.68 C 1258.29 517.68 1256.1 519.87 1256.1 522.57 L 1256.1 545.99 C 1256.1 548.68 1258.29 550.88 1260.99 550.88 L 1284.41 550.88 C 1287.11 550.88 1289.3 548.68 1289.3 545.99 L 1289.3 522.57 C 1289.3 519.87 1287.11 517.68 1284.41 517.68 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 558px; margin-left: 1273px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Replica
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1273" y="570" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Repli...
                </text>
            </switch>
        </g>
        <rect x="1256.1" y="336.27" width="33.2" height="33.2" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 1280.28 340.28 L 1283.3 340.28 L 1283.3 340.95 L 1281.12 340.95 L 1281.12 342.82 L 1282.92 342.82 L 1282.92 343.49 L 1281.12 343.49 L 1281.12 345.56 L 1283.3 345.56 L 1283.3 346.23 L 1280.28 346.23 Z M 1278.16 340.28 L 1279 340.28 L 1279 346.23 L 1278.16 346.23 L 1278.16 343.52 L 1276 343.52 L 1276 346.23 L 1275.17 346.23 L 1275.17 340.28 L 1276 340.28 L 1276 342.85 L 1278.16 342.85 Z M 1273.13 345.58 C 1273.38 345.58 1273.76 345.51 1274.24 345.37 L 1274.24 346.05 C 1273.87 346.23 1273.47 346.31 1273.03 346.31 C 1271.5 346.31 1270.73 345.3 1270.73 343.28 C 1270.73 342.28 1270.93 341.51 1271.32 340.99 C 1271.72 340.47 1272.3 340.21 1273.07 340.21 C 1273.43 340.21 1273.8 340.29 1274.17 340.44 L 1274.17 341.12 C 1273.79 341 1273.45 340.94 1273.16 340.94 C 1272.62 340.94 1272.23 341.12 1271.98 341.47 C 1271.73 341.82 1271.61 342.39 1271.61 343.17 L 1271.61 343.37 C 1271.61 344.15 1271.73 344.71 1271.97 345.06 C 1272.21 345.41 1272.59 345.58 1273.13 345.58 Z M 1267.44 343.99 L 1268.18 341.08 L 1268.91 343.99 Z M 1267.75 340.28 L 1266.03 346.23 L 1266.88 346.23 L 1267.28 344.62 L 1269.08 344.62 L 1269.49 346.23 L 1270.36 346.23 L 1268.65 340.28 Z M 1264.55 345.58 C 1264.81 345.58 1265.18 345.51 1265.67 345.37 L 1265.67 346.05 C 1265.3 346.23 1264.89 346.31 1264.46 346.31 C 1262.93 346.31 1262.16 345.3 1262.16 343.28 C 1262.16 342.28 1262.36 341.51 1262.75 340.99 C 1263.15 340.47 1263.73 340.21 1264.49 340.21 C 1264.86 340.21 1265.23 340.29 1265.6 340.44 L 1265.6 341.12 C 1265.22 341 1264.88 340.94 1264.59 340.94 C 1264.05 340.94 1263.66 341.12 1263.41 341.47 C 1263.16 341.82 1263.04 342.39 1263.04 343.17 L 1263.04 343.37 C 1263.04 344.15 1263.16 344.71 1263.4 345.06 C 1263.64 345.41 1264.02 345.58 1264.55 345.58 Z M 1287.78 364.58 C 1287.78 366.44 1286.27 367.95 1284.41 367.95 L 1260.99 367.95 C 1259.13 367.95 1257.62 366.44 1257.62 364.58 L 1257.62 341.16 C 1257.62 339.3 1259.13 337.79 1260.99 337.79 L 1284.41 337.79 C 1286.27 337.79 1287.78 339.3 1287.78 341.16 Z M 1284.41 336.27 L 1260.99 336.27 C 1258.29 336.27 1256.1 338.46 1256.1 341.16 L 1256.1 364.58 C 1256.1 367.28 1258.29 369.47 1260.99 369.47 L 1284.41 369.47 C 1287.11 369.47 1289.3 367.28 1289.3 364.58 L 1289.3 341.16 C 1289.3 338.46 1287.11 336.27 1284.41 336.27 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 376px; margin-left: 1273px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Primary
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1273" y="388" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Prima...
                </text>
            </switch>
        </g>
        <rect x="1256.1" y="178.16" width="33.2" height="33.2" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 1280.28 182.17 L 1283.3 182.17 L 1283.3 182.84 L 1281.12 182.84 L 1281.12 184.72 L 1282.92 184.72 L 1282.92 185.38 L 1281.12 185.38 L 1281.12 187.45 L 1283.3 187.45 L 1283.3 188.12 L 1280.28 188.12 Z M 1278.16 182.17 L 1279 182.17 L 1279 188.12 L 1278.16 188.12 L 1278.16 185.42 L 1276 185.42 L 1276 188.12 L 1275.17 188.12 L 1275.17 182.17 L 1276 182.17 L 1276 184.74 L 1278.16 184.74 Z M 1273.13 187.48 C 1273.38 187.48 1273.76 187.41 1274.24 187.26 L 1274.24 187.94 C 1273.87 188.12 1273.47 188.21 1273.03 188.21 C 1271.5 188.21 1270.73 187.2 1270.73 185.17 C 1270.73 184.17 1270.93 183.41 1271.32 182.89 C 1271.72 182.37 1272.3 182.11 1273.07 182.11 C 1273.43 182.11 1273.8 182.18 1274.17 182.34 L 1274.17 183.02 C 1273.79 182.9 1273.45 182.84 1273.16 182.84 C 1272.62 182.84 1272.23 183.01 1271.98 183.36 C 1271.73 183.72 1271.61 184.28 1271.61 185.06 L 1271.61 185.26 C 1271.61 186.04 1271.73 186.61 1271.97 186.95 C 1272.21 187.3 1272.59 187.48 1273.13 187.48 Z M 1267.44 185.88 L 1268.18 182.97 L 1268.91 185.88 Z M 1267.75 182.17 L 1266.03 188.12 L 1266.88 188.12 L 1267.28 186.52 L 1269.08 186.52 L 1269.49 188.12 L 1270.36 188.12 L 1268.65 182.17 Z M 1264.55 187.48 C 1264.81 187.48 1265.18 187.41 1265.67 187.26 L 1265.67 187.94 C 1265.3 188.12 1264.89 188.21 1264.46 188.21 C 1262.93 188.21 1262.16 187.2 1262.16 185.17 C 1262.16 184.17 1262.36 183.41 1262.75 182.89 C 1263.15 182.37 1263.73 182.11 1264.49 182.11 C 1264.86 182.11 1265.23 182.18 1265.6 182.34 L 1265.6 183.02 C 1265.22 182.9 1264.88 182.84 1264.59 182.84 C 1264.05 182.84 1263.66 183.01 1263.41 183.36 C 1263.16 183.72 1263.04 184.28 1263.04 185.06 L 1263.04 185.26 C 1263.04 186.04 1263.16 186.61 1263.4 186.95 C 1263.64 187.3 1264.02 187.48 1264.55 187.48 Z M 1287.78 206.47 C 1287.78 208.33 1286.27 209.85 1284.41 209.85 L 1260.99 209.85 C 1259.13 209.85 1257.62 208.33 1257.62 206.47 L 1257.62 183.05 C 1257.62 181.19 1259.13 179.68 1260.99 179.68 L 1284.41 179.68 C 1286.27 179.68 1287.78 181.19 1287.78 183.05 Z M 1284.41 178.16 L 1260.99 178.16 C 1258.29 178.16 1256.1 180.36 1256.1 183.05 L 1256.1 206.47 C 1256.1 209.17 1258.29 211.36 1260.99 211.36 L 1284.41 211.36 C 1287.11 211.36 1289.3 209.17 1289.3 206.47 L 1289.3 183.05 C 1289.3 180.36 1287.11 178.16 1284.41 178.16 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 218px; margin-left: 1273px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Replica
                                <br/>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1273" y="230" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Repli...
                </text>
            </switch>
        </g>
        <rect x="1502.8" y="517.68" width="33.2" height="33.2" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 1526.98 521.69 L 1530 521.69 L 1530 522.36 L 1527.82 522.36 L 1527.82 524.23 L 1529.62 524.23 L 1529.62 524.9 L 1527.82 524.9 L 1527.82 526.96 L 1530 526.96 L 1530 527.63 L 1526.98 527.63 Z M 1524.86 521.69 L 1525.7 521.69 L 1525.7 527.63 L 1524.86 527.63 L 1524.86 524.93 L 1522.7 524.93 L 1522.7 527.63 L 1521.87 527.63 L 1521.87 521.69 L 1522.7 521.69 L 1522.7 524.25 L 1524.86 524.25 Z M 1519.83 526.99 C 1520.08 526.99 1520.46 526.92 1520.94 526.78 L 1520.94 527.45 C 1520.57 527.63 1520.17 527.72 1519.73 527.72 C 1518.2 527.72 1517.43 526.71 1517.43 524.68 C 1517.43 523.68 1517.63 522.92 1518.02 522.4 C 1518.42 521.88 1519 521.62 1519.77 521.62 C 1520.13 521.62 1520.5 521.7 1520.87 521.85 L 1520.87 522.53 C 1520.49 522.41 1520.15 522.35 1519.86 522.35 C 1519.32 522.35 1518.93 522.52 1518.68 522.88 C 1518.43 523.23 1518.31 523.79 1518.31 524.57 L 1518.31 524.78 C 1518.31 525.55 1518.43 526.12 1518.67 526.47 C 1518.91 526.82 1519.29 526.99 1519.83 526.99 Z M 1514.14 525.39 L 1514.88 522.49 L 1515.61 525.39 Z M 1514.45 521.69 L 1512.73 527.63 L 1513.58 527.63 L 1513.98 526.03 L 1515.78 526.03 L 1516.19 527.63 L 1517.06 527.63 L 1515.35 521.69 Z M 1511.25 526.99 C 1511.51 526.99 1511.88 526.92 1512.37 526.78 L 1512.37 527.45 C 1512 527.63 1511.59 527.72 1511.16 527.72 C 1509.63 527.72 1508.86 526.71 1508.86 524.68 C 1508.86 523.68 1509.06 522.92 1509.45 522.4 C 1509.85 521.88 1510.43 521.62 1511.19 521.62 C 1511.56 521.62 1511.93 521.7 1512.3 521.85 L 1512.3 522.53 C 1511.92 522.41 1511.58 522.35 1511.29 522.35 C 1510.75 522.35 1510.36 522.52 1510.11 522.88 C 1509.86 523.23 1509.74 523.79 1509.74 524.57 L 1509.74 524.78 C 1509.74 525.55 1509.86 526.12 1510.1 526.47 C 1510.34 526.82 1510.72 526.99 1511.25 526.99 Z M 1534.48 545.99 C 1534.48 547.85 1532.97 549.36 1531.11 549.36 L 1507.69 549.36 C 1505.83 549.36 1504.32 547.85 1504.32 545.99 L 1504.32 522.57 C 1504.32 520.71 1505.83 519.19 1507.69 519.19 L 1531.11 519.19 C 1532.97 519.19 1534.48 520.71 1534.48 522.57 Z M 1531.11 517.68 L 1507.69 517.68 C 1504.99 517.68 1502.8 519.87 1502.8 522.57 L 1502.8 545.99 C 1502.8 548.68 1504.99 550.88 1507.69 550.88 L 1531.11 550.88 C 1533.81 550.88 1536 548.68 1536 545.99 L 1536 522.57 C 1536 519.87 1533.81 517.68 1531.11 517.68 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 558px; margin-left: 1519px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Primary
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1519" y="570" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Prima...
                </text>
            </switch>
        </g>
        <rect x="1502.8" y="336.27" width="33.2" height="33.2" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 1526.98 340.28 L 1530 340.28 L 1530 340.95 L 1527.82 340.95 L 1527.82 342.82 L 1529.62 342.82 L 1529.62 343.49 L 1527.82 343.49 L 1527.82 345.56 L 1530 345.56 L 1530 346.23 L 1526.98 346.23 Z M 1524.86 340.28 L 1525.7 340.28 L 1525.7 346.23 L 1524.86 346.23 L 1524.86 343.52 L 1522.7 343.52 L 1522.7 346.23 L 1521.87 346.23 L 1521.87 340.28 L 1522.7 340.28 L 1522.7 342.85 L 1524.86 342.85 Z M 1519.83 345.58 C 1520.08 345.58 1520.46 345.51 1520.94 345.37 L 1520.94 346.05 C 1520.57 346.23 1520.17 346.31 1519.73 346.31 C 1518.2 346.31 1517.43 345.3 1517.43 343.28 C 1517.43 342.28 1517.63 341.51 1518.02 340.99 C 1518.42 340.47 1519 340.21 1519.77 340.21 C 1520.13 340.21 1520.5 340.29 1520.87 340.44 L 1520.87 341.12 C 1520.49 341 1520.15 340.94 1519.86 340.94 C 1519.32 340.94 1518.93 341.12 1518.68 341.47 C 1518.43 341.82 1518.31 342.39 1518.31 343.17 L 1518.31 343.37 C 1518.31 344.15 1518.43 344.71 1518.67 345.06 C 1518.91 345.41 1519.29 345.58 1519.83 345.58 Z M 1514.14 343.99 L 1514.88 341.08 L 1515.61 343.99 Z M 1514.45 340.28 L 1512.73 346.23 L 1513.58 346.23 L 1513.98 344.62 L 1515.78 344.62 L 1516.19 346.23 L 1517.06 346.23 L 1515.35 340.28 Z M 1511.25 345.58 C 1511.51 345.58 1511.88 345.51 1512.37 345.37 L 1512.37 346.05 C 1512 346.23 1511.59 346.31 1511.16 346.31 C 1509.63 346.31 1508.86 345.3 1508.86 343.28 C 1508.86 342.28 1509.06 341.51 1509.45 340.99 C 1509.85 340.47 1510.43 340.21 1511.19 340.21 C 1511.56 340.21 1511.93 340.29 1512.3 340.44 L 1512.3 341.12 C 1511.92 341 1511.58 340.94 1511.29 340.94 C 1510.75 340.94 1510.36 341.12 1510.11 341.47 C 1509.86 341.82 1509.74 342.39 1509.74 343.17 L 1509.74 343.37 C 1509.74 344.15 1509.86 344.71 1510.1 345.06 C 1510.34 345.41 1510.72 345.58 1511.25 345.58 Z M 1534.48 364.58 C 1534.48 366.44 1532.97 367.95 1531.11 367.95 L 1507.69 367.95 C 1505.83 367.95 1504.32 366.44 1504.32 364.58 L 1504.32 341.16 C 1504.32 339.3 1505.83 337.79 1507.69 337.79 L 1531.11 337.79 C 1532.97 337.79 1534.48 339.3 1534.48 341.16 Z M 1531.11 336.27 L 1507.69 336.27 C 1504.99 336.27 1502.8 338.46 1502.8 341.16 L 1502.8 364.58 C 1502.8 367.28 1504.99 369.47 1507.69 369.47 L 1531.11 369.47 C 1533.81 369.47 1536 367.28 1536 364.58 L 1536 341.16 C 1536 338.46 1533.81 336.27 1531.11 336.27 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 376px; margin-left: 1519px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Replica
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1519" y="388" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Repli...
                </text>
            </switch>
        </g>
        <rect x="1502.8" y="178.16" width="33.2" height="33.2" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 1526.98 182.17 L 1530 182.17 L 1530 182.84 L 1527.82 182.84 L 1527.82 184.72 L 1529.62 184.72 L 1529.62 185.38 L 1527.82 185.38 L 1527.82 187.45 L 1530 187.45 L 1530 188.12 L 1526.98 188.12 Z M 1524.86 182.17 L 1525.7 182.17 L 1525.7 188.12 L 1524.86 188.12 L 1524.86 185.42 L 1522.7 185.42 L 1522.7 188.12 L 1521.87 188.12 L 1521.87 182.17 L 1522.7 182.17 L 1522.7 184.74 L 1524.86 184.74 Z M 1519.83 187.48 C 1520.08 187.48 1520.46 187.41 1520.94 187.26 L 1520.94 187.94 C 1520.57 188.12 1520.17 188.21 1519.73 188.21 C 1518.2 188.21 1517.43 187.2 1517.43 185.17 C 1517.43 184.17 1517.63 183.41 1518.02 182.89 C 1518.42 182.37 1519 182.11 1519.77 182.11 C 1520.13 182.11 1520.5 182.18 1520.87 182.34 L 1520.87 183.02 C 1520.49 182.9 1520.15 182.84 1519.86 182.84 C 1519.32 182.84 1518.93 183.01 1518.68 183.36 C 1518.43 183.72 1518.31 184.28 1518.31 185.06 L 1518.31 185.26 C 1518.31 186.04 1518.43 186.61 1518.67 186.95 C 1518.91 187.3 1519.29 187.48 1519.83 187.48 Z M 1514.14 185.88 L 1514.88 182.97 L 1515.61 185.88 Z M 1514.45 182.17 L 1512.73 188.12 L 1513.58 188.12 L 1513.98 186.52 L 1515.78 186.52 L 1516.19 188.12 L 1517.06 188.12 L 1515.35 182.17 Z M 1511.25 187.48 C 1511.51 187.48 1511.88 187.41 1512.37 187.26 L 1512.37 187.94 C 1512 188.12 1511.59 188.21 1511.16 188.21 C 1509.63 188.21 1508.86 187.2 1508.86 185.17 C 1508.86 184.17 1509.06 183.41 1509.45 182.89 C 1509.85 182.37 1510.43 182.11 1511.19 182.11 C 1511.56 182.11 1511.93 182.18 1512.3 182.34 L 1512.3 183.02 C 1511.92 182.9 1511.58 182.84 1511.29 182.84 C 1510.75 182.84 1510.36 183.01 1510.11 183.36 C 1509.86 183.72 1509.74 184.28 1509.74 185.06 L 1509.74 185.26 C 1509.74 186.04 1509.86 186.61 1510.1 186.95 C 1510.34 187.3 1510.72 187.48 1511.25 187.48 Z M 1534.48 206.47 C 1534.48 208.33 1532.97 209.85 1531.11 209.85 L 1507.69 209.85 C 1505.83 209.85 1504.32 208.33 1504.32 206.47 L 1504.32 183.05 C 1504.32 181.19 1505.83 179.68 1507.69 179.68 L 1531.11 179.68 C 1532.97 179.68 1534.48 181.19 1534.48 183.05 Z M 1531.11 178.16 L 1507.69 178.16 C 1504.99 178.16 1502.8 180.36 1502.8 183.05 L 1502.8 206.47 C 1502.8 209.17 1504.99 211.36 1507.69 211.36 L 1531.11 211.36 C 1533.81 211.36 1536 209.17 1536 206.47 L 1536 183.05 C 1536 180.36 1533.81 178.16 1531.11 178.16 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 218px; margin-left: 1519px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Replica
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1519" y="230" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Repli...
                </text>
            </switch>
        </g>
        <path d="M 1010.23 331.32 L 1023.51 331.32 L 1023.51 318 L 1030.15 318 L 1030.15 331.32 L 1043.43 331.32 L 1043.43 337.96 L 1030.15 337.96 L 1030.15 351.28 L 1023.51 351.28 L 1023.51 337.96 L 1010.23 337.96 Z" fill="#e51400" stroke="#b20000" stroke-miterlimit="10" transform="rotate(-45,1026.83,334.64)" pointer-events="all"/>
        <rect x="987.47" y="154.01" width="80" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 169px; margin-left: 1027px;">
                        <div data-drawio-colors="color: #FF3333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 51, 51); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <font color="#000000" style="font-size: 12px;">
                                    シャードA
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1027" y="173" fill="#FF3333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャードA
                </text>
            </switch>
        </g>
        <rect x="1232" y="154.01" width="80" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 169px; margin-left: 1272px;">
                        <div data-drawio-colors="color: #FF3333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 51, 51); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <font color="#000000" style="font-size: 12px;">
                                    シャードB
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1272" y="173" fill="#FF3333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャードB
                </text>
            </switch>
        </g>
        <rect x="1477" y="154.01" width="80" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 169px; margin-left: 1517px;">
                        <div data-drawio-colors="color: #FF3333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 51, 51); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <font color="#000000" style="font-size: 12px;">
                                    シャードC
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1517" y="173" fill="#FF3333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャードC
                </text>
            </switch>
        </g>
        <rect x="1010.23" y="376" width="33.2" height="33.2" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 1034.41 380.01 L 1037.43 380.01 L 1037.43 380.68 L 1035.25 380.68 L 1035.25 382.55 L 1037.05 382.55 L 1037.05 383.22 L 1035.25 383.22 L 1035.25 385.28 L 1037.43 385.28 L 1037.43 385.95 L 1034.41 385.95 Z M 1032.29 380.01 L 1033.13 380.01 L 1033.13 385.95 L 1032.29 385.95 L 1032.29 383.25 L 1030.13 383.25 L 1030.13 385.95 L 1029.3 385.95 L 1029.3 380.01 L 1030.13 380.01 L 1030.13 382.57 L 1032.29 382.57 Z M 1027.26 385.31 C 1027.51 385.31 1027.89 385.24 1028.37 385.1 L 1028.37 385.77 C 1028 385.95 1027.6 386.04 1027.16 386.04 C 1025.63 386.04 1024.86 385.03 1024.86 383 C 1024.86 382 1025.06 381.24 1025.45 380.72 C 1025.85 380.2 1026.43 379.94 1027.2 379.94 C 1027.56 379.94 1027.93 380.02 1028.3 380.17 L 1028.3 380.85 C 1027.92 380.73 1027.58 380.67 1027.29 380.67 C 1026.75 380.67 1026.36 380.84 1026.11 381.2 C 1025.86 381.55 1025.74 382.11 1025.74 382.89 L 1025.74 383.1 C 1025.74 383.87 1025.86 384.44 1026.1 384.79 C 1026.34 385.14 1026.72 385.31 1027.26 385.31 Z M 1021.57 383.71 L 1022.31 380.81 L 1023.04 383.71 Z M 1021.88 380.01 L 1020.16 385.95 L 1021.01 385.95 L 1021.41 384.35 L 1023.21 384.35 L 1023.62 385.95 L 1024.49 385.95 L 1022.78 380.01 Z M 1018.68 385.31 C 1018.94 385.31 1019.31 385.24 1019.8 385.1 L 1019.8 385.77 C 1019.43 385.95 1019.02 386.04 1018.59 386.04 C 1017.06 386.04 1016.29 385.03 1016.29 383 C 1016.29 382 1016.49 381.24 1016.88 380.72 C 1017.28 380.2 1017.86 379.94 1018.62 379.94 C 1018.99 379.94 1019.36 380.02 1019.73 380.17 L 1019.73 380.85 C 1019.35 380.73 1019.01 380.67 1018.72 380.67 C 1018.18 380.67 1017.79 380.84 1017.54 381.2 C 1017.29 381.55 1017.17 382.11 1017.17 382.89 L 1017.17 383.1 C 1017.17 383.87 1017.29 384.44 1017.53 384.79 C 1017.77 385.14 1018.15 385.31 1018.68 385.31 Z M 1041.91 404.31 C 1041.91 406.17 1040.4 407.68 1038.54 407.68 L 1015.12 407.68 C 1013.26 407.68 1011.75 406.17 1011.75 404.31 L 1011.75 380.89 C 1011.75 379.03 1013.26 377.51 1015.12 377.51 L 1038.54 377.51 C 1040.4 377.51 1041.91 379.03 1041.91 380.89 Z M 1038.54 376 L 1015.12 376 C 1012.42 376 1010.23 378.19 1010.23 380.89 L 1010.23 404.31 C 1010.23 407 1012.42 409.2 1015.12 409.2 L 1038.54 409.2 C 1041.24 409.2 1043.43 407 1043.43 404.31 L 1043.43 380.89 C 1043.43 378.19 1041.24 376 1038.54 376 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 416px; margin-left: 1027px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <font color="#ff3333">
                                    Replica
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1027" y="428" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Repli...
                </text>
            </switch>
        </g>
        <rect x="966.2" y="365" width="50" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 380px; margin-left: 991px;">
                        <div data-drawio-colors="color: #FF3333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 51, 51); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <b>
                                    <font style="font-size: 15px;">
                                        New
                                    </font>
                                </b>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="991" y="384" fill="#FF3333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    New
                </text>
            </switch>
        </g>
        <rect x="1180" y="11" width="200" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 31px; margin-left: 1280px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <font style="font-size: 18px;">
                                    レプリカノード障害時
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1280" y="35" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    レプリカノード障害時
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>