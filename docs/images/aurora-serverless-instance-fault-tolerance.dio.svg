<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="773px" height="471px" viewBox="-0.5 -0.5 773 471" content="&lt;mxfile&gt;&lt;diagram id=&quot;rxyWzuQO3aeQyH_nDuKp&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-4d72f3-1-3334b9-1-s-0">
            <stop offset="0%" style="stop-color: rgb(51, 52, 185); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(77, 114, 243); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <path d="M 4 46 L 377 46 L 377 469.5 L 4 469.5 Z" fill="none" stroke="#248814" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 14.59 52.65 C 14.53 52.65 14.48 52.65 14.42 52.65 L 14.42 52.65 C 13.11 52.68 12.03 53.24 11.14 54.25 C 11.13 54.25 11.13 54.25 11.13 54.25 C 10.2 55.36 9.87 56.52 9.96 57.73 C 8.81 58.06 8.12 58.92 7.76 59.74 C 7.75 59.75 7.75 59.76 7.74 59.78 C 7.33 61.05 7.68 62.36 8.24 63.16 C 8.25 63.17 8.25 63.17 8.26 63.18 C 8.94 64.05 9.97 64.53 11.02 64.53 L 22.17 64.53 C 23.19 64.53 24.07 64.16 24.8 63.37 C 25.25 62.94 25.49 62.29 25.58 61.59 C 25.67 60.9 25.61 60.16 25.32 59.55 C 25.31 59.54 25.31 59.53 25.31 59.52 C 24.8 58.62 23.95 57.81 22.76 57.64 C 22.74 56.79 22.28 55.99 21.68 55.56 C 21.67 55.55 21.66 55.55 21.65 55.54 C 21.01 55.18 20.4 55.14 19.91 55.3 C 19.6 55.4 19.36 55.56 19.14 55.74 C 18.51 54.36 17.43 53.18 15.81 52.79 C 15.81 52.79 15.81 52.79 15.81 52.79 C 15.38 52.7 14.97 52.65 14.59 52.65 Z M 14.43 53.38 C 14.8 53.38 15.2 53.43 15.64 53.53 C 17.16 53.89 18.15 55.07 18.66 56.48 C 18.71 56.6 18.81 56.69 18.94 56.72 C 19.07 56.74 19.2 56.7 19.29 56.61 C 19.54 56.34 19.83 56.11 20.14 56.01 C 20.44 55.91 20.78 55.92 21.26 56.18 C 21.67 56.49 22.11 57.31 22.03 57.9 C 22.01 58.01 22.05 58.12 22.12 58.2 C 22.19 58.28 22.29 58.33 22.39 58.33 C 23.46 58.34 24.16 59.02 24.64 59.88 C 24.85 60.3 24.91 60.92 24.84 61.5 C 24.76 62.07 24.53 62.59 24.28 62.83 C 24.27 62.84 24.27 62.85 24.26 62.85 C 23.65 63.53 23.03 63.78 22.17 63.78 L 11.02 63.78 C 10.2 63.78 9.39 63.41 8.85 62.73 C 8.44 62.13 8.14 61.02 8.46 60.02 C 8.79 59.27 9.36 58.55 10.41 58.36 C 10.6 58.32 10.74 58.14 10.71 57.94 C 10.56 56.79 10.8 55.81 11.7 54.74 C 12.49 53.85 13.33 53.39 14.43 53.38 Z M 16.2 56.7 C 15.77 56.7 15.4 56.93 15.13 57.21 C 14.85 57.5 14.64 57.85 14.64 58.25 L 14.64 58.71 L 14.14 58.71 C 14.04 58.71 13.94 58.75 13.87 58.82 C 13.8 58.89 13.76 58.98 13.76 59.08 L 13.76 61.7 C 13.76 61.8 13.8 61.89 13.87 61.96 C 13.94 62.03 14.04 62.07 14.14 62.07 L 18.16 62.07 C 18.26 62.07 18.35 62.03 18.42 61.96 C 18.49 61.89 18.53 61.8 18.53 61.7 L 18.53 59.08 C 18.53 58.98 18.49 58.89 18.42 58.82 C 18.35 58.75 18.26 58.71 18.16 58.71 L 17.68 58.71 L 17.68 58.25 C 17.68 57.84 17.47 57.47 17.21 57.2 C 16.94 56.92 16.61 56.7 16.2 56.7 Z M 16.2 57.45 C 16.29 57.45 16.5 57.54 16.67 57.72 C 16.83 57.89 16.93 58.11 16.93 58.25 L 16.93 58.71 L 15.39 58.71 L 15.39 58.25 C 15.39 58.15 15.49 57.91 15.66 57.74 C 15.83 57.56 16.06 57.45 16.2 57.45 Z M 14.51 59.46 L 17.78 59.46 L 17.78 61.32 L 14.51 61.32 Z M 4 71 L 4 46 L 29 46 L 29 71 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 341px; height: 1px; padding-top: 53px; margin-left: 36px;">
                        <div data-drawio-colors="color: #AAB7B8; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(170, 183, 184); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                VPC
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="36" y="65" fill="#AAB7B8" font-family="Helvetica" font-size="12px">
                    VPC
                </text>
            </switch>
        </g>
        <ellipse cx="35" cy="245" rx="20" ry="20" fill="#d0d7fb" stroke="#000000" pointer-events="all"/>
        <rect x="0" y="268" width="70" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 288px; margin-left: 35px;">
                        <div data-drawio-colors="color: #FF3333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 51, 51); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <font color="#000000">
                                    Cluster
                                    <br/>
                                    Endpoint
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="35" y="292" fill="#FF3333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Cluster...
                </text>
            </switch>
        </g>
        <rect x="95.5" y="0" width="180" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 20px; margin-left: 186px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <font style="font-size: 19px;">
                                    プライマリ障害時
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="186" y="24" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    プライマリ障害時
                </text>
            </switch>
        </g>
        <path d="M 399 46 L 772 46 L 772 469.5 L 399 469.5 Z" fill="none" stroke="#248814" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 409.59 52.65 C 409.53 52.65 409.48 52.65 409.42 52.65 L 409.42 52.65 C 408.11 52.68 407.03 53.24 406.14 54.25 C 406.13 54.25 406.13 54.25 406.13 54.25 C 405.2 55.36 404.87 56.52 404.96 57.73 C 403.81 58.06 403.12 58.92 402.76 59.74 C 402.75 59.75 402.75 59.76 402.74 59.78 C 402.33 61.05 402.68 62.36 403.24 63.16 C 403.25 63.17 403.25 63.17 403.26 63.18 C 403.94 64.05 404.97 64.53 406.02 64.53 L 417.17 64.53 C 418.19 64.53 419.07 64.16 419.8 63.37 C 420.25 62.94 420.49 62.29 420.58 61.59 C 420.67 60.9 420.61 60.16 420.32 59.55 C 420.31 59.54 420.31 59.53 420.31 59.52 C 419.8 58.62 418.95 57.81 417.76 57.64 C 417.74 56.79 417.28 55.99 416.68 55.56 C 416.67 55.55 416.66 55.55 416.65 55.54 C 416.01 55.18 415.4 55.14 414.91 55.3 C 414.6 55.4 414.36 55.56 414.14 55.74 C 413.51 54.36 412.43 53.18 410.81 52.79 C 410.81 52.79 410.81 52.79 410.81 52.79 C 410.38 52.7 409.97 52.65 409.59 52.65 Z M 409.43 53.38 C 409.8 53.38 410.2 53.43 410.64 53.53 C 412.16 53.89 413.15 55.07 413.66 56.48 C 413.71 56.6 413.81 56.69 413.94 56.72 C 414.07 56.74 414.2 56.7 414.29 56.61 C 414.54 56.34 414.83 56.11 415.14 56.01 C 415.44 55.91 415.78 55.92 416.26 56.18 C 416.67 56.49 417.11 57.31 417.03 57.9 C 417.01 58.01 417.05 58.12 417.12 58.2 C 417.19 58.28 417.29 58.33 417.39 58.33 C 418.46 58.34 419.16 59.02 419.64 59.88 C 419.85 60.3 419.91 60.92 419.84 61.5 C 419.76 62.07 419.53 62.59 419.28 62.83 C 419.27 62.84 419.27 62.85 419.26 62.85 C 418.65 63.53 418.03 63.78 417.17 63.78 L 406.02 63.78 C 405.2 63.78 404.39 63.41 403.85 62.73 C 403.44 62.13 403.14 61.02 403.46 60.02 C 403.79 59.27 404.36 58.55 405.41 58.36 C 405.6 58.32 405.74 58.14 405.71 57.94 C 405.56 56.79 405.8 55.81 406.7 54.74 C 407.49 53.85 408.33 53.39 409.43 53.38 Z M 411.2 56.7 C 410.77 56.7 410.4 56.93 410.13 57.21 C 409.85 57.5 409.64 57.85 409.64 58.25 L 409.64 58.71 L 409.14 58.71 C 409.04 58.71 408.94 58.75 408.87 58.82 C 408.8 58.89 408.76 58.98 408.76 59.08 L 408.76 61.7 C 408.76 61.8 408.8 61.89 408.87 61.96 C 408.94 62.03 409.04 62.07 409.14 62.07 L 413.16 62.07 C 413.26 62.07 413.35 62.03 413.42 61.96 C 413.49 61.89 413.53 61.8 413.53 61.7 L 413.53 59.08 C 413.53 58.98 413.49 58.89 413.42 58.82 C 413.35 58.75 413.26 58.71 413.16 58.71 L 412.68 58.71 L 412.68 58.25 C 412.68 57.84 412.47 57.47 412.21 57.2 C 411.94 56.92 411.61 56.7 411.2 56.7 Z M 411.2 57.45 C 411.29 57.45 411.5 57.54 411.67 57.72 C 411.83 57.89 411.93 58.11 411.93 58.25 L 411.93 58.71 L 410.39 58.71 L 410.39 58.25 C 410.39 58.15 410.49 57.91 410.66 57.74 C 410.83 57.56 411.06 57.45 411.2 57.45 Z M 409.51 59.46 L 412.78 59.46 L 412.78 61.32 L 409.51 61.32 Z M 399 71 L 399 46 L 424 46 L 424 71 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 341px; height: 1px; padding-top: 53px; margin-left: 431px;">
                        <div data-drawio-colors="color: #AAB7B8; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(170, 183, 184); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                VPC
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="431" y="65" fill="#AAB7B8" font-family="Helvetica" font-size="12px">
                    VPC
                </text>
            </switch>
        </g>
        <ellipse cx="430" cy="285" rx="20" ry="20" fill="#d0d7fb" stroke="#000000" pointer-events="all"/>
        <rect x="395" y="305" width="70" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 325px; margin-left: 430px;">
                        <div data-drawio-colors="color: #FF3333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 51, 51); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <font color="#000000">
                                    Read
                                    <br/>
                                    Endpoint
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="430" y="329" fill="#FF3333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Read...
                </text>
            </switch>
        </g>
        <rect x="475.5" y="0" width="210" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 20px; margin-left: 581px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <font style="font-size: 19px;">
                                    リードレプリカ障害時
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="581" y="24" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    リードレプリカ障害時
                </text>
            </switch>
        </g>
        <ellipse cx="430" cy="162" rx="20" ry="20" fill="#d0d7fb" stroke="#000000" pointer-events="all"/>
        <rect x="396" y="183" width="70" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 203px; margin-left: 431px;">
                        <div data-drawio-colors="color: #FF3333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 51, 51); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <font color="#000000">
                                    Cluster
                                    <br/>
                                    Endpoint
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="431" y="207" fill="#FF3333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Cluster...
                </text>
            </switch>
        </g>
        <path d="M 494.75 96 L 694.75 96 L 694.75 226 L 494.75 226 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 494.75 96 L 519.75 96 L 519.75 121 L 494.75 121 Z M 507.27 99.21 C 506.15 99.21 505.06 99.63 504.24 100.39 C 503.42 101.11 502.95 102.15 502.95 103.24 L 502.95 105.78 L 500.64 105.78 C 500.55 105.78 500.45 105.82 500.39 105.89 C 500.32 105.95 500.29 106.04 500.29 106.14 L 500.29 117.43 C 500.29 117.63 500.45 117.79 500.64 117.79 L 513.86 117.79 C 514.05 117.79 514.21 117.63 514.21 117.43 L 514.21 106.15 C 514.22 106.06 514.18 105.97 514.11 105.9 C 514.05 105.83 513.96 105.79 513.86 105.79 L 511.56 105.79 L 511.56 103.29 C 511.55 102.21 511.1 101.18 510.31 100.44 C 509.49 99.65 508.4 99.22 507.27 99.21 Z M 507.26 99.93 C 508.21 99.92 509.12 100.28 509.81 100.93 C 510.47 101.54 510.85 102.4 510.85 103.29 L 510.85 105.79 L 503.63 105.79 L 503.64 103.26 C 503.65 102.36 504.03 101.51 504.7 100.91 C 505.4 100.27 506.32 99.92 507.26 99.93 Z M 500.99 106.5 L 513.51 106.5 L 513.5 117.07 L 500.99 117.07 Z M 507.26 108.74 C 506.23 108.73 505.36 109.51 505.26 110.53 C 505.17 111.56 505.88 112.48 506.89 112.66 L 506.89 115.44 L 507.61 115.44 L 507.61 112.66 C 508.54 112.49 509.22 111.67 509.23 110.72 C 509.23 109.63 508.35 108.75 507.26 108.74 Z M 507.14 109.45 C 507.18 109.45 507.22 109.45 507.26 109.46 C 507.59 109.46 507.91 109.59 508.15 109.83 C 508.39 110.07 508.52 110.39 508.51 110.72 C 508.52 111.06 508.39 111.38 508.15 111.61 C 507.91 111.85 507.59 111.98 507.26 111.98 C 506.79 112.02 506.35 111.8 506.09 111.42 C 505.83 111.03 505.81 110.53 506.03 110.12 C 506.25 109.71 506.68 109.46 507.14 109.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 103px; margin-left: 527px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="527" y="115" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 494.75 293 L 694.75 293 L 694.75 423 L 494.75 423 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 494.75 293 L 519.75 293 L 519.75 318 L 494.75 318 Z M 507.27 296.21 C 506.15 296.21 505.06 296.63 504.24 297.39 C 503.42 298.11 502.95 299.15 502.95 300.24 L 502.95 302.78 L 500.64 302.78 C 500.55 302.78 500.45 302.82 500.39 302.89 C 500.32 302.95 500.29 303.04 500.29 303.13 L 500.29 314.43 C 500.29 314.63 500.45 314.79 500.64 314.79 L 513.86 314.79 C 514.05 314.79 514.21 314.63 514.21 314.43 L 514.21 303.15 C 514.22 303.06 514.18 302.97 514.11 302.9 C 514.05 302.83 513.96 302.79 513.86 302.79 L 511.56 302.79 L 511.56 300.29 C 511.55 299.21 511.1 298.18 510.31 297.44 C 509.49 296.65 508.4 296.22 507.27 296.21 Z M 507.26 296.93 C 508.21 296.92 509.12 297.28 509.81 297.93 C 510.47 298.54 510.85 299.4 510.85 300.29 L 510.85 302.79 L 503.63 302.79 L 503.64 300.26 C 503.65 299.36 504.03 298.51 504.7 297.91 C 505.4 297.27 506.32 296.92 507.26 296.93 Z M 500.99 303.5 L 513.51 303.5 L 513.5 314.07 L 500.99 314.07 Z M 507.26 305.74 C 506.23 305.73 505.36 306.51 505.26 307.53 C 505.17 308.56 505.88 309.48 506.89 309.66 L 506.89 312.44 L 507.61 312.44 L 507.61 309.66 C 508.54 309.49 509.22 308.67 509.23 307.72 C 509.23 306.63 508.35 305.75 507.26 305.74 Z M 507.14 306.45 C 507.18 306.45 507.22 306.45 507.26 306.46 C 507.59 306.46 507.91 306.59 508.15 306.83 C 508.39 307.07 508.52 307.39 508.51 307.72 C 508.52 308.06 508.39 308.38 508.15 308.61 C 507.91 308.85 507.59 308.98 507.26 308.98 C 506.79 309.02 506.35 308.8 506.09 308.42 C 505.83 308.03 505.81 307.53 506.03 307.12 C 506.25 306.71 506.68 306.46 507.14 306.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 300px; margin-left: 527px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="527" y="312" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 567.25 135.5 L 618.75 135.5 L 618.75 187 L 567.25 187 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 596.62 145.65 L 594.48 145.65 L 594.48 144.22 L 596.62 144.22 L 596.62 142.07 L 598.04 142.07 L 598.04 144.22 L 600.18 144.22 L 600.18 145.65 L 598.04 145.65 L 598.04 147.8 L 596.62 147.8 Z M 605.16 153.54 L 603.03 153.54 L 603.03 152.11 L 605.16 152.11 L 605.16 149.95 L 606.59 149.95 L 606.59 152.11 L 608.72 152.11 L 608.72 153.54 L 606.59 153.54 L 606.59 155.69 L 605.16 155.69 Z M 601.45 176.19 C 600.09 172.73 596.95 169.58 593.52 168.21 C 596.95 166.84 600.09 163.68 601.45 160.22 C 602.81 163.68 605.95 166.84 609.39 168.21 C 605.95 169.58 602.81 172.73 601.45 176.19 Z M 612.89 167.49 C 607.88 167.49 602.17 161.74 602.17 156.7 C 602.17 156.3 601.85 155.98 601.45 155.98 C 601.06 155.98 600.74 156.3 600.74 156.7 C 600.74 161.74 595.03 167.49 590.02 167.49 C 589.63 167.49 589.31 167.81 589.31 168.21 C 589.31 168.6 589.63 168.92 590.02 168.92 C 595.03 168.92 600.74 174.67 600.74 179.71 C 600.74 180.11 601.06 180.43 601.45 180.43 C 601.85 180.43 602.17 180.11 602.17 179.71 C 602.17 174.67 607.88 168.92 612.89 168.92 C 613.28 168.92 613.6 168.6 613.6 168.21 C 613.6 167.81 613.28 167.49 612.89 167.49 Z M 573.82 153.37 C 575.9 154.89 579.93 155.69 583.8 155.69 C 587.66 155.69 591.69 154.89 593.77 153.37 L 593.77 160.23 C 592.74 161.61 588.97 162.96 583.94 162.96 C 578.15 162.96 573.82 161.14 573.82 159.51 Z M 583.8 147.8 C 589.97 147.8 593.77 149.68 593.77 151.03 C 593.77 152.38 589.97 154.26 583.8 154.26 C 577.62 154.26 573.82 152.38 573.82 151.03 C 573.82 149.68 577.62 147.8 583.8 147.8 Z M 593.77 174.71 C 593.77 176.36 589.5 178.21 583.79 178.21 C 578.09 178.21 573.82 176.36 573.82 174.71 L 573.82 170.13 C 575.92 171.73 580.03 172.58 583.98 172.58 C 586.72 172.58 589.37 172.19 591.45 171.48 L 590.99 170.12 C 589.06 170.78 586.57 171.14 583.98 171.14 C 578.17 171.14 573.82 169.32 573.82 167.69 L 573.82 161.95 C 575.92 163.55 580.01 164.39 583.94 164.39 C 588.15 164.39 591.76 163.52 593.77 162.14 L 593.77 164.29 L 595.19 164.29 L 595.19 151.03 C 595.19 148 589.32 146.37 583.8 146.37 C 578.5 146.37 572.89 147.88 572.44 150.67 L 572.4 150.67 L 572.4 174.71 C 572.4 177.91 578.27 179.64 583.79 179.64 C 589.32 179.64 595.19 177.91 595.19 174.71 L 595.19 172.17 L 593.77 172.17 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 194px; margin-left: 593px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Primary
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="593" y="206" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Primary
                </text>
            </switch>
        </g>
        <path d="M 569 332.5 L 620.5 332.5 L 620.5 384 L 569 384 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 598.37 342.65 L 596.23 342.65 L 596.23 341.22 L 598.37 341.22 L 598.37 339.07 L 599.79 339.07 L 599.79 341.22 L 601.93 341.22 L 601.93 342.65 L 599.79 342.65 L 599.79 344.8 L 598.37 344.8 Z M 606.91 350.54 L 604.78 350.54 L 604.78 349.11 L 606.91 349.11 L 606.91 346.95 L 608.34 346.95 L 608.34 349.11 L 610.47 349.11 L 610.47 350.54 L 608.34 350.54 L 608.34 352.69 L 606.91 352.69 Z M 603.2 373.19 C 601.84 369.73 598.7 366.58 595.27 365.21 C 598.7 363.84 601.84 360.68 603.2 357.22 C 604.56 360.68 607.7 363.84 611.14 365.21 C 607.7 366.58 604.56 369.73 603.2 373.19 Z M 614.64 364.49 C 609.63 364.49 603.92 358.74 603.92 353.7 C 603.92 353.3 603.6 352.98 603.2 352.98 C 602.81 352.98 602.49 353.3 602.49 353.7 C 602.49 358.74 596.78 364.49 591.77 364.49 C 591.38 364.49 591.06 364.81 591.06 365.21 C 591.06 365.6 591.38 365.92 591.77 365.92 C 596.78 365.92 602.49 371.67 602.49 376.71 C 602.49 377.11 602.81 377.43 603.2 377.43 C 603.6 377.43 603.92 377.11 603.92 376.71 C 603.92 371.67 609.63 365.92 614.64 365.92 C 615.03 365.92 615.35 365.6 615.35 365.21 C 615.35 364.81 615.03 364.49 614.64 364.49 Z M 575.57 350.37 C 577.65 351.89 581.68 352.69 585.55 352.69 C 589.41 352.69 593.44 351.89 595.52 350.37 L 595.52 357.23 C 594.49 358.61 590.72 359.96 585.69 359.96 C 579.9 359.96 575.57 358.14 575.57 356.51 Z M 585.55 344.8 C 591.72 344.8 595.52 346.68 595.52 348.03 C 595.52 349.38 591.72 351.26 585.55 351.26 C 579.37 351.26 575.57 349.38 575.57 348.03 C 575.57 346.68 579.37 344.8 585.55 344.8 Z M 595.52 371.71 C 595.52 373.36 591.25 375.21 585.54 375.21 C 579.84 375.21 575.57 373.36 575.57 371.71 L 575.57 367.13 C 577.67 368.73 581.78 369.58 585.73 369.58 C 588.47 369.58 591.12 369.19 593.2 368.48 L 592.74 367.12 C 590.81 367.78 588.32 368.14 585.73 368.14 C 579.92 368.14 575.57 366.32 575.57 364.69 L 575.57 358.95 C 577.67 360.55 581.76 361.39 585.69 361.39 C 589.9 361.39 593.51 360.52 595.52 359.14 L 595.52 361.29 L 596.94 361.29 L 596.94 348.03 C 596.94 345 591.07 343.37 585.55 343.37 C 580.25 343.37 574.64 344.88 574.19 347.67 L 574.15 347.67 L 574.15 371.71 C 574.15 374.91 580.02 376.64 585.54 376.64 C 591.07 376.64 596.94 374.91 596.94 371.71 L 596.94 369.17 L 595.52 369.17 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 391px; margin-left: 595px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Replica
                                <br/>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="595" y="403" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Replica
                </text>
            </switch>
        </g>
        <path d="M 574.75 355 L 590.75 355 L 590.75 339 L 598.75 339 L 598.75 355 L 614.75 355 L 614.75 363 L 598.75 363 L 598.75 379 L 590.75 379 L 590.75 363 L 574.75 363 Z" fill="#e51400" stroke="#b20000" stroke-miterlimit="10" transform="rotate(-45,594.75,359)" pointer-events="all"/>
        <rect x="482" y="59" width="220" height="188" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="540" y="67" width="106" height="15" stroke-width="0"/>
            <text x="591.5" y="76.5">
                Availability Zone 1a
            </text>
        </g>
        <rect x="482" y="256" width="220" height="188" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="540" y="264" width="105" height="15" stroke-width="0"/>
            <text x="591.5" y="273.5">
                Availability Zone 1c
            </text>
        </g>
        <path d="M 450 162 L 559.01 161.3" fill="none" stroke="#ff3333" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 565.01 161.26 L 557.04 165.32 L 559.01 161.3 L 556.99 157.32 Z" fill="#ff3333" stroke="#ff3333" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 450 285 L 561.27 179.78" fill="none" stroke="#ff3333" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 565.63 175.66 L 562.56 184.06 L 561.27 179.78 L 557.06 178.25 Z" fill="#ff3333" stroke="#ff3333" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 450 285 L 561.99 353.93" fill="none" stroke="#ff3333" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/>
        <path d="M 567.1 357.08 L 558.19 356.29 L 561.99 353.93 L 562.38 349.48 Z" fill="#ff3333" stroke="#ff3333" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 97.75 96 L 297.75 96 L 297.75 226 L 97.75 226 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 97.75 96 L 122.75 96 L 122.75 121 L 97.75 121 Z M 110.27 99.21 C 109.15 99.21 108.06 99.63 107.23 100.39 C 106.42 101.11 105.95 102.15 105.95 103.24 L 105.95 105.78 L 103.64 105.78 C 103.55 105.78 103.45 105.82 103.39 105.89 C 103.32 105.95 103.29 106.04 103.29 106.14 L 103.29 117.43 C 103.29 117.63 103.45 117.79 103.64 117.79 L 116.86 117.79 C 117.05 117.79 117.21 117.63 117.21 117.43 L 117.21 106.15 C 117.22 106.06 117.18 105.97 117.11 105.9 C 117.05 105.83 116.96 105.79 116.86 105.79 L 114.56 105.79 L 114.56 103.29 C 114.55 102.21 114.1 101.18 113.31 100.44 C 112.49 99.65 111.4 99.22 110.27 99.21 Z M 110.26 99.93 C 111.21 99.92 112.12 100.28 112.81 100.93 C 113.47 101.54 113.85 102.4 113.85 103.29 L 113.85 105.79 L 106.63 105.79 L 106.64 103.26 C 106.65 102.36 107.03 101.51 107.7 100.91 C 108.4 100.27 109.32 99.92 110.26 99.93 Z M 103.99 106.5 L 116.51 106.5 L 116.5 117.07 L 103.99 117.07 Z M 110.26 108.74 C 109.23 108.73 108.36 109.51 108.26 110.53 C 108.17 111.56 108.88 112.48 109.89 112.66 L 109.89 115.44 L 110.61 115.44 L 110.61 112.66 C 111.54 112.49 112.22 111.67 112.23 110.72 C 112.23 109.63 111.35 108.75 110.26 108.74 Z M 110.14 109.45 C 110.18 109.45 110.22 109.45 110.26 109.46 C 110.59 109.46 110.91 109.59 111.15 109.83 C 111.39 110.07 111.52 110.39 111.51 110.72 C 111.52 111.06 111.39 111.38 111.15 111.61 C 110.91 111.85 110.59 111.98 110.26 111.98 C 109.79 112.02 109.35 111.8 109.09 111.42 C 108.83 111.03 108.81 110.53 109.03 110.12 C 109.25 109.71 109.68 109.46 110.14 109.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 103px; margin-left: 130px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="130" y="115" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 97.75 293 L 297.75 293 L 297.75 423 L 97.75 423 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 97.75 293 L 122.75 293 L 122.75 318 L 97.75 318 Z M 110.27 296.21 C 109.15 296.21 108.06 296.63 107.23 297.39 C 106.42 298.11 105.95 299.15 105.95 300.24 L 105.95 302.78 L 103.64 302.78 C 103.55 302.78 103.45 302.82 103.39 302.89 C 103.32 302.95 103.29 303.04 103.29 303.13 L 103.29 314.43 C 103.29 314.63 103.45 314.79 103.64 314.79 L 116.86 314.79 C 117.05 314.79 117.21 314.63 117.21 314.43 L 117.21 303.15 C 117.22 303.06 117.18 302.97 117.11 302.9 C 117.05 302.83 116.96 302.79 116.86 302.79 L 114.56 302.79 L 114.56 300.29 C 114.55 299.21 114.1 298.18 113.31 297.44 C 112.49 296.65 111.4 296.22 110.27 296.21 Z M 110.26 296.93 C 111.21 296.92 112.12 297.28 112.81 297.93 C 113.47 298.54 113.85 299.4 113.85 300.29 L 113.85 302.79 L 106.63 302.79 L 106.64 300.26 C 106.65 299.36 107.03 298.51 107.7 297.91 C 108.4 297.27 109.32 296.92 110.26 296.93 Z M 103.99 303.5 L 116.51 303.5 L 116.5 314.07 L 103.99 314.07 Z M 110.26 305.74 C 109.23 305.73 108.36 306.51 108.26 307.53 C 108.17 308.56 108.88 309.48 109.89 309.66 L 109.89 312.44 L 110.61 312.44 L 110.61 309.66 C 111.54 309.49 112.22 308.67 112.23 307.72 C 112.23 306.63 111.35 305.75 110.26 305.74 Z M 110.14 306.45 C 110.18 306.45 110.22 306.45 110.26 306.46 C 110.59 306.46 110.91 306.59 111.15 306.83 C 111.39 307.07 111.52 307.39 111.51 307.72 C 111.52 308.06 111.39 308.38 111.15 308.61 C 110.91 308.85 110.59 308.98 110.26 308.98 C 109.79 309.02 109.35 308.8 109.09 308.42 C 108.83 308.03 108.81 307.53 109.03 307.12 C 109.25 306.71 109.68 306.46 110.14 306.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 300px; margin-left: 130px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="130" y="312" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 172 332.5 L 223.5 332.5 L 223.5 384 L 172 384 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 201.37 342.65 L 199.23 342.65 L 199.23 341.22 L 201.37 341.22 L 201.37 339.07 L 202.79 339.07 L 202.79 341.22 L 204.93 341.22 L 204.93 342.65 L 202.79 342.65 L 202.79 344.8 L 201.37 344.8 Z M 209.91 350.54 L 207.78 350.54 L 207.78 349.11 L 209.91 349.11 L 209.91 346.95 L 211.34 346.95 L 211.34 349.11 L 213.47 349.11 L 213.47 350.54 L 211.34 350.54 L 211.34 352.69 L 209.91 352.69 Z M 206.2 373.19 C 204.84 369.73 201.7 366.58 198.27 365.21 C 201.7 363.84 204.84 360.68 206.2 357.22 C 207.56 360.68 210.7 363.84 214.14 365.21 C 210.7 366.58 207.56 369.73 206.2 373.19 Z M 217.64 364.49 C 212.63 364.49 206.92 358.74 206.92 353.7 C 206.92 353.3 206.6 352.98 206.2 352.98 C 205.81 352.98 205.49 353.3 205.49 353.7 C 205.49 358.74 199.78 364.49 194.77 364.49 C 194.38 364.49 194.06 364.81 194.06 365.21 C 194.06 365.6 194.38 365.92 194.77 365.92 C 199.78 365.92 205.49 371.67 205.49 376.71 C 205.49 377.11 205.81 377.43 206.2 377.43 C 206.6 377.43 206.92 377.11 206.92 376.71 C 206.92 371.67 212.63 365.92 217.64 365.92 C 218.03 365.92 218.35 365.6 218.35 365.21 C 218.35 364.81 218.03 364.49 217.64 364.49 Z M 178.57 350.37 C 180.65 351.89 184.68 352.69 188.55 352.69 C 192.41 352.69 196.44 351.89 198.52 350.37 L 198.52 357.23 C 197.49 358.61 193.72 359.96 188.69 359.96 C 182.9 359.96 178.57 358.14 178.57 356.51 Z M 188.55 344.8 C 194.72 344.8 198.52 346.68 198.52 348.03 C 198.52 349.38 194.72 351.26 188.55 351.26 C 182.37 351.26 178.57 349.38 178.57 348.03 C 178.57 346.68 182.37 344.8 188.55 344.8 Z M 198.52 371.71 C 198.52 373.36 194.25 375.21 188.54 375.21 C 182.84 375.21 178.57 373.36 178.57 371.71 L 178.57 367.13 C 180.67 368.73 184.78 369.58 188.73 369.58 C 191.47 369.58 194.12 369.19 196.2 368.48 L 195.74 367.12 C 193.81 367.78 191.32 368.14 188.73 368.14 C 182.92 368.14 178.57 366.32 178.57 364.69 L 178.57 358.95 C 180.67 360.55 184.76 361.39 188.69 361.39 C 192.9 361.39 196.51 360.52 198.52 359.14 L 198.52 361.29 L 199.94 361.29 L 199.94 348.03 C 199.94 345 194.07 343.37 188.55 343.37 C 183.25 343.37 177.64 344.88 177.19 347.67 L 177.15 347.67 L 177.15 371.71 C 177.15 374.91 183.02 376.64 188.54 376.64 C 194.07 376.64 199.94 374.91 199.94 371.71 L 199.94 369.17 L 198.52 369.17 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 391px; margin-left: 198px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Replica
                                <br/>
                                →
                                <font color="#ff3333">
                                    Prime
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="198" y="403" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Replica...
                </text>
            </switch>
        </g>
        <path d="M 170.25 135.5 L 221.75 135.5 L 221.75 187 L 170.25 187 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 199.62 145.65 L 197.48 145.65 L 197.48 144.22 L 199.62 144.22 L 199.62 142.07 L 201.04 142.07 L 201.04 144.22 L 203.18 144.22 L 203.18 145.65 L 201.04 145.65 L 201.04 147.8 L 199.62 147.8 Z M 208.16 153.54 L 206.03 153.54 L 206.03 152.11 L 208.16 152.11 L 208.16 149.95 L 209.59 149.95 L 209.59 152.11 L 211.72 152.11 L 211.72 153.54 L 209.59 153.54 L 209.59 155.69 L 208.16 155.69 Z M 204.45 176.19 C 203.09 172.73 199.95 169.58 196.52 168.21 C 199.95 166.84 203.09 163.68 204.45 160.22 C 205.81 163.68 208.95 166.84 212.39 168.21 C 208.95 169.58 205.81 172.73 204.45 176.19 Z M 215.89 167.49 C 210.88 167.49 205.17 161.74 205.17 156.7 C 205.17 156.3 204.85 155.98 204.45 155.98 C 204.06 155.98 203.74 156.3 203.74 156.7 C 203.74 161.74 198.03 167.49 193.02 167.49 C 192.63 167.49 192.31 167.81 192.31 168.21 C 192.31 168.6 192.63 168.92 193.02 168.92 C 198.03 168.92 203.74 174.67 203.74 179.71 C 203.74 180.11 204.06 180.43 204.45 180.43 C 204.85 180.43 205.17 180.11 205.17 179.71 C 205.17 174.67 210.88 168.92 215.89 168.92 C 216.28 168.92 216.6 168.6 216.6 168.21 C 216.6 167.81 216.28 167.49 215.89 167.49 Z M 176.82 153.37 C 178.9 154.89 182.93 155.69 186.8 155.69 C 190.66 155.69 194.69 154.89 196.77 153.37 L 196.77 160.23 C 195.74 161.61 191.97 162.96 186.94 162.96 C 181.15 162.96 176.82 161.14 176.82 159.51 Z M 186.8 147.8 C 192.97 147.8 196.77 149.68 196.77 151.03 C 196.77 152.38 192.97 154.26 186.8 154.26 C 180.62 154.26 176.82 152.38 176.82 151.03 C 176.82 149.68 180.62 147.8 186.8 147.8 Z M 196.77 174.71 C 196.77 176.36 192.5 178.21 186.79 178.21 C 181.09 178.21 176.82 176.36 176.82 174.71 L 176.82 170.13 C 178.92 171.73 183.03 172.58 186.98 172.58 C 189.72 172.58 192.37 172.19 194.45 171.48 L 193.99 170.12 C 192.06 170.78 189.57 171.14 186.98 171.14 C 181.17 171.14 176.82 169.32 176.82 167.69 L 176.82 161.95 C 178.92 163.55 183.01 164.39 186.94 164.39 C 191.15 164.39 194.76 163.52 196.77 162.14 L 196.77 164.29 L 198.19 164.29 L 198.19 151.03 C 198.19 148 192.32 146.37 186.8 146.37 C 181.5 146.37 175.89 147.88 175.44 150.67 L 175.4 150.67 L 175.4 174.71 C 175.4 177.91 181.27 179.64 186.79 179.64 C 192.32 179.64 198.19 177.91 198.19 174.71 L 198.19 172.17 L 196.77 172.17 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 194px; margin-left: 196px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Primary
                                <br/>
                                →Replica(復旧後)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="196" y="206" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Primary...
                </text>
            </switch>
        </g>
        <rect x="87.75" y="59" width="220" height="188" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="none"/>
        <g fill="#147EBA" font-family="Helvetica" pointer-events="none" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="145" y="67" width="106" height="15" stroke-width="0"/>
            <text x="197.25" y="76.5">
                Availability Zone 1a
            </text>
        </g>
        <rect x="87.75" y="256" width="220" height="188" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="none"/>
        <g fill="#147EBA" font-family="Helvetica" pointer-events="none" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="146" y="264" width="105" height="15" stroke-width="0"/>
            <text x="197.25" y="273.5">
                Availability Zone 1c
            </text>
        </g>
        <path d="M 175.75 160 L 191.75 160 L 191.75 144 L 199.75 144 L 199.75 160 L 215.75 160 L 215.75 168 L 199.75 168 L 199.75 184 L 191.75 184 L 191.75 168 L 175.75 168 Z" fill="#e51400" stroke="#b20000" stroke-miterlimit="10" transform="rotate(-45,195.75,164)" pointer-events="none"/>
        <path d="M 55 245 L 163.59 166.09" fill="none" stroke="#ff3333" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/>
        <path d="M 168.44 162.56 L 164.32 170.5 L 163.59 166.09 L 159.62 164.03 Z" fill="#ff3333" stroke="#ff3333" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 55 245 L 166.08 352.52" fill="none" stroke="#ff3333" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 170.39 356.69 L 161.86 354 L 166.08 352.52 L 167.43 348.26 Z" fill="#ff3333" stroke="#ff3333" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>