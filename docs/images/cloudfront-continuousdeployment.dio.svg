<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" style="background: transparent; background-color: transparent;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="511px" height="371px" viewBox="-0.5 -0.5 511 371" content="&lt;mxfile&gt;&lt;diagram id=&quot;cri7rlrUa9hT3uIwlw8c&quot; name=&quot;ページ1&quot;&gt;7VnbbuM2EP0aA7sPNixRspPHyE7SAlvAQB7aPC1oiZaI0KJKUb706zuUSN0oZx3E8XaLxLmIh0Nx5nDmiFRGaLE9PAqcJX/wiLCRO40OI7Qcue6tN4XfCjhWgINmboXEgkYaa4An+g/RoB4YFzQiecdQcs4kzbpgyNOUhLKDYSH4vmu24aw7a4ZjYgFPIWY2+ieNZFKhN+68wX8jNE70zP4Nqjq22NjqQPIER3zfgtD9CC0E57K62h4WhCnuDC3VuIcTvbVfgqTynAFI877DrNCxacfk0QQreJFGRA2YjlCwT6gkTxkOVe8elhewRG4ZtBy4tB3QPu2IkOTQgrRDj4RviRRHMNG9yKyyzo6xIWvfUO0bm6RFM5prEOvljet7NxTAhWZhmBH/woRgRuMUrhnZgJuBooFCIt1pWPLsQqy5XdbmNmne7QBp7iVIQ2ewFgNt2Ymo6krFa2M+PcHJSQK8WS9tHJsBdzBt3AswYEq/xcCjICQFaMWFtNggEQiKbkJ/wmOeYnbfoO0cIgcq/1LpNvF167nVszzoTCwbx1ZjRQSFSIjQWDdxw0Ls6obNtlnTnBci1F57WmuxiInJH22mAjpnmaYTb440WYIwLOmuK6pDq1De7U4IfGwZZJymMm9NtlJAM5Nz43cSwr31eyta3bE32tyebzY5kVYO1BGdlRaelRW5xDFNY/X0o7kUdF1IylMrP/IXIsNEL46OFDr8AL4hXRfVjw+mC4VM1DJY4BA2t0HHNoM/ztAMfXAIm9ugY5uplvG6Cw5hc9/2uD/aGRjt9EbDNwp4IRlNyaLeHCiONzyVC864KPlH8HlQSxpsKGMt/GbhPTxAPgSwdvyFtHo25Rf0RDhP6royev8Nrwlb8ZyWq42Way4l3558IJinRgjlqMq3rQbKU70lclzT1pmjpsR5VoW1oQflRwCbjEx1bg+x2o5N8D73JoJUhf17qPwJoFldda1CxotoI7gShYCpGAIcvsSlkJjYI7LBBZOvisj5zzGnJ+PO7cDTf0DF/UuI+Mwq10Bd/a80HF1Ew68h4G5Xv1F/L/sm86vIPbLyJ4PVwyV3n3L/Kfefcl9LiZH3ocPdB8k7Qq9KeMrTnmYPquKPFda5tSUWDbNwtoCeG6OZ+7WTWEds3lQdkCoRJU2fpqxXNKX50ioabfyfq5dSbom435FKdZ1TNVTk5VzvSvtr7GKcgRSYlRWbZzjt5MLs70K9agrWdZmPw2q57sCOKcfGERYvXxrFA6FV9xZfxuOYlJ16SNkBdo6rPl+/llSZCeAqVn8hzyRNC17kxicIp3KrMjBwRHd96Od7vyQZ48ctLPyKMxoefxADwJ0wfr063NIoKney/TqsO3rvt65SiDHBwjygWs6Kqo4ucgzxuqeQgUOIN1C+3iXK136Xdvft+slzYufzy4s4zjKoXKxc/M44jr6vMcNp+H5lN2+ZBl68ftiGxn69tKcwqZoJRxDS+06sKbjTOrKq5rPmv2w0h9aydWy3+sdWc/w1B97nUfsofMHj75t3awPnYe9jNmvQbP6zU50um3+Poft/AQ==&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <g>
            <rect x="0" y="0" width="510" height="370" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="10" y="90" width="490" height="270" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g/>
        <g>
            <path d="M 335 250 L 335 305 L 206.37 305" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 201.12 305 L 208.12 301.5 L 206.37 305 L 208.12 308.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 305px; margin-left: 250px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    Green Port
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="250" y="308" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        Green Port
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 310 200 L 360 200 L 360 250 L 310 250 Z" fill="#8c4fff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));"/>
            <path d="M 345.8 233.66 C 345.8 232.48 344.84 231.52 343.66 231.52 C 342.48 231.52 341.52 232.48 341.52 233.66 C 341.52 234.84 342.48 235.8 343.66 235.8 C 344.84 235.8 345.8 234.84 345.8 233.66 Z M 347.23 233.66 C 347.23 235.63 345.63 237.23 343.66 237.23 C 341.69 237.23 340.09 235.63 340.09 233.66 C 340.09 231.69 341.69 230.09 343.66 230.09 C 345.63 230.09 347.23 231.69 347.23 233.66 Z M 328.14 223.35 C 328.14 222.17 327.18 221.21 325.99 221.21 C 324.81 221.21 323.85 222.17 323.85 223.35 C 323.85 224.53 324.81 225.49 325.99 225.49 C 327.18 225.49 328.14 224.53 328.14 223.35 Z M 329.57 223.35 C 329.57 225.32 327.96 226.92 325.99 226.92 C 324.02 226.92 322.42 225.32 322.42 223.35 C 322.42 221.38 324.02 219.78 325.99 219.78 C 327.96 219.78 329.57 221.38 329.57 223.35 Z M 334.81 212.15 C 334.81 213.33 335.77 214.29 336.95 214.29 C 338.13 214.29 339.09 213.33 339.09 212.15 C 339.09 210.97 338.13 210 336.95 210 C 335.77 210 334.81 210.97 334.81 212.15 Z M 333.38 212.15 C 333.38 210.18 334.98 208.58 336.95 208.58 C 338.92 208.58 340.52 210.18 340.52 212.15 C 340.52 214.12 338.92 215.72 336.95 215.72 C 334.98 215.72 333.38 214.12 333.38 212.15 Z M 353.57 225 C 353.57 218.38 350.02 212.25 344.29 208.94 C 343.27 209.14 342.27 209.43 341.04 209.87 L 340.55 208.53 C 341.2 208.3 341.78 208.11 342.33 207.95 C 340.03 206.96 337.53 206.43 335 206.43 C 333.79 206.43 332.61 206.55 331.45 206.78 C 332.29 207.27 333.03 207.76 333.75 208.31 L 332.89 209.45 C 331.87 208.68 330.81 208.02 329.43 207.29 C 322.41 209.5 317.36 215.68 316.56 222.95 C 318.03 222.65 319.44 222.49 321.01 222.45 L 321.05 223.88 C 319.4 223.92 317.99 224.09 316.45 224.43 C 316.44 224.62 316.43 224.81 316.43 225 C 316.43 231.18 319.49 236.88 324.52 240.32 C 323.62 237.65 323.18 235.14 323.18 232.68 C 323.18 231.27 323.42 230.12 323.68 228.9 C 323.73 228.62 323.79 228.33 323.85 228.03 L 325.25 228.31 C 325.19 228.61 325.13 228.91 325.07 229.19 C 324.82 230.39 324.61 231.42 324.61 232.68 C 324.61 235.47 325.22 238.35 326.47 241.49 C 329.12 242.87 331.99 243.57 335 243.57 C 336.97 243.57 338.89 243.26 340.72 242.65 C 341.44 241.23 341.98 239.89 342.42 238.36 L 343.79 238.75 C 343.47 239.88 343.09 240.9 342.65 241.91 C 343.8 241.39 344.89 240.75 345.92 240 C 345.67 239.4 345.41 238.8 345.11 238.21 L 346.39 237.57 C 346.64 238.07 346.87 238.58 347.09 239.09 C 351.22 235.55 353.57 230.47 353.57 225 Z M 355 225 C 355 231.23 352.17 237 347.23 240.82 C 346.01 241.77 344.68 242.56 343.28 243.19 C 342.68 243.46 342.08 243.71 341.45 243.92 C 339.4 244.64 337.22 245 335 245 C 331.71 245 328.45 244.18 325.56 242.63 C 319.05 239.15 315 232.39 315 225 C 315 224.51 315.01 224.14 315.04 223.79 C 315.53 215.45 321.25 208.22 329.29 205.83 C 331.12 205.28 333.04 205 335 205 C 338.43 205 341.81 205.88 344.77 207.56 C 351.08 211.09 355 217.77 355 225 Z M 333.08 214.62 L 332.14 213.54 C 330.54 214.94 329.3 216.42 327.84 218.66 L 329.04 219.44 C 330.42 217.32 331.59 215.92 333.08 214.62 Z M 330.91 223.88 L 330.45 225.23 C 333.73 226.35 336.59 228.15 339.45 230.88 L 340.44 229.85 C 337.42 226.97 334.39 225.07 330.91 223.88 Z M 340.46 215.48 C 343.14 219.56 344.65 224.05 344.95 228.81 L 343.52 228.9 C 343.24 224.39 341.81 220.14 339.27 216.26 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 257px; margin-left: 335px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    staging distribution
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="335" y="269" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        staging...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 175 180 L 175 273.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 175 278.88 L 171.5 271.88 L 175 273.63 L 178.5 271.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 250px; margin-left: 175px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    Blue Port
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="175" y="253" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        Blue Port
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 150 130 L 200 130 L 200 180 L 150 180 Z" fill="#8c4fff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));"/>
            <path d="M 185.8 163.66 C 185.8 162.48 184.84 161.52 183.66 161.52 C 182.48 161.52 181.52 162.48 181.52 163.66 C 181.52 164.84 182.48 165.8 183.66 165.8 C 184.84 165.8 185.8 164.84 185.8 163.66 Z M 187.23 163.66 C 187.23 165.63 185.63 167.23 183.66 167.23 C 181.69 167.23 180.09 165.63 180.09 163.66 C 180.09 161.69 181.69 160.09 183.66 160.09 C 185.63 160.09 187.23 161.69 187.23 163.66 Z M 168.14 153.35 C 168.14 152.17 167.18 151.21 165.99 151.21 C 164.81 151.21 163.85 152.17 163.85 153.35 C 163.85 154.53 164.81 155.49 165.99 155.49 C 167.18 155.49 168.14 154.53 168.14 153.35 Z M 169.57 153.35 C 169.57 155.32 167.96 156.92 165.99 156.92 C 164.03 156.92 162.42 155.32 162.42 153.35 C 162.42 151.38 164.03 149.78 165.99 149.78 C 167.96 149.78 169.57 151.38 169.57 153.35 Z M 174.81 142.15 C 174.81 143.33 175.77 144.29 176.95 144.29 C 178.13 144.29 179.09 143.33 179.09 142.15 C 179.09 140.97 178.13 140 176.95 140 C 175.77 140 174.81 140.97 174.81 142.15 Z M 173.38 142.15 C 173.38 140.18 174.98 138.58 176.95 138.58 C 178.92 138.58 180.52 140.18 180.52 142.15 C 180.52 144.12 178.92 145.72 176.95 145.72 C 174.98 145.72 173.38 144.12 173.38 142.15 Z M 193.57 155 C 193.57 148.38 190.02 142.25 184.29 138.94 C 183.27 139.14 182.28 139.43 181.04 139.87 L 180.55 138.53 C 181.2 138.3 181.78 138.11 182.33 137.95 C 180.03 136.96 177.53 136.43 175 136.43 C 173.79 136.43 172.61 136.55 171.45 136.78 C 172.29 137.27 173.03 137.76 173.75 138.31 L 172.89 139.45 C 171.87 138.68 170.81 138.02 169.43 137.29 C 162.41 139.5 157.36 145.68 156.56 152.95 C 158.03 152.65 159.44 152.49 161.01 152.45 L 161.04 153.88 C 159.4 153.92 157.99 154.09 156.45 154.43 C 156.44 154.62 156.43 154.81 156.43 155 C 156.43 161.18 159.49 166.88 164.52 170.32 C 163.62 167.65 163.18 165.14 163.18 162.68 C 163.18 161.27 163.42 160.12 163.68 158.9 C 163.73 158.62 163.79 158.33 163.85 158.03 L 165.25 158.31 C 165.19 158.61 165.13 158.91 165.07 159.19 C 164.82 160.39 164.61 161.42 164.61 162.68 C 164.61 165.47 165.22 168.35 166.47 171.49 C 169.12 172.87 171.99 173.57 175 173.57 C 176.97 173.57 178.89 173.26 180.72 172.65 C 181.44 171.23 181.98 169.89 182.42 168.36 L 183.79 168.75 C 183.47 169.88 183.09 170.9 182.65 171.91 C 183.8 171.39 184.89 170.75 185.92 170 C 185.67 169.4 185.41 168.8 185.11 168.21 L 186.39 167.57 C 186.64 168.07 186.87 168.58 187.09 169.09 C 191.22 165.55 193.57 160.47 193.57 155 Z M 195 155 C 195 161.23 192.17 167 187.23 170.82 C 186.01 171.77 184.68 172.56 183.28 173.19 C 182.68 173.46 182.08 173.71 181.45 173.92 C 179.4 174.64 177.22 175 175 175 C 171.71 175 168.45 174.18 165.56 172.63 C 159.05 169.15 155 162.39 155 155 C 155 154.51 155.01 154.14 155.04 153.79 C 155.53 145.45 161.25 138.22 169.29 135.83 C 171.12 135.28 173.04 135 175 135 C 178.43 135 181.81 135.88 184.77 137.56 C 191.08 141.09 195 147.77 195 155 Z M 173.08 144.62 L 172.14 143.54 C 170.54 144.94 169.3 146.42 167.84 148.66 L 169.04 149.44 C 170.42 147.32 171.59 145.92 173.08 144.62 Z M 170.91 153.88 L 170.45 155.23 C 173.73 156.35 176.59 158.15 179.45 160.88 L 180.44 159.85 C 177.42 156.97 174.39 155.07 170.91 153.88 Z M 180.46 145.48 C 183.14 149.56 184.65 154.05 184.95 158.81 L 183.52 158.9 C 183.24 154.39 181.81 150.14 179.27 146.26 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 187px; margin-left: 175px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    primary distribution
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="175" y="199" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        primary...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 175 60 L 175 123.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 175 128.88 L 171.5 121.88 L 175 123.63 L 178.5 121.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="150" y="10" width="50" height="50" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 154.57 57.73 C 155.14 46.46 164.09 37.47 175 37.47 C 178.69 37.47 182.31 38.51 185.46 40.48 C 191.31 44.13 195.06 50.66 195.42 57.73 Z M 163.85 23.5 C 163.85 17.31 168.85 12.27 175 12.27 C 181.15 12.27 186.14 17.31 186.14 23.5 C 186.14 29.69 181.15 34.72 175 34.72 C 168.85 34.72 163.85 29.69 163.85 23.5 Z M 186.66 38.55 C 184.69 37.32 182.56 36.43 180.34 35.88 C 185.09 33.79 188.42 29.03 188.42 23.5 C 188.42 16.06 182.4 10 175 10 C 167.6 10 161.58 16.06 161.58 23.5 C 161.58 29.04 164.92 33.81 169.68 35.88 C 159.71 38.39 152.27 47.73 152.27 58.86 C 152.27 59.49 152.78 60 153.41 60 L 196.59 60 C 197.22 60 197.73 59.49 197.73 58.86 C 197.73 50.59 193.49 42.81 186.66 38.55 Z" fill="#232f3d" stroke="none" pointer-events="all" style="fill: light-dark(rgb(35, 47, 61), rgb(190, 200, 212));"/>
        </g>
        <g>
            <rect x="190" y="100" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 210 127.1 C 206.09 127.1 202.9 123.91 202.9 120 C 202.9 116.09 206.09 112.9 210 112.9 C 213.91 112.9 217.1 116.09 217.1 120 C 217.1 123.91 213.91 127.1 210 127.1 Z M 210 111.09 C 205.08 111.09 201.09 115.08 201.09 120 C 201.09 124.92 205.08 128.91 210 128.91 C 214.92 128.91 218.91 124.92 218.91 120 C 218.91 115.08 214.92 111.09 210 111.09 Z M 228.18 123.4 L 224.98 123.4 C 224.56 123.4 224.2 123.68 224.1 124.08 C 223.76 125.34 223.22 126.26 222.6 127.32 C 222.39 127.68 222.45 128.13 222.74 128.42 L 225.01 130.67 L 220.65 135.01 L 218.38 132.75 C 218.09 132.46 217.64 132.4 217.28 132.61 L 217.08 132.73 C 216.01 133.34 215.24 133.79 214.08 134.1 C 213.68 134.2 213.4 134.56 213.4 134.98 L 213.4 138.18 L 206.6 138.18 L 206.6 134.98 C 206.6 134.57 206.32 134.21 205.92 134.1 C 204.67 133.77 203.77 133.27 202.64 132.61 C 202.28 132.4 201.83 132.46 201.53 132.75 L 199.29 135.01 L 194.96 130.65 L 197.21 128.38 C 197.5 128.09 197.56 127.64 197.36 127.29 L 197.26 127.12 C 196.63 126.04 196.18 125.25 195.87 124.08 C 195.77 123.68 195.4 123.4 194.99 123.4 L 191.82 123.4 L 191.82 116.6 L 195.02 116.6 C 195.43 116.6 195.79 116.32 195.9 115.92 C 196.24 114.65 196.75 113.71 197.39 112.61 C 197.6 112.26 197.54 111.8 197.25 111.51 L 194.99 109.26 L 199.34 104.91 L 201.61 107.17 C 201.9 107.47 202.35 107.52 202.71 107.32 L 202.87 107.22 C 203.96 106.59 204.74 106.14 205.92 105.83 C 206.32 105.72 206.6 105.36 206.6 104.95 L 206.6 101.82 L 213.4 101.82 L 213.4 105.01 C 213.4 105.42 213.68 105.78 214.08 105.89 C 215.23 106.2 216 106.65 217.07 107.28 L 217.23 107.37 C 217.59 107.59 218.04 107.53 218.33 107.23 L 220.55 104.98 L 224.82 109.32 L 222.59 111.58 C 222.3 111.87 222.25 112.32 222.45 112.67 C 223.09 113.78 223.59 114.66 223.92 115.92 C 224.03 116.32 224.39 116.6 224.8 116.6 L 228.18 116.6 Z M 228.3 114.78 L 225.48 114.78 C 225.18 113.87 224.8 113.13 224.37 112.37 L 226.2 110.52 C 226.85 109.86 226.85 108.78 226.2 108.12 L 221.76 103.63 C 221.12 102.97 219.98 102.97 219.33 103.63 L 217.54 105.45 C 216.8 105.02 216.09 104.64 215.22 104.34 L 215.22 101.7 C 215.22 100.76 214.46 100 213.52 100 L 206.48 100 C 205.54 100 204.78 100.76 204.78 101.7 L 204.78 104.27 C 203.89 104.58 203.16 104.96 202.4 105.4 L 200.55 103.55 C 199.88 102.89 198.81 102.89 198.14 103.55 L 193.62 108.05 C 193.3 108.37 193.12 108.8 193.12 109.26 C 193.12 109.71 193.3 110.14 193.62 110.46 L 195.47 112.31 C 195.04 113.08 194.65 113.86 194.34 114.78 L 191.7 114.78 C 190.77 114.78 190 115.55 190 116.48 L 190 123.52 C 190 124.45 190.77 125.22 191.7 125.22 L 194.32 125.22 C 194.62 126.11 195 126.84 195.43 127.59 L 193.59 129.45 C 192.93 130.11 192.93 131.19 193.59 131.85 L 198.08 136.37 C 198.4 136.7 198.83 136.88 199.29 136.88 L 199.29 136.88 C 199.74 136.88 200.17 136.7 200.5 136.37 L 202.33 134.53 C 203.12 134.97 203.88 135.35 204.78 135.65 L 204.78 138.3 C 204.78 139.23 205.54 140 206.48 140 L 213.52 140 C 214.46 140 215.22 139.23 215.22 138.3 L 215.22 135.65 C 216.11 135.35 216.83 134.97 217.59 134.53 L 219.45 136.38 C 220.11 137.04 221.19 137.04 221.85 136.38 L 226.37 131.88 C 226.7 131.56 226.87 131.13 226.87 130.67 C 226.87 130.22 226.7 129.79 226.37 129.47 L 224.52 127.63 C 224.95 126.89 225.34 126.13 225.65 125.22 L 228.3 125.22 C 229.24 125.22 230 124.45 230 123.52 L 230 116.48 C 230 115.55 229.24 114.78 228.3 114.78 Z" fill="#232f3d" stroke="none" pointer-events="all" style="fill: light-dark(rgb(35, 47, 61), rgb(190, 200, 212));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 120px; margin-left: 232px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    <span style="background-color: light-dark(#ffffff, var(--ge-dark-color, #121212));">
                                        Continuous
                                    </span>
                                    <div>
                                        <span style="background-color: light-dark(#ffffff, var(--ge-dark-color, #121212));">
                                            DeploymentPolicy
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="232" y="124" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Continu...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="150" y="280" width="50" height="50" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 175 280 C 161.21 280 150 291.22 150 305 C 150 318.78 161.21 330 175 330 C 188.78 330 200 318.78 200 305 C 200 291.22 188.78 280 175 280 Z M 175 327.73 C 162.47 327.73 152.27 317.53 152.27 305 C 152.27 292.47 162.47 282.27 175 282.27 C 187.53 282.27 197.73 292.47 197.73 305 C 197.73 317.53 187.53 327.73 175 327.73 Z M 189.81 311.82 L 188.07 311.82 L 188.07 307.98 C 188.07 307.35 187.56 306.85 186.93 306.85 L 184.09 306.85 L 184.09 303.01 C 184.09 302.38 183.58 301.88 182.95 301.88 L 176.14 301.88 L 176.14 299.18 L 182.95 299.18 C 183.58 299.18 184.09 298.67 184.09 298.04 L 184.09 289.09 C 184.09 288.46 183.58 287.95 182.95 287.95 L 167.05 287.95 C 166.42 287.95 165.91 288.46 165.91 289.09 L 165.91 298.04 C 165.91 298.67 166.42 299.18 167.05 299.18 L 173.86 299.18 L 173.86 301.88 L 167.05 301.88 C 166.42 301.88 165.91 302.38 165.91 303.01 L 165.91 306.85 L 163.07 306.85 C 162.44 306.85 161.93 307.35 161.93 307.98 L 161.93 311.82 L 160.19 311.82 C 159.56 311.82 159.05 312.33 159.05 312.95 L 159.05 317.93 C 159.05 318.55 159.56 319.06 160.19 319.06 L 165.06 319.06 C 165.68 319.06 166.19 318.55 166.19 317.93 L 166.19 312.95 C 166.19 312.33 165.68 311.82 165.06 311.82 L 164.21 311.82 L 164.21 309.12 L 168.89 309.12 L 168.89 311.82 L 168.04 311.82 C 167.41 311.82 166.9 312.33 166.9 312.95 L 166.9 317.93 C 166.9 318.55 167.41 319.06 168.04 319.06 L 173.01 319.06 C 173.64 319.06 174.15 318.55 174.15 317.93 L 174.15 312.95 C 174.15 312.33 173.64 311.82 173.01 311.82 L 171.17 311.82 L 171.17 307.98 C 171.17 307.35 170.66 306.85 170.03 306.85 L 168.18 306.85 L 168.18 304.15 L 181.82 304.15 L 181.82 306.85 L 179.97 306.85 C 179.34 306.85 178.84 307.35 178.84 307.98 L 178.84 311.82 L 176.99 311.82 C 176.36 311.82 175.85 312.33 175.85 312.95 L 175.85 317.93 C 175.85 318.55 176.36 319.06 176.99 319.06 L 181.96 319.06 C 182.59 319.06 183.1 318.55 183.1 317.93 L 183.1 312.95 C 183.1 312.33 182.59 311.82 181.96 311.82 L 181.11 311.82 L 181.11 309.12 L 185.8 309.12 L 185.8 311.82 L 184.87 311.82 C 184.25 311.82 183.74 312.33 183.74 312.95 L 183.74 317.93 C 183.74 318.55 184.25 319.06 184.87 319.06 L 189.81 319.06 C 190.44 319.06 190.95 318.55 190.95 317.93 L 190.95 312.95 C 190.95 312.33 190.44 311.82 189.81 311.82 Z M 168.18 296.9 L 168.18 290.23 L 181.82 290.23 L 181.82 296.9 Z M 161.32 316.79 L 161.32 314.09 L 163.92 314.09 L 163.92 316.79 Z M 169.18 316.79 L 169.18 314.09 L 171.88 314.09 L 171.88 316.79 Z M 178.13 316.79 L 178.13 314.09 L 180.82 314.09 L 180.82 316.79 Z M 186.01 316.79 L 186.01 314.09 L 188.68 314.09 L 188.68 316.79 Z" fill="#8c4fff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 337px; margin-left: 175px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    ALB
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="175" y="349" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ALB
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 200 155 L 335 155 L 335 193.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 335 198.88 L 331.5 191.88 L 335 193.63 L 338.5 191.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 155px; margin-left: 290px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    with header
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="290" y="158" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        with header
                    </text>
                </switch>
            </g>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>