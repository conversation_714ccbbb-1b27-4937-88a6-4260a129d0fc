<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="331px" height="251px" viewBox="-0.5 -0.5 331 251" content="&lt;mxfile&gt;&lt;diagram id=&quot;rxyWzuQO3aeQyH_nDuKp&quot; name=&quot;Page-1&quot;&gt;7Vhbb5swGP01kbaHRBhCLo+BtN2kTaqWaX024IBVB2fGNHS/fp+xucV067R00qTQtLXP58t3OT5YmXjhoboT+Jh95glhE9dJqom3nbiuv1rCXwU8a8D10EIjqaCJxlAH7OgPYkDHoCVNSDEYKDlnkh6HYMzznMRygGEh+Gk4bM/ZcNcjTokF7GLMbPSBJjLT6MpddvgHQtOs2Rkt1toS4fgxFbzMzX4T19vXjzYfcLOWCbTIcMJPPci7mXih4Fzq1qEKCVO5bdKm592+YG39FiSXr5rgrPSUJ8xK0vhceyafm2ycMirJ7ohj1T9BxSdekMkDgx6Cpr2nceOJCEmqHmR8uCP8QKR4hiHVsO6GL1OE/LlGTl36Pc+Mynqpd30DYlPytF28CxsaJvLxLCw8Kwn3gj/RgvKcJBN3wWC3IBLQSlWLCxvbEQHxMlIU3+wMFo9ExioMB0YeOc1l7a0fwAf8D/WvD0NDhcxcfwQcw5Y2iOxh8A+N7XAOjmFLG0T2MNVrvB6CY9jStz0+n41GZqOz2fCBYpSS0ZyErRSoHO95LkPOoFAq/x783KrSB6nACSUD23y7BGPPtqUCFoLSgz3nQtEv2FPGenPgmQdrwAsp+CPpWcxp94IEFxlJjDvqJFCQl084IuyeF9QsH3Ep+aE3YMNoqgySq0OGTS8Gr4gYnjoVoRFO5DZ9wzi1JS6OOh17Wik/ApCaozIeqlSJ9gyfivlMkIKXIiYfY+VPAF3dGo7CpeACt+E2kqjcYCqioFW9JhM5HJzG2ou3DaQANaF5Gpj4a48N9lWFvvUvIywrv6bLQFuc+bwFe/Lio5lv60uH/o3AIGc9IrNaQlThBmqx+F7yxjAt6hJvYIDrHas6KY29UZ6Nro7rhKwsVHrNwuCUXlsPs0QJUiiHnLL4dk7LA00SNV0Rhf7AUcu2oaapY4lLyQvDT+ugGHr0T1UDDQ+uUz+X4cLaesl4fq06Z0RAy5H3zPwCrxnUvOZGWBB1ZfozWqD5C7R4EPRXZGjh6P8iyCVEoT3TnSYsR6mwGGGCdxEmoH/IhC8EJ1cmjN471+u27L/jwpgqXIYL7mtvnyMciLiA2k5jnZpNvYx4N5328fejrBheYi+69PUufL0LX+/CZ3L1Ftdb11nZr7I3v95Ct/uGorb1vgbybn4C&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-4d72f3-1-3334b9-1-s-0">
            <stop offset="0%" style="stop-color: rgb(51, 52, 185); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(77, 114, 243); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="0" y="0" width="330" height="250" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <path d="M 75.75 109.25 L 127.25 109.25 L 127.25 160.75 L 75.75 160.75 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 105.12 119.4 L 102.98 119.4 L 102.98 117.97 L 105.12 117.97 L 105.12 115.82 L 106.54 115.82 L 106.54 117.97 L 108.68 117.97 L 108.68 119.4 L 106.54 119.4 L 106.54 121.55 L 105.12 121.55 Z M 113.66 127.29 L 111.53 127.29 L 111.53 125.86 L 113.66 125.86 L 113.66 123.7 L 115.09 123.7 L 115.09 125.86 L 117.22 125.86 L 117.22 127.29 L 115.09 127.29 L 115.09 129.44 L 113.66 129.44 Z M 109.95 149.94 C 108.59 146.48 105.45 143.33 102.02 141.96 C 105.45 140.59 108.59 137.43 109.95 133.97 C 111.31 137.43 114.45 140.59 117.89 141.96 C 114.45 143.33 111.31 146.48 109.95 149.94 Z M 121.39 141.24 C 116.38 141.24 110.67 135.49 110.67 130.45 C 110.67 130.05 110.35 129.73 109.95 129.73 C 109.56 129.73 109.24 130.05 109.24 130.45 C 109.24 135.49 103.53 141.24 98.52 141.24 C 98.13 141.24 97.81 141.56 97.81 141.96 C 97.81 142.35 98.13 142.67 98.52 142.67 C 103.53 142.67 109.24 148.42 109.24 153.46 C 109.24 153.86 109.56 154.18 109.95 154.18 C 110.35 154.18 110.67 153.86 110.67 153.46 C 110.67 148.42 116.38 142.67 121.39 142.67 C 121.78 142.67 122.1 142.35 122.1 141.96 C 122.1 141.56 121.78 141.24 121.39 141.24 Z M 82.32 127.12 C 84.4 128.64 88.43 129.44 92.3 129.44 C 96.16 129.44 100.19 128.64 102.27 127.12 L 102.27 133.98 C 101.24 135.36 97.47 136.71 92.44 136.71 C 86.65 136.71 82.32 134.89 82.32 133.26 Z M 92.3 121.55 C 98.47 121.55 102.27 123.43 102.27 124.78 C 102.27 126.13 98.47 128.01 92.3 128.01 C 86.12 128.01 82.32 126.13 82.32 124.78 C 82.32 123.43 86.12 121.55 92.3 121.55 Z M 102.27 148.46 C 102.27 150.11 98 151.96 92.29 151.96 C 86.59 151.96 82.32 150.11 82.32 148.46 L 82.32 143.88 C 84.42 145.48 88.53 146.33 92.48 146.33 C 95.22 146.33 97.87 145.94 99.95 145.23 L 99.49 143.87 C 97.56 144.53 95.07 144.89 92.48 144.89 C 86.67 144.89 82.32 143.07 82.32 141.44 L 82.32 135.7 C 84.42 137.3 88.51 138.14 92.44 138.14 C 96.65 138.14 100.26 137.27 102.27 135.89 L 102.27 138.04 L 103.69 138.04 L 103.69 124.78 C 103.69 121.75 97.82 120.12 92.3 120.12 C 87 120.12 81.39 121.63 80.94 124.42 L 80.9 124.42 L 80.9 148.46 C 80.9 151.66 86.77 153.39 92.29 153.39 C 97.82 153.39 103.69 151.66 103.69 148.46 L 103.69 145.92 L 102.27 145.92 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 173px; margin-left: 102px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Provisioned
                                <br/>
                                or
                                <br/>
                                ServerlessV2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="102" y="185" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Provisio...
                </text>
            </switch>
        </g>
        <rect x="80" y="18.75" width="170" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 39px; margin-left: 165px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <font style="font-size: 23px;">
                                    Aurora Cluster
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="165" y="42" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Aurora Cluster
                </text>
            </switch>
        </g>
        <rect x="71.5" y="78.75" width="60" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 94px; margin-left: 102px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <b>
                                    <font style="font-size: 14px;">
                                        Writer
                                    </font>
                                </b>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="102" y="97" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Writer
                </text>
            </switch>
        </g>
        <rect x="189.25" y="78.75" width="70" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 94px; margin-left: 224px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <b>
                                    <font style="font-size: 14px;">
                                        Reader
                                    </font>
                                </b>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="224" y="97" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Reader
                </text>
            </switch>
        </g>
        <path d="M 198.5 109.25 L 250 109.25 L 250 160.75 L 198.5 160.75 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 227.87 119.4 L 225.73 119.4 L 225.73 117.97 L 227.87 117.97 L 227.87 115.82 L 229.29 115.82 L 229.29 117.97 L 231.43 117.97 L 231.43 119.4 L 229.29 119.4 L 229.29 121.55 L 227.87 121.55 Z M 236.41 127.29 L 234.28 127.29 L 234.28 125.86 L 236.41 125.86 L 236.41 123.7 L 237.84 123.7 L 237.84 125.86 L 239.97 125.86 L 239.97 127.29 L 237.84 127.29 L 237.84 129.44 L 236.41 129.44 Z M 232.7 149.94 C 231.34 146.48 228.2 143.33 224.77 141.96 C 228.2 140.59 231.34 137.43 232.7 133.97 C 234.06 137.43 237.2 140.59 240.64 141.96 C 237.2 143.33 234.06 146.48 232.7 149.94 Z M 244.14 141.24 C 239.13 141.24 233.42 135.49 233.42 130.45 C 233.42 130.05 233.1 129.73 232.7 129.73 C 232.31 129.73 231.99 130.05 231.99 130.45 C 231.99 135.49 226.28 141.24 221.27 141.24 C 220.88 141.24 220.56 141.56 220.56 141.96 C 220.56 142.35 220.88 142.67 221.27 142.67 C 226.28 142.67 231.99 148.42 231.99 153.46 C 231.99 153.86 232.31 154.18 232.7 154.18 C 233.1 154.18 233.42 153.86 233.42 153.46 C 233.42 148.42 239.13 142.67 244.14 142.67 C 244.53 142.67 244.85 142.35 244.85 141.96 C 244.85 141.56 244.53 141.24 244.14 141.24 Z M 205.07 127.12 C 207.15 128.64 211.18 129.44 215.05 129.44 C 218.91 129.44 222.94 128.64 225.02 127.12 L 225.02 133.98 C 223.99 135.36 220.22 136.71 215.19 136.71 C 209.4 136.71 205.07 134.89 205.07 133.26 Z M 215.05 121.55 C 221.22 121.55 225.02 123.43 225.02 124.78 C 225.02 126.13 221.22 128.01 215.05 128.01 C 208.87 128.01 205.07 126.13 205.07 124.78 C 205.07 123.43 208.87 121.55 215.05 121.55 Z M 225.02 148.46 C 225.02 150.11 220.75 151.96 215.04 151.96 C 209.34 151.96 205.07 150.11 205.07 148.46 L 205.07 143.88 C 207.17 145.48 211.28 146.33 215.23 146.33 C 217.97 146.33 220.62 145.94 222.7 145.23 L 222.24 143.87 C 220.31 144.53 217.82 144.89 215.23 144.89 C 209.42 144.89 205.07 143.07 205.07 141.44 L 205.07 135.7 C 207.17 137.3 211.26 138.14 215.19 138.14 C 219.4 138.14 223.01 137.27 225.02 135.89 L 225.02 138.04 L 226.44 138.04 L 226.44 124.78 C 226.44 121.75 220.57 120.12 215.05 120.12 C 209.75 120.12 204.14 121.63 203.69 124.42 L 203.65 124.42 L 203.65 148.46 C 203.65 151.66 209.52 153.39 215.04 153.39 C 220.57 153.39 226.44 151.66 226.44 148.46 L 226.44 145.92 L 225.02 145.92 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 173px; margin-left: 224px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Provisioned
                                <br style="border-color: var(--border-color);"/>
                                or
                                <br style="border-color: var(--border-color);"/>
                                ServerlessV2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="224" y="185" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Provisio...
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>