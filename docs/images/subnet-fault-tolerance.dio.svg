<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="772px" height="571px" viewBox="-0.5 -0.5 772 571" content="&lt;mxfile&gt;&lt;diagram name=&quot;250116&quot; id=&quot;p_q-UCKEu48xj0kOMTn_&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f78e04-1-d05c17-1-s-0">
            <stop offset="0%" style="stop-color: rgb(208, 92, 23); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(247, 142, 4); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <path d="M 0 0 L 770 0 L 770 570 L 0 570 Z" fill="none" stroke="#248814" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 10.59 6.65 C 10.53 6.65 10.48 6.65 10.42 6.65 L 10.42 6.65 C 9.11 6.68 8.03 7.24 7.14 8.25 C 7.13 8.25 7.13 8.25 7.13 8.25 C 6.2 9.36 5.87 10.52 5.96 11.73 C 4.81 12.06 4.12 12.92 3.76 13.74 C 3.75 13.75 3.75 13.76 3.74 13.78 C 3.33 15.05 3.68 16.36 4.24 17.16 C 4.25 17.17 4.25 17.17 4.26 17.18 C 4.94 18.05 5.97 18.53 7.02 18.53 L 18.17 18.53 C 19.19 18.53 20.07 18.16 20.8 17.37 C 21.25 16.94 21.49 16.29 21.58 15.59 C 21.67 14.9 21.61 14.16 21.32 13.55 C 21.31 13.54 21.31 13.53 21.31 13.52 C 20.8 12.62 19.95 11.81 18.76 11.64 C 18.74 10.79 18.28 9.99 17.68 9.56 C 17.67 9.55 17.66 9.55 17.65 9.54 C 17.01 9.18 16.4 9.14 15.91 9.3 C 15.6 9.4 15.36 9.56 15.14 9.74 C 14.51 8.36 13.43 7.18 11.81 6.79 C 11.81 6.79 11.81 6.79 11.81 6.79 C 11.38 6.7 10.97 6.65 10.59 6.65 Z M 10.43 7.38 C 10.8 7.38 11.2 7.43 11.64 7.53 C 13.16 7.89 14.15 9.07 14.66 10.48 C 14.71 10.6 14.81 10.69 14.94 10.72 C 15.07 10.74 15.2 10.7 15.29 10.61 C 15.54 10.34 15.83 10.11 16.14 10.01 C 16.44 9.91 16.78 9.92 17.26 10.18 C 17.67 10.49 18.11 11.31 18.03 11.9 C 18.01 12.01 18.05 12.12 18.12 12.2 C 18.19 12.28 18.29 12.33 18.39 12.33 C 19.46 12.34 20.16 13.02 20.64 13.88 C 20.85 14.3 20.91 14.92 20.84 15.5 C 20.76 16.07 20.53 16.59 20.28 16.83 C 20.27 16.84 20.27 16.85 20.26 16.85 C 19.65 17.53 19.03 17.78 18.17 17.78 L 7.02 17.78 C 6.2 17.78 5.39 17.41 4.85 16.73 C 4.44 16.13 4.14 15.02 4.46 14.02 C 4.79 13.27 5.36 12.55 6.41 12.36 C 6.6 12.32 6.74 12.14 6.71 11.94 C 6.56 10.79 6.8 9.81 7.7 8.74 C 8.49 7.85 9.33 7.39 10.43 7.38 Z M 12.2 10.7 C 11.77 10.7 11.4 10.93 11.13 11.21 C 10.85 11.5 10.64 11.85 10.64 12.25 L 10.64 12.71 L 10.14 12.71 C 10.04 12.71 9.94 12.75 9.87 12.82 C 9.8 12.89 9.76 12.98 9.76 13.08 L 9.76 15.7 C 9.76 15.8 9.8 15.89 9.87 15.96 C 9.94 16.03 10.04 16.07 10.14 16.07 L 14.16 16.07 C 14.26 16.07 14.35 16.03 14.42 15.96 C 14.49 15.89 14.53 15.8 14.53 15.7 L 14.53 13.08 C 14.53 12.98 14.49 12.89 14.42 12.82 C 14.35 12.75 14.26 12.71 14.16 12.71 L 13.68 12.71 L 13.68 12.25 C 13.68 11.84 13.47 11.47 13.21 11.2 C 12.94 10.92 12.61 10.7 12.2 10.7 Z M 12.2 11.45 C 12.29 11.45 12.5 11.54 12.67 11.72 C 12.83 11.89 12.93 12.11 12.93 12.25 L 12.93 12.71 L 11.39 12.71 L 11.39 12.25 C 11.39 12.15 11.49 11.91 11.66 11.74 C 11.83 11.56 12.06 11.45 12.2 11.45 Z M 10.51 13.46 L 13.78 13.46 L 13.78 15.32 L 10.51 15.32 Z M 0 25 L 0 0 L 25 0 L 25 25 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 738px; height: 1px; padding-top: 7px; margin-left: 32px;">
                        <div data-drawio-colors="color: #AAB7B8; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(170, 183, 184); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                VPC
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="32" y="19" fill="#AAB7B8" font-family="Helvetica" font-size="12px">
                    VPC
                </text>
            </switch>
        </g>
        <rect x="498" y="93" width="220" height="450" fill="#cbcfd2" stroke="#23445d" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="607.5" y="110.5">
                Availability Zone 1d
            </text>
        </g>
        <rect x="28" y="93" width="220" height="450" fill="none" stroke="#147eba" stroke-width="2" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="137.5" y="110.5">
                Availability Zone 1a
            </text>
        </g>
        <path d="M 38 123 L 238 123 L 238 253 L 38 253 Z" fill="#e9f3e6" stroke="none" pointer-events="none"/>
        <path d="M 38 123 L 63 123 L 63 148 L 38 148 Z M 50.52 126.21 C 49.4 126.21 48.31 126.63 47.48 127.39 C 46.67 128.11 46.2 129.15 46.2 130.24 L 46.2 132.78 L 43.89 132.78 C 43.8 132.78 43.7 132.82 43.64 132.89 C 43.57 132.95 43.54 133.04 43.54 133.13 L 43.54 144.43 C 43.54 144.63 43.7 144.79 43.89 144.79 L 57.11 144.79 C 57.3 144.79 57.46 144.63 57.46 144.43 L 57.46 133.15 C 57.47 133.06 57.43 132.97 57.36 132.9 C 57.3 132.83 57.21 132.79 57.11 132.79 L 54.81 132.79 L 54.81 130.29 C 54.8 129.21 54.35 128.18 53.56 127.44 C 52.74 126.65 51.65 126.22 50.52 126.21 Z M 50.51 126.93 C 51.46 126.92 52.37 127.28 53.06 127.93 C 53.72 128.54 54.1 129.4 54.1 130.29 L 54.1 132.79 L 46.88 132.79 L 46.89 130.26 C 46.9 129.36 47.28 128.51 47.95 127.91 C 48.65 127.27 49.57 126.92 50.51 126.93 Z M 44.24 133.5 L 56.76 133.5 L 56.75 144.07 L 44.24 144.07 Z M 50.51 135.74 C 49.48 135.73 48.61 136.51 48.51 137.53 C 48.42 138.56 49.13 139.48 50.14 139.66 L 50.14 142.44 L 50.86 142.44 L 50.86 139.66 C 51.79 139.49 52.47 138.67 52.48 137.72 C 52.48 136.63 51.6 135.75 50.51 135.74 Z M 50.39 136.45 C 50.43 136.45 50.47 136.45 50.51 136.46 C 50.84 136.46 51.16 136.59 51.4 136.83 C 51.64 137.07 51.77 137.39 51.76 137.72 C 51.77 138.06 51.64 138.38 51.4 138.61 C 51.16 138.85 50.84 138.98 50.51 138.98 C 50.04 139.02 49.6 138.8 49.34 138.42 C 49.08 138.03 49.06 137.53 49.28 137.12 C 49.5 136.71 49.93 136.46 50.39 136.45 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 130px; margin-left: 70px;">
                        <div data-drawio-colors="color: #248814; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(36, 136, 20); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Public subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="70" y="142" fill="#248814" font-family="Helvetica" font-size="12px">
                    Public subnet
                </text>
            </switch>
        </g>
        <rect x="48" y="173" width="60" height="60" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 63.85 219.25 L 63.85 215.38 L 66.27 217.32 Z M 63.34 211.48 C 62.93 211.15 62.37 211.09 61.9 211.32 C 61.43 211.54 61.13 212.02 61.13 212.55 L 61.13 222.09 C 61.13 222.62 61.43 223.09 61.9 223.32 C 62.09 223.41 62.29 223.45 62.49 223.45 C 62.79 223.45 63.09 223.35 63.34 223.16 L 69.31 218.38 C 69.63 218.12 69.82 217.73 69.82 217.32 C 69.82 216.9 69.63 216.51 69.31 216.25 Z M 96.07 206.87 L 96.07 201.52 L 98.74 204.19 Z M 101.63 203.23 L 95.67 197.26 C 95.28 196.87 94.69 196.76 94.18 196.97 C 93.67 197.18 93.34 197.68 93.34 198.23 L 93.34 202.83 L 82.94 202.83 L 82.94 189.88 C 82.94 189.12 82.33 188.51 81.58 188.51 L 71.09 188.51 L 71.09 191.24 L 80.22 191.24 L 80.22 202.83 L 70.84 202.83 L 70.84 205.56 L 80.22 205.56 L 80.22 217.15 L 71.09 217.15 L 71.09 219.87 L 81.58 219.87 C 82.33 219.87 82.94 219.27 82.94 218.51 L 82.94 205.56 L 93.34 205.56 L 93.34 210.16 C 93.34 210.71 93.67 211.21 94.18 211.42 C 94.35 211.49 94.53 211.52 94.7 211.52 C 95.06 211.52 95.41 211.38 95.67 211.12 L 101.63 205.16 C 102.17 204.62 102.17 203.76 101.63 203.23 Z M 63.85 206.13 L 63.85 202.26 L 66.27 204.19 Z M 63.34 198.36 C 62.93 198.03 62.37 197.96 61.9 198.19 C 61.43 198.42 61.13 198.9 61.13 199.42 L 61.13 208.97 C 61.13 209.49 61.43 209.97 61.9 210.19 C 62.09 210.29 62.29 210.33 62.49 210.33 C 62.79 210.33 63.09 210.23 63.34 210.03 L 69.31 205.26 C 69.63 205 69.82 204.61 69.82 204.19 C 69.82 203.78 69.63 203.39 69.31 203.13 Z M 63.85 191.81 L 63.85 187.94 L 66.27 189.88 Z M 63.34 184.04 C 62.93 183.71 62.37 183.65 61.9 183.87 C 61.43 184.1 61.13 184.58 61.13 185.1 L 61.13 194.65 C 61.13 195.17 61.43 195.65 61.9 195.88 C 62.09 195.97 62.29 196.01 62.49 196.01 C 62.79 196.01 63.09 195.91 63.34 195.71 L 69.31 190.94 C 69.63 190.68 69.82 190.29 69.82 189.88 C 69.82 189.46 69.63 189.07 69.31 188.81 Z M 78 230.27 C 62.96 230.27 50.73 218.04 50.73 203 C 50.73 187.96 62.96 175.73 78 175.73 C 93.04 175.73 105.27 187.96 105.27 203 C 105.27 218.04 93.04 230.27 78 230.27 Z M 78 173 C 61.46 173 48 186.46 48 203 C 48 219.54 61.46 233 78 233 C 94.54 233 108 219.54 108 203 C 108 186.46 94.54 173 78 173 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 240px; margin-left: 78px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                NAT Gateway
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="78" y="252" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    NAT Gateway
                </text>
            </switch>
        </g>
        <rect x="111" y="149" width="54" height="54" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 138 149 C 123.11 149 111 161.11 111 176 C 111 190.89 123.11 203 138 203 C 152.89 203 165 190.89 165 176 C 165 161.11 152.89 149 138 149 Z M 138 200.54 C 124.47 200.54 113.45 189.53 113.45 176 C 113.45 162.47 124.47 151.46 138 151.46 C 151.53 151.46 162.55 162.47 162.55 176 C 162.55 189.53 151.53 200.54 138 200.54 Z M 154 183.36 L 152.11 183.36 L 152.11 179.22 C 152.11 178.54 151.56 177.99 150.89 177.99 L 147.82 177.99 L 147.82 173.85 C 147.82 173.17 147.27 172.63 146.59 172.63 L 139.23 172.63 L 139.23 169.71 L 146.59 169.71 C 147.27 169.71 147.82 169.16 147.82 168.48 L 147.82 158.82 C 147.82 158.14 147.27 157.59 146.59 157.59 L 129.41 157.59 C 128.73 157.59 128.18 158.14 128.18 158.82 L 128.18 168.48 C 128.18 169.16 128.73 169.71 129.41 169.71 L 136.77 169.71 L 136.77 172.63 L 129.41 172.63 C 128.73 172.63 128.18 173.17 128.18 173.85 L 128.18 177.99 L 125.11 177.99 C 124.44 177.99 123.89 178.54 123.89 179.22 L 123.89 183.36 L 122 183.36 C 121.32 183.36 120.78 183.91 120.78 184.59 L 120.78 189.96 C 120.78 190.64 121.32 191.19 122 191.19 L 127.26 191.19 C 127.94 191.19 128.49 190.64 128.49 189.96 L 128.49 184.59 C 128.49 183.91 127.94 183.36 127.26 183.36 L 126.34 183.36 L 126.34 180.45 L 131.4 180.45 L 131.4 183.36 L 130.48 183.36 C 129.81 183.36 129.26 183.91 129.26 184.59 L 129.26 189.96 C 129.26 190.64 129.81 191.19 130.48 191.19 L 135.85 191.19 C 136.53 191.19 137.08 190.64 137.08 189.96 L 137.08 184.59 C 137.08 183.91 136.53 183.36 135.85 183.36 L 133.86 183.36 L 133.86 179.22 C 133.86 178.54 133.31 177.99 132.63 177.99 L 130.64 177.99 L 130.64 175.08 L 145.36 175.08 L 145.36 177.99 L 143.37 177.99 C 142.69 177.99 142.14 178.54 142.14 179.22 L 142.14 183.36 L 140.15 183.36 C 139.47 183.36 138.92 183.91 138.92 184.59 L 138.92 189.96 C 138.92 190.64 139.47 191.19 140.15 191.19 L 145.52 191.19 C 146.19 191.19 146.74 190.64 146.74 189.96 L 146.74 184.59 C 146.74 183.91 146.19 183.36 145.52 183.36 L 144.6 183.36 L 144.6 180.45 L 149.66 180.45 L 149.66 183.36 L 148.66 183.36 C 147.99 183.36 147.44 183.91 147.44 184.59 L 147.44 189.96 C 147.44 190.64 147.99 191.19 148.66 191.19 L 154 191.19 C 154.67 191.19 155.22 190.64 155.22 189.96 L 155.22 184.59 C 155.22 183.91 154.67 183.36 154 183.36 Z M 130.64 167.26 L 130.64 160.05 L 145.36 160.05 L 145.36 167.26 Z M 123.23 188.73 L 123.23 185.82 L 126.03 185.82 L 126.03 188.73 Z M 131.71 188.73 L 131.71 185.82 L 134.63 185.82 L 134.63 188.73 Z M 141.38 188.73 L 141.38 185.82 L 144.29 185.82 L 144.29 188.73 Z M 149.89 188.73 L 149.89 185.82 L 152.77 185.82 L 152.77 188.73 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 210px; margin-left: 138px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="138" y="222" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
        <path d="M 38 263 L 238 263 L 238 393 L 38 393 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 38 263 L 63 263 L 63 288 L 38 288 Z M 50.52 266.21 C 49.4 266.21 48.31 266.63 47.48 267.39 C 46.67 268.11 46.2 269.15 46.2 270.24 L 46.2 272.78 L 43.89 272.78 C 43.8 272.78 43.7 272.82 43.64 272.89 C 43.57 272.95 43.54 273.04 43.54 273.13 L 43.54 284.43 C 43.54 284.63 43.7 284.79 43.89 284.79 L 57.11 284.79 C 57.3 284.79 57.46 284.63 57.46 284.43 L 57.46 273.15 C 57.47 273.06 57.43 272.97 57.36 272.9 C 57.3 272.83 57.21 272.79 57.11 272.79 L 54.81 272.79 L 54.81 270.29 C 54.8 269.21 54.35 268.18 53.56 267.44 C 52.74 266.65 51.65 266.22 50.52 266.21 Z M 50.51 266.93 C 51.46 266.92 52.37 267.28 53.06 267.93 C 53.72 268.54 54.1 269.4 54.1 270.29 L 54.1 272.79 L 46.88 272.79 L 46.89 270.26 C 46.9 269.36 47.28 268.51 47.95 267.91 C 48.65 267.27 49.57 266.92 50.51 266.93 Z M 44.24 273.5 L 56.76 273.5 L 56.75 284.07 L 44.24 284.07 Z M 50.51 275.74 C 49.48 275.73 48.61 276.51 48.51 277.53 C 48.42 278.56 49.13 279.48 50.14 279.66 L 50.14 282.44 L 50.86 282.44 L 50.86 279.66 C 51.79 279.49 52.47 278.67 52.48 277.72 C 52.48 276.63 51.6 275.75 50.51 275.74 Z M 50.39 276.45 C 50.43 276.45 50.47 276.45 50.51 276.46 C 50.84 276.46 51.16 276.59 51.4 276.83 C 51.64 277.07 51.77 277.39 51.76 277.72 C 51.77 278.06 51.64 278.38 51.4 278.61 C 51.16 278.85 50.84 278.98 50.51 278.98 C 50.04 279.02 49.6 278.8 49.34 278.42 C 49.08 278.03 49.06 277.53 49.28 277.12 C 49.5 276.71 49.93 276.46 50.39 276.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 270px; margin-left: 70px;">
                        <div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Private subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="70" y="282" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Private subnet
                </text>
            </switch>
        </g>
        <rect x="63" y="323" width="48" height="31" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 109.91 323.09 L 64.09 323.09 C 63.49 323.09 63 323.58 63 324.18 L 63 352.82 C 63 353.42 63.49 353.91 64.09 353.91 L 109.91 353.91 C 110.51 353.91 111 353.42 111 352.82 L 111 324.18 C 111 323.58 110.51 323.09 109.91 323.09 Z M 65.18 351.73 L 65.18 325.27 L 108.82 325.27 L 108.82 351.73 Z M 68.73 349 L 70.91 349 L 70.91 328 L 68.73 328 Z M 74.45 349 L 76.64 349 L 76.64 328 L 74.45 328 Z M 80.18 349 L 82.36 349 L 82.36 328 L 80.18 328 Z M 85.91 349 L 88.09 349 L 88.09 328 L 85.91 328 Z M 91.63 349 L 93.82 349 L 93.82 328 L 91.63 328 Z M 97.36 349 L 99.55 349 L 99.55 328 L 97.36 328 Z M 103.09 349 L 105.27 349 L 105.27 328 L 103.09 328 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <path d="M 38 403 L 238 403 L 238 533 L 38 533 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 38 403 L 63 403 L 63 428 L 38 428 Z M 50.52 406.21 C 49.4 406.21 48.31 406.63 47.48 407.39 C 46.67 408.11 46.2 409.15 46.2 410.24 L 46.2 412.78 L 43.89 412.78 C 43.8 412.78 43.7 412.82 43.64 412.89 C 43.57 412.95 43.54 413.04 43.54 413.13 L 43.54 424.43 C 43.54 424.63 43.7 424.79 43.89 424.79 L 57.11 424.79 C 57.3 424.79 57.46 424.63 57.46 424.43 L 57.46 413.15 C 57.47 413.06 57.43 412.97 57.36 412.9 C 57.3 412.83 57.21 412.79 57.11 412.79 L 54.81 412.79 L 54.81 410.29 C 54.8 409.21 54.35 408.18 53.56 407.44 C 52.74 406.65 51.65 406.22 50.52 406.21 Z M 50.51 406.93 C 51.46 406.92 52.37 407.28 53.06 407.93 C 53.72 408.54 54.1 409.4 54.1 410.29 L 54.1 412.79 L 46.88 412.79 L 46.89 410.26 C 46.9 409.36 47.28 408.51 47.95 407.91 C 48.65 407.27 49.57 406.92 50.51 406.93 Z M 44.24 413.5 L 56.76 413.5 L 56.75 424.07 L 44.24 424.07 Z M 50.51 415.74 C 49.48 415.73 48.61 416.51 48.51 417.53 C 48.42 418.56 49.13 419.48 50.14 419.66 L 50.14 422.44 L 50.86 422.44 L 50.86 419.66 C 51.79 419.49 52.47 418.67 52.48 417.72 C 52.48 416.63 51.6 415.75 50.51 415.74 Z M 50.39 416.45 C 50.43 416.45 50.47 416.45 50.51 416.46 C 50.84 416.46 51.16 416.59 51.4 416.83 C 51.64 417.07 51.77 417.39 51.76 417.72 C 51.77 418.06 51.64 418.38 51.4 418.61 C 51.16 418.85 50.84 418.98 50.51 418.98 C 50.04 419.02 49.6 418.8 49.34 418.42 C 49.08 418.03 49.06 417.53 49.28 417.12 C 49.5 416.71 49.93 416.46 50.39 416.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 410px; margin-left: 70px;">
                        <div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Protected subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="70" y="422" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <rect x="262" y="93" width="220" height="450" fill="none" stroke="#147eba" stroke-width="2" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="371.5" y="110.5">
                Availability Zone 1c
            </text>
        </g>
        <path d="M 272 123 L 472 123 L 472 253 L 272 253 Z" fill="#e9f3e6" stroke="none" pointer-events="none"/>
        <path d="M 272 123 L 297 123 L 297 148 L 272 148 Z M 284.52 126.21 C 283.4 126.21 282.31 126.63 281.49 127.39 C 280.67 128.11 280.2 129.15 280.2 130.24 L 280.2 132.78 L 277.89 132.78 C 277.8 132.78 277.7 132.82 277.64 132.89 C 277.57 132.95 277.54 133.04 277.54 133.13 L 277.54 144.43 C 277.54 144.63 277.7 144.79 277.89 144.79 L 291.11 144.79 C 291.3 144.79 291.46 144.63 291.46 144.43 L 291.46 133.15 C 291.47 133.06 291.43 132.97 291.36 132.9 C 291.3 132.83 291.21 132.79 291.11 132.79 L 288.81 132.79 L 288.81 130.29 C 288.8 129.21 288.35 128.18 287.56 127.44 C 286.74 126.65 285.65 126.22 284.52 126.21 Z M 284.51 126.93 C 285.46 126.92 286.37 127.28 287.06 127.93 C 287.72 128.54 288.1 129.4 288.1 130.29 L 288.1 132.79 L 280.88 132.79 L 280.89 130.26 C 280.9 129.36 281.28 128.51 281.95 127.91 C 282.65 127.27 283.57 126.92 284.51 126.93 Z M 278.24 133.5 L 290.76 133.5 L 290.75 144.07 L 278.24 144.07 Z M 284.51 135.74 C 283.48 135.73 282.61 136.51 282.51 137.53 C 282.42 138.56 283.13 139.48 284.14 139.66 L 284.14 142.44 L 284.86 142.44 L 284.86 139.66 C 285.79 139.49 286.47 138.67 286.48 137.72 C 286.48 136.63 285.6 135.75 284.51 135.74 Z M 284.39 136.45 C 284.43 136.45 284.47 136.45 284.51 136.46 C 284.84 136.46 285.16 136.59 285.4 136.83 C 285.64 137.07 285.77 137.39 285.76 137.72 C 285.77 138.06 285.64 138.38 285.4 138.61 C 285.16 138.85 284.84 138.98 284.51 138.98 C 284.04 139.02 283.6 138.8 283.34 138.42 C 283.08 138.03 283.06 137.53 283.28 137.12 C 283.5 136.71 283.93 136.46 284.39 136.45 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 130px; margin-left: 304px;">
                        <div data-drawio-colors="color: #248814; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(36, 136, 20); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Public subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="304" y="142" fill="#248814" font-family="Helvetica" font-size="12px">
                    Public subnet
                </text>
            </switch>
        </g>
        <rect x="278" y="173" width="60" height="60" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 293.85 219.25 L 293.85 215.38 L 296.27 217.32 Z M 293.34 211.48 C 292.93 211.15 292.37 211.09 291.9 211.32 C 291.43 211.54 291.13 212.02 291.13 212.55 L 291.13 222.09 C 291.13 222.62 291.43 223.09 291.9 223.32 C 292.09 223.41 292.29 223.45 292.49 223.45 C 292.79 223.45 293.09 223.35 293.34 223.16 L 299.31 218.38 C 299.63 218.12 299.82 217.73 299.82 217.32 C 299.82 216.9 299.63 216.51 299.31 216.25 Z M 326.07 206.87 L 326.07 201.52 L 328.74 204.19 Z M 331.63 203.23 L 325.67 197.26 C 325.28 196.87 324.69 196.76 324.18 196.97 C 323.67 197.18 323.34 197.68 323.34 198.23 L 323.34 202.83 L 312.94 202.83 L 312.94 189.88 C 312.94 189.12 312.33 188.51 311.58 188.51 L 301.09 188.51 L 301.09 191.24 L 310.22 191.24 L 310.22 202.83 L 300.84 202.83 L 300.84 205.56 L 310.22 205.56 L 310.22 217.15 L 301.09 217.15 L 301.09 219.87 L 311.58 219.87 C 312.33 219.87 312.94 219.27 312.94 218.51 L 312.94 205.56 L 323.34 205.56 L 323.34 210.16 C 323.34 210.71 323.67 211.21 324.18 211.42 C 324.35 211.49 324.53 211.52 324.7 211.52 C 325.06 211.52 325.41 211.38 325.67 211.12 L 331.63 205.16 C 332.17 204.62 332.17 203.76 331.63 203.23 Z M 293.85 206.13 L 293.85 202.26 L 296.27 204.19 Z M 293.34 198.36 C 292.93 198.03 292.37 197.96 291.9 198.19 C 291.43 198.42 291.13 198.9 291.13 199.42 L 291.13 208.97 C 291.13 209.49 291.43 209.97 291.9 210.19 C 292.09 210.29 292.29 210.33 292.49 210.33 C 292.79 210.33 293.09 210.23 293.34 210.03 L 299.31 205.26 C 299.63 205 299.82 204.61 299.82 204.19 C 299.82 203.78 299.63 203.39 299.31 203.13 Z M 293.85 191.81 L 293.85 187.94 L 296.27 189.88 Z M 293.34 184.04 C 292.93 183.71 292.37 183.65 291.9 183.87 C 291.43 184.1 291.13 184.58 291.13 185.1 L 291.13 194.65 C 291.13 195.17 291.43 195.65 291.9 195.88 C 292.09 195.97 292.29 196.01 292.49 196.01 C 292.79 196.01 293.09 195.91 293.34 195.71 L 299.31 190.94 C 299.63 190.68 299.82 190.29 299.82 189.88 C 299.82 189.46 299.63 189.07 299.31 188.81 Z M 308 230.27 C 292.96 230.27 280.73 218.04 280.73 203 C 280.73 187.96 292.96 175.73 308 175.73 C 323.04 175.73 335.27 187.96 335.27 203 C 335.27 218.04 323.04 230.27 308 230.27 Z M 308 173 C 291.46 173 278 186.46 278 203 C 278 219.54 291.46 233 308 233 C 324.54 233 338 219.54 338 203 C 338 186.46 324.54 173 308 173 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 240px; margin-left: 308px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                NAT Gateway
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="308" y="252" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    NAT Gateway
                </text>
            </switch>
        </g>
        <rect x="350" y="149" width="54" height="54" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 377 149 C 362.11 149 350 161.11 350 176 C 350 190.89 362.11 203 377 203 C 391.89 203 404 190.89 404 176 C 404 161.11 391.89 149 377 149 Z M 377 200.54 C 363.47 200.54 352.45 189.53 352.45 176 C 352.45 162.47 363.47 151.46 377 151.46 C 390.53 151.46 401.55 162.47 401.55 176 C 401.55 189.53 390.53 200.54 377 200.54 Z M 393 183.36 L 391.11 183.36 L 391.11 179.22 C 391.11 178.54 390.56 177.99 389.89 177.99 L 386.82 177.99 L 386.82 173.85 C 386.82 173.17 386.27 172.63 385.59 172.63 L 378.23 172.63 L 378.23 169.71 L 385.59 169.71 C 386.27 169.71 386.82 169.16 386.82 168.48 L 386.82 158.82 C 386.82 158.14 386.27 157.59 385.59 157.59 L 368.41 157.59 C 367.73 157.59 367.18 158.14 367.18 158.82 L 367.18 168.48 C 367.18 169.16 367.73 169.71 368.41 169.71 L 375.77 169.71 L 375.77 172.63 L 368.41 172.63 C 367.73 172.63 367.18 173.17 367.18 173.85 L 367.18 177.99 L 364.11 177.99 C 363.44 177.99 362.89 178.54 362.89 179.22 L 362.89 183.36 L 361 183.36 C 360.32 183.36 359.78 183.91 359.78 184.59 L 359.78 189.96 C 359.78 190.64 360.32 191.19 361 191.19 L 366.26 191.19 C 366.94 191.19 367.49 190.64 367.49 189.96 L 367.49 184.59 C 367.49 183.91 366.94 183.36 366.26 183.36 L 365.34 183.36 L 365.34 180.45 L 370.4 180.45 L 370.4 183.36 L 369.48 183.36 C 368.81 183.36 368.26 183.91 368.26 184.59 L 368.26 189.96 C 368.26 190.64 368.81 191.19 369.48 191.19 L 374.85 191.19 C 375.53 191.19 376.08 190.64 376.08 189.96 L 376.08 184.59 C 376.08 183.91 375.53 183.36 374.85 183.36 L 372.86 183.36 L 372.86 179.22 C 372.86 178.54 372.31 177.99 371.63 177.99 L 369.64 177.99 L 369.64 175.08 L 384.36 175.08 L 384.36 177.99 L 382.37 177.99 C 381.69 177.99 381.14 178.54 381.14 179.22 L 381.14 183.36 L 379.15 183.36 C 378.47 183.36 377.92 183.91 377.92 184.59 L 377.92 189.96 C 377.92 190.64 378.47 191.19 379.15 191.19 L 384.52 191.19 C 385.19 191.19 385.74 190.64 385.74 189.96 L 385.74 184.59 C 385.74 183.91 385.19 183.36 384.52 183.36 L 383.6 183.36 L 383.6 180.45 L 388.66 180.45 L 388.66 183.36 L 387.66 183.36 C 386.99 183.36 386.44 183.91 386.44 184.59 L 386.44 189.96 C 386.44 190.64 386.99 191.19 387.66 191.19 L 393 191.19 C 393.67 191.19 394.22 190.64 394.22 189.96 L 394.22 184.59 C 394.22 183.91 393.67 183.36 393 183.36 Z M 369.64 167.26 L 369.64 160.05 L 384.36 160.05 L 384.36 167.26 Z M 362.23 188.73 L 362.23 185.82 L 365.03 185.82 L 365.03 188.73 Z M 370.71 188.73 L 370.71 185.82 L 373.63 185.82 L 373.63 188.73 Z M 380.38 188.73 L 380.38 185.82 L 383.29 185.82 L 383.29 188.73 Z M 388.89 188.73 L 388.89 185.82 L 391.77 185.82 L 391.77 188.73 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 210px; margin-left: 377px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="377" y="222" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
        <path d="M 272 263 L 472 263 L 472 393 L 272 393 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 272 263 L 297 263 L 297 288 L 272 288 Z M 284.52 266.21 C 283.4 266.21 282.31 266.63 281.49 267.39 C 280.67 268.11 280.2 269.15 280.2 270.24 L 280.2 272.78 L 277.89 272.78 C 277.8 272.78 277.7 272.82 277.64 272.89 C 277.57 272.95 277.54 273.04 277.54 273.13 L 277.54 284.43 C 277.54 284.63 277.7 284.79 277.89 284.79 L 291.11 284.79 C 291.3 284.79 291.46 284.63 291.46 284.43 L 291.46 273.15 C 291.47 273.06 291.43 272.97 291.36 272.9 C 291.3 272.83 291.21 272.79 291.11 272.79 L 288.81 272.79 L 288.81 270.29 C 288.8 269.21 288.35 268.18 287.56 267.44 C 286.74 266.65 285.65 266.22 284.52 266.21 Z M 284.51 266.93 C 285.46 266.92 286.37 267.28 287.06 267.93 C 287.72 268.54 288.1 269.4 288.1 270.29 L 288.1 272.79 L 280.88 272.79 L 280.89 270.26 C 280.9 269.36 281.28 268.51 281.95 267.91 C 282.65 267.27 283.57 266.92 284.51 266.93 Z M 278.24 273.5 L 290.76 273.5 L 290.75 284.07 L 278.24 284.07 Z M 284.51 275.74 C 283.48 275.73 282.61 276.51 282.51 277.53 C 282.42 278.56 283.13 279.48 284.14 279.66 L 284.14 282.44 L 284.86 282.44 L 284.86 279.66 C 285.79 279.49 286.47 278.67 286.48 277.72 C 286.48 276.63 285.6 275.75 284.51 275.74 Z M 284.39 276.45 C 284.43 276.45 284.47 276.45 284.51 276.46 C 284.84 276.46 285.16 276.59 285.4 276.83 C 285.64 277.07 285.77 277.39 285.76 277.72 C 285.77 278.06 285.64 278.38 285.4 278.61 C 285.16 278.85 284.84 278.98 284.51 278.98 C 284.04 279.02 283.6 278.8 283.34 278.42 C 283.08 278.03 283.06 277.53 283.28 277.12 C 283.5 276.71 283.93 276.46 284.39 276.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 270px; margin-left: 304px;">
                        <div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Private subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="304" y="282" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Private subnet
                </text>
            </switch>
        </g>
        <rect x="302" y="323" width="48" height="31" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 348.91 323.09 L 303.09 323.09 C 302.49 323.09 302 323.58 302 324.18 L 302 352.82 C 302 353.42 302.49 353.91 303.09 353.91 L 348.91 353.91 C 349.51 353.91 350 353.42 350 352.82 L 350 324.18 C 350 323.58 349.51 323.09 348.91 323.09 Z M 304.18 351.73 L 304.18 325.27 L 347.82 325.27 L 347.82 351.73 Z M 307.73 349 L 309.91 349 L 309.91 328 L 307.73 328 Z M 313.45 349 L 315.64 349 L 315.64 328 L 313.45 328 Z M 319.18 349 L 321.36 349 L 321.36 328 L 319.18 328 Z M 324.91 349 L 327.09 349 L 327.09 328 L 324.91 328 Z M 330.63 349 L 332.82 349 L 332.82 328 L 330.63 328 Z M 336.36 349 L 338.55 349 L 338.55 328 L 336.36 328 Z M 342.09 349 L 344.27 349 L 344.27 328 L 342.09 328 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <path d="M 272 403 L 472 403 L 472 533 L 272 533 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 272 403 L 297 403 L 297 428 L 272 428 Z M 284.52 406.21 C 283.4 406.21 282.31 406.63 281.49 407.39 C 280.67 408.11 280.2 409.15 280.2 410.24 L 280.2 412.78 L 277.89 412.78 C 277.8 412.78 277.7 412.82 277.64 412.89 C 277.57 412.95 277.54 413.04 277.54 413.13 L 277.54 424.43 C 277.54 424.63 277.7 424.79 277.89 424.79 L 291.11 424.79 C 291.3 424.79 291.46 424.63 291.46 424.43 L 291.46 413.15 C 291.47 413.06 291.43 412.97 291.36 412.9 C 291.3 412.83 291.21 412.79 291.11 412.79 L 288.81 412.79 L 288.81 410.29 C 288.8 409.21 288.35 408.18 287.56 407.44 C 286.74 406.65 285.65 406.22 284.52 406.21 Z M 284.51 406.93 C 285.46 406.92 286.37 407.28 287.06 407.93 C 287.72 408.54 288.1 409.4 288.1 410.29 L 288.1 412.79 L 280.88 412.79 L 280.89 410.26 C 280.9 409.36 281.28 408.51 281.95 407.91 C 282.65 407.27 283.57 406.92 284.51 406.93 Z M 278.24 413.5 L 290.76 413.5 L 290.75 424.07 L 278.24 424.07 Z M 284.51 415.74 C 283.48 415.73 282.61 416.51 282.51 417.53 C 282.42 418.56 283.13 419.48 284.14 419.66 L 284.14 422.44 L 284.86 422.44 L 284.86 419.66 C 285.79 419.49 286.47 418.67 286.48 417.72 C 286.48 416.63 285.6 415.75 284.51 415.74 Z M 284.39 416.45 C 284.43 416.45 284.47 416.45 284.51 416.46 C 284.84 416.46 285.16 416.59 285.4 416.83 C 285.64 417.07 285.77 417.39 285.76 417.72 C 285.77 418.06 285.64 418.38 285.4 418.61 C 285.16 418.85 284.84 418.98 284.51 418.98 C 284.04 419.02 283.6 418.8 283.34 418.42 C 283.08 418.03 283.06 417.53 283.28 417.12 C 283.5 416.71 283.93 416.46 284.39 416.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 410px; margin-left: 304px;">
                        <div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Protected subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="304" y="422" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <image x="351.5" y="442.5" width="50" height="50" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAH0AAAB9CAMAAAC4XpwXAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURckl0dBA18wz1PHI8/////XW9tdc3fjk+dpp3+678d134uSS6Oef6+ut7vzx/OGF5dNO2tAF4ssAAAAJcEhZcwAAFxEAABcRAcom8z8AAAQbSURBVGhD7Zphu6IgEEYNQlEQ/f+/dt8ZRrRS08J799nlfEkxOKYyA1hVKBQKhUKhUChcw+0KpO13KH2/AiPNv6GWr+emlvb3+XV7I5v5aIr9PX+TvbYTXFvN+xsFzzUiZ+z3tpMdF59XxmPfyDajUdDLNhNWahC1DmfsqWLLOxHIbrIZIRl+1AJEtKcahOedY3ap73gbcc8wOHmxx30DLdlRLgV0WWB/qgEGbk8r3jmAlZa4rXgTumTn3aoak32MBVWs81QDX6QTHA+7Ceh7+sxgx4PQnHJzVX7wM9hRMHDBcdB2Ljsuo+WC46Aqp8Q89pO/XaEK97kMdvS3cOa+32rIAzeawa5QbjocOYSMbWK42barvqev+L6fGsaxNTttgFNjmxhsduyv4NiqXYLv4Ugb2ulqPrc1jtORV3AM93fFXo2UDQ7b5xyX2qqN4fDzFm/Mi/3TDJvsT6jbiBTadbaubxsP9DV2ZZ2mFLKgaV39cg4X2NXwJJ7R7rFXZbcrP6tDY4zWnGkTenmmue11NAVc54eMebOuldMy8+/PbO/IENxGl8M9IX0MkUReu4I8DHsRm7s1hV8mrx0RK7xLlTCmhJbXjuGehN8dcIqTMq/9UJ6Gckon2e1z+QYjLJfZMTaVnVUU94q8dtPGmQ13qHu72cjooABi7/H9DHbAT1u0g354vgJqtD4dFjtdhm/tcWAw2eXXgabtvSO8b3Ujge4e/PzUUdncRz6zV3YAXBF2W9l4MVbRCEU22VGtm0PTh/YZtoPFRU4E46Nqtj+SzU7cbOcwngTeD90i5/yIfZN/wz6YRpDmTtpT9Ybi81k7vi9Izjxpn3phrH/WLlXBt3aaf3xgx3CZieUn7VIX4edD++MYFfZTGTYic68c9jlub6AwBLnMHtdythlpuetre1CKZt0jPohYTnZMxOPOGnUc7Ytd6pIdH2fsj8zPPNMvkkcC06vpuNgXz3zka/u0Dhna3nV1jSnzWNtu8Bo/jTEYU39nf1jrJBb9nUfsWwRt1Wp/Z46vVcZbloiFZMeH7V9+FWFaviGzXeomYunHiB3gJjutmyaABsM+P6T582zPzGzfodgv4D+3f5DjcgH724ZplfUy+7sUS6vLF9rvd78ZtFQd5zlX2pFq3Gu+UOOQAvBl9pRjMIl0XUfvG7sBKS6tIGrMOi+z22qYcukKoR8v7+/WrU1jg3Y8mfuJaDNa17e0TAowkR/maWSJtBdQ7G+4zI6O9os5zscZ1i70BrWV7byMCCqLVw9r0MuLcHDWcBb++4TfXKmVlxPvV9E/JP57o/H25QrcMI3jTBN2ZpnfYlOO0UhyjpYxB79IcUHv35lvGXZWSs3WC6OMqKF9zbLBtD+gnrCdQ5ZjHlJcoVAoFAqFQuE/pKr+AAGLPAx/jszHAAAAAElFTkSuQmCC" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="376.5" y="510.5">
                ElastiCache
            </text>
        </g>
        <path d="M 508 123 L 708 123 L 708 253 L 508 253 Z" fill="#e9f3e6" stroke="none" pointer-events="none"/>
        <path d="M 508 123 L 533 123 L 533 148 L 508 148 Z M 520.52 126.21 C 519.4 126.21 518.31 126.63 517.49 127.39 C 516.67 128.11 516.2 129.15 516.2 130.24 L 516.2 132.78 L 513.89 132.78 C 513.8 132.78 513.7 132.82 513.64 132.89 C 513.57 132.95 513.54 133.04 513.54 133.13 L 513.54 144.43 C 513.54 144.63 513.7 144.79 513.89 144.79 L 527.11 144.79 C 527.3 144.79 527.46 144.63 527.46 144.43 L 527.46 133.15 C 527.47 133.06 527.43 132.97 527.36 132.9 C 527.3 132.83 527.21 132.79 527.11 132.79 L 524.81 132.79 L 524.81 130.29 C 524.8 129.21 524.35 128.18 523.56 127.44 C 522.74 126.65 521.65 126.22 520.52 126.21 Z M 520.51 126.93 C 521.46 126.92 522.37 127.28 523.06 127.93 C 523.72 128.54 524.1 129.4 524.1 130.29 L 524.1 132.79 L 516.88 132.79 L 516.89 130.26 C 516.9 129.36 517.28 128.51 517.95 127.91 C 518.65 127.27 519.57 126.92 520.51 126.93 Z M 514.24 133.5 L 526.76 133.5 L 526.75 144.07 L 514.24 144.07 Z M 520.51 135.74 C 519.48 135.73 518.61 136.51 518.51 137.53 C 518.42 138.56 519.13 139.48 520.14 139.66 L 520.14 142.44 L 520.86 142.44 L 520.86 139.66 C 521.79 139.49 522.47 138.67 522.48 137.72 C 522.48 136.63 521.6 135.75 520.51 135.74 Z M 520.39 136.45 C 520.43 136.45 520.47 136.45 520.51 136.46 C 520.84 136.46 521.16 136.59 521.4 136.83 C 521.64 137.07 521.77 137.39 521.76 137.72 C 521.77 138.06 521.64 138.38 521.4 138.61 C 521.16 138.85 520.84 138.98 520.51 138.98 C 520.04 139.02 519.6 138.8 519.34 138.42 C 519.08 138.03 519.06 137.53 519.28 137.12 C 519.5 136.71 519.93 136.46 520.39 136.45 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 130px; margin-left: 540px;">
                        <div data-drawio-colors="color: #248814; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(36, 136, 20); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Public subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="540" y="142" fill="#248814" font-family="Helvetica" font-size="12px">
                    Public subnet
                </text>
            </switch>
        </g>
        <rect x="586" y="149" width="54" height="54" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 613 149 C 598.11 149 586 161.11 586 176 C 586 190.89 598.11 203 613 203 C 627.89 203 640 190.89 640 176 C 640 161.11 627.89 149 613 149 Z M 613 200.54 C 599.47 200.54 588.45 189.53 588.45 176 C 588.45 162.47 599.47 151.46 613 151.46 C 626.53 151.46 637.55 162.47 637.55 176 C 637.55 189.53 626.53 200.54 613 200.54 Z M 629 183.36 L 627.11 183.36 L 627.11 179.22 C 627.11 178.54 626.56 177.99 625.89 177.99 L 622.82 177.99 L 622.82 173.85 C 622.82 173.17 622.27 172.63 621.59 172.63 L 614.23 172.63 L 614.23 169.71 L 621.59 169.71 C 622.27 169.71 622.82 169.16 622.82 168.48 L 622.82 158.82 C 622.82 158.14 622.27 157.59 621.59 157.59 L 604.41 157.59 C 603.73 157.59 603.18 158.14 603.18 158.82 L 603.18 168.48 C 603.18 169.16 603.73 169.71 604.41 169.71 L 611.77 169.71 L 611.77 172.63 L 604.41 172.63 C 603.73 172.63 603.18 173.17 603.18 173.85 L 603.18 177.99 L 600.11 177.99 C 599.44 177.99 598.89 178.54 598.89 179.22 L 598.89 183.36 L 597 183.36 C 596.32 183.36 595.78 183.91 595.78 184.59 L 595.78 189.96 C 595.78 190.64 596.32 191.19 597 191.19 L 602.26 191.19 C 602.94 191.19 603.49 190.64 603.49 189.96 L 603.49 184.59 C 603.49 183.91 602.94 183.36 602.26 183.36 L 601.34 183.36 L 601.34 180.45 L 606.4 180.45 L 606.4 183.36 L 605.48 183.36 C 604.81 183.36 604.26 183.91 604.26 184.59 L 604.26 189.96 C 604.26 190.64 604.81 191.19 605.48 191.19 L 610.85 191.19 C 611.53 191.19 612.08 190.64 612.08 189.96 L 612.08 184.59 C 612.08 183.91 611.53 183.36 610.85 183.36 L 608.86 183.36 L 608.86 179.22 C 608.86 178.54 608.31 177.99 607.63 177.99 L 605.64 177.99 L 605.64 175.08 L 620.36 175.08 L 620.36 177.99 L 618.37 177.99 C 617.69 177.99 617.14 178.54 617.14 179.22 L 617.14 183.36 L 615.15 183.36 C 614.47 183.36 613.92 183.91 613.92 184.59 L 613.92 189.96 C 613.92 190.64 614.47 191.19 615.15 191.19 L 620.52 191.19 C 621.19 191.19 621.74 190.64 621.74 189.96 L 621.74 184.59 C 621.74 183.91 621.19 183.36 620.52 183.36 L 619.6 183.36 L 619.6 180.45 L 624.66 180.45 L 624.66 183.36 L 623.66 183.36 C 622.99 183.36 622.44 183.91 622.44 184.59 L 622.44 189.96 C 622.44 190.64 622.99 191.19 623.66 191.19 L 629 191.19 C 629.67 191.19 630.22 190.64 630.22 189.96 L 630.22 184.59 C 630.22 183.91 629.67 183.36 629 183.36 Z M 605.64 167.26 L 605.64 160.05 L 620.36 160.05 L 620.36 167.26 Z M 598.23 188.73 L 598.23 185.82 L 601.03 185.82 L 601.03 188.73 Z M 606.71 188.73 L 606.71 185.82 L 609.63 185.82 L 609.63 188.73 Z M 616.38 188.73 L 616.38 185.82 L 619.29 185.82 L 619.29 188.73 Z M 624.89 188.73 L 624.89 185.82 L 627.77 185.82 L 627.77 188.73 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 210px; margin-left: 613px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="613" y="222" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
        <path d="M 508 263 L 708 263 L 708 399.83 L 508 399.83 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 508 263 L 533 263 L 533 288 L 508 288 Z M 520.52 266.21 C 519.4 266.21 518.31 266.63 517.49 267.39 C 516.67 268.11 516.2 269.15 516.2 270.24 L 516.2 272.78 L 513.89 272.78 C 513.8 272.78 513.7 272.82 513.64 272.89 C 513.57 272.95 513.54 273.04 513.54 273.13 L 513.54 284.43 C 513.54 284.63 513.7 284.79 513.89 284.79 L 527.11 284.79 C 527.3 284.79 527.46 284.63 527.46 284.43 L 527.46 273.15 C 527.47 273.06 527.43 272.97 527.36 272.9 C 527.3 272.83 527.21 272.79 527.11 272.79 L 524.81 272.79 L 524.81 270.29 C 524.8 269.21 524.35 268.18 523.56 267.44 C 522.74 266.65 521.65 266.22 520.52 266.21 Z M 520.51 266.93 C 521.46 266.92 522.37 267.28 523.06 267.93 C 523.72 268.54 524.1 269.4 524.1 270.29 L 524.1 272.79 L 516.88 272.79 L 516.89 270.26 C 516.9 269.36 517.28 268.51 517.95 267.91 C 518.65 267.27 519.57 266.92 520.51 266.93 Z M 514.24 273.5 L 526.76 273.5 L 526.75 284.07 L 514.24 284.07 Z M 520.51 275.74 C 519.48 275.73 518.61 276.51 518.51 277.53 C 518.42 278.56 519.13 279.48 520.14 279.66 L 520.14 282.44 L 520.86 282.44 L 520.86 279.66 C 521.79 279.49 522.47 278.67 522.48 277.72 C 522.48 276.63 521.6 275.75 520.51 275.74 Z M 520.39 276.45 C 520.43 276.45 520.47 276.45 520.51 276.46 C 520.84 276.46 521.16 276.59 521.4 276.83 C 521.64 277.07 521.77 277.39 521.76 277.72 C 521.77 278.06 521.64 278.38 521.4 278.61 C 521.16 278.85 520.84 278.98 520.51 278.98 C 520.04 279.02 519.6 278.8 519.34 278.42 C 519.08 278.03 519.06 277.53 519.28 277.12 C 519.5 276.71 519.93 276.46 520.39 276.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 270px; margin-left: 540px;">
                        <div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Private subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="540" y="282" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Private subnet
                </text>
            </switch>
        </g>
        <rect x="47" y="293" width="561" height="90" fill="none" stroke="#ff8000" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#FF8000" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="327" y="310.5">
                ECS Service
            </text>
        </g>
        <rect x="530" y="323" width="48" height="31" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 576.91 323.09 L 531.09 323.09 C 530.49 323.09 530 323.58 530 324.18 L 530 352.82 C 530 353.42 530.49 353.91 531.09 353.91 L 576.91 353.91 C 577.51 353.91 578 353.42 578 352.82 L 578 324.18 C 578 323.58 577.51 323.09 576.91 323.09 Z M 532.18 351.73 L 532.18 325.27 L 575.82 325.27 L 575.82 351.73 Z M 535.73 349 L 537.91 349 L 537.91 328 L 535.73 328 Z M 541.45 349 L 543.64 349 L 543.64 328 L 541.45 328 Z M 547.18 349 L 549.36 349 L 549.36 328 L 547.18 328 Z M 552.91 349 L 555.09 349 L 555.09 328 L 552.91 328 Z M 558.63 349 L 560.82 349 L 560.82 328 L 558.63 328 Z M 564.36 349 L 566.55 349 L 566.55 328 L 564.36 328 Z M 570.09 349 L 572.27 349 L 572.27 328 L 570.09 328 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <path d="M 47 293 L 68 293 L 68 314 L 47 314 Z" fill="url(#mx-gradient-f78e04-1-d05c17-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 64.42 305.78 L 62.06 304.37 L 62.06 301 C 62.06 300.9 62.01 300.8 61.92 300.75 L 58.53 298.77 L 58.53 295.92 L 64.42 299.4 Z M 64.87 298.98 L 58.39 295.15 C 58.29 295.1 58.18 295.1 58.09 295.15 C 58 295.2 57.94 295.3 57.94 295.41 L 57.94 298.94 C 57.94 299.05 58 299.14 58.09 299.2 L 61.48 301.17 L 61.48 304.54 C 61.48 304.64 61.53 304.74 61.62 304.79 L 64.56 306.56 C 64.61 306.58 64.66 306.6 64.72 306.6 C 64.77 306.6 64.82 306.59 64.86 306.56 C 64.95 306.51 65.01 306.41 65.01 306.3 L 65.01 299.24 C 65.01 299.13 64.95 299.04 64.87 298.98 Z M 57.48 311.27 L 50.58 307.6 L 50.58 299.4 L 56.47 295.92 L 56.47 298.78 L 53.37 300.75 C 53.28 300.81 53.23 300.9 53.23 301 L 53.23 306.01 C 53.23 306.12 53.29 306.22 53.39 306.27 L 57.35 308.33 C 57.44 308.38 57.54 308.38 57.62 308.33 L 61.47 306.35 L 63.83 307.77 Z M 64.57 307.52 L 61.63 305.76 C 61.54 305.7 61.43 305.7 61.34 305.75 L 57.49 307.74 L 53.82 305.83 L 53.82 301.17 L 56.92 299.19 C 57.01 299.14 57.06 299.04 57.06 298.94 L 57.06 295.41 C 57.06 295.3 57 295.2 56.91 295.15 C 56.82 295.1 56.71 295.1 56.61 295.15 L 50.13 298.98 C 50.05 299.04 49.99 299.13 49.99 299.24 L 49.99 307.78 C 49.99 307.89 50.05 307.99 50.15 308.04 L 57.35 311.87 C 57.39 311.89 57.44 311.9 57.49 311.9 C 57.54 311.9 57.58 311.89 57.63 311.86 L 64.56 308.03 C 64.66 307.98 64.71 307.89 64.72 307.78 C 64.72 307.68 64.66 307.58 64.57 307.52 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <path d="M 517.13 284.53 L 593.03 284.53 L 593.03 207.17 L 630.97 207.17 L 630.97 284.53 L 706.87 284.53 L 706.87 322.47 L 630.97 322.47 L 630.97 399.83 L 593.03 399.83 L 593.03 322.47 L 517.13 322.47 Z" fill="#e51400" stroke="#b20000" stroke-miterlimit="10" transform="rotate(-45,612,303.5)" pointer-events="all"/>
        <path d="M 507 403 L 707 403 L 707 533 L 507 533 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 507 403 L 532 403 L 532 428 L 507 428 Z M 519.52 406.21 C 518.4 406.21 517.31 406.63 516.49 407.39 C 515.67 408.11 515.2 409.15 515.2 410.24 L 515.2 412.78 L 512.89 412.78 C 512.8 412.78 512.7 412.82 512.64 412.89 C 512.57 412.95 512.54 413.04 512.54 413.13 L 512.54 424.43 C 512.54 424.63 512.7 424.79 512.89 424.79 L 526.11 424.79 C 526.3 424.79 526.46 424.63 526.46 424.43 L 526.46 413.15 C 526.47 413.06 526.43 412.97 526.36 412.9 C 526.3 412.83 526.21 412.79 526.11 412.79 L 523.81 412.79 L 523.81 410.29 C 523.8 409.21 523.35 408.18 522.56 407.44 C 521.74 406.65 520.65 406.22 519.52 406.21 Z M 519.51 406.93 C 520.46 406.92 521.37 407.28 522.06 407.93 C 522.72 408.54 523.1 409.4 523.1 410.29 L 523.1 412.79 L 515.88 412.79 L 515.89 410.26 C 515.9 409.36 516.28 408.51 516.95 407.91 C 517.65 407.27 518.57 406.92 519.51 406.93 Z M 513.24 413.5 L 525.76 413.5 L 525.75 424.07 L 513.24 424.07 Z M 519.51 415.74 C 518.48 415.73 517.61 416.51 517.51 417.53 C 517.42 418.56 518.13 419.48 519.14 419.66 L 519.14 422.44 L 519.86 422.44 L 519.86 419.66 C 520.79 419.49 521.47 418.67 521.48 417.72 C 521.48 416.63 520.6 415.75 519.51 415.74 Z M 519.39 416.45 C 519.43 416.45 519.47 416.45 519.51 416.46 C 519.84 416.46 520.16 416.59 520.4 416.83 C 520.64 417.07 520.77 417.39 520.76 417.72 C 520.77 418.06 520.64 418.38 520.4 418.61 C 520.16 418.85 519.84 418.98 519.51 418.98 C 519.04 419.02 518.6 418.8 518.34 418.42 C 518.08 418.03 518.06 417.53 518.28 417.12 C 518.5 416.71 518.93 416.46 519.39 416.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 410px; margin-left: 539px;">
                        <div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Protected subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="539" y="422" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <image x="581.5" y="442.5" width="50" height="50" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAH0AAAB9CAMAAAC4XpwXAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURckl0dBA18wz1PHI8/////XW9tdc3fjk+dpp3+678d134uSS6Oef6+ut7vzx/OGF5dNO2tAF4ssAAAAJcEhZcwAAFxEAABcRAcom8z8AAAQbSURBVGhD7Zphu6IgEEYNQlEQ/f+/dt8ZRrRS08J799nlfEkxOKYyA1hVKBQKhUKhUChcw+0KpO13KH2/AiPNv6GWr+emlvb3+XV7I5v5aIr9PX+TvbYTXFvN+xsFzzUiZ+z3tpMdF59XxmPfyDajUdDLNhNWahC1DmfsqWLLOxHIbrIZIRl+1AJEtKcahOedY3ap73gbcc8wOHmxx30DLdlRLgV0WWB/qgEGbk8r3jmAlZa4rXgTumTn3aoak32MBVWs81QDX6QTHA+7Ceh7+sxgx4PQnHJzVX7wM9hRMHDBcdB2Ljsuo+WC46Aqp8Q89pO/XaEK97kMdvS3cOa+32rIAzeawa5QbjocOYSMbWK42barvqev+L6fGsaxNTttgFNjmxhsduyv4NiqXYLv4Ugb2ulqPrc1jtORV3AM93fFXo2UDQ7b5xyX2qqN4fDzFm/Mi/3TDJvsT6jbiBTadbaubxsP9DV2ZZ2mFLKgaV39cg4X2NXwJJ7R7rFXZbcrP6tDY4zWnGkTenmmue11NAVc54eMebOuldMy8+/PbO/IENxGl8M9IX0MkUReu4I8DHsRm7s1hV8mrx0RK7xLlTCmhJbXjuGehN8dcIqTMq/9UJ6Gckon2e1z+QYjLJfZMTaVnVUU94q8dtPGmQ13qHu72cjooABi7/H9DHbAT1u0g354vgJqtD4dFjtdhm/tcWAw2eXXgabtvSO8b3Ujge4e/PzUUdncRz6zV3YAXBF2W9l4MVbRCEU22VGtm0PTh/YZtoPFRU4E46Nqtj+SzU7cbOcwngTeD90i5/yIfZN/wz6YRpDmTtpT9Ybi81k7vi9Izjxpn3phrH/WLlXBt3aaf3xgx3CZieUn7VIX4edD++MYFfZTGTYic68c9jlub6AwBLnMHtdythlpuetre1CKZt0jPohYTnZMxOPOGnUc7Ytd6pIdH2fsj8zPPNMvkkcC06vpuNgXz3zka/u0Dhna3nV1jSnzWNtu8Bo/jTEYU39nf1jrJBb9nUfsWwRt1Wp/Z46vVcZbloiFZMeH7V9+FWFaviGzXeomYunHiB3gJjutmyaABsM+P6T582zPzGzfodgv4D+3f5DjcgH724ZplfUy+7sUS6vLF9rvd78ZtFQd5zlX2pFq3Gu+UOOQAvBl9pRjMIl0XUfvG7sBKS6tIGrMOi+z22qYcukKoR8v7+/WrU1jg3Y8mfuJaDNa17e0TAowkR/maWSJtBdQ7G+4zI6O9os5zscZ1i70BrWV7byMCCqLVw9r0MuLcHDWcBb++4TfXKmVlxPvV9E/JP57o/H25QrcMI3jTBN2ZpnfYlOO0UhyjpYxB79IcUHv35lvGXZWSs3WC6OMqKF9zbLBtD+gnrCdQ5ZjHlJcoVAoFAqFQuE/pKr+AAGLPAx/jszHAAAAAElFTkSuQmCC" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="606.5" y="510.5">
                ElastiCache
            </text>
        </g>
        <path d="M 119 6 L 367 6 L 367 76 L 263 76 L 255.4 106 L 243 76 L 119 76 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 246px; height: 1px; padding-top: 41px; margin-left: 120px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(255, 255, 255);">
                                    各サービスを複数AZに分散配置することで、
                                </span>
                                <br style="border-color: var(--border-color); background-color: rgb(255, 255, 255);"/>
                                <span style="background-color: rgb(255, 255, 255);">
                                    AZ障害時にもサービスを継続させる。
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="243" y="45" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    各サービスを複数AZに分散配置することで、...
                </text>
            </switch>
        </g>
        <image x="112.5" y="442.5" width="50" height="50" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAH0AAAB9CAMAAAC4XpwXAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURckl0dBA18wz1PHI8/////XW9tdc3fjk+dpp3+678d134uSS6Oef6+ut7vzx/OGF5dNO2tAF4ssAAAAJcEhZcwAAFxEAABcRAcom8z8AAAQbSURBVGhD7Zphu6IgEEYNQlEQ/f+/dt8ZRrRS08J799nlfEkxOKYyA1hVKBQKhUKhUChcw+0KpO13KH2/AiPNv6GWr+emlvb3+XV7I5v5aIr9PX+TvbYTXFvN+xsFzzUiZ+z3tpMdF59XxmPfyDajUdDLNhNWahC1DmfsqWLLOxHIbrIZIRl+1AJEtKcahOedY3ap73gbcc8wOHmxx30DLdlRLgV0WWB/qgEGbk8r3jmAlZa4rXgTumTn3aoak32MBVWs81QDX6QTHA+7Ceh7+sxgx4PQnHJzVX7wM9hRMHDBcdB2Ljsuo+WC46Aqp8Q89pO/XaEK97kMdvS3cOa+32rIAzeawa5QbjocOYSMbWK42barvqev+L6fGsaxNTttgFNjmxhsduyv4NiqXYLv4Ugb2ulqPrc1jtORV3AM93fFXo2UDQ7b5xyX2qqN4fDzFm/Mi/3TDJvsT6jbiBTadbaubxsP9DV2ZZ2mFLKgaV39cg4X2NXwJJ7R7rFXZbcrP6tDY4zWnGkTenmmue11NAVc54eMebOuldMy8+/PbO/IENxGl8M9IX0MkUReu4I8DHsRm7s1hV8mrx0RK7xLlTCmhJbXjuGehN8dcIqTMq/9UJ6Gckon2e1z+QYjLJfZMTaVnVUU94q8dtPGmQ13qHu72cjooABi7/H9DHbAT1u0g354vgJqtD4dFjtdhm/tcWAw2eXXgabtvSO8b3Ujge4e/PzUUdncRz6zV3YAXBF2W9l4MVbRCEU22VGtm0PTh/YZtoPFRU4E46Nqtj+SzU7cbOcwngTeD90i5/yIfZN/wz6YRpDmTtpT9Ybi81k7vi9Izjxpn3phrH/WLlXBt3aaf3xgx3CZieUn7VIX4edD++MYFfZTGTYic68c9jlub6AwBLnMHtdythlpuetre1CKZt0jPohYTnZMxOPOGnUc7Ytd6pIdH2fsj8zPPNMvkkcC06vpuNgXz3zka/u0Dhna3nV1jSnzWNtu8Bo/jTEYU39nf1jrJBb9nUfsWwRt1Wp/Z46vVcZbloiFZMeH7V9+FWFaviGzXeomYunHiB3gJjutmyaABsM+P6T582zPzGzfodgv4D+3f5DjcgH724ZplfUy+7sUS6vLF9rvd78ZtFQd5zlX2pFq3Gu+UOOQAvBl9pRjMIl0XUfvG7sBKS6tIGrMOi+z22qYcukKoR8v7+/WrU1jg3Y8mfuJaDNa17e0TAowkR/maWSJtBdQ7G+4zI6O9os5zscZ1i70BrWV7byMCCqLVw9r0MuLcHDWcBb++4TfXKmVlxPvV9E/JP57o/H25QrcMI3jTBN2ZpnfYlOO0UhyjpYxB79IcUHv35lvGXZWSs3WC6OMqKF9zbLBtD+gnrCdQ5ZjHlJcoVAoFAqFQuE/pKr+AAGLPAx/jszHAAAAAElFTkSuQmCC" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="137.5" y="510.5">
                ElastiCache
            </text>
        </g>
        <rect x="518" y="173" width="60" height="60" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 533.85 219.25 L 533.85 215.38 L 536.27 217.32 Z M 533.34 211.48 C 532.93 211.15 532.37 211.09 531.9 211.32 C 531.43 211.54 531.13 212.02 531.13 212.55 L 531.13 222.09 C 531.13 222.62 531.43 223.09 531.9 223.32 C 532.09 223.41 532.29 223.45 532.49 223.45 C 532.79 223.45 533.09 223.35 533.34 223.16 L 539.31 218.38 C 539.63 218.12 539.82 217.73 539.82 217.32 C 539.82 216.9 539.63 216.51 539.31 216.25 Z M 566.07 206.87 L 566.07 201.52 L 568.74 204.19 Z M 571.63 203.23 L 565.67 197.26 C 565.28 196.87 564.69 196.76 564.18 196.97 C 563.67 197.18 563.34 197.68 563.34 198.23 L 563.34 202.83 L 552.94 202.83 L 552.94 189.88 C 552.94 189.12 552.33 188.51 551.58 188.51 L 541.09 188.51 L 541.09 191.24 L 550.22 191.24 L 550.22 202.83 L 540.84 202.83 L 540.84 205.56 L 550.22 205.56 L 550.22 217.15 L 541.09 217.15 L 541.09 219.87 L 551.58 219.87 C 552.33 219.87 552.94 219.27 552.94 218.51 L 552.94 205.56 L 563.34 205.56 L 563.34 210.16 C 563.34 210.71 563.67 211.21 564.18 211.42 C 564.35 211.49 564.53 211.52 564.7 211.52 C 565.06 211.52 565.41 211.38 565.67 211.12 L 571.63 205.16 C 572.17 204.62 572.17 203.76 571.63 203.23 Z M 533.85 206.13 L 533.85 202.26 L 536.27 204.19 Z M 533.34 198.36 C 532.93 198.03 532.37 197.96 531.9 198.19 C 531.43 198.42 531.13 198.9 531.13 199.42 L 531.13 208.97 C 531.13 209.49 531.43 209.97 531.9 210.19 C 532.09 210.29 532.29 210.33 532.49 210.33 C 532.79 210.33 533.09 210.23 533.34 210.03 L 539.31 205.26 C 539.63 205 539.82 204.61 539.82 204.19 C 539.82 203.78 539.63 203.39 539.31 203.13 Z M 533.85 191.81 L 533.85 187.94 L 536.27 189.88 Z M 533.34 184.04 C 532.93 183.71 532.37 183.65 531.9 183.87 C 531.43 184.1 531.13 184.58 531.13 185.1 L 531.13 194.65 C 531.13 195.17 531.43 195.65 531.9 195.88 C 532.09 195.97 532.29 196.01 532.49 196.01 C 532.79 196.01 533.09 195.91 533.34 195.71 L 539.31 190.94 C 539.63 190.68 539.82 190.29 539.82 189.88 C 539.82 189.46 539.63 189.07 539.31 188.81 Z M 548 230.27 C 532.96 230.27 520.73 218.04 520.73 203 C 520.73 187.96 532.96 175.73 548 175.73 C 563.04 175.73 575.27 187.96 575.27 203 C 575.27 218.04 563.04 230.27 548 230.27 Z M 548 173 C 531.46 173 518 186.46 518 203 C 518 219.54 531.46 233 548 233 C 564.54 233 578 219.54 578 203 C 578 186.46 564.54 173 548 173 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 240px; margin-left: 548px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                NAT Gateway
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="548" y="252" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    NAT Gateway
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>