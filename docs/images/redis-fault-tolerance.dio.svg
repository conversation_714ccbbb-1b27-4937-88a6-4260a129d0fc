<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1001px" height="941px" viewBox="-0.5 -0.5 1001 941" content="&lt;mxfile&gt;&lt;diagram id=&quot;rxyWzuQO3aeQyH_nDuKp&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-4d72f3-1-3334b9-1-s-0">
            <stop offset="0%" style="stop-color: rgb(51, 52, 185); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(77, 114, 243); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="44" y="262" width="920" height="211" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="452" y="270" width="106" height="15" stroke-width="0"/>
            <text x="503.5" y="279.5">
                Availability Zone 1a
            </text>
        </g>
        <path d="M 0 0 L 1000 0 L 1000 940 L 0 940 Z" fill="none" stroke="#248814" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 10.59 6.65 C 10.53 6.65 10.48 6.65 10.42 6.65 L 10.42 6.65 C 9.11 6.68 8.03 7.24 7.14 8.25 C 7.13 8.25 7.13 8.25 7.13 8.25 C 6.2 9.36 5.87 10.52 5.96 11.73 C 4.81 12.06 4.12 12.92 3.76 13.74 C 3.75 13.75 3.75 13.76 3.74 13.78 C 3.33 15.05 3.68 16.36 4.24 17.16 C 4.25 17.17 4.25 17.17 4.26 17.18 C 4.94 18.05 5.97 18.53 7.02 18.53 L 18.17 18.53 C 19.19 18.53 20.07 18.16 20.8 17.37 C 21.25 16.94 21.49 16.29 21.58 15.59 C 21.67 14.9 21.61 14.16 21.32 13.55 C 21.31 13.54 21.31 13.53 21.31 13.52 C 20.8 12.62 19.95 11.81 18.76 11.64 C 18.74 10.79 18.28 9.99 17.68 9.56 C 17.67 9.55 17.66 9.55 17.65 9.54 C 17.01 9.18 16.4 9.14 15.91 9.3 C 15.6 9.4 15.36 9.56 15.14 9.74 C 14.51 8.36 13.43 7.18 11.81 6.79 C 11.81 6.79 11.81 6.79 11.81 6.79 C 11.38 6.7 10.97 6.65 10.59 6.65 Z M 10.43 7.38 C 10.8 7.38 11.2 7.43 11.64 7.53 C 13.16 7.89 14.15 9.07 14.66 10.48 C 14.71 10.6 14.81 10.69 14.94 10.72 C 15.07 10.74 15.2 10.7 15.29 10.61 C 15.54 10.34 15.83 10.11 16.14 10.01 C 16.44 9.91 16.78 9.92 17.26 10.18 C 17.67 10.49 18.11 11.31 18.03 11.9 C 18.01 12.01 18.05 12.12 18.12 12.2 C 18.19 12.28 18.29 12.33 18.39 12.33 C 19.46 12.34 20.16 13.02 20.64 13.88 C 20.85 14.3 20.91 14.92 20.84 15.5 C 20.76 16.07 20.53 16.59 20.28 16.83 C 20.27 16.84 20.27 16.85 20.26 16.85 C 19.65 17.53 19.03 17.78 18.17 17.78 L 7.02 17.78 C 6.2 17.78 5.39 17.41 4.85 16.73 C 4.44 16.13 4.14 15.02 4.46 14.02 C 4.79 13.27 5.36 12.55 6.41 12.36 C 6.6 12.32 6.74 12.14 6.71 11.94 C 6.56 10.79 6.8 9.81 7.7 8.74 C 8.49 7.85 9.33 7.39 10.43 7.38 Z M 12.2 10.7 C 11.77 10.7 11.4 10.93 11.13 11.21 C 10.85 11.5 10.64 11.85 10.64 12.25 L 10.64 12.71 L 10.14 12.71 C 10.04 12.71 9.94 12.75 9.87 12.82 C 9.8 12.89 9.76 12.98 9.76 13.08 L 9.76 15.7 C 9.76 15.8 9.8 15.89 9.87 15.96 C 9.94 16.03 10.04 16.07 10.14 16.07 L 14.16 16.07 C 14.26 16.07 14.35 16.03 14.42 15.96 C 14.49 15.89 14.53 15.8 14.53 15.7 L 14.53 13.08 C 14.53 12.98 14.49 12.89 14.42 12.82 C 14.35 12.75 14.26 12.71 14.16 12.71 L 13.68 12.71 L 13.68 12.25 C 13.68 11.84 13.47 11.47 13.21 11.2 C 12.94 10.92 12.61 10.7 12.2 10.7 Z M 12.2 11.45 C 12.29 11.45 12.5 11.54 12.67 11.72 C 12.83 11.89 12.93 12.11 12.93 12.25 L 12.93 12.71 L 11.39 12.71 L 11.39 12.25 C 11.39 12.15 11.49 11.91 11.66 11.74 C 11.83 11.56 12.06 11.45 12.2 11.45 Z M 10.51 13.46 L 13.78 13.46 L 13.78 15.32 L 10.51 15.32 Z M 0 25 L 0 0 L 25 0 L 25 25 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 968px; height: 1px; padding-top: 7px; margin-left: 32px;">
                        <div data-drawio-colors="color: #AAB7B8; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(170, 183, 184); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                VPC
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="32" y="19" fill="#AAB7B8" font-family="Helvetica" font-size="12px">
                    VPC
                </text>
            </switch>
        </g>
        <path d="M 67.5 285.5 L 324 285.5 L 324 462.5 L 67.5 462.5 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 67.5 285.5 L 92.5 285.5 L 92.5 310.5 L 67.5 310.5 Z M 80.02 288.71 C 78.9 288.71 77.81 289.13 76.98 289.89 C 76.17 290.61 75.7 291.65 75.7 292.74 L 75.7 295.28 L 73.39 295.28 C 73.3 295.28 73.2 295.32 73.14 295.39 C 73.07 295.45 73.04 295.54 73.04 295.63 L 73.04 306.93 C 73.04 307.13 73.2 307.29 73.39 307.29 L 86.61 307.29 C 86.8 307.29 86.96 307.13 86.96 306.93 L 86.96 295.65 C 86.97 295.56 86.93 295.47 86.86 295.4 C 86.8 295.33 86.71 295.29 86.61 295.29 L 84.31 295.29 L 84.31 292.79 C 84.3 291.71 83.85 290.68 83.06 289.94 C 82.24 289.15 81.15 288.72 80.02 288.71 Z M 80.01 289.43 C 80.96 289.42 81.87 289.78 82.56 290.43 C 83.22 291.04 83.6 291.9 83.6 292.79 L 83.6 295.29 L 76.38 295.29 L 76.39 292.76 C 76.4 291.86 76.78 291.01 77.45 290.41 C 78.15 289.77 79.07 289.42 80.01 289.43 Z M 73.74 296 L 86.26 296 L 86.25 306.57 L 73.74 306.57 Z M 80.01 298.24 C 78.98 298.23 78.11 299.01 78.01 300.03 C 77.92 301.06 78.63 301.98 79.64 302.16 L 79.64 304.94 L 80.36 304.94 L 80.36 302.16 C 81.29 301.99 81.97 301.17 81.98 300.22 C 81.98 299.13 81.1 298.25 80.01 298.24 Z M 79.89 298.95 C 79.93 298.95 79.97 298.95 80.01 298.96 C 80.34 298.96 80.66 299.09 80.9 299.33 C 81.14 299.57 81.27 299.89 81.26 300.22 C 81.27 300.56 81.14 300.88 80.9 301.11 C 80.66 301.35 80.34 301.48 80.01 301.48 C 79.54 301.52 79.1 301.3 78.84 300.92 C 78.58 300.53 78.56 300.03 78.78 299.62 C 79 299.21 79.43 298.96 79.89 298.95 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 225px; height: 1px; padding-top: 293px; margin-left: 100px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="100" y="305" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 364 285.5 L 620.5 285.5 L 620.5 462.5 L 364 462.5 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 364 285.5 L 389 285.5 L 389 310.5 L 364 310.5 Z M 376.52 288.71 C 375.4 288.71 374.31 289.13 373.49 289.89 C 372.67 290.61 372.2 291.65 372.2 292.74 L 372.2 295.28 L 369.89 295.28 C 369.8 295.28 369.7 295.32 369.64 295.39 C 369.57 295.45 369.54 295.54 369.54 295.63 L 369.54 306.93 C 369.54 307.13 369.7 307.29 369.89 307.29 L 383.11 307.29 C 383.3 307.29 383.46 307.13 383.46 306.93 L 383.46 295.65 C 383.47 295.56 383.43 295.47 383.36 295.4 C 383.3 295.33 383.21 295.29 383.11 295.29 L 380.81 295.29 L 380.81 292.79 C 380.8 291.71 380.35 290.68 379.56 289.94 C 378.74 289.15 377.65 288.72 376.52 288.71 Z M 376.51 289.43 C 377.46 289.42 378.37 289.78 379.06 290.43 C 379.72 291.04 380.1 291.9 380.1 292.79 L 380.1 295.29 L 372.88 295.29 L 372.89 292.76 C 372.9 291.86 373.28 291.01 373.95 290.41 C 374.65 289.77 375.57 289.42 376.51 289.43 Z M 370.24 296 L 382.76 296 L 382.75 306.57 L 370.24 306.57 Z M 376.51 298.24 C 375.48 298.23 374.61 299.01 374.51 300.03 C 374.42 301.06 375.13 301.98 376.14 302.16 L 376.14 304.94 L 376.86 304.94 L 376.86 302.16 C 377.79 301.99 378.47 301.17 378.48 300.22 C 378.48 299.13 377.6 298.25 376.51 298.24 Z M 376.39 298.95 C 376.43 298.95 376.47 298.95 376.51 298.96 C 376.84 298.96 377.16 299.09 377.4 299.33 C 377.64 299.57 377.77 299.89 377.76 300.22 C 377.77 300.56 377.64 300.88 377.4 301.11 C 377.16 301.35 376.84 301.48 376.51 301.48 C 376.04 301.52 375.6 301.3 375.34 300.92 C 375.08 300.53 375.06 300.03 375.28 299.62 C 375.5 299.21 375.93 298.96 376.39 298.95 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 225px; height: 1px; padding-top: 293px; margin-left: 396px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="396" y="305" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 664 285.5 L 920.5 285.5 L 920.5 462.5 L 664 462.5 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 664 285.5 L 689 285.5 L 689 310.5 L 664 310.5 Z M 676.52 288.71 C 675.4 288.71 674.31 289.13 673.49 289.89 C 672.67 290.61 672.2 291.65 672.2 292.74 L 672.2 295.28 L 669.89 295.28 C 669.8 295.28 669.7 295.32 669.64 295.39 C 669.57 295.45 669.54 295.54 669.54 295.63 L 669.54 306.93 C 669.54 307.13 669.7 307.29 669.89 307.29 L 683.11 307.29 C 683.3 307.29 683.46 307.13 683.46 306.93 L 683.46 295.65 C 683.47 295.56 683.43 295.47 683.36 295.4 C 683.3 295.33 683.21 295.29 683.11 295.29 L 680.81 295.29 L 680.81 292.79 C 680.8 291.71 680.35 290.68 679.56 289.94 C 678.74 289.15 677.65 288.72 676.52 288.71 Z M 676.51 289.43 C 677.46 289.42 678.37 289.78 679.06 290.43 C 679.72 291.04 680.1 291.9 680.1 292.79 L 680.1 295.29 L 672.88 295.29 L 672.89 292.76 C 672.9 291.86 673.28 291.01 673.95 290.41 C 674.65 289.77 675.57 289.42 676.51 289.43 Z M 670.24 296 L 682.76 296 L 682.75 306.57 L 670.24 306.57 Z M 676.51 298.24 C 675.48 298.23 674.61 299.01 674.51 300.03 C 674.42 301.06 675.13 301.98 676.14 302.16 L 676.14 304.94 L 676.86 304.94 L 676.86 302.16 C 677.79 301.99 678.47 301.17 678.48 300.22 C 678.48 299.13 677.6 298.25 676.51 298.24 Z M 676.39 298.95 C 676.43 298.95 676.47 298.95 676.51 298.96 C 676.84 298.96 677.16 299.09 677.4 299.33 C 677.64 299.57 677.77 299.89 677.76 300.22 C 677.77 300.56 677.64 300.88 677.4 301.11 C 677.16 301.35 676.84 301.48 676.51 301.48 C 676.04 301.52 675.6 301.3 675.34 300.92 C 675.08 300.53 675.06 300.03 675.28 299.62 C 675.5 299.21 675.93 298.96 676.39 298.95 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 225px; height: 1px; padding-top: 293px; margin-left: 696px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="696" y="305" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 100 319 L 140 319 L 140 359 L 100 359 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 126.29 351.03 L 126.29 347.69 C 124.84 348.62 122.23 349.06 119.75 349.06 C 117.04 349.06 114.93 348.59 113.71 347.78 L 113.71 351.03 C 113.71 352 115.97 353 119.75 353 C 123.6 353 126.29 351.96 126.29 351.03 Z M 119.75 343.98 C 117.04 343.98 114.93 343.51 113.71 342.7 L 113.71 345.96 C 113.73 346.93 115.98 347.92 119.75 347.92 C 123.59 347.92 126.27 346.89 126.29 345.96 L 126.29 342.61 C 124.84 343.54 122.23 343.98 119.75 343.98 Z M 126.29 340.88 L 126.29 337.02 C 124.84 337.95 122.23 338.39 119.75 338.39 C 117.04 338.39 114.93 337.92 113.71 337.11 L 113.71 340.88 C 113.73 341.85 115.98 342.84 119.75 342.84 C 123.59 342.84 126.27 341.81 126.29 340.88 Z M 113.71 335.28 C 113.71 335.28 113.71 335.29 113.71 335.29 L 113.71 335.29 L 113.71 335.29 C 113.73 336.26 115.98 337.25 119.75 337.25 C 123.94 337.25 126.27 336.1 126.29 335.29 L 126.29 335.29 L 126.29 335.29 C 126.29 335.29 126.29 335.28 126.29 335.28 C 126.29 334.48 123.96 333.31 119.75 333.31 C 115.97 333.31 113.71 334.31 113.71 335.28 Z M 127.43 335.3 L 127.43 340.87 L 127.43 340.87 C 127.43 340.88 127.43 340.88 127.43 340.89 L 127.43 345.95 L 127.43 345.95 C 127.43 345.96 127.43 345.96 127.43 345.97 L 127.43 351.03 C 127.43 353.17 123.45 354.14 119.75 354.14 C 115.39 354.14 112.57 352.92 112.57 351.03 L 112.57 345.97 C 112.57 345.96 112.57 345.96 112.57 345.95 L 112.57 345.95 L 112.57 340.89 C 112.57 340.88 112.57 340.88 112.57 340.87 L 112.57 340.87 L 112.57 335.3 C 112.57 335.29 112.57 335.29 112.57 335.28 C 112.57 333.39 115.39 332.17 119.75 332.17 C 123.45 332.17 127.43 333.14 127.43 335.28 C 127.43 335.29 127.43 335.29 127.43 335.3 Z M 135.43 327.93 C 135.74 327.93 136 327.67 136 327.35 L 136 324.43 C 136 324.11 135.74 323.86 135.43 323.86 L 104.57 323.86 C 104.26 323.86 104 324.11 104 324.43 L 104 327.35 C 104 327.67 104.26 327.93 104.57 327.93 C 105.27 327.93 105.84 328.49 105.84 329.18 C 105.84 329.88 105.27 330.44 104.57 330.44 C 104.26 330.44 104 330.7 104 331.01 L 104 342.72 C 104 343.03 104.26 343.29 104.57 343.29 L 110.29 343.29 L 110.29 342.14 L 107.43 342.14 L 107.43 340.43 L 110.29 340.43 L 110.29 339.29 L 106.86 339.29 C 106.54 339.29 106.29 339.54 106.29 339.86 L 106.29 342.14 L 105.14 342.14 L 105.14 331.51 C 106.2 331.26 106.98 330.31 106.98 329.18 C 106.98 328.06 106.2 327.11 105.14 326.85 L 105.14 325 L 134.86 325 L 134.86 326.85 C 133.8 327.11 133.02 328.06 133.02 329.18 C 133.02 330.31 133.8 331.26 134.86 331.51 L 134.86 342.14 L 133.71 342.14 L 133.71 339.86 C 133.71 339.54 133.46 339.29 133.14 339.29 L 129.71 339.29 L 129.71 340.43 L 132.57 340.43 L 132.57 342.14 L 129.71 342.14 L 129.71 343.29 L 135.43 343.29 C 135.74 343.29 136 343.03 136 342.72 L 136 331.01 C 136 330.7 135.74 330.44 135.43 330.44 C 134.73 330.44 134.16 329.88 134.16 329.18 C 134.16 328.49 134.73 327.93 135.43 327.93 Z M 113.14 331.86 L 113.14 327.29 C 113.14 326.97 112.89 326.71 112.57 326.71 L 109.14 326.71 C 108.83 326.71 108.57 326.97 108.57 327.29 L 108.57 337 C 108.57 337.32 108.83 337.57 109.14 337.57 L 110.86 337.57 L 110.86 336.43 L 109.71 336.43 L 109.71 327.86 L 112 327.86 L 112 331.86 Z M 130.29 336.43 L 129.71 336.43 L 129.71 337.57 L 130.86 337.57 C 131.17 337.57 131.43 337.32 131.43 337 L 131.43 327.29 C 131.43 326.97 131.17 326.71 130.86 326.71 L 127.43 326.71 C 127.11 326.71 126.86 326.97 126.86 327.29 L 126.86 331.86 L 128 331.86 L 128 327.86 L 130.29 327.86 Z M 125.71 331.29 L 125.71 327.29 C 125.71 326.97 125.46 326.71 125.14 326.71 L 121.14 326.71 C 120.83 326.71 120.57 326.97 120.57 327.29 L 120.57 330.71 L 121.71 330.71 L 121.71 327.86 L 124.57 327.86 L 124.57 331.29 Z M 118.29 330.71 L 118.29 327.86 L 115.43 327.86 L 115.43 331.29 L 114.29 331.29 L 114.29 327.29 C 114.29 326.97 114.54 326.71 114.86 326.71 L 118.86 326.71 C 119.17 326.71 119.43 326.97 119.43 327.29 L 119.43 330.71 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <rect x="44" y="485" width="920" height="215" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="452" y="493" width="105" height="15" stroke-width="0"/>
            <text x="503.5" y="502.5">
                Availability Zone 1c
            </text>
        </g>
        <path d="M 69.5 510.5 L 326 510.5 L 326 687.5 L 69.5 687.5 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 69.5 510.5 L 94.5 510.5 L 94.5 535.5 L 69.5 535.5 Z M 82.02 513.71 C 80.9 513.71 79.81 514.13 78.98 514.89 C 78.17 515.61 77.7 516.65 77.7 517.74 L 77.7 520.28 L 75.39 520.28 C 75.3 520.28 75.2 520.32 75.14 520.39 C 75.07 520.45 75.04 520.54 75.04 520.63 L 75.04 531.93 C 75.04 532.13 75.2 532.29 75.39 532.29 L 88.61 532.29 C 88.8 532.29 88.96 532.13 88.96 531.93 L 88.96 520.65 C 88.97 520.56 88.93 520.47 88.86 520.4 C 88.8 520.33 88.71 520.29 88.61 520.29 L 86.31 520.29 L 86.31 517.79 C 86.3 516.71 85.85 515.68 85.06 514.94 C 84.24 514.15 83.15 513.72 82.02 513.71 Z M 82.01 514.43 C 82.96 514.42 83.87 514.78 84.56 515.43 C 85.22 516.04 85.6 516.9 85.6 517.79 L 85.6 520.29 L 78.38 520.29 L 78.39 517.76 C 78.4 516.86 78.78 516.01 79.45 515.41 C 80.15 514.77 81.07 514.42 82.01 514.43 Z M 75.74 521 L 88.26 521 L 88.25 531.57 L 75.74 531.57 Z M 82.01 523.24 C 80.98 523.23 80.11 524.01 80.01 525.03 C 79.92 526.06 80.63 526.98 81.64 527.16 L 81.64 529.94 L 82.36 529.94 L 82.36 527.16 C 83.29 526.99 83.97 526.17 83.98 525.22 C 83.98 524.13 83.1 523.25 82.01 523.24 Z M 81.89 523.95 C 81.93 523.95 81.97 523.95 82.01 523.96 C 82.34 523.96 82.66 524.09 82.9 524.33 C 83.14 524.57 83.27 524.89 83.26 525.22 C 83.27 525.56 83.14 525.88 82.9 526.11 C 82.66 526.35 82.34 526.48 82.01 526.48 C 81.54 526.52 81.1 526.3 80.84 525.92 C 80.58 525.53 80.56 525.03 80.78 524.62 C 81 524.21 81.43 523.96 81.89 523.95 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 225px; height: 1px; padding-top: 518px; margin-left: 102px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="102" y="530" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 368 510.5 L 624.5 510.5 L 624.5 687.5 L 368 687.5 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 368 510.5 L 393 510.5 L 393 535.5 L 368 535.5 Z M 380.52 513.71 C 379.4 513.71 378.31 514.13 377.49 514.89 C 376.67 515.61 376.2 516.65 376.2 517.74 L 376.2 520.28 L 373.89 520.28 C 373.8 520.28 373.7 520.32 373.64 520.39 C 373.57 520.45 373.54 520.54 373.54 520.63 L 373.54 531.93 C 373.54 532.13 373.7 532.29 373.89 532.29 L 387.11 532.29 C 387.3 532.29 387.46 532.13 387.46 531.93 L 387.46 520.65 C 387.47 520.56 387.43 520.47 387.36 520.4 C 387.3 520.33 387.21 520.29 387.11 520.29 L 384.81 520.29 L 384.81 517.79 C 384.8 516.71 384.35 515.68 383.56 514.94 C 382.74 514.15 381.65 513.72 380.52 513.71 Z M 380.51 514.43 C 381.46 514.42 382.37 514.78 383.06 515.43 C 383.72 516.04 384.1 516.9 384.1 517.79 L 384.1 520.29 L 376.88 520.29 L 376.89 517.76 C 376.9 516.86 377.28 516.01 377.95 515.41 C 378.65 514.77 379.57 514.42 380.51 514.43 Z M 374.24 521 L 386.76 521 L 386.75 531.57 L 374.24 531.57 Z M 380.51 523.24 C 379.48 523.23 378.61 524.01 378.51 525.03 C 378.42 526.06 379.13 526.98 380.14 527.16 L 380.14 529.94 L 380.86 529.94 L 380.86 527.16 C 381.79 526.99 382.47 526.17 382.48 525.22 C 382.48 524.13 381.6 523.25 380.51 523.24 Z M 380.39 523.95 C 380.43 523.95 380.47 523.95 380.51 523.96 C 380.84 523.96 381.16 524.09 381.4 524.33 C 381.64 524.57 381.77 524.89 381.76 525.22 C 381.77 525.56 381.64 525.88 381.4 526.11 C 381.16 526.35 380.84 526.48 380.51 526.48 C 380.04 526.52 379.6 526.3 379.34 525.92 C 379.08 525.53 379.06 525.03 379.28 524.62 C 379.5 524.21 379.93 523.96 380.39 523.95 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 225px; height: 1px; padding-top: 518px; margin-left: 400px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="400" y="530" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 666 510.5 L 922.5 510.5 L 922.5 687.5 L 666 687.5 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 666 510.5 L 691 510.5 L 691 535.5 L 666 535.5 Z M 678.52 513.71 C 677.4 513.71 676.31 514.13 675.49 514.89 C 674.67 515.61 674.2 516.65 674.2 517.74 L 674.2 520.28 L 671.89 520.28 C 671.8 520.28 671.7 520.32 671.64 520.39 C 671.57 520.45 671.54 520.54 671.54 520.63 L 671.54 531.93 C 671.54 532.13 671.7 532.29 671.89 532.29 L 685.11 532.29 C 685.3 532.29 685.46 532.13 685.46 531.93 L 685.46 520.65 C 685.47 520.56 685.43 520.47 685.36 520.4 C 685.3 520.33 685.21 520.29 685.11 520.29 L 682.81 520.29 L 682.81 517.79 C 682.8 516.71 682.35 515.68 681.56 514.94 C 680.74 514.15 679.65 513.72 678.52 513.71 Z M 678.51 514.43 C 679.46 514.42 680.37 514.78 681.06 515.43 C 681.72 516.04 682.1 516.9 682.1 517.79 L 682.1 520.29 L 674.88 520.29 L 674.89 517.76 C 674.9 516.86 675.28 516.01 675.95 515.41 C 676.65 514.77 677.57 514.42 678.51 514.43 Z M 672.24 521 L 684.76 521 L 684.75 531.57 L 672.24 531.57 Z M 678.51 523.24 C 677.48 523.23 676.61 524.01 676.51 525.03 C 676.42 526.06 677.13 526.98 678.14 527.16 L 678.14 529.94 L 678.86 529.94 L 678.86 527.16 C 679.79 526.99 680.47 526.17 680.48 525.22 C 680.48 524.13 679.6 523.25 678.51 523.24 Z M 678.39 523.95 C 678.43 523.95 678.47 523.95 678.51 523.96 C 678.84 523.96 679.16 524.09 679.4 524.33 C 679.64 524.57 679.77 524.89 679.76 525.22 C 679.77 525.56 679.64 525.88 679.4 526.11 C 679.16 526.35 678.84 526.48 678.51 526.48 C 678.04 526.52 677.6 526.3 677.34 525.92 C 677.08 525.53 677.06 525.03 677.28 524.62 C 677.5 524.21 677.93 523.96 678.39 523.95 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 225px; height: 1px; padding-top: 518px; margin-left: 698px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="698" y="530" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <rect x="44" y="713" width="920" height="215" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="452" y="721" width="106" height="15" stroke-width="0"/>
            <text x="503.5" y="730.5">
                Availability Zone 1d
            </text>
        </g>
        <path d="M 69.5 739.5 L 326 739.5 L 326 916.5 L 69.5 916.5 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 69.5 739.5 L 94.5 739.5 L 94.5 764.5 L 69.5 764.5 Z M 82.02 742.71 C 80.9 742.71 79.81 743.13 78.98 743.89 C 78.17 744.61 77.7 745.65 77.7 746.74 L 77.7 749.28 L 75.39 749.28 C 75.3 749.28 75.2 749.32 75.14 749.39 C 75.07 749.45 75.04 749.54 75.04 749.63 L 75.04 760.93 C 75.04 761.13 75.2 761.29 75.39 761.29 L 88.61 761.29 C 88.8 761.29 88.96 761.13 88.96 760.93 L 88.96 749.65 C 88.97 749.56 88.93 749.47 88.86 749.4 C 88.8 749.33 88.71 749.29 88.61 749.29 L 86.31 749.29 L 86.31 746.79 C 86.3 745.71 85.85 744.68 85.06 743.94 C 84.24 743.15 83.15 742.72 82.02 742.71 Z M 82.01 743.43 C 82.96 743.42 83.87 743.78 84.56 744.43 C 85.22 745.04 85.6 745.9 85.6 746.79 L 85.6 749.29 L 78.38 749.29 L 78.39 746.76 C 78.4 745.86 78.78 745.01 79.45 744.41 C 80.15 743.77 81.07 743.42 82.01 743.43 Z M 75.74 750 L 88.26 750 L 88.25 760.57 L 75.74 760.57 Z M 82.01 752.24 C 80.98 752.23 80.11 753.01 80.01 754.03 C 79.92 755.06 80.63 755.98 81.64 756.16 L 81.64 758.94 L 82.36 758.94 L 82.36 756.16 C 83.29 755.99 83.97 755.17 83.98 754.22 C 83.98 753.13 83.1 752.25 82.01 752.24 Z M 81.89 752.95 C 81.93 752.95 81.97 752.95 82.01 752.96 C 82.34 752.96 82.66 753.09 82.9 753.33 C 83.14 753.57 83.27 753.89 83.26 754.22 C 83.27 754.56 83.14 754.88 82.9 755.11 C 82.66 755.35 82.34 755.48 82.01 755.48 C 81.54 755.52 81.1 755.3 80.84 754.92 C 80.58 754.53 80.56 754.03 80.78 753.62 C 81 753.21 81.43 752.96 81.89 752.95 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 225px; height: 1px; padding-top: 747px; margin-left: 102px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="102" y="759" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 368 738.5 L 624.5 738.5 L 624.5 915.5 L 368 915.5 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 368 738.5 L 393 738.5 L 393 763.5 L 368 763.5 Z M 380.52 741.71 C 379.4 741.71 378.31 742.13 377.49 742.89 C 376.67 743.61 376.2 744.65 376.2 745.74 L 376.2 748.28 L 373.89 748.28 C 373.8 748.28 373.7 748.32 373.64 748.39 C 373.57 748.45 373.54 748.54 373.54 748.63 L 373.54 759.93 C 373.54 760.13 373.7 760.29 373.89 760.29 L 387.11 760.29 C 387.3 760.29 387.46 760.13 387.46 759.93 L 387.46 748.65 C 387.47 748.56 387.43 748.47 387.36 748.4 C 387.3 748.33 387.21 748.29 387.11 748.29 L 384.81 748.29 L 384.81 745.79 C 384.8 744.71 384.35 743.68 383.56 742.94 C 382.74 742.15 381.65 741.72 380.52 741.71 Z M 380.51 742.43 C 381.46 742.42 382.37 742.78 383.06 743.43 C 383.72 744.04 384.1 744.9 384.1 745.79 L 384.1 748.29 L 376.88 748.29 L 376.89 745.76 C 376.9 744.86 377.28 744.01 377.95 743.41 C 378.65 742.77 379.57 742.42 380.51 742.43 Z M 374.24 749 L 386.76 749 L 386.75 759.57 L 374.24 759.57 Z M 380.51 751.24 C 379.48 751.23 378.61 752.01 378.51 753.03 C 378.42 754.06 379.13 754.98 380.14 755.16 L 380.14 757.94 L 380.86 757.94 L 380.86 755.16 C 381.79 754.99 382.47 754.17 382.48 753.22 C 382.48 752.13 381.6 751.25 380.51 751.24 Z M 380.39 751.95 C 380.43 751.95 380.47 751.95 380.51 751.96 C 380.84 751.96 381.16 752.09 381.4 752.33 C 381.64 752.57 381.77 752.89 381.76 753.22 C 381.77 753.56 381.64 753.88 381.4 754.11 C 381.16 754.35 380.84 754.48 380.51 754.48 C 380.04 754.52 379.6 754.3 379.34 753.92 C 379.08 753.53 379.06 753.03 379.28 752.62 C 379.5 752.21 379.93 751.96 380.39 751.95 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 225px; height: 1px; padding-top: 746px; margin-left: 400px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="400" y="758" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 666 738.5 L 922.5 738.5 L 922.5 915.5 L 666 915.5 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 666 738.5 L 691 738.5 L 691 763.5 L 666 763.5 Z M 678.52 741.71 C 677.4 741.71 676.31 742.13 675.49 742.89 C 674.67 743.61 674.2 744.65 674.2 745.74 L 674.2 748.28 L 671.89 748.28 C 671.8 748.28 671.7 748.32 671.64 748.39 C 671.57 748.45 671.54 748.54 671.54 748.63 L 671.54 759.93 C 671.54 760.13 671.7 760.29 671.89 760.29 L 685.11 760.29 C 685.3 760.29 685.46 760.13 685.46 759.93 L 685.46 748.65 C 685.47 748.56 685.43 748.47 685.36 748.4 C 685.3 748.33 685.21 748.29 685.11 748.29 L 682.81 748.29 L 682.81 745.79 C 682.8 744.71 682.35 743.68 681.56 742.94 C 680.74 742.15 679.65 741.72 678.52 741.71 Z M 678.51 742.43 C 679.46 742.42 680.37 742.78 681.06 743.43 C 681.72 744.04 682.1 744.9 682.1 745.79 L 682.1 748.29 L 674.88 748.29 L 674.89 745.76 C 674.9 744.86 675.28 744.01 675.95 743.41 C 676.65 742.77 677.57 742.42 678.51 742.43 Z M 672.24 749 L 684.76 749 L 684.75 759.57 L 672.24 759.57 Z M 678.51 751.24 C 677.48 751.23 676.61 752.01 676.51 753.03 C 676.42 754.06 677.13 754.98 678.14 755.16 L 678.14 757.94 L 678.86 757.94 L 678.86 755.16 C 679.79 754.99 680.47 754.17 680.48 753.22 C 680.48 752.13 679.6 751.25 678.51 751.24 Z M 678.39 751.95 C 678.43 751.95 678.47 751.95 678.51 751.96 C 678.84 751.96 679.16 752.09 679.4 752.33 C 679.64 752.57 679.77 752.89 679.76 753.22 C 679.77 753.56 679.64 753.88 679.4 754.11 C 679.16 754.35 678.84 754.48 678.51 754.48 C 678.04 754.52 677.6 754.3 677.34 753.92 C 677.08 753.53 677.06 753.03 677.28 752.62 C 677.5 752.21 677.93 751.96 678.39 751.95 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 225px; height: 1px; padding-top: 746px; margin-left: 698px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="698" y="758" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <rect x="101" y="320" width="800" height="583" fill="none" stroke="#3333ff" stroke-width="2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 748px; height: 1px; padding-top: 327px; margin-left: 153px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                クラスター
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="153" y="339" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    クラスター
                </text>
            </switch>
        </g>
        <rect x="181" y="793" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 210.14 797.83 L 213.77 797.83 L 213.77 798.64 L 211.14 798.64 L 211.14 800.89 L 213.31 800.89 L 213.31 801.7 L 211.14 801.7 L 211.14 804.19 L 213.77 804.19 L 213.77 805 L 210.14 805 Z M 207.58 797.83 L 208.59 797.83 L 208.59 805 L 207.58 805 L 207.58 801.74 L 204.98 801.74 L 204.98 805 L 203.97 805 L 203.97 797.83 L 204.98 797.83 L 204.98 800.92 L 207.58 800.92 Z M 201.51 804.22 C 201.82 804.22 202.27 804.14 202.86 803.96 L 202.86 804.78 C 202.41 804.99 201.92 805.1 201.4 805.1 C 199.55 805.1 198.63 803.88 198.63 801.44 C 198.63 800.24 198.87 799.32 199.34 798.69 C 199.82 798.06 200.52 797.75 201.44 797.75 C 201.88 797.75 202.33 797.84 202.77 798.03 L 202.77 798.85 C 202.31 798.7 201.91 798.63 201.56 798.63 C 200.91 798.63 200.43 798.84 200.13 799.26 C 199.83 799.69 199.68 800.37 199.68 801.31 L 199.68 801.56 C 199.68 802.49 199.83 803.17 200.12 803.59 C 200.41 804.01 200.87 804.22 201.51 804.22 Z M 194.66 802.3 L 195.55 798.8 L 196.44 802.3 Z M 195.03 797.83 L 192.96 805 L 193.99 805 L 194.47 803.06 L 196.63 803.06 L 197.13 805 L 198.19 805 L 196.12 797.83 Z M 191.19 804.22 C 191.5 804.22 191.94 804.14 192.53 803.96 L 192.53 804.78 C 192.08 804.99 191.6 805.1 191.07 805.1 C 189.22 805.1 188.3 803.88 188.3 801.44 C 188.3 800.24 188.54 799.32 189.01 798.69 C 189.49 798.06 190.19 797.75 191.11 797.75 C 191.55 797.75 192 797.84 192.45 798.03 L 192.45 798.85 C 191.99 798.7 191.58 798.63 191.23 798.63 C 190.58 798.63 190.1 798.84 189.81 799.26 C 189.51 799.69 189.36 800.37 189.36 801.31 L 189.36 801.56 C 189.36 802.49 189.5 803.17 189.79 803.59 C 190.08 804.01 190.55 804.22 191.19 804.22 Z M 219.17 827.11 C 219.17 829.35 217.35 831.17 215.11 831.17 L 186.89 831.17 C 184.65 831.17 182.83 829.35 182.83 827.11 L 182.83 798.89 C 182.83 796.65 184.65 794.83 186.89 794.83 L 215.11 794.83 C 217.35 794.83 219.17 796.65 219.17 798.89 Z M 215.11 793 L 186.89 793 C 183.64 793 181 795.64 181 798.89 L 181 827.11 C 181 830.36 183.64 833 186.89 833 L 215.11 833 C 218.36 833 221 830.36 221 827.11 L 221 798.89 C 221 795.64 218.36 793 215.11 793 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 840px; margin-left: 201px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Replica
                                <br/>
                                0-5454
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="201" y="852" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Replic...
                </text>
            </switch>
        </g>
        <rect x="123" y="361.85" width="150" height="510" fill="none" stroke="#3333ff" stroke-width="2" stroke-dasharray="6 6" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 98px; height: 1px; padding-top: 369px; margin-left: 175px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                シャードA
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="175" y="381" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    シャードA
                </text>
            </switch>
        </g>
        <rect x="410" y="361.85" width="150" height="510" fill="none" stroke="#3333ff" stroke-width="2" stroke-dasharray="6 6" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 98px; height: 1px; padding-top: 369px; margin-left: 462px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                シャードB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="462" y="381" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    シャードB
                </text>
            </switch>
        </g>
        <rect x="700" y="361.85" width="150" height="510" fill="none" stroke="#3333ff" stroke-width="2" stroke-dasharray="6 6" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 98px; height: 1px; padding-top: 369px; margin-left: 752px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                シャードC
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="752" y="381" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    シャードC
                </text>
            </switch>
        </g>
        <rect x="181" y="575" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 210.14 579.83 L 213.77 579.83 L 213.77 580.64 L 211.14 580.64 L 211.14 582.89 L 213.31 582.89 L 213.31 583.7 L 211.14 583.7 L 211.14 586.19 L 213.77 586.19 L 213.77 587 L 210.14 587 Z M 207.58 579.83 L 208.59 579.83 L 208.59 587 L 207.58 587 L 207.58 583.74 L 204.98 583.74 L 204.98 587 L 203.97 587 L 203.97 579.83 L 204.98 579.83 L 204.98 582.92 L 207.58 582.92 Z M 201.51 586.22 C 201.82 586.22 202.27 586.14 202.86 585.96 L 202.86 586.78 C 202.41 586.99 201.92 587.1 201.4 587.1 C 199.55 587.1 198.63 585.88 198.63 583.44 C 198.63 582.24 198.87 581.32 199.34 580.69 C 199.82 580.06 200.52 579.75 201.44 579.75 C 201.88 579.75 202.33 579.84 202.77 580.03 L 202.77 580.85 C 202.31 580.7 201.91 580.63 201.56 580.63 C 200.91 580.63 200.43 580.84 200.13 581.26 C 199.83 581.69 199.68 582.37 199.68 583.31 L 199.68 583.56 C 199.68 584.49 199.83 585.17 200.12 585.59 C 200.41 586.01 200.87 586.22 201.51 586.22 Z M 194.66 584.3 L 195.55 580.8 L 196.44 584.3 Z M 195.03 579.83 L 192.96 587 L 193.99 587 L 194.47 585.06 L 196.63 585.06 L 197.13 587 L 198.19 587 L 196.12 579.83 Z M 191.19 586.22 C 191.5 586.22 191.94 586.14 192.53 585.96 L 192.53 586.78 C 192.08 586.99 191.6 587.1 191.07 587.1 C 189.22 587.1 188.3 585.88 188.3 583.44 C 188.3 582.24 188.54 581.32 189.01 580.69 C 189.49 580.06 190.19 579.75 191.11 579.75 C 191.55 579.75 192 579.84 192.45 580.03 L 192.45 580.85 C 191.99 580.7 191.58 580.63 191.23 580.63 C 190.58 580.63 190.1 580.84 189.81 581.26 C 189.51 581.69 189.36 582.37 189.36 583.31 L 189.36 583.56 C 189.36 584.49 189.5 585.17 189.79 585.59 C 190.08 586.01 190.55 586.22 191.19 586.22 Z M 219.17 609.11 C 219.17 611.35 217.35 613.17 215.11 613.17 L 186.89 613.17 C 184.65 613.17 182.83 611.35 182.83 609.11 L 182.83 580.89 C 182.83 578.65 184.65 576.83 186.89 576.83 L 215.11 576.83 C 217.35 576.83 219.17 578.65 219.17 580.89 Z M 215.11 575 L 186.89 575 C 183.64 575 181 577.64 181 580.89 L 181 609.11 C 181 612.36 183.64 615 186.89 615 L 215.11 615 C 218.36 615 221 612.36 221 609.11 L 221 580.89 C 221 577.64 218.36 575 215.11 575 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 622px; margin-left: 201px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Replica
                                <br/>
                                <font color="#ff3333">
                                    <b>
                                        →Primary
                                        <br/>
                                    </b>
                                </font>
                                0-5454
                                <font color="#ff3333">
                                    <b>
                                        <br/>
                                    </b>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="201" y="634" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Replic...
                </text>
            </switch>
        </g>
        <rect x="181" y="385" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 210.14 389.83 L 213.77 389.83 L 213.77 390.64 L 211.14 390.64 L 211.14 392.89 L 213.31 392.89 L 213.31 393.7 L 211.14 393.7 L 211.14 396.19 L 213.77 396.19 L 213.77 397 L 210.14 397 Z M 207.58 389.83 L 208.59 389.83 L 208.59 397 L 207.58 397 L 207.58 393.74 L 204.98 393.74 L 204.98 397 L 203.97 397 L 203.97 389.83 L 204.98 389.83 L 204.98 392.92 L 207.58 392.92 Z M 201.51 396.22 C 201.82 396.22 202.27 396.14 202.86 395.96 L 202.86 396.78 C 202.41 396.99 201.92 397.1 201.4 397.1 C 199.55 397.1 198.63 395.88 198.63 393.44 C 198.63 392.24 198.87 391.32 199.34 390.69 C 199.82 390.06 200.52 389.75 201.44 389.75 C 201.88 389.75 202.33 389.84 202.77 390.03 L 202.77 390.85 C 202.31 390.7 201.91 390.63 201.56 390.63 C 200.91 390.63 200.43 390.84 200.13 391.26 C 199.83 391.69 199.68 392.37 199.68 393.31 L 199.68 393.56 C 199.68 394.49 199.83 395.17 200.12 395.59 C 200.41 396.01 200.87 396.22 201.51 396.22 Z M 194.66 394.3 L 195.55 390.8 L 196.44 394.3 Z M 195.03 389.83 L 192.96 397 L 193.99 397 L 194.47 395.06 L 196.63 395.06 L 197.13 397 L 198.19 397 L 196.12 389.83 Z M 191.19 396.22 C 191.5 396.22 191.94 396.14 192.53 395.96 L 192.53 396.78 C 192.08 396.99 191.6 397.1 191.07 397.1 C 189.22 397.1 188.3 395.88 188.3 393.44 C 188.3 392.24 188.54 391.32 189.01 390.69 C 189.49 390.06 190.19 389.75 191.11 389.75 C 191.55 389.75 192 389.84 192.45 390.03 L 192.45 390.85 C 191.99 390.7 191.58 390.63 191.23 390.63 C 190.58 390.63 190.1 390.84 189.81 391.26 C 189.51 391.69 189.36 392.37 189.36 393.31 L 189.36 393.56 C 189.36 394.49 189.5 395.17 189.79 395.59 C 190.08 396.01 190.55 396.22 191.19 396.22 Z M 219.17 419.11 C 219.17 421.35 217.35 423.17 215.11 423.17 L 186.89 423.17 C 184.65 423.17 182.83 421.35 182.83 419.11 L 182.83 390.89 C 182.83 388.65 184.65 386.83 186.89 386.83 L 215.11 386.83 C 217.35 386.83 219.17 388.65 219.17 390.89 Z M 215.11 385 L 186.89 385 C 183.64 385 181 387.64 181 390.89 L 181 419.11 C 181 422.36 183.64 425 186.89 425 L 215.11 425 C 218.36 425 221 422.36 221 419.11 L 221 390.89 C 221 387.64 218.36 385 215.11 385 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 432px; margin-left: 201px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Primary
                                <br/>
                                0-5454
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="201" y="444" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Primar...
                </text>
            </switch>
        </g>
        <rect x="470" y="793" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 499.14 797.83 L 502.77 797.83 L 502.77 798.64 L 500.14 798.64 L 500.14 800.89 L 502.31 800.89 L 502.31 801.7 L 500.14 801.7 L 500.14 804.19 L 502.77 804.19 L 502.77 805 L 499.14 805 Z M 496.58 797.83 L 497.59 797.83 L 497.59 805 L 496.58 805 L 496.58 801.74 L 493.98 801.74 L 493.98 805 L 492.97 805 L 492.97 797.83 L 493.98 797.83 L 493.98 800.92 L 496.58 800.92 Z M 490.51 804.22 C 490.82 804.22 491.27 804.14 491.86 803.96 L 491.86 804.78 C 491.41 804.99 490.92 805.1 490.4 805.1 C 488.55 805.1 487.63 803.88 487.63 801.44 C 487.63 800.24 487.87 799.32 488.34 798.69 C 488.82 798.06 489.52 797.75 490.44 797.75 C 490.88 797.75 491.33 797.84 491.77 798.03 L 491.77 798.85 C 491.31 798.7 490.91 798.63 490.56 798.63 C 489.91 798.63 489.43 798.84 489.13 799.26 C 488.83 799.69 488.68 800.37 488.68 801.31 L 488.68 801.56 C 488.68 802.49 488.83 803.17 489.12 803.59 C 489.41 804.01 489.87 804.22 490.51 804.22 Z M 483.66 802.3 L 484.55 798.8 L 485.44 802.3 Z M 484.03 797.83 L 481.96 805 L 482.99 805 L 483.47 803.06 L 485.63 803.06 L 486.13 805 L 487.19 805 L 485.12 797.83 Z M 480.19 804.22 C 480.5 804.22 480.94 804.14 481.53 803.96 L 481.53 804.78 C 481.08 804.99 480.6 805.1 480.07 805.1 C 478.22 805.1 477.3 803.88 477.3 801.44 C 477.3 800.24 477.54 799.32 478.01 798.69 C 478.49 798.06 479.19 797.75 480.11 797.75 C 480.55 797.75 481 797.84 481.45 798.03 L 481.45 798.85 C 480.99 798.7 480.58 798.63 480.23 798.63 C 479.58 798.63 479.1 798.84 478.81 799.26 C 478.51 799.69 478.36 800.37 478.36 801.31 L 478.36 801.56 C 478.36 802.49 478.5 803.17 478.79 803.59 C 479.08 804.01 479.55 804.22 480.19 804.22 Z M 508.17 827.11 C 508.17 829.35 506.35 831.17 504.11 831.17 L 475.89 831.17 C 473.65 831.17 471.83 829.35 471.83 827.11 L 471.83 798.89 C 471.83 796.65 473.65 794.83 475.89 794.83 L 504.11 794.83 C 506.35 794.83 508.17 796.65 508.17 798.89 Z M 504.11 793 L 475.89 793 C 472.64 793 470 795.64 470 798.89 L 470 827.11 C 470 830.36 472.64 833 475.89 833 L 504.11 833 C 507.36 833 510 830.36 510 827.11 L 510 798.89 C 510 795.64 507.36 793 504.11 793 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 840px; margin-left: 490px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Replica
                                <br/>
                                5455-10909
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="490" y="852" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Replic...
                </text>
            </switch>
        </g>
        <rect x="470" y="575" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 499.14 579.83 L 502.77 579.83 L 502.77 580.64 L 500.14 580.64 L 500.14 582.89 L 502.31 582.89 L 502.31 583.7 L 500.14 583.7 L 500.14 586.19 L 502.77 586.19 L 502.77 587 L 499.14 587 Z M 496.58 579.83 L 497.59 579.83 L 497.59 587 L 496.58 587 L 496.58 583.74 L 493.98 583.74 L 493.98 587 L 492.97 587 L 492.97 579.83 L 493.98 579.83 L 493.98 582.92 L 496.58 582.92 Z M 490.51 586.22 C 490.82 586.22 491.27 586.14 491.86 585.96 L 491.86 586.78 C 491.41 586.99 490.92 587.1 490.4 587.1 C 488.55 587.1 487.63 585.88 487.63 583.44 C 487.63 582.24 487.87 581.32 488.34 580.69 C 488.82 580.06 489.52 579.75 490.44 579.75 C 490.88 579.75 491.33 579.84 491.77 580.03 L 491.77 580.85 C 491.31 580.7 490.91 580.63 490.56 580.63 C 489.91 580.63 489.43 580.84 489.13 581.26 C 488.83 581.69 488.68 582.37 488.68 583.31 L 488.68 583.56 C 488.68 584.49 488.83 585.17 489.12 585.59 C 489.41 586.01 489.87 586.22 490.51 586.22 Z M 483.66 584.3 L 484.55 580.8 L 485.44 584.3 Z M 484.03 579.83 L 481.96 587 L 482.99 587 L 483.47 585.06 L 485.63 585.06 L 486.13 587 L 487.19 587 L 485.12 579.83 Z M 480.19 586.22 C 480.5 586.22 480.94 586.14 481.53 585.96 L 481.53 586.78 C 481.08 586.99 480.6 587.1 480.07 587.1 C 478.22 587.1 477.3 585.88 477.3 583.44 C 477.3 582.24 477.54 581.32 478.01 580.69 C 478.49 580.06 479.19 579.75 480.11 579.75 C 480.55 579.75 481 579.84 481.45 580.03 L 481.45 580.85 C 480.99 580.7 480.58 580.63 480.23 580.63 C 479.58 580.63 479.1 580.84 478.81 581.26 C 478.51 581.69 478.36 582.37 478.36 583.31 L 478.36 583.56 C 478.36 584.49 478.5 585.17 478.79 585.59 C 479.08 586.01 479.55 586.22 480.19 586.22 Z M 508.17 609.11 C 508.17 611.35 506.35 613.17 504.11 613.17 L 475.89 613.17 C 473.65 613.17 471.83 611.35 471.83 609.11 L 471.83 580.89 C 471.83 578.65 473.65 576.83 475.89 576.83 L 504.11 576.83 C 506.35 576.83 508.17 578.65 508.17 580.89 Z M 504.11 575 L 475.89 575 C 472.64 575 470 577.64 470 580.89 L 470 609.11 C 470 612.36 472.64 615 475.89 615 L 504.11 615 C 507.36 615 510 612.36 510 609.11 L 510 580.89 C 510 577.64 507.36 575 504.11 575 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 622px; margin-left: 490px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Primary
                                <br/>
                                5455-10909
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="490" y="634" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Primar...
                </text>
            </switch>
        </g>
        <rect x="470" y="385" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 499.14 389.83 L 502.77 389.83 L 502.77 390.64 L 500.14 390.64 L 500.14 392.89 L 502.31 392.89 L 502.31 393.7 L 500.14 393.7 L 500.14 396.19 L 502.77 396.19 L 502.77 397 L 499.14 397 Z M 496.58 389.83 L 497.59 389.83 L 497.59 397 L 496.58 397 L 496.58 393.74 L 493.98 393.74 L 493.98 397 L 492.97 397 L 492.97 389.83 L 493.98 389.83 L 493.98 392.92 L 496.58 392.92 Z M 490.51 396.22 C 490.82 396.22 491.27 396.14 491.86 395.96 L 491.86 396.78 C 491.41 396.99 490.92 397.1 490.4 397.1 C 488.55 397.1 487.63 395.88 487.63 393.44 C 487.63 392.24 487.87 391.32 488.34 390.69 C 488.82 390.06 489.52 389.75 490.44 389.75 C 490.88 389.75 491.33 389.84 491.77 390.03 L 491.77 390.85 C 491.31 390.7 490.91 390.63 490.56 390.63 C 489.91 390.63 489.43 390.84 489.13 391.26 C 488.83 391.69 488.68 392.37 488.68 393.31 L 488.68 393.56 C 488.68 394.49 488.83 395.17 489.12 395.59 C 489.41 396.01 489.87 396.22 490.51 396.22 Z M 483.66 394.3 L 484.55 390.8 L 485.44 394.3 Z M 484.03 389.83 L 481.96 397 L 482.99 397 L 483.47 395.06 L 485.63 395.06 L 486.13 397 L 487.19 397 L 485.12 389.83 Z M 480.19 396.22 C 480.5 396.22 480.94 396.14 481.53 395.96 L 481.53 396.78 C 481.08 396.99 480.6 397.1 480.07 397.1 C 478.22 397.1 477.3 395.88 477.3 393.44 C 477.3 392.24 477.54 391.32 478.01 390.69 C 478.49 390.06 479.19 389.75 480.11 389.75 C 480.55 389.75 481 389.84 481.45 390.03 L 481.45 390.85 C 480.99 390.7 480.58 390.63 480.23 390.63 C 479.58 390.63 479.1 390.84 478.81 391.26 C 478.51 391.69 478.36 392.37 478.36 393.31 L 478.36 393.56 C 478.36 394.49 478.5 395.17 478.79 395.59 C 479.08 396.01 479.55 396.22 480.19 396.22 Z M 508.17 419.11 C 508.17 421.35 506.35 423.17 504.11 423.17 L 475.89 423.17 C 473.65 423.17 471.83 421.35 471.83 419.11 L 471.83 390.89 C 471.83 388.65 473.65 386.83 475.89 386.83 L 504.11 386.83 C 506.35 386.83 508.17 388.65 508.17 390.89 Z M 504.11 385 L 475.89 385 C 472.64 385 470 387.64 470 390.89 L 470 419.11 C 470 422.36 472.64 425 475.89 425 L 504.11 425 C 507.36 425 510 422.36 510 419.11 L 510 390.89 C 510 387.64 507.36 385 504.11 385 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 432px; margin-left: 490px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Replica
                                <br/>
                                5455-10909
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="490" y="444" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Replic...
                </text>
            </switch>
        </g>
        <rect x="760" y="793" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 789.14 797.83 L 792.77 797.83 L 792.77 798.64 L 790.14 798.64 L 790.14 800.89 L 792.31 800.89 L 792.31 801.7 L 790.14 801.7 L 790.14 804.19 L 792.77 804.19 L 792.77 805 L 789.14 805 Z M 786.58 797.83 L 787.59 797.83 L 787.59 805 L 786.58 805 L 786.58 801.74 L 783.98 801.74 L 783.98 805 L 782.97 805 L 782.97 797.83 L 783.98 797.83 L 783.98 800.92 L 786.58 800.92 Z M 780.51 804.22 C 780.82 804.22 781.27 804.14 781.86 803.96 L 781.86 804.78 C 781.41 804.99 780.92 805.1 780.4 805.1 C 778.55 805.1 777.63 803.88 777.63 801.44 C 777.63 800.24 777.87 799.32 778.34 798.69 C 778.82 798.06 779.52 797.75 780.44 797.75 C 780.88 797.75 781.33 797.84 781.77 798.03 L 781.77 798.85 C 781.31 798.7 780.91 798.63 780.56 798.63 C 779.91 798.63 779.43 798.84 779.13 799.26 C 778.83 799.69 778.68 800.37 778.68 801.31 L 778.68 801.56 C 778.68 802.49 778.83 803.17 779.12 803.59 C 779.41 804.01 779.87 804.22 780.51 804.22 Z M 773.66 802.3 L 774.55 798.8 L 775.44 802.3 Z M 774.03 797.83 L 771.96 805 L 772.99 805 L 773.47 803.06 L 775.63 803.06 L 776.13 805 L 777.19 805 L 775.12 797.83 Z M 770.19 804.22 C 770.5 804.22 770.94 804.14 771.53 803.96 L 771.53 804.78 C 771.08 804.99 770.6 805.1 770.07 805.1 C 768.22 805.1 767.3 803.88 767.3 801.44 C 767.3 800.24 767.54 799.32 768.01 798.69 C 768.49 798.06 769.19 797.75 770.11 797.75 C 770.55 797.75 771 797.84 771.45 798.03 L 771.45 798.85 C 770.99 798.7 770.58 798.63 770.23 798.63 C 769.58 798.63 769.1 798.84 768.81 799.26 C 768.51 799.69 768.36 800.37 768.36 801.31 L 768.36 801.56 C 768.36 802.49 768.5 803.17 768.79 803.59 C 769.08 804.01 769.55 804.22 770.19 804.22 Z M 798.17 827.11 C 798.17 829.35 796.35 831.17 794.11 831.17 L 765.89 831.17 C 763.65 831.17 761.83 829.35 761.83 827.11 L 761.83 798.89 C 761.83 796.65 763.65 794.83 765.89 794.83 L 794.11 794.83 C 796.35 794.83 798.17 796.65 798.17 798.89 Z M 794.11 793 L 765.89 793 C 762.64 793 760 795.64 760 798.89 L 760 827.11 C 760 830.36 762.64 833 765.89 833 L 794.11 833 C 797.36 833 800 830.36 800 827.11 L 800 798.89 C 800 795.64 797.36 793 794.11 793 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 840px; margin-left: 780px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Primary
                                <br/>
                                10910-16363
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="780" y="852" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Primar...
                </text>
            </switch>
        </g>
        <rect x="760" y="575" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 789.14 579.83 L 792.77 579.83 L 792.77 580.64 L 790.14 580.64 L 790.14 582.89 L 792.31 582.89 L 792.31 583.7 L 790.14 583.7 L 790.14 586.19 L 792.77 586.19 L 792.77 587 L 789.14 587 Z M 786.58 579.83 L 787.59 579.83 L 787.59 587 L 786.58 587 L 786.58 583.74 L 783.98 583.74 L 783.98 587 L 782.97 587 L 782.97 579.83 L 783.98 579.83 L 783.98 582.92 L 786.58 582.92 Z M 780.51 586.22 C 780.82 586.22 781.27 586.14 781.86 585.96 L 781.86 586.78 C 781.41 586.99 780.92 587.1 780.4 587.1 C 778.55 587.1 777.63 585.88 777.63 583.44 C 777.63 582.24 777.87 581.32 778.34 580.69 C 778.82 580.06 779.52 579.75 780.44 579.75 C 780.88 579.75 781.33 579.84 781.77 580.03 L 781.77 580.85 C 781.31 580.7 780.91 580.63 780.56 580.63 C 779.91 580.63 779.43 580.84 779.13 581.26 C 778.83 581.69 778.68 582.37 778.68 583.31 L 778.68 583.56 C 778.68 584.49 778.83 585.17 779.12 585.59 C 779.41 586.01 779.87 586.22 780.51 586.22 Z M 773.66 584.3 L 774.55 580.8 L 775.44 584.3 Z M 774.03 579.83 L 771.96 587 L 772.99 587 L 773.47 585.06 L 775.63 585.06 L 776.13 587 L 777.19 587 L 775.12 579.83 Z M 770.19 586.22 C 770.5 586.22 770.94 586.14 771.53 585.96 L 771.53 586.78 C 771.08 586.99 770.6 587.1 770.07 587.1 C 768.22 587.1 767.3 585.88 767.3 583.44 C 767.3 582.24 767.54 581.32 768.01 580.69 C 768.49 580.06 769.19 579.75 770.11 579.75 C 770.55 579.75 771 579.84 771.45 580.03 L 771.45 580.85 C 770.99 580.7 770.58 580.63 770.23 580.63 C 769.58 580.63 769.1 580.84 768.81 581.26 C 768.51 581.69 768.36 582.37 768.36 583.31 L 768.36 583.56 C 768.36 584.49 768.5 585.17 768.79 585.59 C 769.08 586.01 769.55 586.22 770.19 586.22 Z M 798.17 609.11 C 798.17 611.35 796.35 613.17 794.11 613.17 L 765.89 613.17 C 763.65 613.17 761.83 611.35 761.83 609.11 L 761.83 580.89 C 761.83 578.65 763.65 576.83 765.89 576.83 L 794.11 576.83 C 796.35 576.83 798.17 578.65 798.17 580.89 Z M 794.11 575 L 765.89 575 C 762.64 575 760 577.64 760 580.89 L 760 609.11 C 760 612.36 762.64 615 765.89 615 L 794.11 615 C 797.36 615 800 612.36 800 609.11 L 800 580.89 C 800 577.64 797.36 575 794.11 575 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 622px; margin-left: 780px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Replica
                                <br/>
                                10910-16363
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="780" y="634" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Replic...
                </text>
            </switch>
        </g>
        <rect x="760" y="385" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 789.14 389.83 L 792.77 389.83 L 792.77 390.64 L 790.14 390.64 L 790.14 392.89 L 792.31 392.89 L 792.31 393.7 L 790.14 393.7 L 790.14 396.19 L 792.77 396.19 L 792.77 397 L 789.14 397 Z M 786.58 389.83 L 787.59 389.83 L 787.59 397 L 786.58 397 L 786.58 393.74 L 783.98 393.74 L 783.98 397 L 782.97 397 L 782.97 389.83 L 783.98 389.83 L 783.98 392.92 L 786.58 392.92 Z M 780.51 396.22 C 780.82 396.22 781.27 396.14 781.86 395.96 L 781.86 396.78 C 781.41 396.99 780.92 397.1 780.4 397.1 C 778.55 397.1 777.63 395.88 777.63 393.44 C 777.63 392.24 777.87 391.32 778.34 390.69 C 778.82 390.06 779.52 389.75 780.44 389.75 C 780.88 389.75 781.33 389.84 781.77 390.03 L 781.77 390.85 C 781.31 390.7 780.91 390.63 780.56 390.63 C 779.91 390.63 779.43 390.84 779.13 391.26 C 778.83 391.69 778.68 392.37 778.68 393.31 L 778.68 393.56 C 778.68 394.49 778.83 395.17 779.12 395.59 C 779.41 396.01 779.87 396.22 780.51 396.22 Z M 773.66 394.3 L 774.55 390.8 L 775.44 394.3 Z M 774.03 389.83 L 771.96 397 L 772.99 397 L 773.47 395.06 L 775.63 395.06 L 776.13 397 L 777.19 397 L 775.12 389.83 Z M 770.19 396.22 C 770.5 396.22 770.94 396.14 771.53 395.96 L 771.53 396.78 C 771.08 396.99 770.6 397.1 770.07 397.1 C 768.22 397.1 767.3 395.88 767.3 393.44 C 767.3 392.24 767.54 391.32 768.01 390.69 C 768.49 390.06 769.19 389.75 770.11 389.75 C 770.55 389.75 771 389.84 771.45 390.03 L 771.45 390.85 C 770.99 390.7 770.58 390.63 770.23 390.63 C 769.58 390.63 769.1 390.84 768.81 391.26 C 768.51 391.69 768.36 392.37 768.36 393.31 L 768.36 393.56 C 768.36 394.49 768.5 395.17 768.79 395.59 C 769.08 396.01 769.55 396.22 770.19 396.22 Z M 798.17 419.11 C 798.17 421.35 796.35 423.17 794.11 423.17 L 765.89 423.17 C 763.65 423.17 761.83 421.35 761.83 419.11 L 761.83 390.89 C 761.83 388.65 763.65 386.83 765.89 386.83 L 794.11 386.83 C 796.35 386.83 798.17 388.65 798.17 390.89 Z M 794.11 385 L 765.89 385 C 762.64 385 760 387.64 760 390.89 L 760 419.11 C 760 422.36 762.64 425 765.89 425 L 794.11 425 C 797.36 425 800 422.36 800 419.11 L 800 390.89 C 800 387.64 797.36 385 794.11 385 Z" fill="#2e27ad" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 432px; margin-left: 780px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Replica
                                <br/>
                                10910-16363
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="780" y="444" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Replic...
                </text>
            </switch>
        </g>
        <path d="M 181 402 L 197 402 L 197 386 L 205 386 L 205 402 L 221 402 L 221 410 L 205 410 L 205 426 L 197 426 L 197 410 L 181 410 Z" fill="#e51400" stroke="#b20000" stroke-miterlimit="10" transform="rotate(-45,201,406)" pointer-events="all"/>
        <path d="M 487.5 230 L 219.43 385.89" fill="none" stroke="#ff3333" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/>
        <path d="M 214.25 388.91 L 219.15 381.43 L 219.43 385.89 L 223.17 388.34 Z" fill="#ff3333" stroke="#ff3333" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/>
        <ellipse cx="489.5" cy="209" rx="20" ry="20" fill="#d0d7fb" stroke="#000000" pointer-events="all"/>
        <rect x="369" y="189" width="90" height="40" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 209px; margin-left: 414px;">
                        <div data-drawio-colors="color: #FF3333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 51, 51); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <font color="#000000">
                                    Configuration
                                    <br/>
                                    Endpoint
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="414" y="213" fill="#FF3333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Configuration...
                </text>
            </switch>
        </g>
        <path d="M 489.5 229 L 489.93 378.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 489.94 383.88 L 486.42 376.89 L 489.93 378.63 L 493.42 376.87 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 430.04 34.1 L 380.96 34.1 C 380.31 34.1 379.79 34.62 379.79 35.27 L 379.79 65.95 C 379.79 66.59 380.31 67.12 380.96 67.12 L 430.04 67.12 C 430.69 67.12 431.21 66.59 431.21 65.95 L 431.21 35.27 C 431.21 34.62 430.69 34.1 430.04 34.1 Z M 382.12 64.78 L 382.12 36.44 L 428.88 36.44 L 428.88 64.78 Z M 385.92 61.86 L 388.26 61.86 L 388.26 39.36 L 385.92 39.36 Z M 392.06 61.86 L 394.4 61.86 L 394.4 39.36 L 392.06 39.36 Z M 398.19 61.86 L 400.53 61.86 L 400.53 39.36 L 398.19 39.36 Z M 404.33 61.86 L 406.67 61.86 L 406.67 39.36 L 404.33 39.36 Z M 410.47 61.86 L 412.81 61.86 L 412.81 39.36 L 410.47 39.36 Z M 416.6 61.86 L 418.94 61.86 L 418.94 39.36 L 416.6 39.36 Z M 422.74 61.86 L 425.08 61.86 L 425.08 39.36 L 422.74 39.36 Z" fill="#d45b07" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 88px; margin-left: 403px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                HashSlot
                                <br/>
                                5000
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="403" y="92" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    HashSlot...
                </text>
            </switch>
        </g>
        <path d="M 589.75 32.1 L 537.39 32.1 C 536.7 32.1 536.14 32.66 536.14 33.35 L 536.14 66.08 C 536.14 66.77 536.7 67.33 537.39 67.33 L 589.75 67.33 C 590.44 67.33 591 66.77 591 66.08 L 591 33.35 C 591 32.66 590.44 32.1 589.75 32.1 Z M 538.64 64.83 L 538.64 34.6 L 588.5 34.6 L 588.5 64.83 Z M 542.69 61.72 L 545.18 61.72 L 545.18 37.71 L 542.69 37.71 Z M 549.23 61.72 L 551.73 61.72 L 551.73 37.71 L 549.23 37.71 Z M 555.78 61.72 L 558.27 61.72 L 558.27 37.71 L 555.78 37.71 Z M 562.32 61.72 L 564.82 61.72 L 564.82 37.71 L 562.32 37.71 Z M 568.87 61.72 L 571.36 61.72 L 571.36 37.71 L 568.87 37.71 Z M 575.41 61.72 L 577.91 61.72 L 577.91 37.71 L 575.41 37.71 Z M 581.96 61.72 L 584.45 61.72 L 584.45 37.71 L 581.96 37.71 Z" fill="#d45b07" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 85px; margin-left: 564px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                HashSlot
                                <br/>
                                10000
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="564" y="89" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    HashSlot...
                </text>
            </switch>
        </g>
        <image x="503.5" y="62.54" width="38.96" height="38.96" xlink:href="data:image/svg+xml;base64,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" preserveAspectRatio="none" pointer-events="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" pointer-events="none" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="510" y="109" width="27" height="15" stroke-width="0"/>
            <text x="522.98" y="119.5">
                Data
            </text>
        </g>
        <path d="M 419.98 108.29 L 483.17 182.72" fill="none" stroke="#ff3333" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/>
        <path d="M 487.05 187.3 L 478.83 183.79 L 483.17 182.72 L 484.92 178.61 Z" fill="#ff3333" stroke="#ff3333" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 549.1 105.22 L 492.23 183.84" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 489.16 188.09 L 490.42 180.37 L 492.23 183.84 L 496.09 184.47 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 424 137 L 440 137 L 440 121 L 448 121 L 448 137 L 464 137 L 464 145 L 448 145 L 448 161 L 440 161 L 440 145 L 424 145 Z" fill="#e51400" stroke="#b20000" stroke-miterlimit="10" transform="rotate(-45,444,141)" pointer-events="none"/>
        <ellipse cx="527.5" cy="141.5" rx="18.5" ry="18.5" fill="none" stroke="#ff3333" stroke-width="3" pointer-events="none"/>
        <path d="M 679.5 -40.5 L 719.5 -40.5 L 719.5 118.5 L 719.5 118.5 L 699.5 148.5 L 699.5 118.5 L 679.5 118.5 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" transform="rotate(90,699.5,54)" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 157px; height: 1px; padding-top: 54px; margin-left: 636px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                シャードBには書込み可能
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="715" y="58" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャードBには書込み可能
                </text>
            </switch>
        </g>
        <path d="M 183.5 -93.5 L 243.5 -93.5 L 243.5 175.5 L 233.5 175.5 L 213.5 205.5 L 213.5 175.5 L 183.5 175.5 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" transform="translate(0,56)scale(1,-1)translate(0,-56)rotate(-90,213.5,56)" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 267px; height: 1px; padding-top: 56px; margin-left: 65px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                シャードAには一時的に書き込み不可となる。
                                <br/>
                                フェイルオーバ完了後に再度書込み可能になる。
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="199" y="60" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャードAには一時的に書き込み不可となる。...
                </text>
            </switch>
        </g>
        <image x="343.5" y="64.54" width="38.96" height="38.96" xlink:href="data:image/svg+xml;base64,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" preserveAspectRatio="none" pointer-events="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" pointer-events="none" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="350" y="111" width="27" height="15" stroke-width="0"/>
            <text x="362.98" y="121.5">
                Data
            </text>
        </g>
        <image x="520.54" y="372.5" width="38.96" height="38.96" xlink:href="data:image/svg+xml;base64,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" preserveAspectRatio="none" pointer-events="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" pointer-events="none" text-anchor="middle" font-size="12px">
            <text x="540.02" y="429.46">
                Data
            </text>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>