<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1175px" height="321px" viewBox="-0.5 -0.5 1175 321" content="&lt;mxfile&gt;&lt;diagram id=&quot;LoVL-qIVPpizgKBHFZMk&quot; name=&quot;250123&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f78e04-1-d05c17-1-s-0">
            <stop offset="0%" style="stop-color: rgb(208, 92, 23); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(247, 142, 4); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-4d72f3-1-3334b9-1-s-0">
            <stop offset="0%" style="stop-color: rgb(51, 52, 185); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(77, 114, 243); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-60a337-1-277116-1-s-0">
            <stop offset="0%" style="stop-color: rgb(39, 113, 22); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(96, 163, 55); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <image x="253.5" y="59.5" width="40" height="40" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAH0AAAB9CAMAAAC4XpwXAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURd00TN9BV+FNYuNabuyNm/KzvPnZ3f3y9P////vm6fS/x+6apvCmsOZneeh0hOqAj/bM0kem2o0AAAAJcEhZcwAAFxEAABcRAcom8z8AAATcSURBVGhD7VrZoqQqEGvcd/3/r52kwAVRRI96H655GT12E2pLFfb8Pnz48OHDhw8f/s9QURzHkbl5FXGSZnkhKKs6Nn99BarONPGErDaPHodqjNFFkU9XRdGYx8+iLYUsa7pY8T7qjCfyXp4/CdUI0SrScS38T5uvKnInYrQN4c82HtwHIc+2Mzym/8sns5/k1a59DEr+HD2923icy+f5U87vsHjqXbzFJzJzfTMUAnuUVgnoK3N9L+DXvDXXu0hB38lV1LY39gAFXQso6AF7jEY9zH1Zcgo0PWAtBdI0mRX4JgGEwAa1kt7QFqWW5Fv6DxIeHg2BkGo9ZBLeYj2kJDCZaXxmNso6KW4QIFikc/kQqizKKUFwc4MCMJlCC0j1i09SgA7r9AgxFjGXJ4HekJrLy4AJFx3IdDWXl4E1LrIzZn/Nu+vsVL/EXF6DDLFX/YdazbamoUAY0S7N7VnIzFUMF+1vtWhnVzWL5SK4MnRuDrEbiJOmGoYqrZ3qVl2jZ+7zosuGvTnEWmit003lssSJyP9gbgPBoI2ivYu4mjuqxtbJQlrOqcLhKDccGD5OElmT9H1fDyZN3FDJPH7C+pBRLkI9M6Xnj3WM1maR80F47qNZHo1yMeOZrdqfEv6NuQLWB04JAMw66BAx/bwxvcmm3BJjww0ddlipftMZmu2zq8wV7hOkXqhmwvEHOco82ini7bHmxJwAofE7nkW0m0X0sqvO2FNg3sEyrzpyxPcMexxrHMfAosDAI+l23KpxNOIj850g4zuB0+kB+zqBVdv3VpIyyOsFEKxwdl+MODMtTO9ZZHasYPyaKpwdX/bFCGkxL6QFhliIIyK/Dg3YA4fMgxjB1lnj2IjzTKuu+ROAtFwJxlEmz1i5doUIRFPpMr8H3nEXc6zd2Flb9gNb3087EM7lDFLjcRhn/dn2HuUzVOjx5X1dtMbc2SQm+uQwqKXNjmAGt3iutBt5rDxHGOyjqi6tww6thh7Bm6FdRo82e75f224+Z6WDtUNYw7DsZ5IDtoqdmmdOmstlZVu+RX0t2VmV+4nkgnq2UyIIy8yOrehd9pZvkTjzl2WyCiw3g4j02y0cD+YOSidldcJ/Fr4F3+g5JVp4NCSuoURBisYdM6xmKe1ckM/1zCYod6qtye15zboLdnGizIYR4lu7HkepFc0xYGqQr8YugHwng/yYNXyEpBLrcTm8xEma2mceuE1ykVlR5E2ozDjo7QODTmQs7pUOCps4vi3y5ch9BVHX4bSgoUM7rb4DPhf9VfpHlJuBlPaO58iMw9PIdTDyXt8/S890soR8jWfpWQ3eM+7z9N5f4p6l5+qrY6Sqq4WuPEuvpTAbpVh1cp5/jX6SQkjx+MP0i/S/eCXFOVvKe/Q/lehWCORVr9bn56fpAUpxZ/4zghzs5UrjBfoloPHWOPwu/Zr9XXqH/VV6l/1NerK3xKIJvEcPdoMX634CW7/Bf0HflAIq76IFvZl6AGVnOf68TI/xxxq+3qVfs79L77ALffgh/m9w2XkKOXeUvQ6w5wkxH0MXJ9unAXaDiRG2n3mH8BfwXZrBSI/Jx3cAuxVJJVgMW7h03wY8C/N6oSwHvkC5fJi+ivntBvA6+5L+6i/Kf0MUtV2f1I3ze82HDx8+fPjw4cOHm/D7/QMkbTQs6b5LhwAAAABJRU5ErkJggg==" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="235" y="44" width="80" height="15" stroke-width="0"/>
            <text x="273.5" y="54.5">
                Identity Center
            </text>
        </g>
        <path d="M 194 0 L 354 0 L 354 320 L 194 320 Z" fill="none" stroke="#232f3e" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 200.09 7.18 C 200.01 7.18 199.93 7.19 199.85 7.19 C 199.5 7.19 199.15 7.23 198.81 7.32 C 198.53 7.39 198.25 7.49 197.98 7.62 C 197.9 7.65 197.84 7.7 197.79 7.76 C 197.75 7.83 197.74 7.91 197.74 7.99 L 197.74 8.32 C 197.74 8.46 197.78 8.53 197.88 8.53 L 197.99 8.53 L 198.22 8.44 C 198.45 8.35 198.69 8.27 198.94 8.21 C 199.17 8.16 199.41 8.13 199.65 8.13 C 200.04 8.09 200.43 8.2 200.74 8.44 C 200.97 8.74 201.09 9.12 201.05 9.5 L 201.05 9.99 C 200.78 9.93 200.54 9.88 200.29 9.84 C 200.05 9.81 199.81 9.79 199.57 9.79 C 198.98 9.76 198.4 9.94 197.94 10.31 C 197.54 10.65 197.32 11.15 197.34 11.68 C 197.31 12.15 197.49 12.62 197.82 12.96 C 198.18 13.29 198.66 13.46 199.15 13.44 C 199.91 13.45 200.63 13.11 201.11 12.51 C 201.18 12.66 201.24 12.79 201.31 12.91 C 201.38 13.02 201.46 13.12 201.55 13.21 C 201.6 13.27 201.67 13.31 201.75 13.31 C 201.81 13.31 201.87 13.29 201.92 13.25 L 202.34 12.97 C 202.41 12.93 202.46 12.86 202.47 12.77 C 202.47 12.72 202.45 12.67 202.42 12.62 C 202.34 12.47 202.26 12.31 202.21 12.14 C 202.15 11.95 202.12 11.75 202.13 11.55 L 202.14 9.37 C 202.2 8.77 202 8.18 201.59 7.74 C 201.17 7.39 200.64 7.19 200.09 7.18 Z M 213.89 7.19 C 213.78 7.19 213.68 7.19 213.57 7.2 C 213.29 7.2 213 7.24 212.73 7.31 C 212.47 7.38 212.23 7.5 212.01 7.66 C 211.82 7.81 211.66 7.99 211.54 8.21 C 211.42 8.43 211.35 8.67 211.36 8.92 C 211.36 9.27 211.48 9.61 211.69 9.89 C 211.97 10.22 212.34 10.46 212.76 10.56 L 213.72 10.87 C 213.97 10.93 214.2 11.05 214.39 11.22 C 214.51 11.35 214.58 11.51 214.57 11.69 C 214.58 11.94 214.45 12.18 214.23 12.31 C 213.93 12.48 213.6 12.56 213.26 12.54 C 212.99 12.54 212.72 12.51 212.46 12.45 C 212.22 12.4 211.98 12.32 211.75 12.22 L 211.59 12.15 C 211.54 12.14 211.5 12.14 211.46 12.15 C 211.36 12.15 211.31 12.22 211.31 12.36 L 211.31 12.69 C 211.31 12.76 211.32 12.82 211.35 12.89 C 211.4 12.97 211.47 13.03 211.56 13.07 C 211.8 13.19 212.06 13.28 212.32 13.34 C 212.66 13.41 213 13.45 213.35 13.45 L 213.33 13.46 C 213.66 13.45 213.98 13.4 214.29 13.3 C 214.55 13.22 214.8 13.09 215.01 12.92 C 215.21 12.77 215.38 12.57 215.49 12.34 C 215.61 12.1 215.67 11.83 215.66 11.56 C 215.67 11.23 215.56 10.9 215.36 10.63 C 215.09 10.32 214.73 10.09 214.33 9.99 L 213.39 9.69 C 213.13 9.61 212.88 9.49 212.67 9.32 C 212.54 9.2 212.47 9.03 212.47 8.85 C 212.46 8.61 212.58 8.38 212.79 8.25 C 213.06 8.11 213.36 8.05 213.67 8.06 C 214.11 8.06 214.55 8.14 214.96 8.32 C 215.04 8.37 215.12 8.4 215.21 8.41 C 215.31 8.41 215.36 8.34 215.36 8.19 L 215.36 7.88 C 215.37 7.8 215.35 7.72 215.31 7.66 C 215.25 7.59 215.18 7.54 215.11 7.49 L 214.83 7.38 L 214.45 7.27 L 214.01 7.2 C 213.97 7.2 213.93 7.19 213.89 7.19 Z M 210.02 7.36 C 209.94 7.35 209.86 7.38 209.79 7.42 C 209.72 7.5 209.68 7.59 209.66 7.69 L 208.51 12.14 L 207.47 7.71 C 207.45 7.61 207.41 7.52 207.34 7.44 C 207.26 7.39 207.17 7.37 207.07 7.38 L 206.54 7.38 C 206.44 7.37 206.35 7.39 206.27 7.44 C 206.2 7.51 206.15 7.61 206.14 7.71 L 205.09 12.14 L 203.97 7.7 C 203.95 7.6 203.91 7.51 203.84 7.44 C 203.76 7.39 203.67 7.36 203.58 7.37 L 202.92 7.37 C 202.81 7.37 202.76 7.43 202.76 7.54 C 202.77 7.63 202.79 7.72 202.82 7.81 L 204.38 12.95 C 204.4 13.05 204.45 13.14 204.52 13.21 C 204.6 13.26 204.69 13.29 204.78 13.28 L 205.36 13.26 C 205.46 13.27 205.55 13.25 205.63 13.19 C 205.7 13.12 205.74 13.03 205.76 12.93 L 206.79 8.64 L 207.82 12.93 C 207.83 13.03 207.88 13.12 207.95 13.19 C 208.03 13.25 208.12 13.27 208.21 13.26 L 208.78 13.26 C 208.88 13.27 208.97 13.25 209.04 13.2 C 209.11 13.13 209.16 13.03 209.18 12.94 L 210.79 7.79 C 210.84 7.72 210.84 7.63 210.84 7.63 C 210.84 7.59 210.84 7.56 210.84 7.52 C 210.84 7.48 210.82 7.43 210.79 7.4 C 210.76 7.37 210.72 7.35 210.67 7.36 L 210.05 7.36 C 210.04 7.36 210.03 7.36 210.02 7.36 Z M 199.65 10.62 C 199.7 10.62 199.75 10.62 199.8 10.62 L 200.43 10.62 C 200.64 10.64 200.85 10.67 201.06 10.71 L 201.06 11.01 C 201.07 11.21 201.05 11.4 201 11.59 C 200.96 11.75 200.88 11.9 200.77 12.01 C 200.61 12.21 200.39 12.36 200.14 12.44 C 199.91 12.52 199.67 12.56 199.43 12.56 C 199.18 12.6 198.93 12.53 198.73 12.37 C 198.55 12.18 198.46 11.92 198.49 11.66 C 198.47 11.36 198.59 11.08 198.81 10.89 C 199.06 10.72 199.35 10.62 199.65 10.62 Z M 215.04 14.72 C 214.34 14.73 213.51 14.89 212.88 15.33 C 212.69 15.46 212.72 15.63 212.94 15.63 C 213.64 15.54 215.21 15.35 215.5 15.71 C 215.78 16.06 215.19 17.54 214.94 18.21 C 214.86 18.41 215.03 18.49 215.21 18.34 C 216.39 17.36 216.72 15.3 216.46 15 C 216.32 14.85 215.74 14.71 215.04 14.72 Z M 196.65 15.1 C 196.5 15.12 196.42 15.3 196.58 15.44 C 199.29 17.89 202.82 19.23 206.48 19.21 C 209.37 19.22 212.2 18.36 214.59 16.74 C 214.95 16.47 214.63 16.07 214.26 16.23 C 211.87 17.24 209.3 17.76 206.71 17.77 C 203.23 17.78 199.82 16.87 196.81 15.14 C 196.75 15.11 196.69 15.1 196.65 15.1 Z M 194 0 L 219 0 L 219 25 L 194 25 Z" fill="#232f3e" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 128px; height: 1px; padding-top: 7px; margin-left: 226px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                踏み台アカウント
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="226" y="19" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    踏み台アカウント
                </text>
            </switch>
        </g>
        <rect x="284" y="90" width="20" height="20" fill="rgb(255, 255, 255)" stroke="none" pointer-events="all"/>
        <image x="283.5" y="89.5" width="20" height="20" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAANs0TN0zS940TN0zS940TN00Td8wSN00S900TN00TNwySt00TNs0TNwzTdo1St8wUKUh+R4AAAARdFJOUwBAn8/fv48gcO//YK+AUDAQhQz//gAAAAlwSFlzAAAXEQAAFxEByibzPwAAAZtJREFUWEft2OuOgyAQBWAvxQJe8P2fdgcZNTbIOTTZTXa3548YM19aGQ3S/LO0Xf+oihmeWvoa63x9Wi2+5vkO5Z1WX2Plgm2rIiV+1PJLOu8HHdKZvLc6vESsWYd07mq+31r0mE+VZZ3RUTY1Vmy5Sce5VFiRckFPcuEtSPEWppAVei0nKGCFhwIMBaw2Pq9CUBSwFOMoZG2Y5yhoJYyisHXcMxxsCcZRjEXnY73GFiehyprLM1pjzdJppZd0hRWpftWTXHgLUtA6li6YQtbodIVAUMAa5W2zYQwFrCBWxCgKWIpxFLISxlHQUoyhsLVhFEVYTTCGohiLzseqy49aZzvAxkDWOu1v5XN0F2Ct8l2SiHN0G/S7jDyMkYiUH8p/E1mKMRS2EsZQhLVhDMVYCcMUY233Cs1hDLYSxWDQSjO4t0YxyNqbgcGAdfYVgSGrP2ZQsK48l8AS7GgGAyhoiZYOEkARFp/fZg1vWNI/2RpZJrlFN5DILLLwyG6srNKa9XH5qV70ck3c3X5P6HQfkE0/cZ+ZfyZN8wU/7Smg12ZtUAAAAABJRU5ErkJggg==" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="306" y="94" width="62" height="15" stroke-width="0"/>
            <text x="305.5" y="104.5">
                許可セット
            </text>
        </g>
        <rect x="34" y="140" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 64.23 165.36 L 66.05 165.36 L 66.05 172.64 L 60.36 172.64 L 60.36 170.82 L 64.23 170.82 Z M 65.14 177.98 C 61.25 177.98 58.09 174.82 58.09 170.93 C 58.09 167.05 61.25 163.89 65.14 163.89 C 69.02 163.89 72.18 167.05 72.18 170.93 C 72.18 174.82 69.02 177.98 65.14 177.98 Z M 65.14 162.07 C 60.25 162.07 56.27 166.04 56.27 170.93 C 56.27 175.82 60.25 179.8 65.14 179.8 C 70.02 179.8 74 175.82 74 170.93 C 74 166.04 70.02 162.07 65.14 162.07 Z M 35.82 173.2 L 54.8 173.2 L 54.8 175.02 L 34.91 175.02 C 34.41 175.02 34 174.62 34 174.11 L 34 170.14 L 35.82 170.14 Z M 67.97 147.14 C 68.71 147.14 69.3 147.73 69.3 148.47 C 69.3 149.2 68.71 149.79 67.97 149.79 C 67.24 149.79 66.65 149.2 66.65 148.47 C 66.65 147.73 67.24 147.14 67.97 147.14 Z M 67.97 151.61 C 69.71 151.61 71.12 150.2 71.12 148.47 C 71.12 146.73 69.71 145.32 67.97 145.32 C 66.24 145.32 64.83 146.73 64.83 148.47 C 64.83 150.2 66.24 151.61 67.97 151.61 Z M 45.37 147.14 L 58.73 147.14 C 59.13 147.14 59.48 146.88 59.6 146.5 C 60.45 143.82 62.92 142.02 65.74 142.02 C 69.29 142.02 72.18 144.91 72.18 148.47 C 72.18 152.02 69.29 154.91 65.74 154.91 C 62.92 154.91 60.45 153.11 59.6 150.43 C 59.48 150.05 59.13 149.79 58.73 149.79 L 56.47 149.79 C 56.22 149.79 55.99 149.89 55.82 150.06 L 54.55 151.34 L 53.27 150.06 C 53.1 149.89 52.87 149.79 52.63 149.79 L 51.35 149.79 C 51.11 149.79 50.88 149.89 50.71 150.06 L 49.43 151.34 L 48.16 150.06 C 47.99 149.89 47.75 149.79 47.51 149.79 L 45.94 149.79 L 44.31 148.37 Z M 45 151.39 C 45.16 151.54 45.37 151.61 45.59 151.61 L 47.14 151.61 L 48.79 153.27 C 49.14 153.62 49.72 153.62 50.07 153.27 L 51.73 151.61 L 52.25 151.61 L 53.9 153.27 C 54.26 153.62 54.83 153.62 55.19 153.27 L 56.84 151.61 L 58.1 151.61 C 59.36 154.7 62.35 156.73 65.74 156.73 C 70.29 156.73 74 153.02 74 148.47 C 74 143.91 70.29 140.2 65.74 140.2 C 62.35 140.2 59.36 142.23 58.1 145.32 L 44.96 145.32 C 44.69 145.32 44.44 145.43 44.26 145.64 L 42.35 147.87 C 42.02 148.25 42.06 148.82 42.44 149.15 Z M 34 162.37 C 34 157.82 37.71 154.11 42.26 154.11 C 45.65 154.11 48.65 156.14 49.91 159.23 L 51.16 159.23 L 52.81 157.58 C 53.15 157.24 53.75 157.24 54.09 157.58 L 55.75 159.23 L 56.27 159.23 L 57.92 157.58 C 58.28 157.22 58.85 157.22 59.21 157.58 L 60.87 159.23 L 62.41 159.23 C 62.63 159.23 62.84 159.31 63 159.45 L 63.75 160.1 L 62.55 161.47 L 62.07 161.05 L 60.49 161.05 C 60.25 161.05 60.02 160.95 59.85 160.78 L 58.57 159.51 L 57.29 160.78 C 57.12 160.95 56.89 161.05 56.65 161.05 L 55.37 161.05 C 55.13 161.05 54.89 160.95 54.72 160.78 L 53.45 159.51 L 52.18 160.78 C 52.01 160.95 51.78 161.05 51.53 161.05 L 49.27 161.05 C 48.87 161.05 48.52 160.79 48.4 160.41 C 47.55 157.73 45.08 155.94 42.26 155.94 C 38.71 155.94 35.82 158.82 35.82 162.37 C 35.82 165.93 38.71 168.82 42.26 168.82 C 45.08 168.82 47.55 167.02 48.4 164.34 C 48.52 163.96 48.87 163.71 49.27 163.71 L 56.95 163.71 L 56.95 165.53 L 49.91 165.53 C 48.65 168.61 45.65 170.64 42.26 170.64 C 37.71 170.64 34 166.93 34 162.37 Z M 40.02 163.71 C 39.29 163.71 38.7 163.11 38.7 162.38 C 38.7 161.65 39.29 161.05 40.02 161.05 C 40.76 161.05 41.35 161.65 41.35 162.38 C 41.35 163.11 40.76 163.71 40.02 163.71 Z M 40.02 159.23 C 38.29 159.23 36.88 160.64 36.88 162.38 C 36.88 164.11 38.29 165.53 40.02 165.53 C 41.76 165.53 43.17 164.11 43.17 162.38 C 43.17 160.64 41.76 159.23 40.02 159.23 Z" fill="#bf0816" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 187px; margin-left: 54px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                一時クレデンシャル
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="54" y="199" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    一時クレデンシャル
                </text>
            </switch>
        </g>
        <rect x="34" y="60" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 37.66 98.18 C 38.11 89.17 45.27 81.98 54 81.98 C 56.95 81.98 59.85 82.81 62.37 84.38 C 67.05 87.3 70.05 92.53 70.34 98.18 Z M 45.08 70.8 C 45.08 65.85 49.08 61.82 54 61.82 C 58.92 61.82 62.92 65.85 62.92 70.8 C 62.92 75.75 58.92 79.78 54 79.78 C 49.08 79.78 45.08 75.75 45.08 70.8 Z M 63.33 82.84 C 61.75 81.86 60.05 81.15 58.27 80.7 C 62.07 79.04 64.73 75.23 64.73 70.8 C 64.73 64.84 59.92 60 54 60 C 48.08 60 43.27 64.84 43.27 70.8 C 43.27 75.23 45.94 79.05 49.74 80.71 C 41.77 82.71 35.82 90.18 35.82 99.09 C 35.82 99.59 36.22 100 36.73 100 L 71.27 100 C 71.77 100 72.18 99.59 72.18 99.09 C 72.18 92.47 68.79 86.25 63.33 82.84 Z" fill="#232f3d" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 57px; margin-left: 54px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                IAM ユーザー
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="54" y="57" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    IAM ユーザー
                </text>
            </switch>
        </g>
        <rect x="54" y="90" width="30" height="30" fill="rgb(255, 255, 255)" stroke="none" pointer-events="all"/>
        <image x="53.5" y="89.5" width="30" height="30" xlink:href="data:image/png;base64,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" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="86" y="99" width="35" height="15" stroke-width="0"/>
            <text x="85.5" y="109.5">
                profile
            </text>
        </g>
        <path d="M 304 80 L 474.16 80" fill="none" stroke="#b85450" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 480.91 80 L 471.91 84.5 L 474.16 80 L 471.91 75.5 Z" fill="#b85450" stroke="#b85450" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 60px; margin-left: 395px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Assume Role
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="395" y="63" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Assume Role
                </text>
            </switch>
        </g>
        <path d="M 84 80 L 233.9 80" fill="none" stroke="#b85450" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 240.65 80 L 231.65 84.5 L 233.9 80 L 231.65 75.5 Z" fill="#b85450" stroke="#b85450" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 60px; margin-left: 134px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                sso login
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="134" y="63" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    sso login
                </text>
            </switch>
        </g>
        <path d="M 775 90 L 805 90 Q 815 90 815 100 L 815 225 Q 815 235 825 235 L 873.9 235" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 880.65 235 L 871.65 239.5 L 873.9 235 L 871.65 230.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 160px; margin-left: 864px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                操作権限無し
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="864" y="163" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    操作権限無し
                </text>
            </switch>
        </g>
        <path d="M 434 0 L 1174 0 L 1174 320 L 434 320 Z" fill="none" stroke="#232f3e" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 440.09 7.18 C 440.01 7.18 439.93 7.19 439.85 7.19 C 439.5 7.19 439.15 7.23 438.81 7.32 C 438.53 7.39 438.25 7.49 437.98 7.62 C 437.9 7.65 437.84 7.7 437.79 7.76 C 437.75 7.83 437.74 7.91 437.74 7.99 L 437.74 8.32 C 437.74 8.46 437.79 8.53 437.88 8.53 L 437.99 8.53 L 438.22 8.44 C 438.45 8.35 438.69 8.27 438.94 8.21 C 439.17 8.16 439.41 8.13 439.65 8.13 C 440.04 8.09 440.43 8.2 440.74 8.44 C 440.97 8.74 441.09 9.12 441.05 9.5 L 441.05 9.99 C 440.79 9.93 440.54 9.88 440.29 9.84 C 440.05 9.81 439.81 9.79 439.57 9.79 C 438.98 9.76 438.4 9.94 437.94 10.31 C 437.54 10.65 437.32 11.15 437.34 11.68 C 437.31 12.15 437.49 12.62 437.82 12.96 C 438.18 13.29 438.66 13.46 439.15 13.44 C 439.91 13.45 440.63 13.11 441.11 12.51 C 441.18 12.66 441.24 12.79 441.31 12.91 C 441.38 13.02 441.46 13.12 441.55 13.21 C 441.6 13.27 441.67 13.31 441.75 13.31 C 441.81 13.31 441.87 13.29 441.92 13.25 L 442.34 12.97 C 442.41 12.93 442.46 12.86 442.47 12.77 C 442.47 12.72 442.45 12.67 442.42 12.62 C 442.34 12.47 442.26 12.31 442.21 12.14 C 442.15 11.95 442.12 11.75 442.13 11.55 L 442.14 9.37 C 442.2 8.77 442 8.18 441.59 7.74 C 441.17 7.39 440.64 7.19 440.09 7.18 Z M 453.89 7.19 C 453.78 7.19 453.68 7.19 453.57 7.2 C 453.29 7.2 453 7.24 452.73 7.31 C 452.47 7.38 452.23 7.5 452.01 7.66 C 451.82 7.81 451.66 7.99 451.54 8.21 C 451.42 8.43 451.35 8.67 451.36 8.92 C 451.36 9.27 451.48 9.61 451.69 9.89 C 451.97 10.22 452.34 10.46 452.76 10.56 L 453.72 10.87 C 453.97 10.93 454.2 11.05 454.39 11.22 C 454.51 11.35 454.58 11.51 454.57 11.69 C 454.58 11.94 454.45 12.18 454.23 12.31 C 453.93 12.48 453.6 12.56 453.26 12.54 C 452.99 12.54 452.72 12.51 452.46 12.45 C 452.22 12.4 451.98 12.32 451.75 12.22 L 451.58 12.15 C 451.54 12.14 451.5 12.14 451.46 12.15 C 451.36 12.15 451.31 12.22 451.31 12.36 L 451.31 12.69 C 451.31 12.76 451.32 12.82 451.35 12.89 C 451.4 12.97 451.47 13.03 451.56 13.07 C 451.8 13.19 452.06 13.28 452.32 13.34 C 452.66 13.41 453 13.45 453.35 13.45 L 453.33 13.46 C 453.66 13.45 453.98 13.4 454.29 13.3 C 454.55 13.22 454.8 13.09 455.01 12.92 C 455.21 12.77 455.38 12.57 455.49 12.34 C 455.61 12.1 455.67 11.83 455.66 11.56 C 455.67 11.23 455.56 10.9 455.36 10.63 C 455.09 10.32 454.73 10.09 454.33 9.99 L 453.39 9.69 C 453.13 9.61 452.88 9.49 452.67 9.32 C 452.54 9.2 452.47 9.03 452.47 8.85 C 452.46 8.61 452.58 8.38 452.79 8.25 C 453.06 8.11 453.36 8.05 453.67 8.06 C 454.11 8.06 454.55 8.14 454.96 8.32 C 455.04 8.37 455.12 8.4 455.21 8.41 C 455.31 8.41 455.36 8.34 455.36 8.19 L 455.36 7.88 C 455.37 7.8 455.35 7.72 455.31 7.66 C 455.25 7.59 455.18 7.54 455.11 7.49 L 454.83 7.38 L 454.45 7.27 L 454.01 7.2 C 453.97 7.2 453.93 7.19 453.89 7.19 Z M 450.02 7.36 C 449.94 7.35 449.86 7.38 449.79 7.42 C 449.72 7.5 449.68 7.59 449.66 7.69 L 448.51 12.14 L 447.47 7.71 C 447.45 7.61 447.41 7.52 447.34 7.44 C 447.26 7.39 447.17 7.37 447.07 7.38 L 446.54 7.38 C 446.44 7.37 446.35 7.39 446.27 7.44 C 446.2 7.51 446.15 7.61 446.14 7.71 L 445.09 12.14 L 443.97 7.7 C 443.95 7.6 443.91 7.51 443.84 7.44 C 443.76 7.39 443.67 7.36 443.58 7.37 L 442.92 7.37 C 442.81 7.37 442.76 7.43 442.76 7.54 C 442.77 7.63 442.79 7.72 442.82 7.81 L 444.38 12.95 C 444.4 13.05 444.45 13.14 444.52 13.21 C 444.6 13.26 444.69 13.29 444.78 13.28 L 445.36 13.26 C 445.46 13.27 445.55 13.25 445.63 13.19 C 445.7 13.12 445.74 13.03 445.76 12.93 L 446.79 8.64 L 447.82 12.93 C 447.83 13.03 447.88 13.12 447.95 13.19 C 448.03 13.25 448.12 13.27 448.21 13.26 L 448.79 13.26 C 448.88 13.27 448.97 13.25 449.04 13.2 C 449.11 13.13 449.16 13.03 449.18 12.94 L 450.79 7.79 C 450.84 7.72 450.84 7.63 450.84 7.63 C 450.84 7.59 450.84 7.56 450.84 7.52 C 450.84 7.48 450.82 7.43 450.79 7.4 C 450.76 7.37 450.72 7.35 450.67 7.36 L 450.05 7.36 C 450.04 7.36 450.03 7.36 450.02 7.36 Z M 439.65 10.62 C 439.7 10.62 439.75 10.62 439.8 10.62 L 440.43 10.62 C 440.64 10.64 440.85 10.67 441.06 10.71 L 441.06 11.01 C 441.07 11.21 441.05 11.4 441 11.59 C 440.96 11.75 440.88 11.9 440.77 12.01 C 440.61 12.21 440.39 12.36 440.14 12.44 C 439.91 12.52 439.67 12.56 439.43 12.56 C 439.18 12.6 438.93 12.53 438.73 12.37 C 438.55 12.18 438.46 11.92 438.49 11.66 C 438.47 11.36 438.59 11.08 438.81 10.89 C 439.06 10.72 439.35 10.62 439.65 10.62 Z M 455.04 14.72 C 454.34 14.73 453.51 14.89 452.88 15.33 C 452.69 15.46 452.72 15.63 452.94 15.63 C 453.64 15.54 455.21 15.35 455.5 15.71 C 455.78 16.06 455.19 17.54 454.94 18.21 C 454.86 18.41 455.04 18.49 455.21 18.34 C 456.39 17.36 456.72 15.3 456.46 15 C 456.32 14.85 455.74 14.71 455.04 14.72 Z M 436.65 15.1 C 436.5 15.12 436.42 15.3 436.58 15.44 C 439.29 17.89 442.82 19.23 446.48 19.21 C 449.37 19.22 452.2 18.36 454.59 16.74 C 454.95 16.47 454.63 16.07 454.26 16.23 C 451.87 17.24 449.3 17.76 446.71 17.77 C 443.23 17.78 439.82 16.87 436.81 15.14 C 436.75 15.11 436.69 15.1 436.65 15.1 Z M 434 0 L 459 0 L 459 25 L 434 25 Z" fill="#232f3e" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 708px; height: 1px; padding-top: 7px; margin-left: 466px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Gevanni アカウント
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="466" y="19" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    Gevanni アカウント
                </text>
            </switch>
        </g>
        <path d="M 775 70 L 873.9 70" fill="none" stroke="#b85450" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 880.65 70 L 871.65 74.5 L 873.9 70 L 871.65 65.5 Z" fill="#b85450" stroke="#b85450" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 50px; margin-left: 824px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                操作権限有り
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="824" y="53" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    操作権限有り
                </text>
            </switch>
        </g>
        <rect x="695" y="60" width="70" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 763.86 86.44 C 763.64 86.24 763.37 86.09 763.08 85.97 C 761.43 71.31 748.94 60 734.04 60 C 727.32 60 720.76 62.34 715.57 66.57 C 708.71 72.15 704.78 80.42 704.78 89.26 C 704.78 89.32 704.79 89.39 704.79 89.45 C 703.74 89.98 702.63 90.48 701.52 90.97 C 698.16 92.44 695.73 93.5 695.37 95.42 C 695.28 95.9 695.26 96.86 696.19 97.78 C 697.82 99.41 700.95 100 704.14 100 C 705.46 100 706.79 99.9 708.03 99.73 C 710.14 99.44 714.08 98.67 716.75 96.71 C 720.97 93.66 730.5 92.51 735.46 92.9 C 738.42 93.13 742.01 94.68 744.89 95.93 C 747.09 96.89 748.83 97.64 750.19 97.83 C 752.06 98.09 755.13 97.78 758.37 97.44 L 758.64 97.42 C 759.49 97.32 760.32 97.25 761.15 97.18 C 761.49 97.15 761.82 97.12 762.27 97.09 C 763.66 96.99 764.74 95.83 764.74 94.44 L 764.74 88.42 C 764.74 87.66 764.42 86.94 763.86 86.44 Z M 734.04 63.15 C 747.22 63.15 758.28 73.06 759.91 85.97 C 759.46 86.01 758.98 86.06 758.47 86.1 L 758.18 86.13 C 758.15 84.68 756.96 83.52 755.51 83.52 L 754.39 83.52 C 750.92 73.32 740.88 66.29 729.52 66.29 C 725.31 66.29 721.19 67.25 717.48 69.08 C 717.51 69.06 717.53 69.04 717.56 69.01 C 722.19 65.23 728.04 63.15 734.04 63.15 Z M 750.23 88.82 L 750.23 86.66 L 755.04 86.66 L 755.04 88.82 Z M 707.61 96.61 C 703.43 97.18 700.14 96.67 698.78 95.83 C 699.6 95.24 701.47 94.43 702.78 93.85 C 703.92 93.36 705.07 92.83 706.18 92.27 C 708 93.66 710.25 94.69 712.74 95.31 C 711.3 95.89 709.52 96.35 707.61 96.61 Z M 761.59 93.98 C 761.35 94 761.1 94.02 760.86 94.04 C 760.04 94.11 759.18 94.19 758.31 94.28 L 758.05 94.31 C 755.27 94.6 752.12 94.92 750.63 94.72 C 749.69 94.58 747.97 93.83 746.14 93.04 C 743.04 91.7 739.19 90.03 735.71 89.76 C 730.91 89.39 722.62 90.3 717.28 92.8 C 714.19 92.66 711.32 91.83 709.1 90.46 C 709.09 90.03 709.08 89.6 709.08 89.44 C 709.08 82.92 712.13 76.82 717.45 72.67 C 721.07 70.55 725.24 69.44 729.52 69.44 C 739.15 69.44 747.71 75.13 751.04 83.52 L 749.76 83.52 C 748.48 83.52 747.41 84.42 747.15 85.63 C 746.93 85.54 746.73 85.47 746.5 85.38 C 743.3 84.15 738.92 82.46 733.85 82.76 C 726.44 83.2 722.8 83.64 719.93 84.46 L 720.79 87.49 C 723.42 86.74 726.88 86.33 734.03 85.9 C 738.42 85.66 742.44 87.19 745.36 88.32 C 745.98 88.56 746.55 88.77 747.08 88.96 L 747.08 89.28 C 747.08 90.76 748.29 91.96 749.76 91.96 L 755.51 91.96 C 756.98 91.96 758.18 90.76 758.19 89.29 L 758.75 89.24 C 759.68 89.16 760.54 89.07 761.26 89 L 761.59 88.97 Z" fill="#bf0816" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 107px; margin-left: 730px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                IAM Role for A
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="730" y="119" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    IAM Role fo...
                </text>
            </switch>
        </g>
        <path d="M 805 150 L 810.83 150 L 815 155.92 L 819.17 150 L 825 150 L 817.92 160 L 825 170 L 819.17 170 L 815 164.08 L 810.83 170 L 805 170 L 811.67 160 Z" fill="#ff0000" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 915 55 L 945 55 L 945 85 L 915 85 Z" fill="url(#mx-gradient-f78e04-1-d05c17-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 939.89 73.26 L 936.52 71.24 L 936.52 66.43 C 936.52 66.28 936.44 66.15 936.31 66.07 L 931.47 63.25 L 931.47 59.18 L 939.89 64.15 Z M 940.52 63.55 L 931.27 58.08 C 931.14 58 930.98 58 930.84 58.07 C 930.71 58.15 930.63 58.29 930.63 58.44 L 930.63 63.49 C 930.63 63.64 930.71 63.78 930.84 63.85 L 935.68 66.68 L 935.68 71.48 C 935.68 71.63 935.76 71.77 935.88 71.84 L 940.09 74.37 C 940.16 74.41 940.23 74.43 940.31 74.43 C 940.38 74.43 940.45 74.41 940.51 74.37 C 940.65 74.3 940.73 74.16 940.73 74.01 L 940.73 63.91 C 940.73 63.76 940.65 63.62 940.52 63.55 Z M 929.98 81.1 L 920.11 75.86 L 920.11 64.15 L 928.53 59.18 L 928.53 63.26 L 924.09 66.08 C 923.97 66.16 923.9 66.29 923.9 66.43 L 923.9 73.59 C 923.9 73.74 923.99 73.89 924.13 73.96 L 929.79 76.9 C 929.91 76.97 930.05 76.97 930.17 76.9 L 935.66 74.07 L 939.04 76.09 Z M 940.1 75.75 L 935.9 73.22 C 935.77 73.15 935.62 73.14 935.49 73.21 L 929.98 76.06 L 924.74 73.33 L 924.74 66.66 L 929.17 63.84 C 929.3 63.77 929.37 63.63 929.37 63.49 L 929.37 58.44 C 929.37 58.29 929.29 58.15 929.16 58.07 C 929.03 58 928.86 58 928.73 58.08 L 919.48 63.55 C 919.35 63.62 919.27 63.76 919.27 63.91 L 919.27 76.11 C 919.27 76.27 919.36 76.41 919.49 76.48 L 929.78 81.95 C 929.84 81.98 929.91 82 929.98 82 C 930.05 82 930.12 81.98 930.18 81.95 L 940.09 76.48 C 940.22 76.41 940.3 76.27 940.31 76.12 C 940.31 75.97 940.23 75.83 940.1 75.75 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 92px; margin-left: 930px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ECS
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="930" y="104" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ECS
                </text>
            </switch>
        </g>
        <path d="M 975 55 L 1005 55 L 1005 85 L 975 85 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 994.71 79.02 L 994.71 76.52 C 993.63 77.22 991.67 77.55 989.81 77.55 C 987.78 77.55 986.19 77.19 985.29 76.59 L 985.29 79.02 C 985.29 79.75 986.98 80.5 989.81 80.5 C 992.7 80.5 994.71 79.72 994.71 79.02 Z M 989.81 73.74 C 987.78 73.74 986.19 73.38 985.29 72.78 L 985.29 75.22 C 985.3 75.95 986.99 76.69 989.81 76.69 C 992.69 76.69 994.7 75.92 994.71 75.22 L 994.71 72.7 C 993.63 73.41 991.67 73.74 989.81 73.74 Z M 994.71 71.41 L 994.71 68.51 C 993.63 69.21 991.67 69.55 989.81 69.55 C 987.78 69.55 986.19 69.19 985.29 68.58 L 985.29 71.41 C 985.3 72.14 986.99 72.88 989.81 72.88 C 992.69 72.88 994.7 72.11 994.71 71.41 Z M 985.28 67.21 C 985.28 67.21 985.28 67.21 985.28 67.22 L 985.29 67.22 L 985.29 67.22 C 985.3 67.94 986.99 68.69 989.81 68.69 C 992.96 68.69 994.7 67.82 994.71 67.22 L 994.71 67.22 L 994.72 67.22 C 994.72 67.21 994.72 67.21 994.72 67.21 C 994.72 66.61 992.97 65.73 989.81 65.73 C 986.98 65.73 985.28 66.49 985.28 67.21 Z M 995.57 67.22 L 995.57 71.4 L 995.57 71.4 C 995.57 71.41 995.57 71.41 995.57 71.41 L 995.57 75.21 L 995.57 75.21 C 995.57 75.22 995.57 75.22 995.57 75.23 L 995.57 79.02 C 995.57 80.63 992.58 81.36 989.81 81.36 C 986.54 81.36 984.43 80.44 984.43 79.02 L 984.43 75.23 C 984.43 75.22 984.43 75.22 984.43 75.21 L 984.43 75.21 L 984.43 71.42 C 984.43 71.41 984.43 71.41 984.43 71.4 L 984.43 71.4 L 984.43 67.22 C 984.43 67.22 984.43 67.22 984.43 67.21 C 984.43 65.79 986.54 64.88 989.81 64.88 C 992.59 64.88 995.57 65.61 995.57 67.21 C 995.57 67.22 995.57 67.22 995.57 67.22 Z M 1001.57 61.69 C 1001.81 61.69 1002 61.5 1002 61.27 L 1002 59.07 C 1002 58.83 1001.81 58.64 1001.57 58.64 L 978.43 58.64 C 978.19 58.64 978 58.83 978 59.07 L 978 61.27 C 978 61.5 978.19 61.69 978.43 61.69 C 978.95 61.69 979.38 62.12 979.38 62.64 C 979.38 63.16 978.95 63.58 978.43 63.58 C 978.19 63.58 978 63.77 978 64.01 L 978 72.79 C 978 73.02 978.19 73.22 978.43 73.22 L 982.71 73.22 L 982.71 72.36 L 980.57 72.36 L 980.57 71.07 L 982.71 71.07 L 982.71 70.22 L 980.14 70.22 C 979.91 70.22 979.71 70.41 979.71 70.64 L 979.71 72.36 L 978.86 72.36 L 978.86 64.39 C 979.65 64.19 980.23 63.48 980.23 62.64 C 980.23 61.79 979.65 61.08 978.86 60.89 L 978.86 59.5 L 1001.14 59.5 L 1001.14 60.89 C 1000.35 61.08 999.77 61.79 999.77 62.64 C 999.77 63.48 1000.35 64.19 1001.14 64.39 L 1001.14 72.36 L 1000.29 72.36 L 1000.29 70.64 C 1000.29 70.41 1000.09 70.22 999.86 70.22 L 997.29 70.22 L 997.29 71.07 L 999.43 71.07 L 999.43 72.36 L 997.29 72.36 L 997.29 73.22 L 1001.57 73.22 C 1001.81 73.22 1002 73.02 1002 72.79 L 1002 64.01 C 1002 63.77 1001.81 63.58 1001.57 63.58 C 1001.05 63.58 1000.62 63.16 1000.62 62.64 C 1000.62 62.12 1001.05 61.69 1001.57 61.69 Z M 984.86 64.64 L 984.86 61.21 C 984.86 60.98 984.67 60.79 984.43 60.79 L 981.86 60.79 C 981.62 60.79 981.43 60.98 981.43 61.21 L 981.43 68.5 C 981.43 68.74 981.62 68.93 981.86 68.93 L 983.14 68.93 L 983.14 68.07 L 982.29 68.07 L 982.29 61.64 L 984 61.64 L 984 64.64 Z M 997.71 68.07 L 997.29 68.07 L 997.29 68.93 L 998.14 68.93 C 998.38 68.93 998.57 68.74 998.57 68.5 L 998.57 61.21 C 998.57 60.98 998.38 60.79 998.14 60.79 L 995.57 60.79 C 995.33 60.79 995.14 60.98 995.14 61.21 L 995.14 64.64 L 996 64.64 L 996 61.64 L 997.71 61.64 Z M 994.29 64.21 L 994.29 61.21 C 994.29 60.98 994.09 60.79 993.86 60.79 L 990.86 60.79 C 990.62 60.79 990.43 60.98 990.43 61.21 L 990.43 63.79 L 991.29 63.79 L 991.29 61.64 L 993.43 61.64 L 993.43 64.21 Z M 988.71 63.79 L 988.71 61.64 L 986.57 61.64 L 986.57 64.21 L 985.71 64.21 L 985.71 61.21 C 985.71 60.98 985.91 60.79 986.14 60.79 L 989.14 60.79 C 989.38 60.79 989.57 60.98 989.57 61.21 L 989.57 63.79 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 92px; margin-left: 990px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ElastiCache
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="990" y="104" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Elast...
                </text>
            </switch>
        </g>
        <path d="M 1035 55 L 1065 55 L 1065 85 L 1035 85 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 1046.31 63.15 C 1049 62.03 1050.9 63.51 1051.59 64.19 C 1052.16 64.76 1052.6 65.47 1052.9 66.32 C 1052.95 66.47 1053.08 66.58 1053.24 66.6 C 1053.4 66.62 1053.55 66.56 1053.65 66.44 C 1053.98 65.99 1054.48 65.73 1055 65.73 C 1055.78 65.73 1056.73 66.32 1056.83 68 C 1056.84 68.19 1056.98 68.35 1057.17 68.39 C 1059.11 68.8 1060.1 69.88 1060.1 71.59 C 1060.1 73.19 1059.37 74.17 1057.93 74.52 L 1058.13 75.36 C 1059.95 74.91 1060.96 73.58 1060.96 71.59 C 1060.96 69.57 1059.79 68.17 1057.66 67.62 C 1057.42 65.73 1056.18 64.87 1055 64.87 C 1054.44 64.87 1053.9 65.06 1053.46 65.41 C 1053.13 64.71 1052.71 64.09 1052.2 63.59 C 1050.5 61.9 1048.17 61.45 1045.98 62.36 C 1044.11 63.15 1042.69 65.32 1042.69 67.41 C 1042.69 67.51 1042.69 67.62 1042.7 67.73 C 1040.98 68.29 1039.96 69.71 1039.96 71.57 C 1039.96 71.66 1039.97 71.75 1039.97 71.85 C 1040.06 73.46 1041.14 74.85 1042.71 75.39 L 1042.99 74.58 C 1041.75 74.15 1040.9 73.06 1040.83 71.8 C 1040.83 71.72 1040.82 71.65 1040.82 71.57 C 1040.82 69.39 1042.35 68.68 1043.26 68.46 C 1043.47 68.4 1043.61 68.2 1043.59 67.99 C 1043.56 67.78 1043.55 67.59 1043.55 67.41 C 1043.55 65.67 1044.76 63.8 1046.31 63.15 Z M 1055.14 72.14 C 1055.14 71.97 1055.11 71.84 1055.04 71.78 C 1054.97 71.71 1054.84 71.71 1054.74 71.71 L 1049.14 71.71 C 1048.91 71.71 1048.71 71.52 1048.71 71.29 C 1048.71 71.17 1048.67 70.84 1048.62 70.64 L 1047.14 70.64 C 1047.07 70.84 1047 71.17 1047 71.29 C 1046.99 71.52 1046.8 71.71 1046.57 71.71 L 1045.29 71.71 C 1045.16 71.71 1045.03 71.71 1044.96 71.78 C 1044.89 71.84 1044.86 71.97 1044.86 72.14 L 1044.86 78.14 L 1055.14 78.14 Z M 1056 73 L 1056 76.1 L 1057.11 73 Z M 1055.98 78.72 L 1055.97 78.72 C 1055.91 78.88 1055.76 79 1055.57 79 L 1044.43 79 C 1044.19 79 1044 78.81 1044 78.57 L 1044 72.14 C 1044 71.62 1044.2 71.32 1044.37 71.16 C 1044.54 71 1044.83 70.82 1045.31 70.86 L 1046.19 70.86 C 1046.27 70.41 1046.46 69.79 1046.92 69.79 L 1048.87 69.79 C 1048.89 69.79 1048.92 69.79 1048.95 69.79 C 1049.35 69.87 1049.49 70.44 1049.54 70.86 L 1054.71 70.86 C 1055.17 70.83 1055.46 71 1055.63 71.16 C 1055.8 71.32 1056 71.62 1056 72.14 L 1057.71 72.14 C 1057.85 72.14 1057.98 72.21 1058.06 72.32 C 1058.14 72.44 1058.16 72.58 1058.12 72.72 Z M 1061.14 80.54 L 1057.88 77.28 L 1057.28 77.88 L 1060.54 81.14 L 1058.18 81.14 L 1058.18 82 L 1061.57 82 C 1061.81 82 1062 81.81 1062 81.57 L 1062 78.18 L 1061.14 78.18 Z M 1042.56 77.26 L 1038.86 80.61 L 1038.86 78.18 L 1038 78.18 L 1038 81.57 C 1038 81.81 1038.19 82 1038.43 82 L 1042.25 82 L 1042.25 81.14 L 1039.54 81.14 L 1043.14 77.9 Z M 1061.57 58 L 1057.75 58 L 1057.75 58.86 L 1060.61 58.86 L 1057.26 62.56 L 1057.9 63.14 L 1061.14 59.54 L 1061.14 62.25 L 1062 62.25 L 1062 58.43 C 1062 58.19 1061.81 58 1061.57 58 Z M 1038.86 62.25 L 1038 62.25 L 1038 58.43 C 1038 58.19 1038.19 58 1038.43 58 L 1042.25 58 L 1042.25 58.86 L 1039.46 58.86 L 1043.15 62.55 L 1042.55 63.15 L 1038.86 59.46 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 92px; margin-left: 1050px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                EFS
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1050" y="104" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    EFS
                </text>
            </switch>
        </g>
        <rect x="1065" y="55" width="60" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 70px; margin-left: 1066px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                ・・・
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1095" y="74" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ・・・
                </text>
            </switch>
        </g>
        <rect x="895" y="35" width="240" height="90" fill="none" stroke="#b85450" stroke-width="2" stroke-dasharray="16 16" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 238px; height: 1px; padding-top: 32px; margin-left: 896px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                担当プロジェクト管轄リソース
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1015" y="32" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    担当プロジェクト管轄リソース
                </text>
            </switch>
        </g>
        <path d="M 915 210 L 945 210 L 945 240 L 915 240 Z" fill="url(#mx-gradient-f78e04-1-d05c17-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 939.89 228.26 L 936.52 226.24 L 936.52 221.43 C 936.52 221.28 936.44 221.15 936.31 221.07 L 931.47 218.25 L 931.47 214.18 L 939.89 219.15 Z M 940.52 218.55 L 931.27 213.08 C 931.14 213 930.98 213 930.84 213.07 C 930.71 213.15 930.63 213.29 930.63 213.44 L 930.63 218.49 C 930.63 218.64 930.71 218.78 930.84 218.85 L 935.68 221.68 L 935.68 226.48 C 935.68 226.63 935.76 226.77 935.88 226.84 L 940.09 229.37 C 940.16 229.41 940.23 229.43 940.31 229.43 C 940.38 229.43 940.45 229.41 940.51 229.37 C 940.65 229.3 940.73 229.16 940.73 229.01 L 940.73 218.91 C 940.73 218.76 940.65 218.62 940.52 218.55 Z M 929.98 236.1 L 920.11 230.86 L 920.11 219.15 L 928.53 214.18 L 928.53 218.26 L 924.09 221.08 C 923.97 221.16 923.9 221.29 923.9 221.43 L 923.9 228.59 C 923.9 228.74 923.99 228.89 924.13 228.96 L 929.79 231.9 C 929.91 231.97 930.05 231.97 930.17 231.9 L 935.66 229.07 L 939.04 231.09 Z M 940.1 230.75 L 935.9 228.22 C 935.77 228.15 935.62 228.14 935.49 228.21 L 929.98 231.06 L 924.74 228.33 L 924.74 221.66 L 929.17 218.84 C 929.3 218.77 929.37 218.63 929.37 218.49 L 929.37 213.44 C 929.37 213.29 929.29 213.15 929.16 213.07 C 929.03 213 928.86 213 928.73 213.08 L 919.48 218.55 C 919.35 218.62 919.27 218.76 919.27 218.91 L 919.27 231.11 C 919.27 231.27 919.36 231.41 919.49 231.48 L 929.78 236.95 C 929.84 236.98 929.91 237 929.98 237 C 930.05 237 930.12 236.98 930.18 236.95 L 940.09 231.48 C 940.22 231.41 940.3 231.27 940.31 231.12 C 940.31 230.97 940.23 230.83 940.1 230.75 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 247px; margin-left: 930px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ECS
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="930" y="259" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ECS
                </text>
            </switch>
        </g>
        <path d="M 975 210 L 1005 210 L 1005 240 L 975 240 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 994.71 234.02 L 994.71 231.52 C 993.63 232.22 991.67 232.55 989.81 232.55 C 987.78 232.55 986.19 232.19 985.29 231.59 L 985.29 234.02 C 985.29 234.75 986.98 235.5 989.81 235.5 C 992.7 235.5 994.71 234.72 994.71 234.02 Z M 989.81 228.74 C 987.78 228.74 986.19 228.38 985.29 227.78 L 985.29 230.22 C 985.3 230.95 986.99 231.69 989.81 231.69 C 992.69 231.69 994.7 230.92 994.71 230.22 L 994.71 227.7 C 993.63 228.41 991.67 228.74 989.81 228.74 Z M 994.71 226.41 L 994.71 223.51 C 993.63 224.21 991.67 224.55 989.81 224.55 C 987.78 224.55 986.19 224.19 985.29 223.58 L 985.29 226.41 C 985.3 227.14 986.99 227.88 989.81 227.88 C 992.69 227.88 994.7 227.11 994.71 226.41 Z M 985.28 222.21 C 985.28 222.21 985.28 222.21 985.28 222.22 L 985.29 222.22 L 985.29 222.22 C 985.3 222.94 986.99 223.69 989.81 223.69 C 992.96 223.69 994.7 222.82 994.71 222.22 L 994.71 222.22 L 994.72 222.22 C 994.72 222.21 994.72 222.21 994.72 222.21 C 994.72 221.61 992.97 220.73 989.81 220.73 C 986.98 220.73 985.28 221.49 985.28 222.21 Z M 995.57 222.22 L 995.57 226.4 L 995.57 226.4 C 995.57 226.41 995.57 226.41 995.57 226.41 L 995.57 230.21 L 995.57 230.21 C 995.57 230.22 995.57 230.22 995.57 230.23 L 995.57 234.02 C 995.57 235.63 992.58 236.36 989.81 236.36 C 986.54 236.36 984.43 235.44 984.43 234.02 L 984.43 230.23 C 984.43 230.22 984.43 230.22 984.43 230.21 L 984.43 230.21 L 984.43 226.42 C 984.43 226.41 984.43 226.41 984.43 226.4 L 984.43 226.4 L 984.43 222.22 C 984.43 222.22 984.43 222.22 984.43 222.21 C 984.43 220.79 986.54 219.88 989.81 219.88 C 992.59 219.88 995.57 220.61 995.57 222.21 C 995.57 222.22 995.57 222.22 995.57 222.22 Z M 1001.57 216.69 C 1001.81 216.69 1002 216.5 1002 216.27 L 1002 214.07 C 1002 213.83 1001.81 213.64 1001.57 213.64 L 978.43 213.64 C 978.19 213.64 978 213.83 978 214.07 L 978 216.27 C 978 216.5 978.19 216.69 978.43 216.69 C 978.95 216.69 979.38 217.12 979.38 217.64 C 979.38 218.16 978.95 218.58 978.43 218.58 C 978.19 218.58 978 218.77 978 219.01 L 978 227.79 C 978 228.02 978.19 228.22 978.43 228.22 L 982.71 228.22 L 982.71 227.36 L 980.57 227.36 L 980.57 226.07 L 982.71 226.07 L 982.71 225.22 L 980.14 225.22 C 979.91 225.22 979.71 225.41 979.71 225.64 L 979.71 227.36 L 978.86 227.36 L 978.86 219.39 C 979.65 219.19 980.23 218.48 980.23 217.64 C 980.23 216.79 979.65 216.08 978.86 215.89 L 978.86 214.5 L 1001.14 214.5 L 1001.14 215.89 C 1000.35 216.08 999.77 216.79 999.77 217.64 C 999.77 218.48 1000.35 219.19 1001.14 219.39 L 1001.14 227.36 L 1000.29 227.36 L 1000.29 225.64 C 1000.29 225.41 1000.09 225.22 999.86 225.22 L 997.29 225.22 L 997.29 226.07 L 999.43 226.07 L 999.43 227.36 L 997.29 227.36 L 997.29 228.22 L 1001.57 228.22 C 1001.81 228.22 1002 228.02 1002 227.79 L 1002 219.01 C 1002 218.77 1001.81 218.58 1001.57 218.58 C 1001.05 218.58 1000.62 218.16 1000.62 217.64 C 1000.62 217.12 1001.05 216.69 1001.57 216.69 Z M 984.86 219.64 L 984.86 216.21 C 984.86 215.98 984.67 215.79 984.43 215.79 L 981.86 215.79 C 981.62 215.79 981.43 215.98 981.43 216.21 L 981.43 223.5 C 981.43 223.74 981.62 223.93 981.86 223.93 L 983.14 223.93 L 983.14 223.07 L 982.29 223.07 L 982.29 216.64 L 984 216.64 L 984 219.64 Z M 997.71 223.07 L 997.29 223.07 L 997.29 223.93 L 998.14 223.93 C 998.38 223.93 998.57 223.74 998.57 223.5 L 998.57 216.21 C 998.57 215.98 998.38 215.79 998.14 215.79 L 995.57 215.79 C 995.33 215.79 995.14 215.98 995.14 216.21 L 995.14 219.64 L 996 219.64 L 996 216.64 L 997.71 216.64 Z M 994.29 219.21 L 994.29 216.21 C 994.29 215.98 994.09 215.79 993.86 215.79 L 990.86 215.79 C 990.62 215.79 990.43 215.98 990.43 216.21 L 990.43 218.79 L 991.29 218.79 L 991.29 216.64 L 993.43 216.64 L 993.43 219.21 Z M 988.71 218.79 L 988.71 216.64 L 986.57 216.64 L 986.57 219.21 L 985.71 219.21 L 985.71 216.21 C 985.71 215.98 985.91 215.79 986.14 215.79 L 989.14 215.79 C 989.38 215.79 989.57 215.98 989.57 216.21 L 989.57 218.79 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 247px; margin-left: 990px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ElastiCache
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="990" y="259" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Elast...
                </text>
            </switch>
        </g>
        <path d="M 1035 210 L 1065 210 L 1065 240 L 1035 240 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 1046.31 218.15 C 1049 217.03 1050.9 218.51 1051.59 219.19 C 1052.16 219.76 1052.6 220.47 1052.9 221.32 C 1052.95 221.47 1053.08 221.58 1053.24 221.6 C 1053.4 221.62 1053.55 221.56 1053.65 221.44 C 1053.98 220.99 1054.48 220.73 1055 220.73 C 1055.78 220.73 1056.73 221.32 1056.83 223 C 1056.84 223.19 1056.98 223.35 1057.17 223.39 C 1059.11 223.8 1060.1 224.88 1060.1 226.59 C 1060.1 228.19 1059.37 229.17 1057.93 229.52 L 1058.13 230.36 C 1059.95 229.91 1060.96 228.58 1060.96 226.59 C 1060.96 224.57 1059.79 223.17 1057.66 222.62 C 1057.42 220.73 1056.18 219.87 1055 219.87 C 1054.44 219.87 1053.9 220.06 1053.46 220.41 C 1053.13 219.71 1052.71 219.09 1052.2 218.59 C 1050.5 216.9 1048.17 216.45 1045.98 217.36 C 1044.11 218.15 1042.69 220.32 1042.69 222.41 C 1042.69 222.51 1042.69 222.62 1042.7 222.73 C 1040.98 223.29 1039.96 224.71 1039.96 226.57 C 1039.96 226.66 1039.97 226.75 1039.97 226.85 C 1040.06 228.46 1041.14 229.85 1042.71 230.39 L 1042.99 229.58 C 1041.75 229.15 1040.9 228.06 1040.83 226.8 C 1040.83 226.72 1040.82 226.65 1040.82 226.57 C 1040.82 224.39 1042.35 223.68 1043.26 223.46 C 1043.47 223.4 1043.61 223.2 1043.59 222.99 C 1043.56 222.78 1043.55 222.59 1043.55 222.41 C 1043.55 220.67 1044.76 218.8 1046.31 218.15 Z M 1055.14 227.14 C 1055.14 226.97 1055.11 226.84 1055.04 226.78 C 1054.97 226.71 1054.84 226.71 1054.74 226.71 L 1049.14 226.71 C 1048.91 226.71 1048.71 226.52 1048.71 226.29 C 1048.71 226.17 1048.67 225.84 1048.62 225.64 L 1047.14 225.64 C 1047.07 225.84 1047 226.17 1047 226.29 C 1046.99 226.52 1046.8 226.71 1046.57 226.71 L 1045.29 226.71 C 1045.16 226.71 1045.03 226.71 1044.96 226.78 C 1044.89 226.84 1044.86 226.97 1044.86 227.14 L 1044.86 233.14 L 1055.14 233.14 Z M 1056 228 L 1056 231.1 L 1057.11 228 Z M 1055.98 233.72 L 1055.97 233.72 C 1055.91 233.88 1055.76 234 1055.57 234 L 1044.43 234 C 1044.19 234 1044 233.81 1044 233.57 L 1044 227.14 C 1044 226.62 1044.2 226.32 1044.37 226.16 C 1044.54 226 1044.83 225.82 1045.31 225.86 L 1046.19 225.86 C 1046.27 225.41 1046.46 224.79 1046.92 224.79 L 1048.87 224.79 C 1048.89 224.79 1048.92 224.79 1048.95 224.79 C 1049.35 224.87 1049.49 225.44 1049.54 225.86 L 1054.71 225.86 C 1055.17 225.83 1055.46 226 1055.63 226.16 C 1055.8 226.32 1056 226.62 1056 227.14 L 1057.71 227.14 C 1057.85 227.14 1057.98 227.21 1058.06 227.32 C 1058.14 227.44 1058.16 227.58 1058.12 227.72 Z M 1061.14 235.54 L 1057.88 232.28 L 1057.28 232.88 L 1060.54 236.14 L 1058.18 236.14 L 1058.18 237 L 1061.57 237 C 1061.81 237 1062 236.81 1062 236.57 L 1062 233.18 L 1061.14 233.18 Z M 1042.56 232.26 L 1038.86 235.61 L 1038.86 233.18 L 1038 233.18 L 1038 236.57 C 1038 236.81 1038.19 237 1038.43 237 L 1042.25 237 L 1042.25 236.14 L 1039.54 236.14 L 1043.14 232.9 Z M 1061.57 213 L 1057.75 213 L 1057.75 213.86 L 1060.61 213.86 L 1057.26 217.56 L 1057.9 218.14 L 1061.14 214.54 L 1061.14 217.25 L 1062 217.25 L 1062 213.43 C 1062 213.19 1061.81 213 1061.57 213 Z M 1038.86 217.25 L 1038 217.25 L 1038 213.43 C 1038 213.19 1038.19 213 1038.43 213 L 1042.25 213 L 1042.25 213.86 L 1039.46 213.86 L 1043.15 217.54 L 1042.55 218.15 L 1038.86 214.46 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 247px; margin-left: 1050px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                EFS
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1050" y="259" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    EFS
                </text>
            </switch>
        </g>
        <rect x="1065" y="210" width="60" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 225px; margin-left: 1066px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                ・・・
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1095" y="229" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ・・・
                </text>
            </switch>
        </g>
        <rect x="895" y="190" width="240" height="90" fill="none" stroke="#6c8ebf" stroke-width="2" stroke-dasharray="16 16" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 238px; height: 1px; padding-top: 187px; margin-left: 896px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                他プロジェクト管轄リソース
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1015" y="187" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    他プロジェクト管轄リソース
                </text>
            </switch>
        </g>
        <path d="M 564 80 L 685.16 80" fill="none" stroke="#b85450" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 691.91 80 L 682.91 84.5 L 685.16 80 L 682.91 75.5 Z" fill="#b85450" stroke="#b85450" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 60px; margin-left: 624px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                スイッチ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="624" y="63" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    スイッチ
                </text>
            </switch>
        </g>
        <path d="M 584 130 L 676.11 203.69" fill="none" stroke="#6c8ebf" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 681.38 207.9 L 671.54 205.8 L 676.11 203.69 L 677.16 198.77 Z" fill="#6c8ebf" stroke="#6c8ebf" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 186px; margin-left: 615px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                スイッチ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="615" y="189" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    スイッチ
                </text>
            </switch>
        </g>
        <path d="M 514 130 L 514 150 Q 514 160 504 160 L 94.1 160" fill="none" stroke="#b85450" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 87.35 160 L 96.35 155.5 L 94.1 160 L 96.35 164.5 Z" fill="#b85450" stroke="#b85450" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 140px; margin-left: 394px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                払い出し
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="394" y="143" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    払い出し
                </text>
            </switch>
        </g>
        <rect x="484" y="60" width="70" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 552.86 86.44 C 552.64 86.24 552.37 86.09 552.08 85.97 C 550.43 71.31 537.94 60 523.04 60 C 516.32 60 509.76 62.34 504.57 66.57 C 497.71 72.15 493.78 80.42 493.78 89.26 C 493.78 89.32 493.79 89.39 493.79 89.45 C 492.74 89.98 491.63 90.48 490.52 90.97 C 487.16 92.44 484.73 93.5 484.37 95.42 C 484.28 95.9 484.26 96.86 485.19 97.78 C 486.82 99.41 489.95 100 493.14 100 C 494.46 100 495.79 99.9 497.03 99.73 C 499.14 99.44 503.08 98.67 505.75 96.71 C 509.97 93.66 519.5 92.51 524.46 92.9 C 527.42 93.13 531.01 94.68 533.89 95.93 C 536.09 96.89 537.83 97.64 539.19 97.83 C 541.06 98.09 544.13 97.78 547.37 97.44 L 547.64 97.42 C 548.49 97.32 549.32 97.25 550.15 97.18 C 550.49 97.15 550.82 97.12 551.27 97.09 C 552.66 96.99 553.74 95.83 553.74 94.44 L 553.74 88.42 C 553.74 87.66 553.42 86.94 552.86 86.44 Z M 523.04 63.15 C 536.22 63.15 547.28 73.06 548.91 85.97 C 548.46 86.01 547.98 86.06 547.47 86.1 L 547.18 86.13 C 547.15 84.68 545.96 83.52 544.51 83.52 L 543.39 83.52 C 539.92 73.32 529.88 66.29 518.52 66.29 C 514.31 66.29 510.19 67.25 506.48 69.08 C 506.51 69.06 506.53 69.04 506.56 69.01 C 511.19 65.23 517.04 63.15 523.04 63.15 Z M 539.23 88.82 L 539.23 86.66 L 544.04 86.66 L 544.04 88.82 Z M 496.61 96.61 C 492.43 97.18 489.14 96.67 487.78 95.83 C 488.6 95.24 490.47 94.43 491.78 93.85 C 492.92 93.36 494.07 92.83 495.18 92.27 C 497 93.66 499.25 94.69 501.74 95.31 C 500.3 95.89 498.52 96.35 496.61 96.61 Z M 550.59 93.98 C 550.35 94 550.1 94.02 549.86 94.04 C 549.04 94.11 548.18 94.19 547.31 94.28 L 547.05 94.31 C 544.27 94.6 541.12 94.92 539.63 94.72 C 538.69 94.58 536.97 93.83 535.14 93.04 C 532.04 91.7 528.19 90.03 524.71 89.76 C 519.91 89.39 511.62 90.3 506.28 92.8 C 503.19 92.66 500.32 91.83 498.1 90.46 C 498.09 90.03 498.08 89.6 498.08 89.44 C 498.08 82.92 501.13 76.82 506.45 72.67 C 510.07 70.55 514.24 69.44 518.52 69.44 C 528.15 69.44 536.71 75.13 540.04 83.52 L 538.76 83.52 C 537.48 83.52 536.41 84.42 536.15 85.63 C 535.93 85.54 535.73 85.47 535.5 85.38 C 532.3 84.15 527.92 82.46 522.85 82.76 C 515.44 83.2 511.8 83.64 508.93 84.46 L 509.79 87.49 C 512.42 86.74 515.88 86.33 523.03 85.9 C 527.42 85.66 531.44 87.19 534.36 88.32 C 534.98 88.56 535.55 88.77 536.08 88.96 L 536.08 89.28 C 536.08 90.76 537.29 91.96 538.76 91.96 L 544.51 91.96 C 545.98 91.96 547.18 90.76 547.19 89.29 L 547.75 89.24 C 548.68 89.16 549.54 89.07 550.26 89 L 550.59 88.97 Z" fill="#bf0816" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 107px; margin-left: 519px;">
                        <div data-drawio-colors="color: #232F3E; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                AssumeRoleOnlyAccess
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="519" y="119" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    AssumeRoleO...
                </text>
            </switch>
        </g>
        <rect x="695" y="220" width="70" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 763.86 246.44 C 763.64 246.24 763.37 246.09 763.08 245.97 C 761.43 231.31 748.94 220 734.04 220 C 727.32 220 720.76 222.34 715.57 226.57 C 708.71 232.15 704.78 240.42 704.78 249.26 C 704.78 249.32 704.79 249.39 704.79 249.45 C 703.74 249.98 702.63 250.48 701.52 250.97 C 698.16 252.44 695.73 253.5 695.37 255.42 C 695.28 255.9 695.26 256.86 696.19 257.78 C 697.82 259.41 700.95 260 704.14 260 C 705.46 260 706.79 259.9 708.03 259.73 C 710.14 259.44 714.08 258.67 716.75 256.71 C 720.97 253.66 730.5 252.51 735.46 252.9 C 738.42 253.13 742.01 254.68 744.89 255.93 C 747.09 256.89 748.83 257.64 750.19 257.83 C 752.06 258.09 755.13 257.78 758.37 257.44 L 758.64 257.42 C 759.49 257.32 760.32 257.25 761.15 257.18 C 761.49 257.15 761.82 257.12 762.27 257.09 C 763.66 256.99 764.74 255.83 764.74 254.44 L 764.74 248.42 C 764.74 247.66 764.42 246.94 763.86 246.44 Z M 734.04 223.15 C 747.22 223.15 758.28 233.06 759.91 245.97 C 759.46 246.01 758.98 246.06 758.47 246.1 L 758.18 246.13 C 758.15 244.68 756.96 243.52 755.51 243.52 L 754.39 243.52 C 750.92 233.32 740.88 226.29 729.52 226.29 C 725.31 226.29 721.19 227.25 717.48 229.08 C 717.51 229.06 717.53 229.04 717.56 229.01 C 722.19 225.23 728.04 223.15 734.04 223.15 Z M 750.23 248.82 L 750.23 246.66 L 755.04 246.66 L 755.04 248.82 Z M 707.61 256.61 C 703.43 257.18 700.14 256.67 698.78 255.83 C 699.6 255.24 701.47 254.43 702.78 253.85 C 703.92 253.36 705.07 252.83 706.18 252.27 C 708 253.66 710.25 254.69 712.74 255.31 C 711.3 255.89 709.52 256.35 707.61 256.61 Z M 761.59 253.98 C 761.35 254 761.1 254.02 760.86 254.04 C 760.04 254.11 759.18 254.19 758.31 254.28 L 758.05 254.31 C 755.27 254.6 752.12 254.92 750.63 254.72 C 749.69 254.58 747.97 253.83 746.14 253.04 C 743.04 251.7 739.19 250.03 735.71 249.76 C 730.91 249.39 722.62 250.3 717.28 252.8 C 714.19 252.66 711.32 251.83 709.1 250.46 C 709.09 250.03 709.08 249.6 709.08 249.44 C 709.08 242.92 712.13 236.82 717.45 232.67 C 721.07 230.55 725.24 229.44 729.52 229.44 C 739.15 229.44 747.71 235.13 751.04 243.52 L 749.76 243.52 C 748.48 243.52 747.41 244.42 747.15 245.63 C 746.93 245.54 746.73 245.47 746.5 245.38 C 743.3 244.15 738.92 242.46 733.85 242.76 C 726.44 243.2 722.8 243.64 719.93 244.46 L 720.79 247.49 C 723.42 246.74 726.88 246.33 734.03 245.9 C 738.42 245.66 742.44 247.19 745.36 248.32 C 745.98 248.56 746.55 248.77 747.08 248.96 L 747.08 249.28 C 747.08 250.76 748.29 251.96 749.76 251.96 L 755.51 251.96 C 756.98 251.96 758.18 250.76 758.19 249.29 L 758.75 249.24 C 759.68 249.16 760.54 249.07 761.26 249 L 761.59 248.97 Z" fill="#bf0816" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 267px; margin-left: 730px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                IAM Role for B
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="730" y="279" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    IAM Role fo...
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>