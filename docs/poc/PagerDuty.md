# 検証項目

PagerDuty のトライアルアカウントで下記の検証を行う

- Amazon Cloudwatch のアラートを受け取れる
- New Relic のアラートを受け取れる
- TiDB のアラートを受け取れる
- Amazon Cloudwatch からのアラートをメール(グループメール)で通知する
- Amazon Cloudwatch からのアラートを電話で通知する
- Amazon Cloudwatch からのアラートを Slack へ通知する
- Amazon Cloudwatch からのアラートを Teams へ通知する
- New Relic からのアラートをメール(グループメール)で通知する
- New Relic からのアラートを電話で通知する
- New Relic からのアラートを Slack へ通知する
- New Relic からのアラートを Teams へ通知する
- TiDB のアラート(コスト制限など)をメール(グループメール)で通知する
- TiDB のアラート(コスト制限など)を電話で通知する
- TiDB のアラート(コスト制限など)を Slack へ通知する
- TiDB のアラート(コスト制限など)を Teams へ通知する
- 上記アラートをサービス単位で管理できる（アラート元・通知先を分けるなど）
- サービス単位で権限を制御できる
- CLI(API)経由でサービスの作成ができる
- CLI(API)経由で CloudWatch のアラート連携が設定できる
- CLI(API)経由で NewRelic のアラート連携が設定できる
- CLI(API)経由で TiDB のアラート連携が設定できる
- CLI(API)経由でユーザーの招待ができる
- CLI(API)経由でユーザーへ権限の付与が出来る

# 検証結果

## 出来たこと

- Amazon Cloudwatch のアラートを受け取れる
- New Relic のアラートを受け取れる
- Amazon Cloudwatch からのアラートをメール（個人）で通知する
- Amazon Cloudwatch からのアラートを電話で通知する
- Amazon Cloudwatch からのアラートを Slack へ通知する
- New Relic からのアラートをメール（個人）で通知する
- New Relic からのアラートを電話で通知する
- New Relic からのアラートを Slack へ通知する
- 上記アラートをサービス単位で管理できる（アラート元・通知先を分けるなど）
- サービス単位で権限を制御できる
- CLI(API)経由でサービスの作成ができる
- CLI(API)経由で CloudWatch のアラート連携が設定できる
- CLI(API)経由で NewRelic のアラート連携が設定できる
- CLI(API)経由で TiDB のアラート連携が設定できる
- CLI(API)経由でユーザーの招待ができる
- CLI(API)経由でユーザーへ権限の付与が出来る

## 出来なかったこと

- Amazon Cloudwatch からのアラートをメール(グループメール)で通知する
- New Relic からのアラートをメール(グループメール)で通知する
- TiDB のアラート(コスト制限など)をメール(グループメール)で通知する

通知できるのは、PagerDuty のライセンスを持つユーザーのみ  
PagerDuty の規約上、ライセンスは一人１つなので複数人が所属するグループメールは登録不可能

## 要検討

- TiDB のアラートを受け取れる
- TiDB のアラート(コスト制限など)を電話で通知する
- TiDB のアラート(コスト制限など)を Slack へ通知する

TiDB も工夫すれば、アラートを受け取れますが、金額制限の通知を受け取るには強い権限が必要そうです。

## 未検証

- Amazon Cloudwatch からのアラートを Teams へ通知する
- New Relic からのアラートを Teams へ通知する
- TiDB のアラート(コスト制限など)を Teams へ通知する

例外申請が必要なので、一時保留

# 検証内容詳細

# PagerDuty とは

PagerDuty（ペイジャーデューティ）はシステムのインシデント対応を一元化するプラットフォームです。システム障害対応に費やす時間を軽減し、貴重なエンジニアリソースをビジネス拡大に充てることができます。（[公式サイト](https://www.pagerduty.co.jp/)より）

<details>

<summary>用語解説</summary>

# 用語解説

PagerDuty 独自の単位や用語の説明

1. [**インシデント**](#インシデントincident参照)
1. [**サービス**](#サービスservice参照)
1. [**インテグレーション**](#インテグレーションintegration参照)
1. [**ユーザー**](#ユーザーuser参照)
1. [**オンコールスケジュール**](#オンコールスケジュールon-call-schedule参照)
1. [**エスカレーションポリシー**](#エスカレーションポリシーescalation-policy参照)

### インシデント（Incident）([参照](https://support.pagerduty.com/main/lang-ja/docs/incidents))

インシデントは、対処して解決する必要のある問題または課題を表します。  
インシデントはサービス上で発生し、エスカレーション・ポリシーにより担当者に通知が送られます。

![incident_sample](images/pagerduty/Incident_Sample.png)

#### インシデントのプロパティ

| Name             | 内容                                                                                                                   |
| ---------------- | ---------------------------------------------------------------------------------------------------------------------- |
| Title            | タイトル                                                                                                               |
| Impacted Service | 対象サービス<br/>既存のサービスから選択                                                                                |
| Description      | 説明                                                                                                                   |
| Urgency          | 緊急度<br/>`High or Low`<br/>個人の通知設定で緊急度ごとに設定を分けられる<br/>緊急度が低い場合は電話通知したくないなど |
| Priority         | 優先度<br/>`None/P1/P2/~/P5`から選択                                                                                   |
| Assignee         | 担当者<br/>`ユーザーまたはエスカレーションポリシー`を選択                                                              |

#### インシデントには 3 つのステータスがあります

| 　ステータス　 | 　　　　　いつ？　　　　　　　                              | どんな状態？                                                                                                                |
| -------------- | ----------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------- |
| Triggered      | インシデントが発生した<br/>～<br/>担当者が認知(Ack)するまで | インシデントが発生し、担当者に通知されている状態<br/>事前に設定されたエスカレーションポリシーに従って、担当者へ通知されます |
| Acknowledged   | 担当者が認知(Ack)した<br/>～<br/>解決(Resolve)されるまで    | 担当者がインシデントを認知し、問題が解決されていない状態                                                                    |
| Resolved       | 問題が解決済み                                              | 問題が解消された状態                                                                                                        |

### サービス（Service）([参照](https://support.pagerduty.com/main/lang-ja/docs/services-and-integrations))

インシデントを管理する枠組みとしてサービスという単位があります。  
Service には 2 種類あり、1 つは[Business Service](https://support.pagerduty.com/main/lang-ja/docs/business-services)、もう 1 つが[Technical Service](https://support.pagerduty.com/main/lang-ja/docs/services-and-integrations#section-configuring-services-and-integrations)です。

| 　　　種類　　　  | 概要                                                                                                                                                                                                                                                                                                                                      |
| ----------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Business Service  | エンドユーザーの存在するサービス<br>例: EC ウェブサイト、EC モバイルアプリ、外部 API 等<br><br/>経営者やカスタマーサービスなどの関係者(主にエンジニア以外)に対して、サービスの状況を伝えるために利用します。                                                                                                                              |
| Technical Service | Business Service が正常に稼働するために必要な技術コンポーネント(マイクロサービス等)<br>例: ユーザー認証、決済プロセス、CMS、API Gateway 等<br><br/>各 Technical Service には Escalation Policy を紐付け、インシデント発生時の通知先を指定します。<br>PagerDuty のドキュメントで、単に Service と呼ぶ場合は Technical Service を指します。 |

![service_category](images/pagerduty/Service_Category.jpeg)

### インテグレーション（Integration）([参照](https://support.pagerduty.com/main/lang-ja/docs/services-and-integrations))

イベントを連携するツールのことです。  
実サービスから PagerDuty へのイベント受信や PagerDuty から Slack や Teams などのイベント送信を行うものです。

![integration_top](images/pagerduty/Integration_Top.png)

主要なインテグレーションに関しては、割とちゃんとしたドキュメントがあります。

![integration_document_cw](images/pagerduty/Integration_Document_CW.png)

### ユーザー（User）([参照](https://support.pagerduty.com/main/lang-ja/docs/manage-users))

PagerDuty のユーザーは、ユーザー単位のライセンス制なのでライセンスを払い出した利用者のことです。（ユーザー単位のライセンス制なのでグループメールでの登録は禁止）  
ライセンスには２種類あり、Full User と Stakeholder です。  
使い分けとしては、

- インシデントに直接関わるのが Full User
- 経営層などのサービス関係者が利用するのが Stakeholder

#### フルユーザー（Full User）

- 権限の範囲内で PagerDuty のすべてのサービスにアクセスが可能

#### ステークホルダー（Stakeholder）

- Business Service の閲覧がメイン（その他少しだけ閲覧可能）
- ライセンス料がお安い

### オンコールスケジュール（On-call Schedule）([参照](https://support.pagerduty.com/main/lang-ja/docs/schedule-basics))

担当者のシフト表みたいなものです。  
複数のユーザーで On-call のローテーションを組めます。

![oncall_schedule](images/pagerduty/OnCall_Schedule.png)

### エスカレーションポリシー（Escalation Policy）([参照](https://support.pagerduty.com/main/lang-ja/docs/escalation-policies))

インシデント発生時の通知先をまとめて管理するものです。
担当者が複数人いる場合には、これを通知先として送信します。
A さんが反応しない場合は B さんへ通知など、通知先には上の On-call Schedule も含められる。
前の人がタイムアウトまでに Ack しなければ次の人へ通知する。

![escalation_policy](images/pagerduty/Escalation_Policy.png)

</details>

# 検証

**検証項目**

1. [**Amazon CloudWatch Alarm の連携**](#1-amazon-cloudwatch-alarm-の連携)
1. [**NewRelic の連携**](#2-newrelic-の連携)
1. [**TiDB の連携**](#3-tidb-の連携email-検知)
1. [**サービスの管理**](#4-サービスの管理)
1. [**権限管理**](#5-権限管理)

## 1. Amazon CloudWatch Alarm の連携

### 仕組み

CloudWatch Alarm → SNS トピック → Integration URL → PagerDuty で検知

### 検証手順

最初なので細かく書きます。

1. [Technical Service の作成](#ⅰ-technical-serviceの作成)
1. [インテグレーションの設定](#ⅱ-インテグレーションの設定)
1. [インシデントを出してみる](#ⅲ-インシデントを出してみる)
1. [イベントルールの設定](#ⅳ-イベントルールの設定)
1. [メール/電話/SMS 通知を行う](#ⅴ-メール電話sms通知を行う)
1. [Teams へ通知](#ⅵ-teamsへ通知)
1. [Slack へ通知](#ⅶ-slackへ通知)

### Ⅰ. Technical Service の作成

まず Technical Service を作ります。
「Services」→「Service Directory」→「＋ New Service」

#### サービス名を設定

![cw_create_service_1](images/pagerduty/CW_Create_Service_1.png)

#### エスカレーションポリシーを設定

まだ一つもないので、今回は新規作成

![cw_create_service_2](images/pagerduty/CW_Create_Service_2.png)

#### アラートのグルーピング（AIOps の機能）

連続でアラートが発生したりする場合に、それらのアラートをグルーピングしてくれます。
グルーピングは以下の通り、

| 種類                        | 概要                                                     |
| --------------------------- | -------------------------------------------------------- |
| Intelligent                 | アラートの類似性や過去の傾向でインテリジェンスに纏めます |
| Alert Content               | 指定したアラートフィールドの内容が一致した際に纏めます   |
| Intelligent + Alert Content | ↑2 つの複合系                                            |
| Time-Based                  | 一定期間で纏める                                         |
| Turn Off Alert Grouping     | 纏めない                                                 |

![cw_create_service_3](images/pagerduty/CW_Create_Service_3.png)

**Transient Alerts**
自動復旧する可能性があるアラートを AI で特定し、担当者への通知を一定時間差し止める機能です。

![cw_create_service_4](images/pagerduty/CW_Create_Service_4.png)

#### Integration を選択

最後にイベントを受信するインテグレーションを選択します。
今回は CloudWatch Alarm から取得するので「Amazon CloudWatch」を選択

![cw_create_service_5](images/pagerduty/CW_Create_Service_5.png)

サービスが作成できました。続いてインテグレーションの設定を行います。

![cw_create_service_6](images/pagerduty/CW_Create_Service_6.png)

### Ⅱ. インテグレーションの設定

それではインテグレーションと CloudWatch Alarm を連携します。
今回検知する Alarm は Gevanni のデプロイ失敗時のアラーム。

#### 連携方法

アラームの通知先に設定されている SNS トピックの subscription に、インテグレーションの URL を追加。
URL はサービス内のインテグレーションの詳細に書かれてます。

![cw_integration_setting_1](images/pagerduty/CW_Integration_Setting_1.png)

![cw_integration_setting_2](images/pagerduty/CW_Integration_Setting_2.png)

登録すると自動で確認済みになります。

![cw_integration_setting_3](images/pagerduty/CW_Integration_Setting_3.png)

### Ⅲ. インシデントを出してみる

今回はわざとコンテナのデプロイに失敗して、アラートを PagerDuty に送信します。
具体的には以下のようなコンテナをデプロイ。

```dockerfile:dockerfile
FROM alpine:latest
CMD ["sh", "-c", "exit 1"]
```

インシデントが作成されました。

![cw_pop_alert_1](images/pagerduty/CW_Pop_Alert_1.png)

これでもインシデントの作成は出来るのですが、現状の設定だと SNS トピックに送られるすべてのアラームに反応してしまうので柔軟性に難あり。

![cw_pop_alert_2](images/pagerduty/CW_Pop_Alert_2.png)

### Ⅳ. イベントルールの設定

送られてくるアラートを内容で重要度などを場合分けしたい場合には、イベントルールの設定を行います。  
サービス →「Setting」→「Event Management」からイベントルールの設定が行えます。  
今回は、コンテナデプロイに失敗したら通知それ以外は無視で設定します。  
イベントルールの設定画面はフローチャートのように設定できます。

![cw_eventrule_setting_1](images/pagerduty/CW_EventRule_Setting_1.png)

まずコンテナデプロイイベントを分岐します。
右部より過去のイベントを参考にしながら設定できます。

![cw_eventrule_setting_2](images/pagerduty/CW_EventRule_Setting_2.png)

![cw_eventrule_setting_3](images/pagerduty/CW_EventRule_Setting_3.png)

![cw_eventrule_setting_4](images/pagerduty/CW_EventRule_Setting_4.png)

それ以外のすべてのイベントは全て無効

![cw_eventrule_setting_5](images/pagerduty/CW_EventRule_Setting_5.png)

![cw_eventrule_setting_6](images/pagerduty/CW_EventRule_Setting_6.png)

これで ECS のコンテナデプロイイベントのみ検知するようになりました。

### Ⅴ. メール/電話/SMS 通知を行う

個人設定の通知先に、メール/電話/SMS を追加。  
この際にアラート発生から何分後に通知するか設定できます。  
例えば、メールと SMS 通知はすぐ行い、3 分反応なければ電話が飛ぶなど。  
また、インシデントの緊急度ごとに変更できます。

![cw_nortifier_1](images/pagerduty/CW_Nortifier_1.png)

アラートを出すとそれぞれに通知が来ました。

**メール**

<details>

<summary>画像</summary>

![cw_nortifier_2](images/pagerduty/CW_Nortifier_2.png)

</details>

**SMS**

<details>

<summary>画像</summary>

![cw_nortifier_3](images/pagerduty/CW_Nortifier_3.png)

</details>

**電話**

<details>

<summary>画像</summary>

![cw_nortifier_4](images/pagerduty/CW_Nortifier_4.png)

通知される電話番号については、以下から取得可能です。  
https://support.pagerduty.com/main/lang-ja/docs/notification-phone-numbers

</details>

### Ⅵ. Teams へ通知

準備中

### Ⅶ. Slack へ通知

Slack へ連絡するためには、事前に Slack App のインストールが必要です。

#### Slack インテグレーションの設定

ワークスペースの追加

![cw_slack_integration_1](images/pagerduty/CW_Slack_Integration_1.png)

![cw_slack_integration_2](images/pagerduty/CW_Slack_Integration_2.png)

チャンネルの追加（add Connection より）

![cw_slack_integration_3](images/pagerduty/CW_Slack_Integration_3.png)

通知対象者を担当者かステークホルダーか選べる。

![cw_slack_integration_4](images/pagerduty/CW_Slack_Integration_4.png)

それ以外にも重要度や優先度での選択も可能。（優先度別にチャンネルを作るとか？）

![cw_slack_integration_5](images/pagerduty/CW_Slack_Integration_5.png)

「add Connection」を押すと、勝手に Slack チャンネルにアプリが追加されました。  
（PagerDuty と Slack のユーザ連携していないはずだが、なぜか追加した人を検知）

![cw_slack_integration_6](images/pagerduty/CW_Slack_Integration_6.png)

アラートを出すと Slack に連絡が来ました。

![cw_slack_integration_7](images/pagerduty/CW_Slack_Integration_7.png)

「Acknowledge」を押すと PagerDuty のユーザと連携するリンクが出てきました。

![cw_slack_integration_8](images/pagerduty/CW_Slack_Integration_8.png)

ここで PagerDuty と連携すると、Slack 上から「Acknowledge」や「Resolve」出来るようになります。

![cw_slack_integration_9](images/pagerduty/CW_Slack_Integration_9.png)

![cw_slack_integration_10](images/pagerduty/CW_Slack_Integration_10.png)

![cw_slack_integration_11](images/pagerduty/CW_Slack_Integration_11.png)

ちなみに、「Create Dedicated Channel」を押すと勝手に専用チャンネルが作成されます。

![cw_slack_integration_12](images/pagerduty/CW_Slack_Integration_12.png)

## 2. NewRelic の連携

### 検証手順

1. [Technical Service の作成](#ⅰ-technical-serviceの作成-1)
1. [インテグレーションの設定](#ⅱ-インテグレーションの設定-1)
1. [インシデントを出してみる](#ⅲ-インシデントを出してみる)
1. [メール電話 sms 通知を行う](#ⅳ-メール電話sms通知を行う)
1. [Teams へ通知](#ⅳ-teamsへ通知)
1. [Slack へ通知](#ⅴ-slackへ通知)

### Ⅰ. Technical Service の作成

テクニカルサービスを作成します。

![nr_create_service_1](images/pagerduty/NR_Create_Service_1.png)

エスカレーションポリシーは、先ほど作成したもの

![nr_create_service_2](images/pagerduty/NR_Create_Service_2.png)

![nr_create_service_3](images/pagerduty/NR_Create_Service_3.png)

インテグレーションで NewRelic を選択

![nr_create_service_4](images/pagerduty/NR_Create_Service_4.png)

作成できました。

![nr_create_service_5](images/pagerduty/NR_Create_Service_5.png)

### Ⅱ. インテグレーションの設定

既に NewRelic ではアラートを作成しているので、連携されているポリシーにワークフローを追加します。

![nr_integration_setting_1](images/pagerduty/NR_Integration_Setting_1.png)

![nr_integration_setting_2](images/pagerduty/NR_Integration_Setting_2.png)

チャンネルに PagerDuty を選択

![nr_integration_setting_3](images/pagerduty/NR_Integration_Setting_3.png)

![nr_integration_setting_4](images/pagerduty/NR_Integration_Setting_4.png)

サービス名とインテグレーションキーは、PagerDuty のインテグレーション設定に書かれている（Integration Name・Integration Key）を入力。

![nr_integration_setting_5](images/pagerduty/NR_Integration_Setting_5.png)

![nr_integration_setting_6](images/pagerduty/NR_Integration_Setting_6.png)

テストアラートが受け取れました。

![nr_integration_setting_7](images/pagerduty/NR_Integration_Setting_7.png)

![nr_integration_setting_8](images/pagerduty/NR_Integration_Setting_8.png)

![nr_integration_setting_9](images/pagerduty/NR_Integration_Setting_9.png)

### Ⅲ. メール/電話/SMS 通知を行う

通知設定は済んでいるのでそのままアラートを出してみる。（今回のアラート内容は ECS サービスの CPU 使用率が 80%を超えたとき。）

![nr_pop_alert_1](images/pagerduty/NR_Pop_Alert_1.png)

それぞれちゃんと受け取れました。

**メール**

<details>

<summary>画像</summary>

![nr_pop_alert_2](images/pagerduty/NR_Pop_Alert_2.png)

</details>

**SMS**

<details>

<summary>画像</summary>

![nr_pop_alert_3](images/pagerduty/NR_Pop_Alert_3.png)

</details>

**電話**

<details>

<summary>画像</summary>

![nr_pop_alert_4](images/pagerduty/NR_Pop_Alert_4.png)

</details>

### Ⅳ. Teams へ通知

準備中

### Ⅴ. Slack へ通知

Slack でも受け取れました。

![nr_slack_integration_1](images/pagerduty/NR_Slack_Integration_1.png)

特に設定してないですが、NewRelic のアラートが解消されたら自動で解決されました。

![nr_slack_integration_2](images/pagerduty/NR_Slack_Integration_2.png)

## 3. TiDB の連携(email 検知)

TiDB には PagerDuty と連携できるインテグレーションが存在しないので、代わりに Email Integration を利用します。
サービス作成などは省略

Email Integration を設定すると、Email アドレスが設定できます。  
ドメインは、PagerDuty の組織ドメイン。

![tidb_poc_1](images/pagerduty/TiDB_PoC_1.png)

アドレスのユーザー名部分は任意のものに変更が可能

![tidb_poc_2](images/pagerduty/TiDB_PoC_2.png)

TiDB のアラートメール部分より設定

![tidb_poc_3](images/pagerduty/TiDB_PoC_3.png)

![tidb_poc_4](images/pagerduty/TiDB_PoC_4.png)

しようと思ったのですが、TiDB のアラートメールはユーザーのものしか設定できませんでした。  
Integration Email には入れないので、TiDB からアラートメールの設定は出来なさそうです...

と思ったのですが、閃きました！  
アラートとして招待情報を飛ばせばよいのでは？

試してみる。ユーザー招待から、招待メールを飛ばしてみる。（何も権限のないメンバーロールで検証）

![tidb_poc_5](images/pagerduty/TiDB_PoC_5.png)

受け取ったデータもちゃんと見れそうです。

![tidb_poc_6](images/pagerduty/TiDB_PoC_6.png)

![tidb_poc_7](images/pagerduty/TiDB_PoC_7.png)

Accept から

![tidb_poc_8](images/pagerduty/TiDB_PoC_8.png)

![tidb_poc_9](images/pagerduty/TiDB_PoC_9.png)

出来ました！  
これで先ほどのアラート設定に行きます。

![tidb_poc_10](images/pagerduty/TiDB_PoC_10.png)

出来ました！

![tidb_poc_11](images/pagerduty/TiDB_PoC_11.png)

TiDB の月額使用量制限を 0.01 ドルにして、アラートを出してみます。

![tidb_poc_12](images/pagerduty/TiDB_PoC_12.png)

mysqlslap を使いアラートを出しました。

![tidb_poc_13](images/pagerduty/TiDB_PoC_13.png)

が、Alert Subscribe したアドレスには送信されず。Organization Owner にしか送信されていない

![tidb_poc_14](images/pagerduty/TiDB_PoC_14.png)

流石に PagerDuty を Organization Owner にするわけにも行かず。潔く断念しました。  
一応テストメールからの通知は、全部受け取れました。

**メール**

<details>

<summary>画像</summary>

![tidb_poc_15](images/pagerduty/TiDB_PoC_15.png)

</details>

**SMS/電話**
同じなので省略

**Slack**

<details>

<summary>画像</summary>

![tidb_poc_16](images/pagerduty/TiDB_PoC_16.png)

</details>

## 4. サービスの管理

### アラートの管理

上記の検証で既にわかる通り、アラートはサービス単位で作成するため。  
アラートの管理もサービス単位になります。  
アラートをサービスに連携し、フィルタリングすることでアラート元や重要度などでインシデントを場合分けできます。

![alert_management](images/pagerduty/Alert_Management.png)

### Service Directory

[機能一覧](https://www.pagerduty.co.jp/full-feature-comparison/)では、大層な機能のように書かれていますが、普通のサービス一覧です。  
未確認のインシデントがあるときは赤  
未解決のインシデントがあるときは黄色  
パッと見でそれらのサービスの状態がわかります。

![Service_Directory](images/pagerduty/Service_Directory.png)

## 5. 権限管理

### ロールの種類

ロールの種類は大きく３種類あります。

| 種類               | 概要                                                                                                                     |
| ------------------ | ------------------------------------------------------------------------------------------------------------------------ |
| ベースロール       | 全てのユーザーに紐づく基本ロール（Account Owner や Global Admin のような強めの権限はチームロールなどよりも優先されます） |
| チームロール       | チーム単位で割り当てられるものというわけでは無く、チームの持つスコープ内で何が出来るかなどチーム内で割り当てられるロール |
| オブジェクトロール | スケジュールやエスカレーションポリシー、サービスのスコープ内で個々のユーザーに与えられるロール                           |

公式ドキュメントの画像がわかりやすかったです。

![role_category](images/pagerduty/Role_Category.png)

### カスタムロール

カスタムロールのような自分らで作成できるロールはありませんでした。

### ロールの割り当て

ロールの割り当ては、各ロールの種類ごとに決まった中から選択。  
**_ベースロールの割り当て_**
ユーザー一覧から

![attach_baseRole](images/pagerduty/Attach_BaseRole.png)

**_チームロールの割り当て_**
チームのユーザー一覧から  
（上位のベースロールを持っている人は変更できません。）

![attach_baseRole](images/pagerduty/Attach_TeamRole.png)

**_オブジェクトロールの割り当て_**
ユーザー詳細から
（上位のロールを持っている人は追加できません）

![attach_objectrole](images/pagerduty/Attach_ObjectRole.png)

サービス・エスカレーションポリシー・スケジュール単位で設定が可能

各種ロールがどのような権限を持つのかは一覧表をご覧ください

https://support.pagerduty.com/main/lang-ja/docs/advanced-permissions#base-roles

## ６．API の利用

割と最近 PagerDutyAPI の記事がクラメソから出ていました。

### API の種類

API リファレンスに行くと何種類かの API がありました。

![use_api](images/pagerduty/Use_API.png)

主に使うのは PagerDuty API と V2 Events API です。  
クラメソ記事にもありますが、PagerDuty API は同期 API、Events API は非同期 API らしいです。  
PagerDuty API の方が資料が充実していそうだったのでこちらで検証。

### API キーの種類

キーにも種類があり、一般アクセス用 REST API キーとユーザートークン REST API キーの２種類あります。

#### 一般アクセス用 REST API キー

一般アクセス用 REST API キーは Account Owner と Admin 権限を持つユーザーしか作れません。（無効化・有効化・削除もこのユーザーのみ可能）  
API キーの権限も Read-Only か FullAccess の２種類です。

#### ユーザートークン REST API キー

個人用の API アクセスキーです。  
作成するのに権限は不要で、そのユーザーの権限の範囲でアクセスが可能。  
Account Owner と Admin は他ユーザーのキーを削除可能。

### API キーの作成

一般アクセス用 REST API キーを作ります。  
Integration > API Access Key より

![create_api_key](images/pagerduty/Create_API_Key.png)

### API でサービスの作成

リクエストの Body はこのような感じ。設定項目が多かったので名前と説明だけ変更。

```json:body
{
  "service": {
    "type": "service",
    "name": "API Test Service",
    "description": "テスト用のサービスです",
    "auto_resolve_timeout": 14400,
    "acknowledgement_timeout": 600,
    "status": "active",
    "escalation_policy": {
      "id": "PWIP6CQ",
      "type": "escalation_policy_reference"
    },
    "incident_urgency_rule": {
      "type": "use_support_hours",
      "during_support_hours": {
        "type": "constant",
        "urgency": "high"
      },
      "outside_support_hours": {
        "type": "constant",
        "urgency": "low"
      }
    },
    "support_hours": {
      "type": "fixed_time_per_day",
      "time_zone": "America/Lima",
      "start_time": "09:00:00",
      "end_time": "17:00:00",
      "days_of_week": [
        1,
        2,
        3,
        4,
        5
      ]
    },
    "scheduled_actions": [
      {
        "type": "urgency_change",
        "at": {
          "type": "named_time",
          "name": "support_hours_start"
        },
        "to_urgency": "high"
      }
    ],
    "alert_creation": "create_alerts_and_incidents",
    "alert_grouping_parameters": {
      "type": "time",
      "config": {
        "timeout": 2
      }
    },
    "auto_pause_notifications_parameters": {
      "enabled": true,
      "timeout": 300
    }
  }
}
```

送信。  
失敗しました。

![api_create_service_1](images/pagerduty/API_Create_Service_1.png)

エスカレーションポリシーの ID は、エスカレーションポリシーの詳細画面の URL から確認できます。

![api_create_service_2](images/pagerduty/API_Create_Service_2.png)

修正して再度送信。

![api_create_service_3](images/pagerduty/API_Create_Service_3.png)

作成されたっぽいです。

コンソールからも確認できました。

![api_create_service_4](images/pagerduty/API_Create_Service_4.png)

OpenAPI 形式でリファレンスからリクエストがテスト出来てとても楽でした。

### API でインテグレーションの連携

サービスにインテグレーションを連携します。  
ここで行うのは Pagerduty 側での設定まで。残りは送信元サービスの設定なので割愛。

#### CloudWatch Integration の連携

↑ の「Create a Service」の下に「Create a new integration」という API があったのでこれを使います。

![api_create_integration_1](images/pagerduty/API_Create_CW_Integration_1.png)

サンプル Body より、

- type を「aws_cloudwatch_inbound_integration」に変更
- name は「CloudWatch」
- サービス ID は先ほど作成したサービスのレスポンスより「PID7R3Z」
- Cloudwatch インテグレーションの vendorID は「PZQ6AUS」（別の「List vendors」から取得）

```json:body
{
  "integration": {
    "type": "aws_cloudwatch_inbound_integration",
    "name": "CloudWatch",
    "service": {
      "id": "PID7R3Z",
      "type": "service"
    },
    "vendor": {
      "type": "vendor",
      "id": "PZQ6AUS"
    }
  }
}
```

送信

![api_create_cw_integration_2](images/pagerduty/API_Create_CW_Integration_2.png)

出来ました。  
下の方に行くとインテグレーションキーもあったので、あとは AWS 側で SNS の設定するだけです。  
以上！

#### NewRelic の連携

CloudWatch とほぼ同じです。  
サンプル Body より、

- type を「generic_events_api_inbound_integration」に変更
- name は「NewRelic」
- サービス ID は先ほど作成したサービスのレスポンスより「PID7R3Z」
- NewRelic インテグレーションの vendorID は「PQFCJER」（別の「List vendors」から取得）

```json:body
 {
 "integration": {
   "type": "generic_events_api_inbound_integration",
   "name": "NewRelic",
   "service": {
     "id": "PID7R3Z",
     "type": "service"
   },
   "vendor": {
     "type": "vendor",
     "id": "PQFCJER"
   }
 }
}
```

送信。

![api_create_nr_integration_1](images/pagerduty/API_Create_NR_Integration_1.png)

成功。

![api_create_nr_integration_2](images/pagerduty/API_Create_NR_Integration_2.png)

確認。
以上！

#### TiDB の連携

TiDB は GUI でも出来なかったため、やりません。

### API でユーザーの招待

「Create a User」API より

```json:body
{
  "user": {
    "type": "user",
    "name": "Kikuchi API Test",
    "email": "<EMAIL>",
    "time_zone": "Asia/Tokyo",
    "color": "green",
    "role": "admin",
    "description": "api test"
  }
}
```

送信。

![api_invite_user_1](images/pagerduty/API_Invite_User_1.png)

成功。

![api_invite_user_2](images/pagerduty/API_Invite_User_2.png)

招待確認。  
以上！

### API でユーザーの権限管理

先ほど作成したユーザーのロールを observer に格下げします。

```json:body
{
  "user": {
    "type": "user",
    "name": "Kikuchi API Test",
    "email": "<EMAIL>",
    "role": "observer"
  }
}
```

送信。

![api_management_role_1](images/pagerduty/API_Management_Role_1.png)

成功。

![api_management_role_2](images/pagerduty/API_Management_Role_2.png)

確認。
以上！

# 結果

## 出来たこと

### CloudWatch からユーザーへ通知

CloudWatch Alarm や EventBridge からユーザーへの通知は出来ました。  
確認済みの通知先は、メール・電話・SMS・Slack。

### NewRelic からユーザーへ通知

NewRelice からユーザーへの通知は出来ました。  
確認済みの通知先は、メール・電話・SMS・Slack。

### メールからアラート

Email Integration からのアラート送信は出来ました。  
確認済みの通知先は、メール・電話・SMS・Slack。

### API でのユーザー作成・更新

API 経由でユーザーの作成と権限変更できました。

### API でのサービス作成

API 経由でサービスの作成ができました。

### API でのインテグレーション連携

API 経由で CloudWatch と NewRelic のインテグレーションを追加＋インテグレーションキーの作成まで出来ました。

## 出来なかったこと

### TiDB のアラート通知

PagerDuty を OrganizationOwner にすれば出来ると思いますが、さすがにそこまでの権限を付けられないので出来ませんでした。  
Billing Admin のアカウントも登録している（<EMAIL>）のですが、それにも届いて居なかったため OrganizationOwner が必要かと思われます。

### 非ユーザーへの通知

ユーザーライセンスを払い出ししていないユーザーへ通知することはできません。  
通知先として選択できるのは、フルユーザーまたはステークホルダーです。  
Slack への通知でインシデントが発生したことはわかるが、詳細を見るのにはライセンスが必要。
