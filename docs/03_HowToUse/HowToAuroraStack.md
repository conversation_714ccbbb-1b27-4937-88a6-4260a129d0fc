# HowToUseAurora

ここでは、Aurora スタックの利用方法を記載する

## 概要

- インスタンスタイプはプロビジョンドタイプと ServerlessV2 の２タイプが用意されている。
- Writer と Reader でそれぞれ別のインスタンスタイプを指定することが可能。

  - ※ Writer: 書き込み用の DB インスタンス
  - ※ Reader: 読み込み専用の DB インスタンス

  ![Auroraクラスター](../images/aurora-stack.dio.svg)

## Aurora 構成図

Aurora 構成図については以下の通り。

![Aurora構成図](../images/Aurora-Detail-Architecture.dio.svg)

- Aurora の DB エンジンは環境パラメーターによって管理される。
  - Postgre SQL または MySQL
- Aurora のメトリクスとイベントを監視しており、異常があった場合は ShareResources で作成した SNS Topic にアラートを通知する。

Logging

![Aurora構成図](../images/aurora-logging.png)

## インスタンスタイプの切替方法

- CDK コード内では Writer と Reader でそれぞれインスタンスタイプを指定する箇所がある。
- プロビジョンドタイプの場合は、`ClusterInstance.provisioned`と記載する。
- ServerlessV2 の場合は、`ClusterInstance.serverlessV2`と記載する。
- この指定方法は、Writer と Reader も変わらないが、Reader は複数台設定することが想定されるため配列構造になっている。

### 変更箇所

```typescript
this.cluster = new rds.DatabaseCluster(this, 'Aurora', {
  ...,
  writer: rds.ClusterInstance.provisioned('instance1', {
    ...,
  }),
  readers: [
    rds.ClusterInstance.provisioned('instance2', {
      ...,
    }),
  ],
  ...,
});
```

## DB エンジンの切替方法

DB エンジンを Postgre SQL か MySQL のどちらを選ぶかによってコメントイン/アウト箇所が変わるため、要件によって以下を参考に変更する。

- 対象パラメータ： `AuroraParam`
- 下記のパラメーターを変更する必要がある。
  - `dbVersion`
  - `clusterParameters`
  - `instanceParameters`

### 変更箇所

**dbVersion**

```typescript
dbVersion: rds.AuroraMysqlEngineVersion.VER_3_04_1,
// dbVersion: rds.AuroraPostgresEngineVersion.VER_15_4,
```

**clusterParameters, instanceParameters**

```typescript
//ParameterGroupforMySQL
clusterParameters: {
   ...
},
instanceParameters: {
   ...
},
// ParameterGroupforPostgreSQL
// clusterParameters: {
//    ...
// },
// instanceParameters: {
//    ...
// },
```
