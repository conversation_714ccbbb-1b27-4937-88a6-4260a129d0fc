# NewRelicのセットアップ手順

## 目次
- [新規サービスのセットアップフロー](#新規サービスのセットアップフロー)
  - [1. Serviceアカウント発行](#1-serviceアカウント発行)
  - [2. Gevanni<PERSON>dmin / Infra グループ権限付与](#2-gevanniadmin--infra-グループ権限付与)
  - [3. Serviceグループ作成](#3-serviceグループ作成)
  - [4. Serviceグループの権限付与](#4-serviceグループの権限付与)
  - [5. Lisenseキー作成](#5-lisenseキー作成)
  - [6. Lisenseキー登録](#6-lisenseキー登録)
  - [7. 責任者ユーザーの作成](#7-責任者ユーザーの作成)
  - [8. 責任者ユーザーに権限付与](#8-責任者ユーザーに権限付与)
  - [9. アカウントの初期設定](#9-アカウントの初期設定)
  - [10. デフォルトアラートの作成](#10-デフォルトアラートの作成)

# 新規サービスのセットアップフロー
## 1. Serviceアカウント発行

左下の自分のユーザー名をクリックし、`Administator`>`Access Management`を選択。  
`Account`>`Create account`からアカウントを作成する。

- アカウント名([命名規則](../02_Detaildesign/New_Relic_Observability.md#%E5%91%BD%E5%90%8D%E8%A6%8F%E5%89%87) より)
  - 形式 : {専有リソースのサービスID}_{連携先AWSアカウントID}
  - 例: example01-stg-ab_0123456789

## 2. GevanniAdmin / Infra グループ権限付与

左下の自分のユーザー名をクリックし、`Administator`>`Access Management`を選択。  
`Groups`>`グループ一覧`の`GevanniAdmin`の欄の3点リーダーから`Manage group access`を選択。  
グループ設定の`Account access`から対象Accountへの接続権限を付与する。

- Service アカウントへの接続権限
  - Role : `All Product Admin`
  - Account : 対象のServiceアカウント

`Infra`グループにも同様の設定を行う

## 3. Serviceグループ作成

左下の自分のユーザー名をクリックし、`Administator`>`Access Management`を選択。  
`Account`>`Create new group`からグループを作成する。

グループは、AdminグループとMemberグループの２種類を作成する。  

- グループ名([命名規則](../02_Detaildesign/New_Relic_Observability.md#%E5%91%BD%E5%90%8D%E8%A6%8F%E5%89%87) より)
  - 形式 : {サービスIDのプレフィックス}-{ランダムな英小文字*3}_{権限}
  - Adminグループの例: example01-abc_admin
  - Memberグループの例: example01-abc_member
  


## 4. Serviceグループの権限付与

左下の自分のユーザー名をクリックし、`Administator`>`Access Management`を選択。  
`Groups`>`グループ一覧`の対象グループの欄の3点リーダーから`Manage group access`を選択。  

### 4-1. Memberグループの権限付与

#### Account access
グループ設定の`Account access`から対象Accountへの接続権限を付与する。  

- Service アカウントへの接続権限
  - Role : `Read Only`
  - Account : アカウント発行で作成したアカウント

- 対象環境の Gevanni アカウントへの接続権限
  - Role : `Spectator` (`Read Only`以下の最低限の閲覧権限)
  - Account : 対象環境の Gevanni共有 アカウント
    - Stg01環境の例 : `Gevanni-stg01`

### 4-2. Adminグループの権限付与

#### Account access

グループ設定の`Account access`から対象Accountへの接続権限を付与する。  

- Service アカウントへの接続権限
  - Role : `All Product Admin`
  - Account : アカウント発行で作成したアカウント

- 対象環境の Gevanni アカウントへの接続権限
  - Role : `Spectator` (`Read Only`以下の最低限の閲覧権限)
  - Account : 対象環境の Gevanni アカウント
    - Stg01環境の例 : `Gevanni-stg01`

#### Group access

グループ設定の`Group access`から Memberグループへの管理権限を付与する。  

- Member アカウントへの管理権限
  - Role : `Group Admin`
  - Account : 対象のMemberグループ

#### Administrative settings

グループ設定の`Administrative settings`から追加の権限を付与する。  

`Authentication domain settings`>`Authentication Domain Add Users`にチェックを付ける。  

## 以降のフローはAWS環境に専有リソースがデプロイされてから行う
## 5. Lisenseキー作成

左下の自分のユーザー名をクリックし、`Administator`>`Api Keys`を選択。  
`Create a key`からキーを作成する。

- API Keyの設定
  - Account : 対象のServiceアカウント
  - Key type : `Ingest - License`
  - Name : 任意 (例:service_key)
  - Notes : 任意

## 6. Lisenseキー登録

GevanniのAWS環境に入り、Secrets Managerを開く。  
対象のSecretを調べる。  
`aws cloudformation describe-stacks --stack-name {環境名}-{専有リソースのサービスID}-Secrets --query "Stacks[0].Outputs[?OutputKey=='NewRelicSecretArn'].OutputValue" --output text`

対象のSecretにLisenseキーを登録する。

- Key : api_key
- Value : 作成したLisenseキーの値

## 7. 責任者ユーザーの作成
※ このフローは、既に責任者ユーザーが作成済みの場合はスキップする。

左下の自分のユーザー名をクリックし、`Administator`>`User Management`を選択。  
`Add user`からユーザーを作成する。

ユーザー情報を入力し、`Create user`で作成。

- ユーザー情報
  - Name : {mynavi.jpのユーザー名}
  - Email : {メールアドレス}
  - Type : 基本`Basic`
  

## 8. 責任者ユーザーに権限付与

左下の自分のユーザー名をクリックし、`Administator`>`User Management`を選択。  
ユーザー一覧から、対象ユーザーをクリック。  
`Access`からAdminグループに追加する。 

## 9. アカウントの初期設定
専有サービスのメトリクスの機能を活用するためには、以下の作業を行う。

### 9-1. クロスアカウントダッシュボードの作成

#### ユーザーキーの作成
左下の自分のユーザー名をクリックし、`Administator`>`Api Keys`を選択。  
`Create a key`からキーを作成する。

- API Keyの設定
  - Account : Gevanni共有アカウント
  - Key type : `User`
  - Name : 任意
  - Notes : 任意

#### NerdGraph API実行
左のサイドメニューから、`Apps`>`NerdGraph API Explorer`を検索。  
`NerdGraph API Explorer`で先ほどのユーザーキーを使い、以下のクエリを実行。  
NerdGraphではクエリを動的生成出来ないため、実行時は以下の文字列を適宜置き換える。  

- `Service_ID` → GevanniのサービスID
- `TiDB_Project_Name` → TiDBのプロジェクト名

<details>

**<summary>クエリ</summary>**

```
# 各プロジェクトのインフラメトリクスダッシュボード作成（アカウント内のすべてのユーザーは閲覧権限をもつ）
mutation CreateDashboard($commonAccountId: Int!, $serviceAccountId: Int!) {
  dashboardCreate(
    accountId: $serviceAccountId
    dashboard: {
      name: "AWS Infrastructure Metrics Dashboard"
      permissions: PUBLIC_READ_ONLY
      pages: [
        {
          name: "Application ELB Metrics"
          widgets: [
            {
              title: "ALB Request Count"
              configuration: {
                line: {
                  nrqlQueries: [
                    {
                      accountId: $commonAccountId
                      query: "FROM Metric SELECT sum(aws.applicationelb.RequestCount) WHERE `tags.Service` = 'Service_ID' TIMESERIES SINCE 30 minutes ago LIMIT MAX"
                    }
                  ]
                }
              }
            },
            {
              title: "ALB New and Rejected Connections"
              configuration: {
                line: {
                  nrqlQueries: [
                    {
                      accountId: $commonAccountId
                      query: "FROM Metric SELECT sum(aws.applicationelb.NewConnectionCount), sum(aws.applicationelb.RejectedConnectionCount) WHERE `tags.Service` = 'Service_ID' TIMESERIES SINCE 30 minutes ago LIMIT MAX"
                    }
                  ]
                }
              }
            },
            {
              title: "ALB TLS Negotiation Errors"
              configuration: {
                line: {
                  nrqlQueries: [
                    {
                      accountId: $commonAccountId
                      query: "FROM Metric SELECT sum(aws.applicationelb.ClientTLSNegotiationErrorCount) WHERE `tags.Service` = 'Service_ID' TIMESERIES SINCE 30 minutes ago LIMIT MAX"
                    }
                  ]
                }
              }
            },
            {
              title: "ALB HTTP Status Codes"
              configuration: {
                line: {
                  nrqlQueries: [
                    {
                      accountId: $commonAccountId
                      query: "FROM Metric SELECT sum(aws.applicationelb.HTTPCode_ELB_5XX_Count), sum(aws.applicationelb.HTTPCode_ELB_4XX_Count), sum(aws.applicationelb.HTTPCode_Target_2XX_Count), sum(aws.applicationelb.HTTPCode_Target_5XX_Count), sum(aws.applicationelb.HTTPCode_Target_4XX_Count) WHERE `tags.Service` = 'Service_ID' TIMESERIES SINCE 30 minutes ago LIMIT MAX"
                    }
                  ]
                }
              }
            },
            {
              title: "ALB Target Connection Errors"
              configuration: {
                line: {
                  nrqlQueries: [
                    {
                      accountId: $commonAccountId
                      query: "FROM Metric SELECT sum(aws.applicationelb.TargetConnectionErrorCount), sum(aws.applicationelb.TargetTLSNegotiationErrorCount) WHERE `tags.Service` = 'Service_ID' TIMESERIES SINCE 30 minutes ago LIMIT MAX"
                    }
                  ]
                }
              }
            },
            {
              title: "ALB Target Response Time"
              configuration: {
                line: {
                  nrqlQueries: [
                    {
                      accountId: $commonAccountId
                      query: "FROM Metric SELECT average(aws.applicationelb.TargetResponseTime) WHERE `tags.Service` = 'Service_ID' TIMESERIES SINCE 30 minutes ago LIMIT MAX"
                    }
                  ]
                }
              }
            },
            {
              title: "ALB Request Count Per Target"
              configuration: {
                line: {
                  nrqlQueries: [
                    {
                      accountId: $commonAccountId
                      query: "FROM Metric SELECT average(aws.applicationelb.RequestCountPerTarget) WHERE `tags.Service` = 'Service_ID' TIMESERIES SINCE 30 minutes ago LIMIT MAX"
                    }
                  ]
                }
              }
            }
          ]
        },
        {
          name: "ECS Metrics"
          widgets: [
            {
              title: "ECS Memory Utilization"
              configuration: {
                line: {
                  nrqlQueries: [
                    {
                      accountId: $commonAccountId
                      query: "FROM Metric SELECT average(aws.ecs.MemoryUtilization.byService) WHERE `tags.Service` = 'Service_ID' TIMESERIES SINCE 30 minutes ago LIMIT MAX"
                    }
                  ]
                }
              }
            },
            {
              title: "ECS CPU Utilization"
              configuration: {
                line: {
                  nrqlQueries: [
                    {
                      accountId: $commonAccountId
                      query: "FROM Metric SELECT average(aws.ecs.CPUUtilization.byService) WHERE `tags.Service` = 'Service_ID' TIMESERIES SINCE 30 minutes ago LIMIT MAX"
                    }
                  ]
                }
              }
            }
          ]
        },
        {
          name: "ECS Container Insights"
          widgets: [
            {
              title: "ECS Desired Task Count"
              configuration: {
                line: {
                  nrqlQueries: [
                    {
                      accountId: $commonAccountId
                      query: "FROM Metric SELECT count(aws.ecs.containerinsights.DesiredTaskCount) WHERE `tags.Service` = 'Service_ID' TIMESERIES SINCE 30 minutes ago LIMIT MAX"
                    }
                  ]
                }
              }
            },
            {
              title: "ECS Running Task Count"
              configuration: {
                line: {
                  nrqlQueries: [
                    {
                      accountId: $commonAccountId
                      query: "FROM Metric SELECT count(aws.ecs.containerinsights.RunningTaskCount) WHERE `tags.Service` = 'Service_ID' TIMESERIES SINCE 30 minutes ago LIMIT MAX"
                    }
                  ]
                }
              }
            },
            {
              title: "ECS Pending Task Count"
              configuration: {
                line: {
                  nrqlQueries: [
                    {
                      accountId: $commonAccountId
                      query: "FROM Metric SELECT count(aws.ecs.containerinsights.PendingTaskCount) WHERE `tags.Service` = 'Service_ID' TIMESERIES SINCE 30 minutes ago LIMIT MAX"
                    }
                  ]
                }
              }
            },
          ]
        },
        {
          name: "TiDB"
          widgets: [
            {
              title: "TiDB Cost Metrics"
              configuration: {
                line: {
                  nrqlQueries: [
                    {
                      accountId: $commonAccountId
                      query: "FROM Metric SELECT max(`aws.tidb.CostMetrics`) WHERE `aws.tidb.Project` = 'TiDB_Project_Name' TIMESERIES SINCE 2 weeks ago LIMIT MAX"
                    }
                  ]
                }
              }
            }
          ]
        }
      ]
    }
  ) {
    errors {
      description
      type
    }
    entityResult {
      guid
    }
  }
}
```
</details>

<details>

**<summary>引数</summary>**

```
{
  "commonAccountId": <NewRelicのGevanni共有アカウントのID>,
  "serviceAccountId": <NewRelicのServiceアカウントのID>
}
```

</details>

##### ダッシュボード上に作成されるメトリクス
|Service|Metrics|
|-|-|
|Application ELB Metrics|ALB Request Count|
|Application ELB Metrics|ALB New and Rejected Connections|
|Application ELB Metrics|ALB TLS Negotiation Errors|
|Application ELB Metrics|ALB HTTP Status Codes|
|Application ELB Metrics|ALB Target Connection Errors|
|Application ELB Metrics|ALB Target Response Time|
|Application ELB Metrics|ALB Request Count Per Target|
|ECS Metrics|ECS Memory Utilization|
|ECS Metrics|ECS CPU Utilization|
|ECS Container Insights|ECS Desired Task Count|
|ECS Container Insights|ECS Running Task Count|
|ECS Container Insights|ECS Pending Task Count|
|TiDB|TiDB Cost Metrics|

<details>

<summary>sample</summary>

![NewRelic_Dashboard_ALB.png](../images/NewRelic_Dashboard_ALB.png)
![NewRelic_Dashboard_ECS.png](../images/NewRelic_Dashboard_ECS.png)
![NewRelic_Dashboard_ECS_Container_Insights.png](../images/NewRelic_Dashboard_ECS_Container_Insights.png)
![NewRelic_Dashboard_TiDB.png](../images/NewRelic_Dashboard_TiDB.png)

</details>

### 9-2. クロスアカウントアラートの有効化

メトリクスはGevanniの共有アカウントにあるため、Gevanniの共有アカウントへの書き込み権限がないユーザーがアラートを設定するには、`クロスアカウントアラート`を有効化する必要がある。

左のサイドメニューから、`Alerts`>`General`を選択。  
`Cross-account alerts`>`Let other accounts query data from this account`を有効化する。  


## 10. デフォルトアラートの作成

デフォルトのアラートを作成する。  
詳細はWorkFlowリポジトリにある`docs/JPN/NewRelic_Observations.md`を参照。  

