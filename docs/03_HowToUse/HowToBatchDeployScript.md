# バッチデプロイスクリプト詳細設計

## バッチ定義ファイル概要

### バッチ呼び出し定義ファイルの構造

以下に、サンプルと定義を示す。

例:

```json
{
  "version": "1",
  "triggers": {
    "dbConnectionSchedule": {
      "type": "cron",
      "cron": {},
      "state": "DISABLED",
      "inputs": {
        "task1": {
          "commands": [
            {
              "containerName": "batch",
              "passedCommand": ["app/batch1.js"]
            },
            {
              "containerName": "sshClient",
              "passedCommand": ["app/batch2.js"]
            }
          ]
        },
        "task2": {
          "commands": [
            {
              "containerName": "batch",
              "passedCommand": ["app/batch2.js"]
            }
          ]
        }
      }
    },
    "backlogWebhookEndpoint": {
      "type": "api",
      "apiPath": "/sample"
    }
  }
}
```

|      項目       |         データ型          | 必須 |                       説明                       |                                                   備考                                                    |
| :-------------: | :-----------------------: | :--: | :----------------------------------------------: | :-------------------------------------------------------------------------------------------------------: |
|    `version`    |         `string`          |  ◎   |               スキーマのバージョン               |                       新しいパラメータを追加する場合などで処理の分岐に使用する予定                        |
|   `triggers`    |         `object`          |  ◎   |                  トリガーの定義                  |                                                                                                           |
| `<トリガー名>`  |         `string`          |  ◎   |                  トリガーの名前                  |                                                                                                           |
|     `type`      |       `cron`, `api`       |  ◎   |                 トリガーのタイプ                 |                                   `cron` か `api` のどちらかを指定する                                    |
|     `cron`      |         `object`          |  ◯   |      EventBridge Scheduler の起動時刻の定義      |                                          cron タイプのみ指定可能                                          |
|    `minute`     |         `string`          |      |      EventBridge Scheduler の起動時刻 (分)       |                                          cron タイプのみ指定可能                                          |
|     `hour`      |         `object`          |      |     EventBridge Scheduler の起動時刻 (時間)      |                                          cron タイプのみ指定可能                                          |
|     `date`      |         `object`          |      |     EventBridge Scheduler の起動時刻 (日付)      |                                          cron タイプのみ指定可能                                          |
|     `month`     |         `object`          |      |      EventBridge Scheduler の起動時刻 (月)       |                                          cron タイプのみ指定可能                                          |
|   `dayOfWeek`   |         `object`          |      |     EventBridge Scheduler の起動時刻 (曜日)      |                                          cron タイプのみ指定可能                                          |
|     `year`      |         `object`          |      |      EventBridge Scheduler の起動時刻 (年)       |                                          cron タイプのみ指定可能                                          |
|    `apiPath`    |         `string`          |  ◯   |                 API Gateway パス                 |                       api タイプのみ指定可能。設計が未完了のため、後ほど追記予定。                        |
|     `state`     | `"ENABLED" or "DISABLED"` |  ◯   |           EventBridge Scheduler の設定           |                                        `cron` を指定した場合は必須                                        |
|    `inputs`     |         `object`          |  ◯   |        トリガーがステートマシンに渡す入力        | 後述のステートマシンの定義で、`ContainerOverrides` オプションを指定した場合以下のすべての項目が必須となる |
|  `<タスク名>`   |         `object`          |  ◯   | ステートマシンに含まれるタスクにわたす入力の内容 |                                   `inputs` を指定した場合は必ず指定する                                   |
|   `commands`    |          `list`           |  ◯   |               タスクに渡すコマンド               |                                                                                                           |
| `containerName` |         `string`          |  ◯   |       コマンドを上書きしたいコンテナの名前       |                                   commands を記載した場合は必ず指定する                                   |
| `passedCommand` |          `list`           |  ◯   |                上書きするコマンド                |                                   commands を記載した場合は必ず指定する                                   |

※ ◎ は、必須項目であることを示し、◯ は条件付きで必須となる項目を示す。  
※ `<>` で囲まれている値は任意の値が指定できることを示す。

### バッチ呼び出し定義ファイル命名規則

ファイル名は、`<バッチ名>.trigger.json` とする。

例:
`batch1.trigger.json`

### タスク定義ファイルの構造

バッチ側のタスク定義 JSON ファイルはアプリ側と同様に ECS タスク定義のテンプレートファイルを使用する。  
バッチのタスク定義では、定義ファイルのレンダリングする際、jinja2 を使用するため、プレースホルダーの記述方法が異なる。  
使用できる値は、[ECS タスク定義パラメータ](https://docs.aws.amazon.com/ja_jp/AmazonECS/latest/developerguide/task_definition_parameters.html) に準ずる。

例:

```json
{
  "containerDefinitions": [
    {
      "name": "BatchContainer",
      "essential": true,
      "image": "{{ image0 }}",
      "logConfiguration": {
        "logDriver": "awsfirelens",
        "options": {
          "Name": "firehose",
          "region": "{{ AWS_REGION }}",
          "delivery_stream": "{{ STREAM_NAME }}",
          "retry_limit": "2"
        }
      },
      "memoryReservation": 100,
      "dependsOn": [
        {
          "containerName": "logRouter",
          "condition": "HEALTHY"
        }
      ]
    },
    {
      "name": "logRouter",
      "firelensConfiguration": {
        "type": "fluentbit"
      },
      "essential": true,
      "image": "{{ FIRELENS_IMAGE_URI }}",
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "{{ LOG_GROUP_FIRELENS }}",
          "awslogs-region": "{{ AWS_REGION }}",
          "awslogs-create-group": "true",
          "awslogs-stream-prefix": "{{ family }}-firelens"
        }
      },
      "memoryReservation": 50,
      "healthCheck": {
        "command": ["CMD-SHELL", "echo '{\"health\": \"check\"}' | nc 127.0.0.1 8877 || exit 1"]
      }
    }
  ],
  "cpu": "{{ BatchTask_cpu }}",
  "executionRoleArn": "{{ EXECUTION_ROLE_ARN }}",
  "taskRoleArn": "{{ BatchTask_taskRoleArn }}",
  "family": "{{ family }}",
  "memory": "{{ BatchTask_memory }}",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"]
}
```

タスク定義ファイルでは以下の部分は変更可能で、それ以外はこの構造に示した値をそのまま利用することを想定している。

- コンテナ定義
  - コンテナ名
  - essential
  - image のプレースホルダーの値
  - memoryReservation
  - 上記の例に記載していないタスク定義のパラメータ (mount など)
  - depensOn
    - logRouter との依存関係はすべてのコンテナで作成する必要があるためこのままとし、それ以外のコンテナとの依存関係は任意で作成して良い
- cpu, メモリ、タスクロール ARN などタスク名を使用するプレースホルダーのタスク名部分

### タスク定義ファイル命名規則

ファイルの名前を `<英数字・ハイフン・アンダースコア>ecs-task-def.json` とする。  
※ [タスク定義ファイルの編集で候補が出てくる](https://docs.aws.amazon.com/ja_jp/toolkit-for-vscode/latest/userguide/ecs-definition-files.html) ようになるため。
例: `batch1-task1-ecs-task-def.json`

### ステートマシン定義ファイルの構造

ステートマシンの定義ファイルは、
[ASL (Amazon States Languag)](https://docs.aws.amazon.com/ja_jp/step-functions/latest/dg/concepts-amazon-states-language.html)
で記述し、タスク定義ファイルと同様の方法で、プレースホルダーを記述する。  
使用できる値は [Amazon States Language の言語仕様](https://states-language.net/spec.html) に準ずる。

例:

```json
{
  "StartAt": "{{ BatchTask }}",
  "States": {
    "{{ BatchTask }}": {
      "Type": "Task",
      "Resource": "arn:aws:states:::ecs:runTask.sync",
      "Parameters": {
        "Cluster": "{{ CLUSTER_NAME }}",
        "LaunchType": "FARGATE",
        "EnableExecuteCommand": true,
        "NetworkConfiguration": {
          "AwsvpcConfiguration": {
            "AssignPublicIp": "ENABLED",
            "SecurityGroups": ["{{ SECURITY_GROUP_ID }}"],
            "Subnets": ["{{ SUBNET_ID_0 }}", "{{ SUBNET_ID_1 }}", "{{ SUBNET_ID_2 }}"]
          }
        },
        "PlatformVersion": "1.4.0",
        "TaskDefinition": "{{ BatchTask_task_def }}",
        "Overrides": {}
      },
      "End": true
    }
  }
}
```

ステートマシン定義ファイルでは以下の部分は変更可能で、それ以外はこの構造に示した値をそのまま利用することを想定している。

- ステートマシンの定義
  - Type が Task のステートでは、Resource は `arn:aws:states:::ecs:runTask.sync` および `arn:aws:states:::ecs:runTask.sync` のみ許可
- RunTask のパラメータ
  - EnableExecuteCommand
  - Overrides
    - 現在は ContainerOverrides オプションのみサポート
      - この値を指定した場合以下の書式とする
        - `ContainerOverrides.$: "$.containerOverrides.[{{ <タスク名>_commands }}]"`
      - これを指定した場合、すべてのトリガーが inputs セクションを持つ必要がある
        - ステートマシンの定義が上書きコマンドを要求するようなものとなっているため、このステートマシンを実行するときには必ず上書きコマンドが必要となる
  - その他 fargate タイプでサポートされている、以下のドキュメントで指定可能なパラメータ
    - <https://docs.aws.amazon.com/ja_jp/step-functions/latest/dg/connect-ecs.html>

### ステートマシン定義ファイル命名規則

ファイルの拡張子を、`asl.json` とする。  
例: `batch1.asl.json`

## バッチ環境ファイル概要

### バッチ環境ファイルの構造

以下にバッチ環境ファイルのサンプルと構造を示す。

例:

```toml
[env]
name = "Dev01"
service_prefix = "sample01-dev01-ab"


[tasks.task1.definition]
name = "batch1-task1-ecs-task-def.json"
spec = { cpu = 512, memory = 1024 }

# <タスク名>.<コンテナ名>
[tasks.task1.build.batch]
context = "app/batch/echo"
image_placeholder = "image0"

[tasks.task1.build.sshClient]
context = "app/batch/echo"
image_placeholder = "image1"

[tasks.task2.definition]
name = "batch1-task2-ecs-task-def.json"
spec = { cpu = 512, memory = 1024 }

[tasks.task2.build.batch]
context = "app/batch/echo"
image_placeholder = "image0"
```

|        項目         | データ型 | 必須 |                         説明                         |                         備考                         |
| :-----------------: | :------: | :--: | :--------------------------------------------------: | :--------------------------------------------------: |
|        `env`        | `object` |  ◎   |            Gevanni の環境名とサービス ID             |                                                      |
|       `name`        | `string` |  ◎   |                   gevanni の環境名                   |                                                      |
|  `service_prefix`   | `string` |  ◎   |                 gevanni サービス ID                  |                                                      |
|       `tasks`       | `object` |  ◎   |             バッチに含まれるタスクの定義             |                                                      |
|    `<タスク名>`     | `object` |  ◎   |                     タスクの定義                     |                                                      |
|    `definition`     | `object` |  ◎   |   タスクで使用するタスク定義に関するパラメータ定義   |                                                      |
|       `name`        | `string` |  ◎   |                タスク定義のファイル名                |                                                      |
|       `spec`        | `object` |  ◎   |                 タスク定義のスペック                 |                                                      |
|        `cpu`        |   int    |  ◎   |                     タスクの CPU                     |                                                      |
|      `memory`       |   int    |  ◎   |                    タスクのメモリ                    |                                                      |
|       `build`       | `object` |  ◎   |   タスク定義に含まれるコンテナのビルドに関する定義   |                                                      |
|   `<コンテナ名>`    | `object` |  ◎   |          コンテナのビルドに関する定義の詳細          |                                                      |
|      `context`      | `string` |  ◎   |             コンテナのビルドコンテキスト             |                                                      |
|    `dockerfile`     | `string` |      |    コンテナのビルドの際使用する Dockerfile のパス    | 指定しない場合はコンテキスト直下の Dockerfile を使用 |
| `image_placeholder` | `string` |  ◎   | タスク定義ファイルに記載しているプレースホルダーの値 |                                                      |

※ ◎ は、必須項目であることを示し、◯ は条件付きで必須となる項目を示す。  
※ `<>` で囲まれている値は任意の値が指定できることを示す。

### バッチ環境ファイル命名規則

{dev|stg|prod}.toml とする。
例: dev.toml

### プレースホルダーのルール

プレースホルダーの記述方法は jinja2 の仕様に従う。  
基本的に環境変数は、変数名を大文字で記述する。

例: `"{{ CLUSTER_NAME }}"`

アプリチームに任意に入力してもらう値は、スクリプト中では変数や辞書のキーとして扱われるため、小文字とし、英数字とハイフンのみ使用可能とする。

例: `"{{ task_definition_name }}"`

### 想定される定義ファイルの配置パターン

```plain text
app_repositry_root/
└── infra/
    └── batch/
        ├── batch1/
        │   ├── batch1.asl.json
        │   ├── batch1-task1-ecs-task-def.json
        │   ├── batch1-task2-ecs-task-def.json
        │   └── batch1.trigger.json
        └── batch2/
            ├── batch2.asl.json
            ├── batch2-task1-ecs-task-def.json
            └── batch2.trigger.json
```

## デプロイパイプライン概要

以下にバッチデプロイで使用するデプロイパイプラインの概要を記載する。

### ビルドプロジェクト詳細

デプロイパイプラインのデプロイステージで使用する CodeBuild の　 BuildSpec について記す。  
前提として、バッチの定義ファイル、デプロイスクリプト、`requirements.txt` はソースステージの S3 バケットに zip ファイルとして配置され、
自動的に作業ディレクトリにファイルが展開される。(CodeBuild が行うため、BuildSpec 内に解凍処理が含まれているわけではない)

- 作業ディレクトリ内のファイルを一覧
- python の仮想環境を作成
  - 仮想環境を作成する理由は、今回使用する CodeBuild のベースイメージにインストールされているライブラリと、デプロイスクリプト用のライブラリが競合を起こすため
- デプロイスクリプト実行

#### ビルドプロジェクト IAM 権限詳細

ビルドプロジェクトには、以下のアクションを許可する IAM ロールを付与する。  
操作できるリソースを特定することが難しいため、Resource 部分はすべて許可とする。

- `ecs:RegisterTaskDefinition`
- `ecs:ListTagsForResource`
- `ecs:TagResource`
- `states:DescribeStateMachine`
- `states:UpdateStateMachine`
- `states:CreateStateMachine`
- `states:ListTagsForResource`
- `states:TagResource`
- `scheduler:GetSchedule`
- `scheduler:UpdateSchedule`
- `scheduler:CreateScheduleGroup`
- `scheduler:CreateSchedule`
- `scheduler:TagResource`
- `sts:AssumeRole`
- `ssm:GetParameter`
- `iam:PassRole`

### デプロイスクリプト詳細

スクリプトは、インフラリポジトリの GitHub Actions にて、共有バケットに配置され、アプリチームの変更に従って、アプリリポジトリ側の GitHub Actions から CodeBuild で利用される。  
スクリプトは、`scripts/deploy_batch/main.py` に記載されており、CodeBuild が実行される際、各種定義ファイルと一緒に CodeBuild 実行環境の作業ディレクトリのルートに展開される。  
以下にデプロイスクリプトの処理の概略を示す。

- バッチ呼び出し部分定義ファイルとコンテナと ECR の対応表ファイルを読み込む
- ディレクトリ内の、タスク定義ファイルを読み込み、コンテナと ECR の対応情報を利用して、タスク定義を作成

  - 既に該当のタスク定義が作成されていた場合は、新しいリビジョンを作成する
  - 登録したタスク定義に以下のタグを付与する

    ```json
    {
      // タグ名: 値
      "SerivceName": "<Gevanni サービス名>",
      "BatchName": "<Batch 名>"
    }
    ```

- ディレクトリ内のステートマシン定義ファイルを読み込み、タスク定義の ARN を利用してステートマシンを作成
  - 既に該当のステートマシンがある場合は、更新 API を呼び出し、ステートマシンを更新する
- バッチ呼び出し部分定義ファイルの情報から、ファイルに記載分のスケジュール、API を作成
- ステートマシンに以下のタグを付与

  ```json
  {
    // タグ名: 値
    "SerivceName": "<Gevanni サービス名>",
    "BatchName": "<Batch 名>",
    "TriggerType": "CRON or API"
  }
  ```

このスクリプトでは、以下の値を環境変数から取得する。  
なお、環境変数は CodeBuild に設定されており、この値は CDK でビルドプロジェクトを作成する際割り当てる。

|               キー                |                            値の概要                             |
| :-------------------------------: | :-------------------------------------------------------------: |
|           `AWS_REGION`            |                      使用するリージョン名                       |
|          `CLUSTER_NAME`           |          ECS クラスタ名 (アプリ側で作成したものを使用)          |
|      `GEVANNI_SERVICE_NAME`       |                gevanni 内で使用されるサービス名                 |
|       `EXECUTION_ROLE_ARN`        |                      ECS の実行ロール ARN                       |
|           `SUBNET_ID_0`           |       ECS タスクが配置されるサブネットの ID (1 つ目の AZ)       |
|           `SUBNET_ID_1`           |       ECS タスクが配置されるサブネットの ID (2 つ目の AZ)       |
|           `SUBNET_ID_2`           |       ECS タスクが配置されるサブネットの ID (3 つ目の AZ)       |
|        `SECURITY_GROUP_ID`        |          ECS タスクが使用するセキュリティグループの ID          |
|           `BATCH_NAME`            |                バッチ名 (申請フォーム経由で取得)                |
|     `STATE_MACHINE_ROLE_ARN`      |              ステートマシンで使用するロールの ARN               |
|   `STATE_MACHINE_EXECUTOR_ARN`    |             ステートマシンを実行する Lambda の ARN              |
| `STATE_MACHINE_EXECUTOR_ROLE_ARN` | 上記 Lambda を実行するための EventBridge Scheduler のロール ARN |
|       `FIRELENS_IMAGE_URI`        |       NewRelic にログを転送する fluentbit のイメージ URI        |
|       `LOG_GROUP_FIRELENS`        |      firelens のログを記録する CloudWatch のロググループ名      |
|           `STREAM_NAME`           |  fluentbit がログを転送するときに使用する Data Firehose の名前  |
