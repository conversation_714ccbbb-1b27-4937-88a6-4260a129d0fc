# セキュリティ設計
## 基本方針

- 不正侵入や漏洩など、要件定義で規定した各リスクついて具体的な対策を本設計にて記載する。
- アプリケーション実装においては、マイナビ内に「Webアプリケーションセキュリティ対策チェックシート」が用意されているため準拠した実装を行う。
- 利用環境としてパブリッククラウドであるAWSを使用するため、環境管理部分であるAWSアカウント環境への攻撃対策も行う。

## 不正侵入・不正使用対策

### AWS アカウントへの対策

- AWS アカウントは インフラ/アプリ チームの管轄毎に分け、さらに dev, stg, prod 等の環境毎に分けることで、リソースやアクセス権限の管理を明確にし最小権限の原則を徹底する。
- アプリチームはマネジメントコンソールの操作を禁止とし、管轄リソース (担当している内製案件でデプロイしたリソース) に対する最小権限が付与された IAM ロールにスイッチロールして操作を実施する。
  - マネジメントコンソールの操作を禁止とする背景は下記の通り。
    - GUI を介した不正操作や誤操作の設定変更のリスクを軽減する。
    - IAM ロールによる最小権限の適用により、ユーザーが必要以上の権限を持つことを防ぎ、万が一アカウントが侵害された場合でも被害範囲を限定する。
    - アプリチームにはベンダ社員も含まれるため、閲覧権限を厳密に制御する。
      - リスト権限が必要となるリソースが多く、リスト権限はサービス単位で対象のリソースを制御できない。
      - そのため、リスト権限を許可してしまうと、対象メンバーの担当外のサービスの情報も閲覧できてしまう。
- マイナビ社員とベンダ社員ではログイン方法を分けている。
  - マイナビ社員は IAM Identity Center (AWS SSO) を利用したログインで、ベンダ社員は IAM ユーザへのログインとしている。
  - マイナビ社員のログイン方法は以下の通り。
    - Identity Center によって払い出されてた `AssumeRoleOnlyAccess` に Assume Role し、そこから担当プロジェクトの管轄リソースへの最小限の権限が付与されたロールへスイッチして作業を行う。
    - ログインイメージは以下の通り。  
      ![](/docs/images/sso-login-local-architecture.dio.svg)
    - 詳細手順は下記を参照。
      - [csys-infra-gevanni-workflow-sample/docs/JPN/HowToSwitchRoleAndConfigLocalForMynavi.md](https://github.com/mynavi-group/csys-infra-gevanni-workflow-sample/blob/main/docs/JPN/HowToSwitchRoleAndConfigLocalForMynavi.md)
  - ベンダユーザーのログイン方法
    - 踏み台用 AWS アカウントに作成された IAM ユーザーにログインし、そこから担当プロジェクトの管轄リソースへの直接最小限の権限が付与されたロールへスイッチして作業を行う。
    - ログインイメージは以下の通り。  
      ![](/docs/images/switch-iam-role-local-architecture.dio.svg)
    - 詳細手順は下記を参照。
      - [csys-infra-gevanni-workflow-sample/docs/JPN/HowToSwitchRoleAndConfigLocal.md](https://github.com/mynavi-group/csys-infra-gevanni-workflow-sample/blob/main/docs/JPN/HowToSwitchRoleAndConfigLocal.md)

### アプリケーションおよびサービス稼働環境への対策

- アプリケーションおよびサービス稼働環境への不正侵入および脆弱性対策として、サービス間のアクセス制限、VPC サブネットの切り分け、AWS WAF (Web ACL) の適応を行う。
- サービス間のアクセス制限
  - Gevanni では様々な内製案件のサービスがデプロイされる集約基盤なため、デプロイされるサービス間のアクセスを制限する。
    - ※ ここで説明するサービスとは、「Gevanni 内の専有リソースを作成する単位としてのサービス」のこと。
  - アクセス経路の制限として、外部からのコンテナへのアクセスは原則サービス毎の CloudFront 経由とする。
    - 送信元がサービス毎の CloudFront から通信のみを許可するルールを設定した WAF (Web ACL) を ALB にアタッチすることで制限を行う。
      - CloudFront からのリクエストに含まれる `x-pre-shared-key` ヘッダの値を WAF に登録し、値が一致したリクエストのみ許可する。
  - 異なるサービスの ECS サービス間の通信制限は、Cloud Map の名前空間では制御せず、セキュリティグループのみで制御する。
    - 案件によってはバックエンドコンテナに VPC 外からの接続が必要なケースがあり、その対応としてスタックを分割して別サービスのフロントエンドコンテナとしてデプロイをしている。
    - 別スタックのリソースとの名前解決が必要となり Cloud Map の名前空間をスタック毎の専有リソースではなく、共有リソースとして管理とし、名前空間を同一環境内 (例：stg01, prod01...) の全てのサービスで同一としている。
    - そのため、異なるサービスの ECS サービス同士も名前解決可能となっているが、セキュリティグループによってサービス間の通信をデフォルトで禁止とすることで不正アクセスを防止する。
      - 別サービスとの通信が必要となた場合、デプロイ時のオプションとして別サービスのフロントエンドからのアクセスを許可する。
      - 接続元サービスと接続先サービスはスタックが異なるので、セキュリティグループ ID は SSM パラメーターストアで連携させる。  
        ![](/docs/images/security-architecture.dio.svg)
- VPC サブネットの切り分け
  - VPC サブネットは以下の構成とし、ネットワークレベルで不要なアクセスを防止する。
    - Public: インターネットからアクセス可能なサブネットのため、ALB、NAT Gateway のみの配置とする。
    - Private: インターネットへの外部通信が可能なサブネット。コンテナイメージの取得や外部 API の実行が必要な ECS コンテナを配置する。
    - Protected: インターネットへの通信が不可能なサブネット。RDS / ElastiCache / EFS といったデータ格納を行うリソースを配置する。
- AWS WAF (Web ACL) の適応
  - WAF は専有リソースの CloudFront と ALB にアタッチされる。管理管轄は以下の通り。
    - CloudFront :
      - CloudFront アタッチする WAF についてはアプリアカウント管理となる。理由は下記の通り。
        - 使用するマネージドルールやどのルールを除外するかがアプリケーションごとに大きく異なるため。
        - 誤検知があった場合、対象ルールの除外をアプリチームだけで迅速に行えるようにするため。
    - ALB :
      - ALB にアタッチする WAF についてはインフラ管轄となる。
  - 適応する WAF ルールの詳細は下記の通り。

**CloudFront に適応する WAF ルール**

| ルール名 | タイプ (独自ルール \| マネージドルール) | 内容 | 選定理由 |
| --- | --- | --- | --- |
| CSIRTBlockSpecificIPs | 独自ルール (Mynavi-CSIRT 管理) | 他のマイナビシステムに攻撃を仕掛けてきた IP アドレスの拒否 | Mynavi-CSIRT が更新した IP 制限リストを自動で反映させるため。 |
| CSIRTManagerRules | 独自ルール (Mynavi-CSIRT 管理) | 重大な脆弱性に対する暫定的な対応。 | Mynavi-CSIRT が更新したマネージドルールを自動で反映させるため。 |
| IPset | 独自ルール | IP アドレス制限 | マイナビ社内または協力会社の人間しかアクセスできないようにするため。 |
| BasicAuth | 独自ルール | ベーシック認証 | 社内ユーザの誤操作・誤アクセス防止を目的としている。 |
| [AWSManagedRulesCommonRuleSet](https://docs.aws.amazon.com/ja_jp/waf/latest/developerguide/aws-managed-rule-groups-baseline.html#aws-managed-rule-groups-baseline-crs) | マネージドルール | Webアプリケーションに一般的に適用されるルールが含まれている。OWASP Top 10 などの OWASP 出版物に記載されている高リスクで一般的に発生する脆弱性の一部を含む、広範な脆弱性の悪用に対する保護を提供する。 | [AWS公式ドキュメント](https://docs.aws.amazon.com/ja_jp/waf/latest/developerguide/aws-managed-rule-groups-baseline.html#aws-managed-rule-groups-baseline-crs)ですべてのAWS WAFユースケースでの利用が推奨されているため |
| [AWSManagedRulesKnownBadInputsRuleSet](https://docs.aws.amazon.com/ja_jp/waf/latest/developerguide/aws-managed-rule-groups-baseline.html#aws-managed-rule-groups-baseline-known-bad-inputs) | マネージドルール | 脆弱性の悪用や発見に関連する既知のリクエストパターンをブロックするルールが含まれている。これにより、悪意のある行為者が脆弱なアプリケーションを発見するリスクを低減することが可能 | ユーザーの個人情報や機密情報などの重要なデータを取り扱うため、悪意のあるユーザからの不正アクセスを防止する。|
| [AWSManagedRulesAmazonIpReputationList](https://docs.aws.amazon.com/ja_jp/waf/latest/developerguide/aws-managed-rule-groups-ip-rep.html#aws-managed-rule-groups-ip-rep-amazon) | マネージドルール | Amazon内部の脅威インテリジェンスに基づくルールが含まれている。これらのIPアドレスをブロックすることで、ボットを軽減し、悪意のあるアクターが脆弱なアプリケーションを発見するリスクを低減することが可能 | 不特定多数のユーザアクセスが想定されるため、事前に悪意のある IP アドレスをブロックする。|
| [AWSManagedRulesLinuxRuleSet](https://docs.aws.amazon.com/ja_jp/waf/latest/developerguide/aws-managed-rule-groups-use-case.html#aws-managed-rule-groups-use-case-linux-os) | マネージドルール | Linux 固有の脆弱性の悪用に関連するリクエストパターンをブロックするルールが含まれている。これにより、攻撃者がアクセスできないはずのファイルの内容を公開したり、コードを実行したりする攻撃を防ぐことが可能 | ECS 上で実行されるコンテナは Linux ベースで動作するため、悪意のあるユーザからの不正アクセスを防止する。|
| [AWSManagedRulesSQLiRuleSet](https://docs.aws.amazon.com/ja_jp/waf/latest/developerguide/aws-managed-rule-groups-use-case.html#aws-managed-rule-groups-use-case-sql-db) | マネージドルール | SQLインジェクション攻撃など、SQLデータベースの悪用に関連するリクエストパターンをブロックするルールが含まれている。これにより、不正なクエリのリモートインジェクションを防ぐことが可能 | Aurora MySQL（リレーショナルデータベース）を使用するため、悪意のあるユーザからの不正アクセスを防止する。|

- ルール名のプレフィックス `CSIRT` が付いた `CSIRTxxx` ルールは `Mynavi-CSIRT` が各 AWS アカウントへ配布する AWS WAF のルールや拒否 IP リストである。
  - `CSIRTxxx` ルールはマイナビグループの Organization に含まれる AWS アカウントに自動配布される。
  - `CSIRTxxx` ルールを AWS WAF に適応させ、他のマイナビシステムに攻撃を仕掛けてきた IP アドレスの拒否と、重大な脆弱性に対する暫定的な対応を実施する。
- `IPSet` と `BasicAuth` ルールを適応して、IP 制限とカスタムヘッダフィルタリングのルールを追加している。
  - IP アドレスはデフォルトではマイナビの社内プロキシを指しており、マイナビ社内の人間しかアクセスできないようになっている。  
    (※ dev, stg, prod 全ての環境が対象。prod 環境については、リリース時に IP 制限を外す対応が必要となる。)
  - IP 制限に加えて社内ユーザの誤操作・誤アクセス防止を目的にベーシック認証も併せて適応する。

**ALB に適応される WAF ルール**

| ルール名 | タイプ (独自ルール \| マネージドルール) | 内容 | 選定理由 |
| --- | --- | --- | --- |
| IPSet | 独自ルール | IP アドレス制限 | マイナビ社内または協力会社の人間しかアクセスできないようにするため。 |
| preSharedKey | 独自ルール | カスタムヘッダフィルタリング | CloudFront からのアクセスしか許可しないようにするため。 |

- ALB にアタッチしている WAF は、許可された IP アドレスかつ、CloudFront からのアクセスのみ許可するようにルールを適応している。

## 監査証跡の取得

- AWSリソースに対する操作履歴は、CloudTrailを利用して収集し、収集したログは暗号化する。なお、ログはデフォルトでSSE-S3によって暗号化される。
- また、ログは最低5年間保存し、うち直近1年分は即時取り出しが可能な状態にする。
- 個人情報が含まれるログの場合は、復号した者や時間を追跡できるようにするため、ログを格納するS3バケットをSSE-KMSで暗号化する。
- マイナビのWebアプリケーションセキュリティ対策チェックシートに記載されている、アプリケーションのログやデータなど、システム内へのアクセスに関わる以下のログを取得する。
  - アクセスログ
    - 記録する送信元IPアドレスは、（プロキシなどの中継ノードではなく）実際の送信元ノードのものにする。
  - データベースの監査ログ
  - セキュリティ関連のシステムログ
  - ネットワークフローローグ
  - DNSクエリログ
    - Route53のクエリログを収集する。 
  - メールに関するログ
  - アプリケーションログ
    - 認証および機密情報に対するアクセスを、アプリケーションログとして記録する。
    - 監査の観点以外など、エラー時の調査用にアプリケーション動作に関するログを記録する。
  - システムおよびプラットフォームの操作ログ
    - システム管理者（開発者、運用者）によるシステムの操作や破壊的な変更、機密情報へのアクセスなどといった特権的な操作履歴を追跡できるようにする。
- 具体的なログファイルの詳細は、運用設計にて記載する。

## 脆弱性検知の方針

- マイナビの社内ルールとして、サーバー (コンテナ) 脆弱性管理ソリューション「FutureVuls」の導入が全システムで必須となっている。
  - 詳細は下記を参照。
  - [sys_eng_dev_standards/3.設定ファイル/Github/workflows/README.md](https://github.com/mynavi-group/sys_eng_dev_standards/blob/main/3.%E8%A8%AD%E5%AE%9A%E3%83%95%E3%82%A1%E3%82%A4%E3%83%AB/Github/workflows/README.md#futurevuls-trivy%E3%82%B3%E3%83%B3%E3%83%86%E3%83%8A%E3%82%A4%E3%83%A1%E3%83%BC%E3%82%B8%E3%82%B9%E3%82%AD%E3%83%A3%E3%83%B3%E9%80%A3%E6%90%BA%E7%94%A8%E3%83%AF%E3%83%BC%E3%82%AF%E3%83%95%E3%83%AD%E3%83%BC)
- コンテナ構成の場合、CI/CD の中で脆弱性スキャナである「Trivy」でイメージスキャンを行った結果を FutureVuls にアップロードする必要があるため、Gevanni では `build.py` に FutureVuls を導入する。
  - ※ Gevanni ではコンテナ ビルド/デプロイ を build.py で実装しているため。
- コンテナイメージをビルドする度に脆弱性スキャンを行う。
- Gevanni 集約基盤に ECS コンテナをデプロイする内製案件毎に事前準備が必要となる。詳細は下記 README.md を参照すること。
  - [csys-infra-gevanni-workflow-sample/.github/workflows/README_ja.md](https://github.com/mynavi-group/csys-infra-gevanni-workflow-sample/blob/main/.github/workflows/README_ja.md)
