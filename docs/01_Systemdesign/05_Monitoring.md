# 監視設計

## 基本方針

- Gevanni 上で提供している各サービスがユーザーに提供できているか（＝各サービスのシステムを使用したビジネスが継続できているか）を把握し、提供できていない場合や提供に影響があった場合にすぐに復旧作業を実施できるよう、システム監視を行う。
- Gevanni の共有リソースと各サービスの専有リソースともに、システム監視を JIG-SAW 社と連携して行う。対象アラートや一時対応、JIG-SAW 社からの通知フローなどは各アプリチームごとに設定してもらうため、本設計書では共通箇所に関して記載していく。
- Gevanni では監視対象の各 AWS リソースや、TiDB のログ収集とメトリクス監視に NewRelic を利用して集約し JIG-SAW を通して通知を行う。
  - JIG-SAW からの通知のフローは以下の通り。  
    ![](/docs/images/jig-saw-monitoring-flow-architecture.dio.svg)
  - NewRelic の詳細は [04_Detaildesign_New_Relic_Observability](/docs/02_%20Detaildesign/04_Detaildesign_New_Relic_Observability.md) を参照すること。

## サービス監視方針

### 監視対象

- 共有リソース
  - ★ (2025/01 時点) 現状は共有リソースの監視対象はなし。監視対象が追加された場合、ここに追加する。
- 専有リソース
  - 各 AWS リソースのメトリクス
  - ECS コンテナ内のアプリケーションログ
  - ALB のアクセスログ
  - CodeBuild のビルドログ
  - ElastiCache のスローログとエンジンログ
- 外部サービス
  - TiDB のメトリクス

### 監視指標

#### 共有リソース

- ★ (2025/01 時点) 現状は共有リソースの監視対象はなし。監視対象が追加された場合、ここに追加する。

| メトリクス | 詳細 |
| ---------- | ---- |
| xxx        | xxx  |

#### 専有リソース

**ALB**
|メトリクス|詳細|
|---|---|
|RequestCount|ALB に到達したリクエストの総数|
|NewConnectionCount|新しく確立された TCP 接続数|
|RejectedConnectionCount|容量不足などで拒否された接続数|
|ClientTLSNegotiationErrorCount|クライアントとの TLS ネゴシエーション中に発生したエラー数|
|HTTPCode_ELB_5XX_Count|ALB がクライアントに返した HTTP5XX 系ステータスコードの数|
|HTTPCode_ELB_4XX_Count|ALB がクライアントに返した HTTP4XX 系ステータスコードの数|
|HTTPCode_Target_2XX_Count|ALB 経由でターゲットがクライアントに返した HTTP2XX 系ステータスコードの数|
|HTTPCode_Target_5XX_Count|ALB 経由でターゲットがクライアントに返した HTTP5XX 系ステータスコードの数|
|HTTPCode_Target_4XX_Count|ALB 経由でターゲットがクライアントに返した HTTP4XX 系ステータスコードの数|
|TargetConnectionErrorCount|ターゲットへの接続時に発生したエラーの数|
|TargetTLSNegotiationErrorCount|ターゲットとの TLS ネゴシエーション中に発生したエラーの数|
|TargetResponseTime|ターゲットがリクエストに応答するまでの平均時間|
|RequestCountPerTarget|ターゲット毎のリクエスト数|

※ [追加のメトリクス](https://docs.aws.amazon.com/ja_jp/elasticloadbalancing/latest/application/load-balancer-cloudwatch-metrics.html) を計測する場合も連携方法は同じ。

**ECS/Fargate**
|メトリクス|詳細|
|---|---|
|CPUUtilization|クラスターまたはサービスで使用されている CPU ユニットの割合|
|MemoryUtilization|クラスターまたはサービスで使用されているメモリの割合|

**ECS/ContainerInsights**
|メトリクス|詳細|
|---|---|
|DesiredTaskCount|ECS サービスに必要なタスク数|
|RunningTaskCount|現在 Running 状態にあるタスク数|
|PendingTaskCount|現在 Pending 状態にあるタスク数|

**ElastiCache**
| メトリクス名 | 詳細 | プロビジョンド | サーバーレス |
|---|---|---|---|
| CPUUtilization | ホスト全体の CPU 使用率 ※ノードタイプが 2 vCPUs 以下 | 〇 | |
| EngineCPUUtilization | エンジンスレッドの CPU 使用率 ※ノードタイプが 4 vCPUs 個以上 | 〇 | |
| ElastiCacheProcessingUnits | リクエストによって消費された ECPUs の合計数 | | 〇 |
| SwapUsage | ホストで使用されるスワップのバイト数 | 〇 | |
| Evictions | キャッシュによって削除されたキーの数 | 〇 | 〇 |
| CurrConnections | キャッシュノードに接続されているクライアントの数 | 〇 | 〇 |
| FreeableMemory | ホスト上で使用可能な空きメモリの量 | 〇 | |
| DatabaseMemoryUsagePercentage | クラスターノードが使用しているメモリの割合 | 〇 | |
| NetworkBytesIn | キャッシュに転送された合計バイト数 | 〇 | 〇 |
| NetworkBytesOut | キャッシュから転送された合計バイト数 | 〇 | 〇 |
| SuccessfulReadRequestLatency | 読み取りリクエストのレイテンシー | 〇 | 〇 |
| SuccessfulWriteRequestLatency | 書き込みリクエストのレイテンシー | 〇 | 〇 |
| ReplicationBytes | プライマリノードがレプリカノードに送信するバイト数 | 〇 | |

**ログ**

- コンテナアプリケーションの標準出力/標準エラー出力
- S3 に保存される ALB のアクセスログ
- CodeBuild の ECS デプロイ失敗エラーログ
- ElastiCache のクエリログ

#### TiDB (外部サービス)

★ ここに TiDB の監視設計を記載する。

| メトリクス | 詳細 |
| ---------- | ---- |
| xxx        | xxx  |

## JIG-SAW 社との連携方針

- Gevanni では監視項目の取得に AWS マネージドサービスである CloudWatch や EventBridge を利用するが、最終的なアラート通知には JIG-SAW 社の通知システムを使用する。
- アラートのしきい値や監視対象、重要度は内製案件毎に異なるため、アプリチームが方針詳細を決める。
