# 耐障害性方式

## 基本方針

- データセンターやハードウェアなどの物理的な部分はAWS管理のデータセンタで冗長化されているため考慮しない。
- 本システムのサービス提供で使用するサービスにおいて、複数インスタンスかつマルチAZ構成とし、単一のインスタンスまたは単一のAZ障害時にサービス継続ができるものとする。
- 各サービスに関する障害対策について以下に記載する。

|  サービス名　|  障害対策方式  |  AWS SLA  |  備考  | 
| ---- | ---- | ---- | ---- | 
| VPC（サブネット） | 複数AZを使用 | - | NWという論理的なグループのためSLAは無し |
| VPC（インターネットゲートウェイ） | サービスで冗長化 | - | AWS側にSLA記載なし |
| VPC（NAT Gateway） | 異なるAZに3個配置 | 99.9%(1つあたり) |  |
| VPC（ゲートウェイ型VPCエンドポイント） | サービスで冗長化 | - | AWS側にSLA記載なし |
| ECS | マルチAZ構成 | 99.99%  |  |
| ECR | サービスで冗長化 | 99.9% |  |
| Elastic Load Balancing | サービスで冗長化 | 99.99% |  |
| Aurora | マルチAZ構成 | 99.99% |  |
| S3 | サービスで冗長化 | 99.9% |   |
| OpenSearch | マルチAZ構成 | 99.9% |  |
| CloudFront | サービスで冗長化 | 99.9% |  |
| AWS WAF | サービスで冗長化 | 99.95% |  |

**マーケットプレイスサードパーティサービス**

| サービス名 | 障害対策方式 | SLA | 備考 |
| --- | --- | --- | --- |
| TiDB Cloud Dedicated | サービスで冗長化 | 99.99% | SLA 参考: https://www.pingcap.com/legal/service-level-agreement-for-tidb-cloud-services/#:~:text=99.99%25%20for%20TiDB%20Dedicated%20clusters |
| TiDB Cloud Serverless | マルチ AZ 構成 | 99.9% | SLA 参考: https://www.pingcap.com/legal/service-level-agreement-for-tidb-cloud-services/#:~:text=99.9%25%20for%20TiDB%20Serverless%20clusters |

- 上記の表で「サービスで冗長化」となっているサービス以外について、利用者側で耐障害性設計項目があるため、それらのサービスについて詳細を記載する。

## 各種リソースの耐障害性

## AZ耐障害性方式（VPC）

- ３つのAZに対し各サブネット（Public.Private,Protected）を作成する。
- 各AWSサービスのリソースを複数AZのサブネットに分散配置することで、AZ障害時もサービス継続が可能な構成とする。

![](/docs/images/subnet-fault-tolerance.dio.svg)

## NAT Gateway耐障害性方式

- NAT Gatewayはインスタンスレベルの障害はAWS管理となるため、特に設計は不要で自動復旧となる。
- AZ障害があった場合は、当該AZについてNAT Gatewayを使用したVPC外への通信ができなくなるが、NAT Gatewayを使用する送信元のAWSサービスも合わせて障害中（利用不可）となっているため、全体としてサービス影響は無い。

![](/docs/images/natgateway-fault-tolerance.dio.svg)

## Elastic Load Balancing耐障害性方式

- 今回使用するElastic Load Balancingの1機能であるApplication Load Balancerは、2AZ以上の構成が必須となるため、AZ障害時に当該AZを切り離せるように3AZ構成とする。
- AZの障害が発生し、中途半端に通信が残ってしまい、手動切り離しが必要な場合は、当該AZをマネジメントコンソールより除外する。

![](/docs/images/alb-fault-tolerance.dio.svg)

## Aurora耐障害性方式

### インスタンス障害発生時

#### プロビジョンドタイプの場合

- インスタンス障害やAZ障害に耐えるため、本システムではマルチAZ構成を採用する。
- プライマリインスタンスの障害時は、30秒以内にAuroraレプリカ（スタンバイ）の昇格が行われサービスが復旧する。復旧後、プライマリインスタンスは自動的に冗長構成に組み込まれAuroraレプリカ（スタンバイ）として稼働する。アプリケーションからは、クラスターエンドポイントに接続していれば接続先の切替対応は不要。レプリカの昇格が行われる間(最大30秒)は書き込み処理が不可となる。
- Auroraレプリカ障害時は、書込処理については、プライマリインスタンスが使用されているため、Auroraレプリカ障害による影響は発生しない。読込エンドポイントを使用する読込処理は一時的に処理がエラーになるが、プライマリインスタンスに読込エンドポイントが通信するようになり復旧する。
- AZ障害があった場合も、各AZにインスタンスが配置されているため、インスタンス単体の障害と同様の動作となる。稼働しているAZで各インスタンスが復旧する。

![](/docs/images/aurora-serverless-instance-fault-tolerance.dio.svg)

#### ServerlessV2の場合

- ServerlessV2は1つのインスタンスタイプとして稼働するため、フェイルオーバーの挙動はプロビジョンドタイプと同様になる。

![](/docs/images/aurora-serverless-instanceV2-fault-tolerance.dio.svg)

### クラスターボリューム障害発生時

- クラスターボリュームは以下のような構成で管理はAWSによって行われる。特に構成の設計は利用者側で不要となる。
  - クラスターボリュームは3AZにまたがり計6つ（各AZに2つのディスクが配置）のディスクが分散配置される。
  - 6つのディスクのうち、2つのディスクに障害が発生した場合でも読み込み/書き込みが可能で、3つのディスクに障害が発生した場合でも読み込みは継続が可能。
- ServerlessV2の場合もクラスターボリュームの管理は同様となる

![](/docs/images/aurora-serverless-clustervolume-fault-tolerance.dio.svg)


## ECS耐障害性方式

### コンテナ実環境（データプレーン）障害時

- 本システムではタスクの実行環境にFargateを採用する。FargateはAWSによるフルマネージドサービスのため可用性はAWSによって担保されるため考慮しない。

### コンテナ（ECSタスク）障害発生時

- ECSサービスの作成時に稼働するサブネットを指定するため、Privateサブネットを各AZ分選択することにより、AZの冗長構成を取れるようにする。
- ECSタスクに障害が発生した場合は、サービスで設定したタスク数を維持するよう自動的に復旧が行われる。
- AZ単位で障害が発生した場合は、障害が発生していないAZに対して再配置が行われる。

![](/docs/images/ecs-task-alb-fault-tolerance.dio.svg)

## OpenSearch耐障害性方式(プロビジョンド)

#### マスターノード障害発生時

- マスターノードに障害が発生した際は、マスター候補がマスターに自動で昇格する。
- OpenSearchではクォーラムという仕組みを採用しており、クォーラムとはマスター（候補）ノードに障害が発生した際に新たなマスターノードの選出をする上で最低限参加すべきノードの最小数を指す。
- クォーラムの数は、マスターノードの数/2 + 1 (直近の整数まで切り捨て) となり、AWSでは[3台が推奨されている。](https://docs.aws.amazon.com/ja_jp/opensearch-service/latest/developerguide/managedomains-dedicatedmasternodes.html#dedicatedmasternodes-number
)ため、本システムでは3台構成とする。
- マスターノードに障害があった場合、クォーラムの仕組みによって新たなマスターノードがマスター候補から即座に選択されるため、[ダウンタイムが発生しない。](https://docs.aws.amazon.com/ja_jp/opensearch-service/latest/developerguide/managedomains-multiaz.html#managedomains-za-summary)

![](/docs/images/opensearch-provisioned-masternode-fault-tolerance.dio.svg)

#### データノード障害発生時

- 障害が発生したノードはクラスタから削除され、新しいノードがクラスタに登録される。
- データノードやAZ障害時、AWS側でヘルスチェック管理が行われるためサービス断は発生しないが、一時的に特定のデータノードに負荷がかかる可能性がある。2AZ分のインスタンスでも充分な性能が発揮できるようインスタンスタイプを選択しておくようにする。
- OpenSearchのバージョン1.3、2.3によってデータノード障害時の動作が以下のように変わってくるが、いずれの場合も書き込みおよび読み込み処理は継続して実行可能となる。
  - 1.3は、別の稼働ノードにシャードが再作成され、シャード内のデータが再分散される（ノードあたりのシャード数が増えて高負荷となる）
  - 2.3は、ノードの再作成は行われず、プライマリ→レプリカの切替のみ行われる（レプリカあたりの読込アクセスは増える可能性がある）
- [各バージョンの詳細な動作についてはAWS公式のブログ情報を参考](https://aws.amazon.com/jp/blogs/news/impact-of-infrastructure-failures-on-shard-in-amazon-opensearch-service/)

![](/docs/images/opensearch-provisioned-node-fault-tolerance.dio.svg)

#### AZ障害発生時

- AZ障害時は前述したマスターノード障害時、データノード障害時の動作が同時に発生するのみで、各動作に違いはない。

## OpenSearch耐障害性方式(サーバレス)

- OpenSearch Serverlessはコンピューティングノードとストレージノード（S3）が分離している。
- 加えて、コンピューティングノードに関しては更にインデックス作成ノードとクエリ用ノードで役割が分かれている。
- インデックス作成ノードとクエリ用ノードは、障害発生時に可用性を維持するために、アベイラビリティゾーン間でアクティブ/アクティブ モードでデプロイされます。
- デフォルトでは、OpenSearch Serverless はアベイラビリティ ゾーン間でインデックスを複製され、インデックス化されたデータはAmazon S3に保存されているため、高い可用性を (11 ナイン) を提供する。
- [AWS公式ブログ](https://aws.amazon.com/jp/blogs/big-data/amazon-opensearch-serverless-is-now-generally-available/)

![](/docs/images/opensearch-serverless-fault-tolerance.png)


## ElastiCache 耐障害性方式

### プライマリ・レプリカノード障害発生時

- プライマリノードに障害が発生した場合には、レプリケーションの遅延が最短のリードレプリカがプライマリに昇格される。フェイルオーバ中はダウンタイムが10-20秒発生するため、その間はアプリケーションからの書込みは不可となる。
- リードレプリカノードに障害が発生した場合には、障害が発生した同じAZに置き換え用のリードレプリカが作成およびプロビジョニングされる。

![](/docs/images/redis-node-fault-tolerance.dio.svg)

- ElastiCacheではハッシュスロット番号がシャードごとに振られるが、プライマリに採番されているハッシュスロットを取得したアプリケーションは、フェールオーバが完了するまで書き込み処理が不可となる。障害が発生したプライム以外のノードに書き込む場合、または読み込み処理をする場合はアプリケーション側に特に影響は出ない。

![](/docs/images/redis-fault-tolerance.dio.svg)

### AZ障害発生時

- AZ単位で障害が発生した際は、レプリケーションの遅延が最短の別AZ内のリードレプリカがプライマリに昇格される。
- プライマリの切替はAZ間で行われるが、リードレプリカの復旧は同AZ内で行われるため、AZが復旧するまで通常時よりも少ないノード数で稼働することになる。

![](/docs/images/redis-az-fault-tolerance.dio.svg)

※プロビジョンドタイプはプライマリノードの障害に備えてレプリカノードの手配や、AZ障害に備えたマルチAZ構成でのデプロイをする必要があったが、サーバレスではノードの可用性はAWS側で担保されるため、ユーザーが意識する必要はない。

## TiDB 耐障害性方式

- クラスターデプロイ時に `リージョン高可用性` を有効にすることでマルチ AZ 構成を取ることができる。
- プライマリ AZ とスタンバイ AZ の間でレプリケーションを行い可用性を確保する。

### AZ 障害発生時

- プライマリ AZ 単位で障害が発生した際は、ゲートウェイや TiDB などの重要な OLTP ワークロード コンポーネントがスタンバイ AZ で自動的に起動される。
- トラフィックはスタンバイ AZ に自動的に切り替わりることで復旧と継続性の維持が保証される。
  - スタンバイ AZ に Gateway と TiDB の新しいレプリカを自動的に作成される。
  - ELB を使用して 障害が発生した AZ からスタンバイ AZ へトラフィックがリダイレクトされる。

![](/docs/images/serverless-regional-high-avaliability-aws.png)

出典：https://docs.pingcap.com/ja/tidbcloud/serverless-high-availability#regional-high-availability-architecture

## DR 設計

- ここでは DR 設計の方針について記載する。
- ここで記載する DR 構成とは、システム障害や災害が発生した場合でもサービスを継続できるように、クロスリージョンで復旧や冗長性を確保する仕組みのこと。
- Gevanni では EFS と Aurora に対して DR 構成を実装する。
- 2025年03月時点では RTO がタイトではないプロジェクトがメインのため、日次バックアップレベルの実装を行う。
  - ※ よりタイトな RTO への対応は、要望がきてからか、もしくは他に優先度の高い issue がない時に対応を行う想定。
  - 要望が来た場合、EFS Replication や Aurora Global Database 等を検討する。
- 日次バックアップには AWS Buckup を利用しバックアップをクロスリージョン構成で配置する。
- 構成イメージは以下の通り。  
  ![](/docs/images/dr-backup-architecture.dio.svg)

### EFS

- AWS Backup にバックアップをとり、別リージョンにセカンダリ AWS Backup をレプリケーションする。
- バックアップ期間
  - 期間はデフォルトで2週間を指定している。
    - ※ 1週間の場合、年末年始など1週間を超える長期休みがある場合不足するため。
  - 専有リソースのパラメーターファイルによってサービスごとのバックアップ期間を制御しているため、アプリチームの要望により、サービス単位で2週間以上にすることも可能。
- バックアップ方法
  - Backup Plan を作成し、バックアップスケジュールを設定して日次バックアップを Backup Vault に保存。
  - Backup Vault を別リージョンにセカンダリとしてコピーを保存する。
- データ損失
  - Backup Plan で設定したバックアップスケジュールの時点から障害発生までの時間分。
- CDK 実装
  - 別リージョンに Backup スタックをデプロイする必要があるため、Backup スタック分離させて実装する。
    - プライマリリージョン用と、セカンダリリージョン用に Backup Vault をデプロイするようにスタックを2つ定義する。
  - Backup Plan コンストラクタを作成し、バックアップスケジュールとセカンダリリージョンの Backup Vault にコピーする設定を記載する。

### Aurora

- AWS Buckup にバックアップを取り、別リージョンにセカンダリ AWS Backup をレプリケーションする。
- バックアップ方法
  - Backup Plan を作成し、継続的なバックアップを有効にする。
  - AWS Backup Vault を別リージョンにセカンダリとしてコピーを保存する。
- データ損失について
  - [ポイントインタイムリカバリに関する考慮事項](https://docs.aws.amazon.com/ja_jp/aws-backup/latest/devguide/point-in-time-recovery.html?icmpid=docs_console_unmapped#point-in-time-recovery-considerations) に基づき、RPO は **5 分**。
- CDK 実装
  - 別リージョンに Backup スタックをデプロイする必要があるため、Backup スタック分離させて実装する。
    - プライマリリージョン用と、セカンダリリージョン用に Backup Vault をデプロイするようにスタックを2つ定義する。
  - Backup Plan コンストラクタを作成し、継続的なバックアップ有効とセカンダリリージョンの Backup Vault にコピーする設定を記載する。
