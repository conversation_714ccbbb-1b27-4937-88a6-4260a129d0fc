# 全体概要

## Gevanni とは

- Gevanni とはマイナビ社内で作成された AWS の集約基盤である。
- マイナビ社内では内製案件が年々、増加傾向にあり、従来は案件毎に AWS アカウントを払出して、各インフラメンバが要件をヒアリングした後、CDK コードをカスタマイズしていた。
- ただし、その手法では構築にリードタイムがかかってしまうため、内製案件を一つのコンテナ基盤に集約し、インフラ構築をある程度自動化することでリードタイムを短縮することを目的としている。
- 利用者（主にアプリケーションチーム）は Google Form から必要項目を入力し申請ボタンを押下することで、バックエンドで StepFunctions が動き、案件周りのリソース（主に AWS）がある程度、自動で展開されるようになっている。
  ※Gevanni とはデスノートに登場するキャラクタで、「ジェバンニが一晩でやってくれました」というフレーズからインフラ構築のリードタイムを短くして、利用者であるアプリケーションチームの利便性を向上させることを目的として命名された。

## 全体構成図

- 構成図は以下のとおりで、主に ECS を中心とした構成となっている。
- Gevanni では**専有リソース**と**共有リソース**という概念があり、**専有リソース**とは各内製案件毎に作成されるリソース（ECS クラスタ等）を指し、**共有リソース**とは基盤全体で共有されるリソース（VPC 等）を指す。
  ![システム構成図](../images/gevannni-overall-architecture.dio.png)

## AWS アカウント戦略

### 各環境の定義

- prod
  - 本番環境
- stg
  - 検証環境(アプリチーム向け)
    - 1 つの prod 環境に対し、stg 環境が 2 つ以上存在する場合もある。
      - アプリチームの開発方針上、開発環境とステージングを分けたい場合
      - 負荷試験環境が必要な場合
- dev
  - 検証環境(Gevanni 管理者向け)
    - Gevanni 自体の検証に使用
    - アプリチームはこの環境にはアクセス不可

### AWS アカウントの分割方法

#### 本体用アカウント

- 本体のリソース
  - VPC
  - ALB
  - ECS
  - ECR
  - コンテナのパイプライン
- 初期リリース(2024 年 4 月)時点では、各環境ごとに 1 アカウント
  - サービスの増加とともに、クォータを考慮してスケールアウト予定

#### Route53 用アカウント

- ALB のドメイン用ホストゾーンを管理
  - CloudFront(または他 CDN)のオリジンとして設定するドメイン
  - 上記ドメインの ACM 認証用 CNAME レコードと ALB 用 Alias レコード。
  - 上記についても CDK で管理する。
- 本体用から分ける理由
  - マイナビの全ドメインは、インフラチームの 1 つの AWS アカウント内の Route53 で集中管理している。
  - しかし、ALB のドメインは社外ユーザーが直接アクセスしないので、新規サービス作成時のリードタイム短縮のため、集約基盤専用のサブドメインをこのアカウントに移譲する。
  - 本体用アカウントがスケールする際に CDK コードの差異を生じさせないため

#### マーケットプレイス購入用アカウント

- 各環境の本体用アカウントから共通で使うリソースのマーケットプレイス購入用
  - AWS マーケットプレイスからこのアカウントで TiDB Cloud を購入する。
  - 購入リソースの例
    - TiDB Cloud
    - PostgreSQL 版の TiDB のようなサービス(CockroachDB や YugabyteDB など)
- 本体用から分ける理由
  - クォータの都合により、本体用アカウント間でサービスを移動する必要が生じる可能性がある。
  - 移動の際は、データ移行が一番のネックになると思われる。
  - そのため、TiDB を管理する AWS アカウントは、各環境ごとに共通としたい。

#### アプリ管理用アカウント

- アプリ側が管理するリソース
- CloudFront や CloudFront にアタッチする WAF、OpenSearch Serverless 等はアプリ側の AWS アカウントで管理する方針となっている。理由は以下のとおり。
  - CloudFront
    - キャッシュ設定やパス設定がアプリケーションごとに大きく異なるため。
    - Traffic 料金は請求タグによる費用振り替えできないため、CloudFront 宛通信は主管部署が請求先となっている AWS アカウントで管理するため。
    - Gevanni の ALB の仕様として、CloudFront 以外の CDN も対応可能にしているが、マイナビでは CloudFront 以外の CDN は事業部管理になることが多いため、CDN は基本的にアプリ管理としている。
  - WAF
    - 使用するマネージドルールやどのルールを除外するかがアプリケーションごとに大きく異なるため。
    - 誤検知があった場合、対象ルールの除外をアプリチームだけで迅速に行えるようにするため。
  - OpenSearch Serverless
    - サーバレスの管理ユニットである OpenSearch Compute Units (OCU) はアカウント全体で共有されるため、本体用アカウントにデプロイすると上限に抵触する可能性があるため。
  - CodePipeline
    - BlueGreen デプロイ時に CodeDeploy のタスク切替をアプリチームの任意のタイミングで行えるようにするため。
    - デプロイ開始後に進捗状況をリアルタイムで把握できるようにするため。

### 本体用アカウントのスケールアウトと、各環境の命名

- AWS アカウントは、環境(prod/stg/dev)ごとに分ける。
- サービスの増加に伴い、AWS アカウントのクォータに達してしまうので、各環境の AWS アカウントをスケールアウトする必要がある。
- スケールアウトのため、各環境の末尾に数字を付与する。
  - prod01,prod02,...
  - stg01,stg02,...
  - dev01,dev02,...

### AWS アカウント命名規則

- `<Env>`: `prod/stg/dev`
- `<Num>`: `01,02,03,...`
- プレフィックス
  - `mynavi_csys-gevanni-`
- 本体用アカウント
  - `orchestrator-<Env><Num>`
- Route53 用アカウント
  - `route53-<Env>`
- マーケットプレイス購入用アカウント
  - `common-mp-<Env>`

## 共有リソースと専有リソース

### 定義

- 共有リソース: 環境単位で作成
- 専有リソース: サービス単位で作成

### 共有リソース一覧

- VPC
- サブネット
- NAT Gateway
- VPC エンドポイント
- セキュリティグループ
  - VPC エンドポイント用
- ECS(踏み台)
  - ECS クラスター
  - ECR
- CDK デプロイパイプライン
- Route53 ホストゾーン(ALB ドメイン用)
- TiDB 組織

### 専有リソース一覧

- セキュリティグループ
  - VPC エンドポイント用以外
- ALB
- ACM
- WAF
- ECS(踏み台)
  - ECS タスク
- ECS(アプリ、バッチ)
  - ECS サービス
  - ECS タスク
  - ECR
  - コンテナデプロイパイプライン
  - Secrets Manager(コンテナ環境変数用)
- TiDB
  - プロジェクト
  - Serverless クラスター
- EFS
- ElastiCache
- OpenSearch 接続用 VPC エンドポイント
- CloudWatch
  - メトリクス
  - ダッシュボード

## VPC/サブネット設計

### 方針

- 基本的に CDK ベースコード通りだが、Gevanni の仕様に合わせて調整
- サブネット
  - ECS タスクを大量に起動するため、サブネットマスクを変更
- 3AZ
- NAT Gateway は 3 つ
  - CDK ベースコードのデフォルト設定は 2 つだが、2AZ 使用できない障害に備えて 3 つにしておく。
- AWS サービスへの接続は VPC エンドポイントを使用する。
- Gevanni 外のシステムとは、VPC ピアリングや Direct Connect による接続を行わない。
  - ただし Gevanni 内では、VPC ピアリング接続する可能性あり(詳細は「VPC の CIDR」に記載)
- PrivateLink は使用する。
  - TiDB Cloud 接続用
    - 参考: [プライベートエンドポイント経由で TiDB サーバーレスに接続する](https://docs.pingcap.com/ja/tidbcloud/set-up-private-endpoint-connections-serverless)

### VPC の CIDR

- 各環境(prod/stg/dev)共通で以下のような CIDR とする。
  - 01 環境：10.101.0.0/16
  - 02 環境：10.102.0.0/16
  - 03 環境：10.103.0.0/16
- 理由
  - prod01->prod02 のようなサービス移行する際に VPC ピアリングを使う可能性があるため。
  - CDK ベースコードのデフォルトは 10.100.0.0/16 だが、第二オクテット(10x)と環境ナンバーの対応関係をわかりやすくするため上記のように採番
  - セキュリティ上、prod/stg/dev 間で VPC ピアリングはしないので、prod01/stg01/dev01 は重複しても問題ない。

### サブネット

#### サブネットの定義

- Public: インバウンド/アウトバウンド共に通信が可能なサブネット
- Private: アウトバウンドのみ NAT Gateway を経由して通信が可能なサブネット
- Protected: VPC 外部への通信が一切できないサブネット

#### 各サブネットのリソースと IP アドレス最大想定利用数

- サービス数の最大値
  - 50 と仮定
    - [リージョンあたりの ALB のクォータ](https://docs.aws.amazon.com/ja_jp/elasticloadbalancing/latest/application/load-balancer-limits.html)が 50 のため
- Public
  - NAT Gateway: 1
  - ALB: サービス数
- Private
  - ECS タスク
    - フロントエンド: サービス数 x 5 (3AZ 合計で 15)
    - バックエンド: サービス数 x 5 (3AZ 合計で 15)
    - バッチ: サービス数 x 10(複数バッチ並行実行を想定)
    - 踏み台: アプリチームのメンバー数(ベンダー含む) / 3
      - サービスごとのロールで起動するため、1 人が同時に複数起動はしない想定
- Protected
  - EFS: サービス数
  - ElastiCache: サービス数
- 各サブネットの IP アドレス最大数(予約 IP アドレスは除く)
  - Public: 51
  - Private: 1000 + 踏み台利用者数 / 3
  - Protected: 100

#### 各サブネットのサブネットマスク

- サブネットマスクごとの IP アドレスの数(AWS の予約 IP アドレス含む)
  - 24: 256
  - 22: 1024
  - 20: 4096
  - 18: 16384
- CDK ベースコードでは以下の通り(オリジナルの blea と同一)
  - Public: 24
    - 利用可能な IP 数: 256
  - Private: 22
    - 利用可能な IP 数: 1024
  - Protected: 22
    - 利用可能な IP 数: 1024
  - ※利用可能な IP 数は、AWS の予約 IP アドレスを含む。
- Private が 1024 だと不足しそうなので、可能な限り多くなるように CDK ベースコードから変更する。
  - Public: 24
  - Private: 18
  - Protected: 22

## CDK としての管理方針

- Gevanni 自体は基本的に CDK によって管理する。
- ベースコードとは異なり内製案件毎に Git リポジトリは作成せずに、基本的に共通のリポジトリを利用する。
- 存在するリポジトリは下記のとおり。

  - [csys-infra-gevanni-infra](https://github.com/mynavi-group/csys-infra-gevanni-infra)
    - Gevanni の本体を管理するリポジトリ。共有リソースと専有リソースを管理している。
  - [csys-infra-gevanni-workflow-sample](https://github.com/mynavi-group/csys-infra-gevanni-workflow-sample)
    - アプリケーションチームが利用するワークフローファイル（GHA）を管理するリポジトリ。ECS 上にデプロイするための物やバッチ専用のワークフロー等が管理されている。
  - [csys-infra-gevanni-cf-sample](https://github.com/mynavi-group/csys-infra-gevanni-cf-sample)
    - アプリケーションチームで管理する AWS リソースを管理するリポジトリ。CDK のサンプルコード（CloudFront や、CloudFront にアタッチする WAF 等）が格納されている。
  - [csys-infra-gevanni-sampleapp](https://github.com/mynavi-group/csys-infra-gevanni-sampleapp)
    - インフラがテストする際に利用するリポジトリ。他のリポジトリに影響を及ばさないようにテスト専用のリポジトリを作成。

- 各ブランチと AWS アカウントの関係図は以下の通り。  
  ![](/docs/images/branch-aws-account.dio.svg)

# 共有リソースのデプロイ

## 概要

- 共有リソースのコードの変更は申請フォームトリガーではなく、インフラチームにより手動で行う。

**前提**

- Gevanni は prod/stg 環境が複数存在する。
- それぞれの環境に対応したブランチをリリースブランチとする。リリースブランチのコードを対応したアカウントへデプロイしていく。
  - prod01,prod02,...
  - stg01,stg02,...
- main ブランチには、すべての設定ファイルをマージするが、main ブランチから直接リリースは行わない。

## 各環境へのデプロイフロー

**フロー図**  
![](../images/scaling-branch-strategy.dio.svg)

1. dev 環境での検証

   1. main ブランチから feature ブランチを作成し、CDK コードを変更する。
   2. dev 環境で動作確認を実施し、問題がなければ stg01 ブランチ へのプルリクエストを作成する。

1. stg01 環境へのデプロイ

   1. GitHub Actions 上で stg01 ブランチ へのプルリクエスト作成をトリガーに単体テストを実施する。
   2. テスト結果に問題がなければ、レビューを依頼する。レビュアーはプルリクエストを確認し、問題がなければ承認する。本変更のレビューは完了しているため、別ブランチに反映する際のレビューは不要である。
   3. stg01 ブランチへのマージをトリガーに、stg01 環境上の S3 バケットにコードの ZIP ファイルを格納し、cdk deploy が実行される。

1. stg02 環境へのデプロイ

   1. stg01 ブランチへのマージが完了したら、stg02 ブランチにも変更を反映する。
   2. stg01 ブランチと同様に、stg02 ブランチへのマージをトリガーに stg02 環境へのデプロイが実行される。

1. stg01 環境,stg02 環境での動作確認

   1. 各 stg 環境で最低一週間以上動作確認を実施する。stg 環境で不具合が発生しなければ、prod 環境にも反映していく。

1. prod 環境へのデプロイ
   1. prod01 環境と prod02 環境も stg 環境と同様に、対応したブランチへのマージをトリガーに各環境にデプロイが実行される。

# サービス専有リソースのデプロイフロー

## 概要

- アプリケーションチーム からの AWS リソース 作成/更新 申請から、承認・デプロイまでのフローについて記載する。
- CDK デプロイ時に利用される CI/CD はベースコードの CodePipeline リソースから流用している。
- デプロイフローは 新規作成 / 個別変更 / 一括変更 の 3 つ存在する。
  - 新規作成 と 個別変更のデプロイフローは基本的に同一だが申請内容が異なる。
  - 一括変更は上記 2 つとフローが異なるため、詳細は検討中。

**役割 (ロール) について**

- デプロイフローで関わる役割 (ロール) が、申請者、承認者、作業者の 3 つ存在する。
- 役割についての説明は以下の通り。
  - 申請者
    - 内製案件のインフラとして、必要な AWS リソースを申請するアプリケーションチーム。
  - 承認者
    - 用途が適切かどうか、申請内容の妥当性を確認する。
  - 作業者
    - 自動生成された CDK コードの確認。
    - CDK 管理外の設定を行う。
    - GitHub プルリクエストをマージする。

**デプロイフローの流れ**

- デプロイフローイメージは以下の通り。  
  ![](/docs/images/application-flow-for-creating-aws-resource.dio.svg)
- デプロイフローの大まかな流れは以下の通り。
  1. 申請者が Google Forms から AWS リソースの作成申請を行う。
  1. GAS によって申請内容を S3 バケットへアップロード。
  1. S3 へのアップロードをトリガーに Step Functions ワークフローが実行。
  1. Backlog に申請チケットが自動作成される。
  1. 承認者は、申請チケットを確認し承認する。
  1. 作業者は、承認されたチケットに記載してあるリンクから、承認トリガーを発火させる。
  1. 自動で GitHub にプルリクが作成され、プルリク作成をトリガーに cdk diff を実行。
  1. 意図しない変更等がないか作業者は cdk diff を確認し、問題なければマージをする。
  1. プルリクのマージがトリガーに CDK コードを 圧縮/アップロード を行うワークフロー実行。
  1. S3 へのアップロードをトリガーに CodeBuild を実行。
  1. CodeBuild が cdk deploy を実行し AWS リソースをデプロイ。
  1. 作業者は自動化できない手動作業を行う。
  1. デプロイが完了したら申請者に完了通知メールを送信する。
