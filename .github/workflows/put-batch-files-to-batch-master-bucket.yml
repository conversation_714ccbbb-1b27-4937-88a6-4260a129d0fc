name: upload image to BatchMasterBucket

on:
  pull_request_target:
    branches: ["prod01", "stg01"]
    paths: ["scripts/deploy_batch/**"]
    types:
      - closed

env:
  # parameter is case-sensitive.
  environment: ${{ github.ref_name == 'prod01' && 'Prod01' || github.ref_name == 'stg01' && 'Stg01' }}

permissions:
  id-token: write
  contents: read

jobs:
  build:
    if: github.event.pull_request.merged == true
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.batch_confmasterbucketrole }}
          role-session-name: GitHubActions
          aws-region: ap-northeast-1

      - name: Get BatchMasterBucket
        run: |
          echo ${{ env.environment }}
          batch_master_bucket=$(aws ssm get-parameter --name "/${{ env.environment }}-GEVANNI-common/BatchMasterBucketName" --query "Parameter.Value" --output text)
          echo "BatchMasterBucket is $batch_master_bucket"
          echo "batch_master_bucket=$batch_master_bucket" >> $GITHUB_ENV

      - name: Upload files to BatchMasterBucket
        run: |
          aws s3 cp \
            ./scripts/deploy_batch \
            s3://"$batch_master_bucket"/deploy_batch \
            --recursive
